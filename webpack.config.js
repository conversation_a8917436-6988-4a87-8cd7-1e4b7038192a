const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const fs = require('fs');

// ⬇️ Customize this if Evidence output is elsewhere
const EVIDENCE_BUILD_DIR = path.resolve(__dirname, 'evidence_build');
const EVIDENCE_INDEX = path.resolve(EVIDENCE_BUILD_DIR, 'index.html');
const DDX_TEMPLATE = path.resolve(__dirname, 'ddx-template.html');



// Create entry point for Evidence SvelteKit build
const createEntryPoint = () => {
  const entryPath = path.resolve(__dirname, 'temp-entry.js');
  let entryContent = '// Auto-generated entry point for Evidence SvelteKit build\n';

  // Check if Evidence build directory exists
  if (fs.existsSync(EVIDENCE_BUILD_DIR)) {
    // Find all CSS files in the Evidence build (including _app/immutable/assets)
    const findCssFiles = (dir) => {
      const files = [];
      if (fs.existsSync(dir)) {
        const items = fs.readdirSync(dir);
        items.forEach(item => {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          if (stat.isDirectory()) {
            files.push(...findCssFiles(fullPath));
          } else if (item.endsWith('.css')) {
            files.push(fullPath);
          }
        });
      }
      return files;
    };

    const cssFiles = findCssFiles(EVIDENCE_BUILD_DIR);
    cssFiles.forEach(cssFile => {
      const relativePath = path.relative(__dirname, cssFile);
      entryContent += `import './${relativePath.replace(/\\/g, '/')}';\n`;
    });

    // Skip service worker for Domo DDX compatibility (blob URLs not supported)
    console.log('🚫 Skipping Service Worker registration for Domo DDX compatibility');

    // Also import any main JavaScript files
    const indexHtml = path.join(EVIDENCE_BUILD_DIR, 'index.html');
    if (fs.existsSync(indexHtml)) {
      entryContent += `\n// Evidence app initialization\n`;
      entryContent += `console.log('Evidence app loaded for Domo DDX');\n`;
    }

    // Include enhanced Domo integration (with DuckDB filtering for DDX)
    const enhancedIntegrationPath = path.join(__dirname, 'enhanced-domo-integration.js');
    if (fs.existsSync(enhancedIntegrationPath)) {
      let enhancedIntegrationContent = fs.readFileSync(enhancedIntegrationPath, 'utf8');

      // Filter out any DuckDB-WASM imports or Service Worker registrations
      enhancedIntegrationContent = enhancedIntegrationContent
        .replace(/import.*duckdb.*from.*['"].*['"];?/gi, '// DuckDB import removed for DDX compatibility')
        .replace(/navigator\.serviceWorker\.register.*\);?/gi, '// Service Worker registration removed for DDX compatibility')
        .replace(/new Worker\(.*blob:.*\)/gi, '// Blob worker removed for DDX compatibility');

      entryContent += `\n// Enhanced Domo-DuckDB Integration (DDX Compatible)\n`;
      entryContent += enhancedIntegrationContent;
    }

    // Add iframe detection and compatibility fixes
    entryContent += `\n// Iframe compatibility and initialization\n`;
    entryContent += `
if (typeof window !== 'undefined') {
  // Enhanced iframe detection
  window.isInIframe = window.self !== window.top;
  window.isDomoEnvironment = typeof window.domo !== 'undefined' || window.isInIframe;

  // Fix for iframe asset loading
  if (window.isInIframe) {
    console.log('🖼️ Running in iframe mode - applying compatibility fixes');

    // Override console methods to prevent iframe issues
    const originalLog = console.log;
    console.log = function(...args) {
      try {
        originalLog.apply(console, args);
      } catch (e) {
        // Silently fail if console access is restricted
      }
    };

    // Fix relative URLs for iframe environment
    document.addEventListener('DOMContentLoaded', function() {
      const images = document.querySelectorAll('img[src^="_app/"]');
      images.forEach(img => {
        if (img.src.includes('_app/')) {
          img.src = img.src.replace('_app/_app/', '_app/');
        }
      });

      // Hide missing Evidence branding images to prevent 404 errors
      const brandingImages = document.querySelectorAll('img[src*="wordmark"]');
      brandingImages.forEach(img => {
        img.style.display = 'none';
        console.log('🖼️ Hidden Evidence branding image to prevent 404:', img.src);
      });
    });
  }

  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Evidence DDX App initialized');
    console.log('Environment:', {
      isIframe: window.isInIframe,
      isDomo: window.isDomoEnvironment,
      hasDomoAPI: typeof window.domo !== 'undefined'
    });
  });
}
`;
  }

  // Write the entry file
  fs.writeFileSync(entryPath, entryContent);
  return entryPath;
};

module.exports = {
  mode: 'production',

  // Create dynamic entry point
  entry: createEntryPoint(),

  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'app.js', // Single consolidated JS file for DDX
    clean: true,
    environment: {
      dynamicImport: false, // Disable dynamic imports for DDX compatibility
    },
  },

  externals: {
    // Prevent webpack from bundling DuckDB-WASM and related modules
    '@duckdb/duckdb-wasm': 'var null',
    'duckdb-wasm': 'var null',
    'duckdb': 'var null'
  },

  module: {
    rules: [
      {
        test: /\.html$/,
        loader: 'html-loader',
        options: {
          sources: false, // Disable automatic asset processing for Evidence SvelteKit build
        },
      },
      {
        test: /\.css$/i,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              url: {
                filter: (url) => {
                  // Don't process URLs that start with /_app/ - they're handled by CopyPlugin
                  return !url.startsWith('/_app/');
                },
              },
            },
          }
        ],
      },
      {
        test: /\.(png|jpe?g|gif|svg|ico|webp)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'assets/[name][ext]',
        },
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'assets/fonts/[name][ext]',
        },
      },
    ],
  },

  plugins: [
    new MiniCssExtractPlugin({
      filename: 'styles.css',
      chunkFilename: 'styles.css',
    }),
    new HtmlWebpackPlugin({
      template: DDX_TEMPLATE,
      filename: 'index.html',
      inject: 'head', // Inject CSS in head, JS at end of body
      scriptLoading: 'blocking',
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true,
      }
    }),

    new CopyPlugin({
      patterns: [
        // Copy all Evidence SvelteKit assets (preserving directory structure)
        {
          from: `${EVIDENCE_BUILD_DIR}/_app/**/*`,
          to: ({ context, absoluteFilename }) => {
            const evidenceBuildPath = path.resolve(context, EVIDENCE_BUILD_DIR);
            const relativePath = path.relative(evidenceBuildPath, absoluteFilename);
            return `_app/${relativePath}`;
          },
          noErrorOnMissing: true,
          filter: (resourcePath) => {
            // Don't copy CSS files (webpack handles them)
            if (resourcePath.endsWith('.css')) return false;

            // Don't copy DuckDB-WASM files that cause Service Worker issues
            if (resourcePath.includes('duckdb-browser') && resourcePath.includes('worker')) return false;
            if (resourcePath.includes('duckdb-eh.') || resourcePath.includes('duckdb-mvp.')) return false;
            if (resourcePath.endsWith('.wasm')) return false;

            return true;
          },
        },
        // Copy Evidence API and data files (preserving directory structure)
        {
          from: `${EVIDENCE_BUILD_DIR}/api/**/*`,
          to: ({ context, absoluteFilename }) => {
            const evidenceBuildPath = path.resolve(context, EVIDENCE_BUILD_DIR);
            const relativePath = path.relative(evidenceBuildPath, absoluteFilename);
            return `api/${relativePath}`;
          },
          noErrorOnMissing: true,
        },
        {
          from: `${EVIDENCE_BUILD_DIR}/data/**/*`,
          to: ({ context, absoluteFilename }) => {
            const evidenceBuildPath = path.resolve(context, EVIDENCE_BUILD_DIR);
            const relativePath = path.relative(evidenceBuildPath, absoluteFilename);
            return `data/${relativePath}`;
          },
          noErrorOnMissing: true,
        },
        // Copy Evidence static assets (excluding JS files that are bundled)
        {
          from: `${EVIDENCE_BUILD_DIR}/*.{ico,png,svg,webmanifest}`,
          to: '[name][ext]',
          noErrorOnMissing: true,
          filter: (resourcePath) => {
            // Don't copy Service Worker files that cause blob URL issues in Domo DDX
            if (resourcePath.includes('service-worker') || resourcePath.includes('fix-tprotocol')) return false;
            return true;
          },
        },
      ],
    }),
    // Post-process to remove Service Worker registrations for Domo DDX compatibility
    {
      apply: (compiler) => {
        compiler.hooks.emit.tap('RemoveServiceWorkerRegistration', (compilation) => {
          // Process the main app.js file
          const appJsAsset = compilation.assets['app.js'];
          if (appJsAsset) {
            let source = appJsAsset.source();

            // Remove Service Worker registration patterns
            source = source
              .replace(/navigator\.serviceWorker\.register\([^)]*\)[^}]*}/g, '// Service Worker registration removed for DDX compatibility')
              .replace(/new Blob\(\[[^}]*serviceWorker[^}]*\]/g, '// Service Worker blob removed for DDX compatibility')
              .replace(/URL\.createObjectURL\([^)]*serviceWorker[^)]*\)/g, '// Service Worker URL removed for DDX compatibility')
              .replace(/"serviceWorker"in navigator[^}]*}/g, '// Service Worker check removed for DDX compatibility');

            // Update the asset
            compilation.assets['app.js'] = {
              source: () => source,
              size: () => source.length
            };

            console.log('🚫 Removed Service Worker registration from app.js for DDX compatibility');
          }
        });

        compiler.hooks.done.tap('CleanupTempFiles', () => {
          const tempEntry = path.resolve(__dirname, 'temp-entry.js');
          if (fs.existsSync(tempEntry)) {
            fs.unlinkSync(tempEntry);
          }
        });
      },
    },
  ],


};

#!/bin/bash

# Build script for Domo DDX Evidence App
# This script builds the Evidence project and packages it for Domo DDX deployment

set -e

echo "🚀 Building Evidence app for Domo DDX..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ and try again."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

# Clean previous builds
clean_build() {
    print_status "Cleaning previous builds..."
    
    # Remove Evidence build directory
    if [ -d "evidence-project-fresh/build" ]; then
        rm -rf evidence-project-fresh/build
        print_status "Removed Evidence build directory"
    fi
    
    # Remove webpack dist directory
    if [ -d "dist" ]; then
        rm -rf dist
        print_status "Removed webpack dist directory"
    fi
    
    # Remove evidence_build directory
    if [ -d "evidence_build" ]; then
        rm -rf evidence_build
        print_status "Removed evidence_build directory"
    fi
    
    print_success "Clean completed"
}

# Build Evidence project
build_evidence() {
    print_status "Building Evidence project..."
    
    cd evidence-project-fresh
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing Evidence dependencies..."
        npm ci
    fi
    
    # Build Evidence project (sources will be processed automatically)
    print_status "Building Evidence project..."
    npm run build
    
    cd ..
    
    # Copy build to evidence_build directory
    if [ -d "evidence-project-fresh/build" ]; then
        cp -r evidence-project-fresh/build evidence_build
        print_success "Evidence build completed"
    else
        print_error "Evidence build failed - build directory not found"
        exit 1
    fi
}

# Bundle with webpack for DDX
bundle_for_ddx() {
    print_status "Bundling for Domo DDX with webpack..."
    
    # Install webpack dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing webpack dependencies..."
        npm install
    fi
    
    # Run webpack build
    print_status "Running webpack build..."
    npx webpack
    
    if [ -d "dist" ]; then
        print_success "Webpack bundling completed"
    else
        print_error "Webpack bundling failed"
        exit 1
    fi
}

# Copy DDX-specific files
copy_ddx_files() {
    print_status "Copying DDX-specific files..."

    # Copy main manifest.json (required for DDX)
    if [ -f "manifest.json" ]; then
        cp manifest.json dist/
        print_status "Copied main manifest.json"
    fi

    # Copy Domo manifest
    if [ -f "domo-manifest.json" ]; then
        cp domo-manifest.json dist/
        print_status "Copied Domo manifest"
    fi

    # Copy thumbnail image
    if [ -f "thumbnail.png" ]; then
        cp thumbnail.png dist/
        print_status "Copied thumbnail.png"
    else
        print_warning "thumbnail.png not found, skipping..."
    fi

    # Copy any additional DDX assets
    if [ -d "ddx-assets" ]; then
        cp -r ddx-assets/* dist/
        print_status "Copied DDX assets"
    fi

    print_success "DDX files copied"
}

# Validate the build
validate_build() {
    print_status "Validating build..."
    
    # Check if required files exist
    required_files=("dist/index.html" "dist/styles.css")
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file missing: $file"
            exit 1
        fi
    done
    
    # Check if Domo integration script is included
    if [ -f "dist/static/domo-duckdb-integration.js" ]; then
        print_status "Domo integration script found"
    else
        print_warning "Domo integration script not found in dist"
    fi
    
    # Check file sizes
    index_size=$(stat -f%z "dist/index.html" 2>/dev/null || stat -c%s "dist/index.html" 2>/dev/null)
    if [ "$index_size" -lt 1000 ]; then
        print_warning "index.html seems unusually small ($index_size bytes)"
    fi
    
    print_success "Build validation completed"
}

# Create deployment package
create_package() {
    print_status "Creating deployment package..."
    
    # Create a zip file for DDX deployment
    if command -v zip &> /dev/null; then
        cd dist
        zip -r ../evidence-ddx-app.zip .
        cd ..
        print_success "Created evidence-ddx-app.zip"
    else
        print_warning "zip command not found. Please manually zip the dist/ directory for deployment."
    fi
}

# Display build summary
show_summary() {
    echo ""
    echo "=========================================="
    echo "🎉 Build Summary"
    echo "=========================================="
    echo ""
    print_success "Evidence app built successfully for Domo DDX!"
    echo ""
    echo "📁 Build artifacts:"
    echo "   - dist/              (DDX app files)"
    echo "   - evidence-ddx-app.zip (deployment package)"
    echo ""
    echo "🚀 Next steps:"
    echo "   1. Test the app by opening dist/index.html in a browser"
    echo "   2. Upload evidence-ddx-app.zip to Domo DDX"
    echo "   3. Configure the app in your Domo instance"
    echo ""
    echo "📚 Documentation:"
    echo "   - Evidence: https://docs.evidence.dev"
    echo "   - Domo DDX: https://developer.domo.com"
    echo ""
}

# Main build process
main() {
    echo "=========================================="
    echo "🔨 Evidence DDX Build Process"
    echo "=========================================="
    echo ""
    
    check_dependencies
    clean_build
    build_evidence
    bundle_for_ddx
    copy_ddx_files
    validate_build
    create_package
    show_summary
}

# Run the main function
main "$@"

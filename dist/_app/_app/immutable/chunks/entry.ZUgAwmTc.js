import{v as M,s as _e,C as we,z as ve,E as be,L as wt,B as Ae}from"./scheduler.DQwIXrE4.js";function ke(e,t){return"/"===e||"ignore"===t?e:"never"===t?e.endsWith("/")?e.slice(0,-1):e:"always"!==t||e.endsWith("/")?e:e+"/"}function Ee(e){return e.split("%25").map(decodeURI).join("%25")}function Se(e){for(const t in e)e[t]=decodeURIComponent(e[t]);return e}function gt({href:e}){return e.split("#")[0]}new URL("sveltekit-internal://");const Re=["href","pathname","search","toString","toJSON"];function Ie(e,t,n){const r=new URL(e);Object.defineProperty(r,"searchParams",{value:new Proxy(r.searchParams,{get(e,r){if("get"===r||"getAll"===r||"has"===r)return t=>(n(t),e[r](t));t();const o=Reflect.get(e,r);return"function"==typeof o?o.bind(e):o}}),enumerable:!0,configurable:!0});for(const n of Re)Object.defineProperty(r,n,{get:()=>(t(),e[n]),enumerable:!0,configurable:!0});return r}const Le="/__data.json",Ue=".html__data.json";function xe(e){return e.endsWith(".html")?e.replace(/\.html$/,Ue):e.replace(/\/$/,"")+Le}function Te(...e){let t=5381;for(const n of e)if("string"==typeof n){let e=n.length;for(;e;)t=33*t^n.charCodeAt(--e)}else{if(!ArrayBuffer.isView(n))throw new TypeError("value must be a string or TypedArray");{const e=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);let r=e.length;for(;r;)t=33*t^e[--r]}}return(t>>>0).toString(36)}function Pe(e){const t=atob(e),n=new Uint8Array(t.length);for(let e=0;e<t.length;e++)n[e]=t.charCodeAt(e);return n.buffer}const Kt=window.fetch;window.fetch=(e,t)=>("GET"!==(e instanceof Request?e.method:(null==t?void 0:t.method)||"GET")&&H.delete(Et(e)),Kt(e,t));const H=new Map;function Ce(e,t){const n=Et(e,t),r=document.querySelector(n);if(null!=r&&r.textContent){let{body:e,...t}=JSON.parse(r.textContent);const o=r.getAttribute("data-ttl");return o&&H.set(n,{body:e,init:t,ttl:1e3*Number(o)}),null!==r.getAttribute("data-b64")&&(e=Pe(e)),Promise.resolve(new Response(e,t))}return window.fetch(e,t)}function Ne(e,t,n){if(H.size>0){const t=Et(e,n),r=H.get(t);if(r){if(performance.now()<r.ttl&&["default","force-cache","only-if-cached",void 0].includes(null==n?void 0:n.cache))return new Response(r.body,r.init);H.delete(t)}}return window.fetch(t,n)}function Et(e,t){let n=`script[data-sveltekit-fetched][data-url=${JSON.stringify(e instanceof Request?e.url:e)}]`;if(null!=t&&t.headers||null!=t&&t.body){const e=[];t.headers&&e.push([...new Headers(t.headers)].join(",")),t.body&&("string"==typeof t.body||ArrayBuffer.isView(t.body))&&e.push(t.body),n+=`[data-hash="${Te(...e)}"]`}return n}const Oe=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function je(e){const t=[];return{pattern:"/"===e?/^\/$/:new RegExp(`^${De(e).map((e=>{const n=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(e);if(n)return t.push({name:n[1],matcher:n[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const r=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(e);if(r)return t.push({name:r[1],matcher:r[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!e)return;const o=e.split(/\[(.+?)\](?!\])/);return"/"+o.map(((e,n)=>{if(n%2){if(e.startsWith("x+"))return mt(String.fromCharCode(parseInt(e.slice(2),16)));if(e.startsWith("u+"))return mt(String.fromCharCode(...e.slice(2).split("-").map((e=>parseInt(e,16)))));const r=Oe.exec(e),[,a,s,l,i]=r;return t.push({name:l,matcher:i,optional:!!a,rest:!!s,chained:!!s&&1===n&&""===o[0]}),s?"(.*?)":a?"([^/]*)?":"([^/]+?)"}return mt(e)})).join("")})).join("")}/?$`),params:t}}function $e(e){return!/^\([^)]+\)$/.test(e)}function De(e){return e.slice(1).split("/").filter($e)}function Be(e,t,n){const r={},o=e.slice(1),a=o.filter((e=>void 0!==e));let s=0;for(let e=0;e<t.length;e+=1){const l=t[e];let i=o[e-s];if(l.chained&&l.rest&&s&&(i=o.slice(e-s,e+1).filter((e=>e)).join("/"),s=0),void 0!==i)if(l.matcher&&!n[l.matcher](i)){if(!l.optional||!l.chained)return;s++}else{r[l.name]=i;const n=t[e+1],c=o[e+1];n&&!n.rest&&n.optional&&c&&l.chained&&(s=0),!n&&!c&&Object.keys(r).length===a.length&&(s=0)}else l.rest&&(r[l.name]="")}if(!s)return r}function mt(e){return e.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function Fe({nodes:e,server_loads:t,dictionary:n,matchers:r}){const o=new Set(t);return Object.entries(n).map((([t,[n,o,l]])=>{const{pattern:i,params:c}=je(t),u={id:t,exec:e=>{const t=i.exec(e);if(t)return Be(t,c,r)},errors:[1,...l||[]].map((t=>e[t])),layouts:[0,...o||[]].map(s),leaf:a(n)};return u.errors.length=u.layouts.length=Math.max(u.errors.length,u.layouts.length),u}));function a(t){const n=t<0;return n&&(t=~t),[n,e[t]]}function s(t){return void 0===t?t:[o.has(t),e[t]]}}function zt(e,t=JSON.parse){try{return t(sessionStorage[e])}catch{}}function Ot(e,t,n=JSON.stringify){const r=n(t);try{sessionStorage[e]=r}catch{}}const j=[];function Ve(e,t){return{subscribe:st(e,t).subscribe}}function st(e,t=M){let n;const r=new Set;function o(t){if(_e(e,t)&&(e=t,n)){const t=!j.length;for(const t of r)t[1](),j.push(t,e);if(t){for(let e=0;e<j.length;e+=2)j[e][0](j[e+1]);j.length=0}}}function a(t){o(t(e))}return{set:o,update:a,subscribe:function(s,l=M){const i=[s,l];return r.add(i),1===r.size&&(n=t(o,a)||M),s(e),()=>{r.delete(i),0===r.size&&n&&(n(),n=null)}}}}function mn(e,t,n){const r=!Array.isArray(e),o=r?[e]:e;if(!o.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const a=t.length<2;return Ve(n,((e,n)=>{let s=!1;const l=[];let i=0,c=M;const u=()=>{if(i)return;c();const o=t(r?l[0]:l,e,n);a?e(o):c=be(o)?o:M},d=o.map(((e,t)=>we(e,(e=>{l[t]=e,i&=~(1<<t),s&&u()}),(()=>{i|=1<<t}))));return s=!0,u(),function(){ve(d),c(),s=!1}}))}function yn(e){return{subscribe:e.subscribe.bind(e)}}var Mt;const U=(null==(Mt=globalThis.__sveltekit_hi3xao)?void 0:Mt.base)??"";var Ht;const qe=(null==(Ht=globalThis.__sveltekit_hi3xao)?void 0:Ht.assets)??U,Ge="1749955253883",Wt="sveltekit:snapshot",Yt="sveltekit:scroll",Jt="sveltekit:states",Me="sveltekit:pageurl",D="sveltekit:history",z="sveltekit:navigation",tt={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},X=location.origin;function St(e){if(e instanceof URL)return e;let t=document.baseURI;if(!t){const e=document.getElementsByTagName("base");t=e.length?e[0].href:document.URL}return new URL(e,t)}function Rt(){return{x:pageXOffset,y:pageYOffset}}function $(e,t){return e.getAttribute(`data-sveltekit-${t}`)}const jt={...tt,"":tt.hover};function Xt(e){let t=e.assignedSlot??e.parentNode;return 11===(null==t?void 0:t.nodeType)&&(t=t.host),t}function Zt(e,t){for(;e&&e!==t;){if("A"===e.nodeName.toUpperCase()&&e.hasAttribute("href"))return e;e=Xt(e)}}function vt(e,t){let n;try{n=new URL(e instanceof SVGAElement?e.href.baseVal:e.href,document.baseURI)}catch{}const r=e instanceof SVGAElement?e.target.baseVal:e.target;return{url:n,external:!n||!!r||it(n,t)||(e.getAttribute("rel")||"").split(/\s+/).includes("external"),target:r,download:(null==n?void 0:n.origin)===X&&e.hasAttribute("download")}}function et(e){let t=null,n=null,r=null,o=null,a=null,s=null,l=e;for(;l&&l!==document.documentElement;)null===r&&(r=$(l,"preload-code")),null===o&&(o=$(l,"preload-data")),null===t&&(t=$(l,"keepfocus")),null===n&&(n=$(l,"noscroll")),null===a&&(a=$(l,"reload")),null===s&&(s=$(l,"replacestate")),l=Xt(l);function i(e){switch(e){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:jt[r??"off"],preload_data:jt[o??"off"],keepfocus:i(t),noscroll:i(n),reload:i(a),replace_state:i(s)}}function $t(e){const t=st(e);let n=!0;return{notify:function(){n=!0,t.update((e=>e))},set:function(e){n=!1,t.set(e)},subscribe:function(e){let r;return t.subscribe((t=>{(void 0===r||n&&t!==r)&&e(r=t)}))}}}function He(){const{set:e,subscribe:t}=st(!1);let n;return{subscribe:t,check:async function(){clearTimeout(n);try{const t=await fetch(`${qe}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!t.ok)return!1;const r=(await t.json()).version!==Ge;return r&&(e(!0),clearTimeout(n)),r}catch{return!1}}}}function it(e,t){return e.origin!==X||!e.pathname.startsWith(t)}function Dt(e){const t=ze(e),n=new ArrayBuffer(t.length),r=new DataView(n);for(let e=0;e<n.byteLength;e++)r.setUint8(e,t.charCodeAt(e));return n}const Ke="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function ze(e){e.length%4==0&&(e=e.replace(/==?$/,""));let t="",n=0,r=0;for(let o=0;o<e.length;o++)n<<=6,n|=Ke.indexOf(e[o]),r+=6,24===r&&(t+=String.fromCharCode((16711680&n)>>16),t+=String.fromCharCode((65280&n)>>8),t+=String.fromCharCode(255&n),n=r=0);return 12===r?(n>>=4,t+=String.fromCharCode(n)):18===r&&(n>>=2,t+=String.fromCharCode((65280&n)>>8),t+=String.fromCharCode(255&n)),t}const We=-1,Ye=-2,Je=-3,Xe=-4,Ze=-5,Qe=-6;function _n(e,t){return Qt(JSON.parse(e),t)}function Qt(e,t){if("number"==typeof e)return o(e,!0);if(!Array.isArray(e)||0===e.length)throw new Error("Invalid input");const n=e,r=Array(n.length);function o(e,a=!1){if(-1===e)return;if(-3===e)return NaN;if(-4===e)return 1/0;if(-5===e)return-1/0;if(-6===e)return-0;if(a)throw new Error("Invalid input");if(e in r)return r[e];const s=n[e];if(s&&"object"==typeof s)if(Array.isArray(s))if("string"==typeof s[0]){const n=s[0],a=null==t?void 0:t[n];if(a)return r[e]=a(o(s[1]));switch(n){case"Date":r[e]=new Date(s[1]);break;case"Set":const t=new Set;r[e]=t;for(let e=1;e<s.length;e+=1)t.add(o(s[e]));break;case"Map":const a=new Map;r[e]=a;for(let e=1;e<s.length;e+=2)a.set(o(s[e]),o(s[e+1]));break;case"RegExp":r[e]=new RegExp(s[1],s[2]);break;case"Object":r[e]=Object(s[1]);break;case"BigInt":r[e]=BigInt(s[1]);break;case"null":const l=Object.create(null);r[e]=l;for(let e=1;e<s.length;e+=2)l[s[e]]=o(s[e+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const t=new(0,globalThis[n])(Dt(s[1]));r[e]=t;break}case"ArrayBuffer":{const t=Dt(s[1]);r[e]=t;break}default:throw new Error(`Unknown type ${n}`)}}else{const t=new Array(s.length);r[e]=t;for(let e=0;e<s.length;e+=1){const n=s[e];-2!==n&&(t[e]=o(n))}}else{const t={};r[e]=t;for(const e in s){const n=s[e];t[e]=o(n)}}else r[e]=s;return r[e]}return o(0)}const te=new Set(["load","prerender","csr","ssr","trailingSlash","config"]),tn=new Set([...te]);function en(e){return e.filter((e=>null!=e))}class ct{constructor(e,t){this.status=e,this.body="string"==typeof t?{message:t}:t||{message:`Error: ${e}`}}toString(){return JSON.stringify(this.body)}}class ee{constructor(e,t){this.status=e,this.location=t}}class It extends Error{constructor(e,t,n){super(n),this.status=e,this.text=t}}const nn="x-sveltekit-invalidated",rn="x-sveltekit-trailing-slash";function nt(e){return e instanceof ct||e instanceof It?e.status:500}function an(e){return e instanceof It?e.text:"Internal Error"}const O=zt(Yt)??{},W=zt(Wt)??{},T={url:$t({}),page:$t({}),navigating:st(null),updated:He()};function Lt(e){O[e]=Rt()}function on(e,t){let n=e+1;for(;O[n];)delete O[n],n+=1;for(n=t+1;W[n];)delete W[n],n+=1}function F(e){return location.href=e.href,new Promise((()=>{}))}async function ne(){if("serviceWorker"in navigator){const e=await navigator.serviceWorker.getRegistration(U||"/");e&&await e.update()}}function Bt(){}let lt,bt,rt,x,At,q;const re=[],at=[];let I=null;const ae=[],sn=[];let N,E,L,S,V,C=[],_={branch:[],error:null,url:null},Ut=!1,ot=!1,Ft=!0,Y=!1,G=!1,oe=!1,ft=!1;const K=new Set;let yt;async function wn(e,t,n){var r,o;document.URL!==location.href&&(location.href=location.href),q=e,lt=Fe(e),x=document.documentElement,At=t,bt=e.nodes[0],rt=e.nodes[1],bt(),rt(),E=null==(r=history.state)?void 0:r[D],L=null==(o=history.state)?void 0:o[z],E||(E=L=Date.now(),history.replaceState({...history.state,[D]:E,[z]:L},""));const a=O[E];a&&(history.scrollRestoration="manual",scrollTo(a.x,a.y)),n?await pn(At,n):dn(location.href,{replaceState:!0}),hn()}async function cn(){if(await(yt||(yt=Promise.resolve())),!yt)return;yt=null;const e=Z(_.url,!0);I=null;const t=V={},n=e&&await Pt(e);if(n&&t===V){if("redirect"===n.type)return ut(new URL(n.location,_.url).href,{},1,t);n.props.page&&(S=n.props.page),_=n.state,se(),N.$set(n.props)}}function se(){re.length=0,ft=!1}function ie(e){at.some((e=>null==e?void 0:e.snapshot))&&(W[e]=at.map((e=>{var t;return null==(t=null==e?void 0:e.snapshot)?void 0:t.capture()})))}function ce(e){var t;null==(t=W[e])||t.forEach(((e,t)=>{var n,r;null==(r=null==(n=at[t])?void 0:n.snapshot)||r.restore(e)}))}function Vt(){Lt(E),Ot(Yt,O),ie(L),Ot(Wt,W)}async function ut(e,t,n,r){return Q({type:"goto",url:St(e),keepfocus:t.keepFocus,noscroll:t.noScroll,replace_state:t.replaceState,state:t.state,redirect_count:n,nav_token:r,accept:()=>{t.invalidateAll&&(ft=!0)}})}async function le(e){if(e.id!==(null==I?void 0:I.id)){const t={};K.add(t),I={id:e.id,token:t,promise:Pt({...e,preload:t}).then((e=>(K.delete(t),"loaded"===e.type&&e.state.error&&(I=null),e)))}}return I.promise}async function _t(e){const t=lt.find((t=>t.exec(de(e))));t&&await Promise.all([...t.layouts,t.leaf].map((e=>null==e?void 0:e[1]())))}function fe(e,t,n){var r;_=e.state;const o=document.querySelector("style[data-sveltekit]");o&&o.remove(),S=e.props.page,N=new q.root({target:t,props:{...e.props,stores:T,components:at},hydrate:n,sync:!1}),ce(L);const a={from:null,to:{params:_.params,route:{id:(null==(r=_.route)?void 0:r.id)??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};C.forEach((e=>e(a))),ot=!0}function J({url:e,params:t,branch:n,status:r,error:o,route:a,form:s}){let l="never";if(!U||e.pathname!==U&&e.pathname!==U+"/")for(const e of n)void 0!==(null==e?void 0:e.slash)&&(l=e.slash);else l="always";e.pathname=ke(e.pathname,l),e.search=e.search;const i={type:"loaded",state:{url:e,params:t,branch:n,error:o,route:a},props:{constructors:en(n).map((e=>e.node.component)),page:S}};void 0!==s&&(i.props.form=s);let c={},u=!S,d=0;for(let e=0;e<Math.max(n.length,_.branch.length);e+=1){const t=n[e],r=_.branch[e];(null==t?void 0:t.data)!==(null==r?void 0:r.data)&&(u=!0),t&&(c={...c,...t.data},u&&(i.props[`data_${d}`]=c),d+=1)}return(!_.url||e.href!==_.url.href||_.error!==o||void 0!==s&&s!==S.form||u)&&(i.props.page={error:o,params:t,route:{id:(null==a?void 0:a.id)??null},state:{},status:r,url:new URL(e),form:s??null,data:u?c:S.data}),i}async function xt({loader:e,parent:t,url:n,params:r,route:o,server_data_node:a}){var s,l,i;let c=null,u=!0;const d={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},f=await e();if(null!=(s=f.universal)&&s.load){let e=function(...e){for(const t of e){const{href:e}=new URL(t,n);d.dependencies.add(e)}};const s={route:new Proxy(o,{get:(e,t)=>(u&&(d.route=!0),e[t])}),params:new Proxy(r,{get:(e,t)=>(u&&d.params.add(t),e[t])}),data:(null==a?void 0:a.data)??null,url:Ie(n,(()=>{u&&(d.url=!0)}),(e=>{u&&d.search_params.add(e)})),async fetch(t,r){let o;t instanceof Request?(o=t.url,r={body:"GET"===t.method||"HEAD"===t.method?void 0:await t.blob(),cache:t.cache,credentials:t.credentials,headers:[...t.headers].length?t.headers:void 0,integrity:t.integrity,keepalive:t.keepalive,method:t.method,mode:t.mode,redirect:t.redirect,referrer:t.referrer,referrerPolicy:t.referrerPolicy,signal:t.signal,...r}):o=t;const a=new URL(o,n);return u&&e(a.href),a.origin===n.origin&&(o=a.href.slice(n.origin.length)),ot?Ne(o,a.href,r):Ce(o,r)},setHeaders:()=>{},depends:e,parent:()=>(u&&(d.parent=!0),t()),untrack(e){u=!1;try{return e()}finally{u=!0}}};c=await f.universal.load.call(null,s)??null}return{node:f,loader:e,server:a,universal:null!=(l=f.universal)&&l.load?{type:"data",data:c,uses:d}:null,data:c??(null==a?void 0:a.data)??null,slash:(null==(i=f.universal)?void 0:i.trailingSlash)??(null==a?void 0:a.slash)}}function qt(e,t,n,r,o,a){if(ft)return!0;if(!o)return!1;if(o.parent&&e||o.route&&t||o.url&&n)return!0;for(const e of o.search_params)if(r.has(e))return!0;for(const e of o.params)if(a[e]!==_.params[e])return!0;for(const e of o.dependencies)if(re.some((t=>t(new URL(e)))))return!0;return!1}function Tt(e,t){return"data"===(null==e?void 0:e.type)?e:"skip"===(null==e?void 0:e.type)?t??null:null}function ln(e,t){if(!e)return new Set(t.searchParams.keys());const n=new Set([...e.searchParams.keys(),...t.searchParams.keys()]);for(const r of n){const o=e.searchParams.getAll(r),a=t.searchParams.getAll(r);o.every((e=>a.includes(e)))&&a.every((e=>o.includes(e)))&&n.delete(r)}return n}function Gt({error:e,url:t,route:n,params:r}){return{type:"loaded",state:{error:e,url:t,route:n,params:r,branch:[]},props:{page:S,constructors:[]}}}async function Pt({id:e,invalidating:t,url:n,params:r,route:o,preload:a}){if((null==I?void 0:I.id)===e)return K.delete(I.token),I.promise;const{errors:s,layouts:l,leaf:i}=o,c=[...l,i];s.forEach((e=>null==e?void 0:e().catch((()=>{})))),c.forEach((e=>null==e?void 0:e[1]().catch((()=>{}))));let u=null;const d=!!_.url&&e!==_.url.pathname+_.url.search,f=!!_.route&&o.id!==_.route.id,p=ln(_.url,n);let h=!1;const m=c.map(((e,t)=>{var n;const o=_.branch[t],a=!(null==e||!e[0])&&((null==o?void 0:o.loader)!==e[1]||qt(h,f,d,p,null==(n=o.server)?void 0:n.uses,r));return a&&(h=!0),a}));if(m.some(Boolean)){try{u=await ge(n,m)}catch(t){const s=await B(t,{url:n,params:r,route:{id:e}});return K.has(a)?Gt({error:s,url:n,params:r,route:o}):dt({status:nt(t),error:s,url:n,route:o})}if("redirect"===u.type)return u}const g=null==u?void 0:u.nodes;let v=!1;const y=c.map((async(e,t)=>{var a;if(!e)return;const s=_.branch[t],l=null==g?void 0:g[t];if(!(l&&"skip"!==l.type||e[1]!==(null==s?void 0:s.loader)||qt(v,f,d,p,null==(a=s.universal)?void 0:a.uses,r)))return s;if(v=!0,"error"===(null==l?void 0:l.type))throw l;return xt({loader:e[1],url:n,params:r,route:o,parent:async()=>{var e;const n={};for(let r=0;r<t;r+=1)Object.assign(n,null==(e=await y[r])?void 0:e.data);return n},server_data_node:Tt(void 0===l&&e[0]?{type:"skip"}:l??null,e[0]?null==s?void 0:s.server:void 0)})}));for(const e of y)e.catch((()=>{}));const w=[];for(let e=0;e<c.length;e+=1)if(c[e])try{w.push(await y[e])}catch(t){if(t instanceof ee)return{type:"redirect",location:t.location};if(K.has(a))return Gt({error:await B(t,{params:r,url:n,route:{id:o.id}}),url:n,params:r,route:o});let l,i=nt(t);if(null!=g&&g.includes(t))i=t.status??i,l=t.error;else if(t instanceof ct)l=t.body;else{if(await T.updated.check())return await ne(),await F(n);l=await B(t,{params:r,url:n,route:{id:o.id}})}const c=await ue(e,w,s);return c?J({url:n,params:r,branch:w.slice(0,c.idx).concat(c.node),status:i,error:l,route:o}):await pe(n,{id:o.id},l,i)}else w.push(void 0);return J({url:n,params:r,branch:w,status:200,error:null,route:o,form:t?void 0:null})}async function ue(e,t,n){for(;e--;)if(n[e]){let r=e;for(;!t[r];)r-=1;try{return{idx:r+1,node:{node:await n[e](),loader:n[e],data:{},server:null,universal:null}}}catch{continue}}}async function dt({status:e,error:t,url:n,route:r}){const o={};let a=null;if(0===q.server_loads[0])try{const e=await ge(n,[!0]);if("data"!==e.type||e.nodes[0]&&"data"!==e.nodes[0].type)throw 0;a=e.nodes[0]??null}catch{(n.origin!==X||n.pathname!==location.pathname||Ut)&&await F(n)}return J({url:n,params:o,branch:[await xt({loader:bt,url:n,params:o,route:r,parent:()=>Promise.resolve({}),server_data_node:Tt(a)}),{node:await rt(),loader:rt,universal:null,server:null,data:null}],status:e,error:t,route:null})}function Z(e,t){if(!e||it(e,U))return;let n;try{n=q.hooks.reroute({url:new URL(e)})??e.pathname}catch{return}const r=de(n);for(const n of lt){const o=n.exec(r);if(o)return{id:e.pathname+e.search,invalidating:t,route:n,params:Se(o),url:e}}}function de(e){return Ee(e.slice(U.length)||"/")}function he({url:e,type:t,intent:n,delta:r}){let o=!1;const a=ye(_,n,e,t);void 0!==r&&(a.navigation.delta=r);const s={...a.navigation,cancel:()=>{o=!0,a.reject(new Error("navigation cancelled"))}};return Y||ae.forEach((e=>e(s))),o?null:a}async function Q({type:e,url:t,popped:n,keepfocus:r,noscroll:o,replace_state:a,state:s={},redirect_count:l=0,nav_token:i={},accept:c=Bt,block:u=Bt}){const d=Z(t,!1),f=he({url:t,type:e,delta:null==n?void 0:n.delta,intent:d});if(!f)return void u();const p=E,h=L;c(),Y=!0,ot&&T.navigating.set(f.navigation),V=i;let m=d&&await Pt(d);if(!m){if(it(t,U))return await F(t);m=await pe(t,{id:null},await B(new It(404,"Not Found",`Not found: ${t.pathname}`),{url:t,params:{},route:{id:null}}),404)}if(t=(null==d?void 0:d.url)||t,V!==i)return f.reject(new Error("navigation aborted")),!1;if("redirect"===m.type){if(!(l>=20))return ut(new URL(m.location,t).href,{},l+1,i),!1;m=await dt({status:500,error:await B(new Error("Redirect loop"),{url:t,params:{},route:{id:null}}),url:t,route:{id:null}})}else m.props.page.status>=400&&await T.updated.check()&&(await ne(),await F(t));if(se(),Lt(p),ie(h),m.props.page.url.pathname!==t.pathname&&(t.pathname=m.props.page.url.pathname),s=n?n.state:s,!n){const e=a?0:1,n={[D]:E+=e,[z]:L+=e,[Jt]:s};(a?history.replaceState:history.pushState).call(history,n,"",t),a||on(E,L)}if(I=null,m.props.page.state=s,ot){_=m.state,m.props.page&&(m.props.page.url=t);const e=(await Promise.all(sn.map((e=>e(f.navigation))))).filter((e=>"function"==typeof e));if(e.length>0){let t=function(){C=C.filter((t=>!e.includes(t)))};e.push(t),C.push(...e)}N.$set(m.props),oe=!0}else fe(m,At,!1);const{activeElement:g}=document;await wt();const v=n?n.scroll:o?Rt():null;if(Ft){const e=t.hash&&document.getElementById(decodeURIComponent(t.hash.slice(1)));v?scrollTo(v.x,v.y):e?e.scrollIntoView():scrollTo(0,0)}const y=document.activeElement!==g&&document.activeElement!==document.body;!r&&!y&&kt(),Ft=!0,m.props.page&&(S=m.props.page),Y=!1,"popstate"===e&&ce(L),f.fulfil(void 0),C.forEach((e=>e(f.navigation))),T.navigating.set(null)}async function pe(e,t,n,r){return e.origin!==X||e.pathname!==location.pathname||Ut?await F(e):await dt({status:r,error:n,url:e,route:t})}function fn(){let e;function t(e){e.defaultPrevented||r(e.composedPath()[0],1)}x.addEventListener("mousemove",(t=>{const n=t.target;clearTimeout(e),e=setTimeout((()=>{r(n,2)}),20)})),x.addEventListener("mousedown",t),x.addEventListener("touchstart",t,{passive:!0});const n=new IntersectionObserver((e=>{for(const t of e)t.isIntersecting&&(_t(t.target.href),n.unobserve(t.target))}),{threshold:0});function r(e,t){const n=Zt(e,x);if(!n)return;const{url:r,external:o,download:a}=vt(n,U);if(o||a)return;const s=et(n),l=r&&_.url.pathname+_.url.search===r.pathname+r.search;if(!s.reload&&!l)if(t<=s.preload_data){const e=Z(r,!1);e&&le(e)}else t<=s.preload_code&&_t(r.pathname)}function o(){n.disconnect();for(const e of x.querySelectorAll("a")){const{url:t,external:r,download:o}=vt(e,U);if(r||o)continue;const a=et(e);a.reload||(a.preload_code===tt.viewport&&n.observe(e),a.preload_code===tt.eager&&_t(t.pathname))}}C.push(o),o()}function B(e,t){if(e instanceof ct)return e.body;const n=nt(e),r=an(e);return q.hooks.handleError({error:e,event:t,status:n,message:r})??{message:r}}function un(e,t){Ae((()=>(e.push(t),()=>{const n=e.indexOf(t);e.splice(n,1)})))}function vn(e){un(C,e)}function dn(e,t={}){return(e=St(e)).origin!==X?Promise.reject(new Error("goto: invalid URL")):ut(e,t,0)}function bn(){return ft=!0,cn()}async function An(e){const t=St(e),n=Z(t,!1);if(!n)throw new Error(`Attempted to preload a URL that does not belong to this app: ${t}`);const r=await le(n);if("redirect"===r.type)return{type:r.type,location:r.location};const{status:o,data:a}=r.props.page??S;return{type:r.type,status:o,data:a}}async function kn(e){if("error"===e.type){const t=new URL(location.href),{branch:n,route:r}=_;if(!r)return;const o=await ue(_.branch.length,n,r.errors);if(o){const a=J({url:t,params:_.params,branch:n.slice(0,o.idx).concat(o.node),status:e.status??500,error:e.error,route:r});_=a.state,N.$set(a.props),wt().then(kt)}}else"redirect"===e.type?ut(e.location,{invalidateAll:!0},0):(N.$set({form:null,page:{...S,form:e.data,status:e.status}}),await wt(),N.$set({form:e.data}),"success"===e.type&&kt())}function hn(){var e;history.scrollRestoration="manual",addEventListener("beforeunload",(e=>{let t=!1;if(Vt(),!Y){const e=ye(_,void 0,null,"leave"),n={...e.navigation,cancel:()=>{t=!0,e.reject(new Error("navigation cancelled"))}};ae.forEach((e=>e(n)))}t?(e.preventDefault(),e.returnValue=""):history.scrollRestoration="auto"})),addEventListener("visibilitychange",(()=>{"hidden"===document.visibilityState&&Vt()})),null!=(e=navigator.connection)&&e.saveData||fn(),x.addEventListener("click",(async e=>{if(e.button||1!==e.which||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.defaultPrevented)return;const n=Zt(e.composedPath()[0],x);if(!n)return;const{url:r,external:o,target:a,download:s}=vt(n,U);if(!r)return;if("_parent"===a||"_top"===a){if(window.parent!==window)return}else if(a&&"_self"!==a)return;const l=et(n);if(!(n instanceof SVGAElement)&&r.protocol!==location.protocol&&"https:"!==r.protocol&&"http:"!==r.protocol||s)return;const[i,c]=r.href.split("#"),u=i===gt(location);if(!o&&(!l.reload||u&&c)){if(void 0!==c&&u){const[,o]=_.url.href.split("#");if(o===c){if(e.preventDefault(),""===c||"top"===c&&null===n.ownerDocument.getElementById("top"))window.scrollTo({top:0});else{const e=n.ownerDocument.getElementById(decodeURIComponent(c));e&&(e.scrollIntoView(),e.focus())}return}if(G=!0,Lt(E),t(r),!l.replace_state)return;G=!1}e.preventDefault(),await new Promise((e=>{requestAnimationFrame((()=>{setTimeout(e,0)})),setTimeout(e,100)})),Q({type:"link",url:r,keepfocus:l.keepfocus,noscroll:l.noscroll,replace_state:l.replace_state??r.href===location.href})}else he({url:r,type:"link"})?Y=!0:e.preventDefault()})),x.addEventListener("submit",(e=>{if(e.defaultPrevented)return;const t=HTMLFormElement.prototype.cloneNode.call(e.target),n=e.submitter;if("_blank"===((null==n?void 0:n.formTarget)||t.target)||"get"!==((null==n?void 0:n.formMethod)||t.method))return;const r=new URL((null==n?void 0:n.hasAttribute("formaction"))&&(null==n?void 0:n.formAction)||t.action);if(it(r,U))return;const o=e.target,a=et(o);if(a.reload)return;e.preventDefault(),e.stopPropagation();const s=new FormData(o),l=null==n?void 0:n.getAttribute("name");l&&s.append(l,(null==n?void 0:n.getAttribute("value"))??""),r.search=new URLSearchParams(s).toString(),Q({type:"form",url:r,keepfocus:a.keepfocus,noscroll:a.noscroll,replace_state:a.replace_state??r.href===location.href})})),addEventListener("popstate",(async e=>{var n;if(null!=(n=e.state)&&n[D]){const n=e.state[D];if(V={},n===E)return;const r=O[n],o=e.state[Jt]??{},a=new URL(e.state[Me]??location.href),s=e.state[z],l=gt(location)===gt(_.url);if(s===L&&(oe||l))return t(a),O[E]=Rt(),r&&scrollTo(r.x,r.y),o!==S.state&&(S={...S,state:o},N.$set({page:S})),void(E=n);const i=n-E;await Q({type:"popstate",url:a,popped:{state:o,scroll:r,delta:i},accept:()=>{E=n,L=s},block:()=>{history.go(-i)},nav_token:V})}else G||t(new URL(location.href))})),addEventListener("hashchange",(()=>{G&&(G=!1,history.replaceState({...history.state,[D]:++E,[z]:L},"",location.href))}));for(const e of document.querySelectorAll("link"))"icon"===e.rel&&(e.href=e.href);function t(e){_.url=e,T.page.set({...S,url:e}),T.page.notify()}addEventListener("pageshow",(e=>{e.persisted&&T.navigating.set(null)}))}async function pn(e,{status:t=200,error:n,node_ids:r,params:o,route:a,data:s,form:l}){Ut=!0;const i=new URL(location.href);let c;({params:o={},route:a={id:null}}=Z(i,!1)||{});try{const e=r.map((async(t,n)=>{const r=s[n];return null!=r&&r.uses&&(r.uses=me(r.uses)),xt({loader:q.nodes[t],url:i,params:o,route:a,parent:async()=>{const t={};for(let r=0;r<n;r+=1)Object.assign(t,(await e[r]).data);return t},server_data_node:Tt(r)})})),u=await Promise.all(e),d=lt.find((({id:e})=>e===a.id));if(d){const e=d.layouts;for(let t=0;t<e.length;t++)e[t]||u.splice(t,0,void 0)}c=J({url:i,params:o,branch:u,status:t,error:n,form:l,route:d??null})}catch(e){if(e instanceof ee)return void await F(new URL(e.location,location.href));c=await dt({status:nt(e),error:await B(e,{url:i,params:o,route:a}),url:i,route:a})}c.props.page&&(c.props.page.state={}),fe(c,e,!0)}async function ge(e,t){var n;const r=new URL(e);r.pathname=xe(e.pathname),e.pathname.endsWith("/")&&r.searchParams.append(rn,"1"),r.searchParams.append(nn,t.map((e=>e?"1":"0")).join(""));const o=await Kt(r.href);if(!o.ok){let e;throw null!=(n=o.headers.get("content-type"))&&n.includes("application/json")?e=await o.json():404===o.status?e="Not Found":500===o.status&&(e="Internal Error"),new ct(o.status,e)}return new Promise((async e=>{var t;const n=new Map,r=o.body.getReader(),a=new TextDecoder;function s(e){return Qt(e,{Promise:e=>new Promise(((t,r)=>{n.set(e,{fulfil:t,reject:r})}))})}let l="";for(;;){const{done:o,value:i}=await r.read();if(o&&!l)break;for(l+=!i&&l?"\n":a.decode(i,{stream:!0});;){const r=l.indexOf("\n");if(-1===r)break;const o=JSON.parse(l.slice(0,r));if(l=l.slice(r+1),"redirect"===o.type)return e(o);if("data"===o.type)null==(t=o.nodes)||t.forEach((e=>{"data"===(null==e?void 0:e.type)&&(e.uses=me(e.uses),e.data=s(e.data))})),e(o);else if("chunk"===o.type){const{id:e,data:t,error:r}=o,a=n.get(e);n.delete(e),r?a.reject(s(r)):a.fulfil(s(t))}}}}))}function me(e){return{dependencies:new Set((null==e?void 0:e.dependencies)??[]),params:new Set((null==e?void 0:e.params)??[]),parent:!(null==e||!e.parent),route:!(null==e||!e.route),url:!(null==e||!e.url),search_params:new Set((null==e?void 0:e.search_params)??[])}}function kt(){const e=document.querySelector("[autofocus]");if(e)e.focus();else{const e=document.body,t=e.getAttribute("tabindex");e.tabIndex=-1,e.focus({preventScroll:!0,focusVisible:!1}),null!==t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex");const n=getSelection();if(n&&"None"!==n.type){const e=[];for(let t=0;t<n.rangeCount;t+=1)e.push(n.getRangeAt(t));setTimeout((()=>{if(n.rangeCount===e.length){for(let t=0;t<n.rangeCount;t+=1){const r=e[t],o=n.getRangeAt(t);if(r.commonAncestorContainer!==o.commonAncestorContainer||r.startContainer!==o.startContainer||r.endContainer!==o.endContainer||r.startOffset!==o.startOffset||r.endOffset!==o.endOffset)return}n.removeAllRanges()}}))}}}function ye(e,t,n,r){var o,a;let s,l;const i=new Promise(((e,t)=>{s=e,l=t}));return i.catch((()=>{})),{navigation:{from:{params:e.params,route:{id:(null==(o=e.route)?void 0:o.id)??null},url:e.url},to:n&&{params:(null==t?void 0:t.params)??null,route:{id:(null==(a=null==t?void 0:t.route)?void 0:a.id)??null},url:n},willUnload:!t,type:r,complete:i},fulfil:s,reject:l}}export{kn as a,Ve as b,vn as c,mn as d,An as e,wn as f,dn as g,bn as i,_n as p,yn as r,T as s,st as w};
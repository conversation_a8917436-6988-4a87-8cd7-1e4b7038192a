var F=Object.defineProperty,G=(t,n,e)=>n in t?F(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e,_=(t,n,e)=>G(t,"symbol"!=typeof n?n+"":n,e);function H(){}const _t=t=>t;function I(t,n){for(const e in n)t[e]=n[e];return t}function ht(t){return!!t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof t.then}function U(t){return t()}function dt(){return Object.create(null)}function z(t){t.forEach(U)}function W(t){return"function"==typeof t}function mt(t,n){return t!=t?n==n:t!==n||t&&"object"==typeof t||"function"==typeof t}let p;function pt(t,n){return t===n||(p||(p=document.createElement("a")),p.href=n,t===p.href)}function yt(t){return 0===Object.keys(t).length}function L(t,...n){if(null==t){for(const t of n)t(void 0);return H}const e=t.subscribe(...n);return e.unsubscribe?()=>e.unsubscribe():e}function gt(t){let n;return L(t,(t=>n=t))(),n}function bt(t,n,e){t.$$.on_destroy.push(L(n,e))}function xt(t,n,e,o){if(t){const r=S(t,n,e,o);return t[0](r)}}function S(t,n,e,o){return t[1]&&o?I(e.ctx.slice(),t[1](o(n))):e.ctx}function Et(t,n,e,o){if(t[2]&&o){const r=t[2](o(e));if(void 0===n.dirty)return r;if("object"==typeof r){const t=[],e=Math.max(n.dirty.length,r.length);for(let o=0;o<e;o+=1)t[o]=n.dirty[o]|r[o];return t}return n.dirty|r}return n.dirty}function wt(t,n,e,o,r,i){if(r){const s=S(n,e,o,i);t.p(s,r)}}function Tt(t){if(t.ctx.length>32){const n=[],e=t.ctx.length/32;for(let t=0;t<e;t++)n[t]=-1;return n}return-1}function Nt(t){const n={};for(const e in t)"$"!==e[0]&&(n[e]=t[e]);return n}function kt(t,n){const e={};n=new Set(n);for(const o in t)!n.has(o)&&"$"!==o[0]&&(e[o]=t[o]);return e}function At(t){const n={};for(const e in t)n[e]=!0;return n}function vt(t){return t??""}function Ct(t,n,e){return t.set(e),n}function jt(t){return t&&W(t.destroy)?t.destroy:H}function Dt(t){const n="string"==typeof t&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return n?[parseFloat(n[1]),n[2]||"px"]:[t,"px"]}let g=!1;function Ht(){g=!0}function Lt(){g=!1}function J(t,n,e,o){for(;t<n;){const r=t+(n-t>>1);e(r)<=o?t=r+1:n=r}return t}function K(t){if(t.hydrate_init)return;t.hydrate_init=!0;let n=t.childNodes;if("HEAD"===t.nodeName){const t=[];for(let e=0;e<n.length;e++){const o=n[e];void 0!==o.claim_order&&t.push(o)}n=t}const e=new Int32Array(n.length+1),o=new Int32Array(n.length);e[0]=-1;let r=0;for(let t=0;t<n.length;t++){const i=n[t].claim_order,s=(r>0&&n[e[r]].claim_order<=i?r+1:J(1,r,(t=>n[e[t]].claim_order),i))-1;o[t]=e[s]+1;const c=s+1;e[c]=t,r=Math.max(c,r)}const i=[],s=[];let c=n.length-1;for(let t=e[r]+1;0!=t;t=o[t-1]){for(i.push(n[t-1]);c>=t;c--)s.push(n[c]);c--}for(;c>=0;c--)s.push(n[c]);i.reverse(),s.sort(((t,n)=>t.claim_order-n.claim_order));for(let n=0,e=0;n<s.length;n++){for(;e<i.length&&s[n].claim_order>=i[e].claim_order;)e++;const o=e<i.length?i[e]:null;t.insertBefore(s[n],o)}}function Q(t,n){t.appendChild(n)}function V(t){if(!t)return document;const n=t.getRootNode?t.getRootNode():t.ownerDocument;return n&&n.host?n:t.ownerDocument}function St(t){const n=k("style");return n.textContent="/* empty */",X(V(t),n),n.sheet}function X(t,n){return Q(t.head||t,n),n.sheet}function Y(t,n){if(g){for(K(t),(void 0===t.actual_end_child||null!==t.actual_end_child&&t.actual_end_child.parentNode!==t)&&(t.actual_end_child=t.firstChild);null!==t.actual_end_child&&void 0===t.actual_end_child.claim_order;)t.actual_end_child=t.actual_end_child.nextSibling;n!==t.actual_end_child?(void 0!==n.claim_order||n.parentNode!==t)&&t.insertBefore(n,t.actual_end_child):t.actual_end_child=n.nextSibling}else(n.parentNode!==t||null!==n.nextSibling)&&t.appendChild(n)}function Z(t,n,e){t.insertBefore(n,e||null)}function $(t,n,e){g&&!e?Y(t,n):(n.parentNode!==t||n.nextSibling!=e)&&t.insertBefore(n,e||null)}function w(t){t.parentNode&&t.parentNode.removeChild(t)}function Mt(t,n){for(let e=0;e<t.length;e+=1)t[e]&&t[e].d(n)}function k(t){return document.createElement(t)}function M(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function A(t){return document.createTextNode(t)}function Pt(){return A(" ")}function Ot(){return A("")}function qt(t,n,e,o){return t.addEventListener(n,e,o),()=>t.removeEventListener(n,e,o)}function Bt(t){return function(n){return n.preventDefault(),t.call(this,n)}}function Rt(t){return function(n){return n.stopPropagation(),t.call(this,n)}}function v(t,n,e){null==e?t.removeAttribute(n):t.getAttribute(n)!==e&&t.setAttribute(n,e)}const tt=["width","height"];function et(t,n){const e=Object.getOwnPropertyDescriptors(t.__proto__);for(const o in n)null==n[o]?t.removeAttribute(o):"style"===o?t.style.cssText=n[o]:"__value"===o?t.value=t[o]=n[o]:e[o]&&e[o].set&&-1===tt.indexOf(o)?t[o]=n[o]:v(t,o,n[o])}function Ft(t,n){for(const e in n)v(t,e,n[e])}function nt(t,n){Object.keys(n).forEach((e=>{it(t,e,n[e])}))}function it(t,n,e){const o=n.toLowerCase();o in t?t[o]="boolean"==typeof t[o]&&""===e||e:n in t?t[n]="boolean"==typeof t[n]&&""===e||e:v(t,n,e)}function Gt(t){return/-/.test(t)?nt:et}function It(t){return t.dataset.svelteH}function Ut(t){return""===t?null:+t}function zt(t){return Array.from(t.childNodes)}function P(t){void 0===t.claim_info&&(t.claim_info={last_index:0,total_claimed:0})}function O(t,n,e,o,r=!1){P(t);const i=(()=>{for(let o=t.claim_info.last_index;o<t.length;o++){const i=t[o];if(n(i)){const n=e(i);return void 0===n?t.splice(o,1):t[o]=n,r||(t.claim_info.last_index=o),i}}for(let o=t.claim_info.last_index-1;o>=0;o--){const i=t[o];if(n(i)){const n=e(i);return void 0===n?t.splice(o,1):t[o]=n,r?void 0===n&&t.claim_info.last_index--:t.claim_info.last_index=o,i}}return o()})();return i.claim_order=t.claim_info.total_claimed,t.claim_info.total_claimed+=1,i}function q(t,n,e,o){return O(t,(t=>t.nodeName===n),(t=>{const n=[];for(let o=0;o<t.attributes.length;o++){const r=t.attributes[o];e[r.name]||n.push(r.name)}n.forEach((n=>t.removeAttribute(n)))}),(()=>o(n)))}function Wt(t,n,e){return q(t,n,e,k)}function Jt(t,n,e){return q(t,n,e,M)}function st(t,n){return O(t,(t=>3===t.nodeType),(t=>{const e=""+n;if(t.data.startsWith(e)){if(t.data.length!==e.length)return t.splitText(e.length)}else t.data=e}),(()=>A(n)),!0)}function Kt(t){return st(t," ")}function j(t,n,e){for(let o=e;o<t.length;o+=1){const e=t[o];if(8===e.nodeType&&e.textContent.trim()===n)return o}return-1}function Qt(t,n){const e=j(t,"HTML_TAG_START",0),o=j(t,"HTML_TAG_END",e+1);if(-1===e||-1===o)return new b(n);P(t);const r=t.splice(e,o-e+1);w(r[0]),w(r[r.length-1]);const i=r.slice(1,r.length-1);if(0===i.length)return new b(n);for(const n of i)n.claim_order=t.claim_info.total_claimed,t.claim_info.total_claimed+=1;return new b(n,i)}function Vt(t,n){n=""+n,t.data!==n&&(t.data=n)}function Xt(t,n){t.value=n??""}function Yt(t,n,e,o){null==e?t.style.removeProperty(n):t.style.setProperty(n,e,"")}function Zt(t,n,e){for(let e=0;e<t.options.length;e+=1){const o=t.options[e];if(o.__value===n)return void(o.selected=!0)}(!e||void 0!==n)&&(t.selectedIndex=-1)}function $t(t){const n=t.querySelector(":checked");return n&&n.__value}function te(t,n,e){t.classList.toggle(n,!!e)}function rt(t,n,{bubbles:e=!1,cancelable:o=!1}={}){return new CustomEvent(t,{detail:n,bubbles:e,cancelable:o})}function ee(t,n){const e=[];let o=0;for(const r of n.childNodes)if(8===r.nodeType){const n=r.textContent.trim();n===`HEAD_${t}_END`?(o-=1,e.push(r)):n===`HEAD_${t}_START`&&(o+=1,e.push(r))}else o>0&&e.push(r);return e}class ct{constructor(t=!1){_(this,"is_svg",!1),_(this,"e"),_(this,"n"),_(this,"t"),_(this,"a"),this.is_svg=t,this.e=this.n=null}c(t){this.h(t)}m(t,n,e=null){this.e||(this.is_svg?this.e=M(n.nodeName):this.e=k(11===n.nodeType?"TEMPLATE":n.nodeName),this.t="TEMPLATE"!==n.tagName?n:n.content,this.c(t)),this.i(e)}h(t){this.e.innerHTML=t,this.n=Array.from("TEMPLATE"===this.e.nodeName?this.e.content.childNodes:this.e.childNodes)}i(t){for(let n=0;n<this.n.length;n+=1)Z(this.t,this.n[n],t)}p(t){this.d(),this.h(t),this.i(this.a)}d(){this.n.forEach(w)}}class b extends ct{constructor(t=!1,n){super(t),_(this,"l"),this.e=this.n=null,this.l=n}c(t){this.l?this.n=this.l:super.c(t)}i(t){for(let n=0;n<this.n.length;n+=1)$(this.t,this.n[n],t)}}function ne(t,n){return new t(n)}let y;function x(t){y=t}function u(){if(!y)throw new Error("Function called outside component initialization");return y}function ie(t){u().$$.before_update.push(t)}function se(t){u().$$.on_mount.push(t)}function re(t){u().$$.after_update.push(t)}function ce(t){u().$$.on_destroy.push(t)}function oe(){const t=u();return(n,e,{cancelable:o=!1}={})=>{const r=t.$$.callbacks[n];if(r){const i=rt(n,e,{cancelable:o});return r.slice().forEach((n=>{n.call(t,i)})),!i.defaultPrevented}return!0}}function le(t,n){return u().$$.context.set(t,n),n}function ae(t){return u().$$.context.get(t)}function ue(){return u().$$.context}function fe(t,n){const e=t.$$.callbacks[n.type];e&&e.slice().forEach((t=>t.call(this,n)))}const m=[],D=[];let d=[];const T=[],B=Promise.resolve();let N=!1;function ot(){N||(N=!0,B.then(at))}function _e(){return ot(),B}function lt(t){d.push(t)}function he(t){T.push(t)}const E=new Set;let h=0;function at(){if(0!==h)return;const t=y;do{try{for(;h<m.length;){const t=m[h];h++,x(t),ut(t.$$)}}catch(t){throw m.length=0,h=0,t}for(x(null),m.length=0,h=0;D.length;)D.pop()();for(let t=0;t<d.length;t+=1){const n=d[t];E.has(n)||(E.add(n),n())}d.length=0}while(m.length);for(;T.length;)T.pop()();N=!1,E.clear(),x(t)}function ut(t){if(null!==t.fragment){t.update(),z(t.before_update);const n=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,n),t.after_update.forEach(lt)}}function de(t){const n=[],e=[];d.forEach((o=>-1===t.indexOf(o)?n.push(o):e.push(o))),e.forEach((t=>t())),d=n}export{ot as $,jt as A,se as B,L as C,D,W as E,ht as F,u as G,x as H,at as I,te as J,re as K,_e as L,ne as M,Yt as N,gt as O,V as P,St as Q,_t as R,rt as S,yt as T,y as U,dt as V,de as W,U as X,Ht as Y,Lt as Z,m as _,Et as a,le as a0,ae as a1,kt as a2,I as a3,Nt as a4,et as a5,At as a6,Jt as a7,M as a8,Mt as a9,Xt as aa,Ut as ab,Qt as ac,b as ad,vt as ae,Ct as af,he as ag,oe as ah,ie as ai,ee as aj,ce as ak,$t as al,Zt as am,Bt as an,Ft as ao,Gt as ap,pt as aq,ue as ar,Dt as as,v as b,xt as c,w as d,Y as e,Rt as f,Tt as g,Wt as h,$ as i,zt as j,Kt as k,qt as l,k as m,Pt as n,fe as o,lt as p,It as q,Ot as r,mt as s,bt as t,wt as u,H as v,Vt as w,st as x,A as y,z};
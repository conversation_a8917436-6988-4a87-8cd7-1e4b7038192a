import{s as Y,d as h,z as ne,i as k,e as Q,A as re,l as V,b as C,h as S,q as le,k as B,j as q,m as R,n as D,v as Z,B as ie,t as ae,w as oe,x as H,y as T,p as ue,C as fe,D as ce,r as N,E as me}from"../chunks/scheduler.DQwIXrE4.js";import{S as $,i as x,t as E,a as p,g as O,c as j,d as z,m as A,b as I,e as P,f as L}from"../chunks/index.BEt_7cXZ.js";import{s as de,b as M,a as _e,g as he,E as W,c as F,d as be,D as ge,e as G}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CT5-6w2d.js";import"../chunks/entry.Bsu4L07B.js";import{B as J}from"../chunks/Button.Calaql0q.js";function K(e){let s,t,a,n,r;return t=new J({props:{size:"sm",outline:!0,icon:e[1]?W:F,$$slots:{default:[ye]},$$scope:{ctx:e}}}),t.$on("click",e[12]),n=new J({props:{size:"sm",variant:"positive",outline:!0,icon:be,$$slots:{default:[we]},$$scope:{ctx:e}}}),n.$on("click",e[13]),{c(){s=R("div"),P(t.$$.fragment),a=D(),P(n.$$.fragment),this.h()},l(e){s=S(e,"DIV",{class:!0});var r=q(s);I(t.$$.fragment,r),a=B(r),I(n.$$.fragment,r),r.forEach(h),this.h()},h(){C(s,"class","absolute bottom-2 right-2 z-10 flex gap-2")},m(e,l){k(e,s,l),A(t,s,null),Q(s,a),A(n,s,null),r=!0},p(e,s){const a={};2&s&&(a.icon=e[1]?W:F),131074&s&&(a.$$scope={dirty:s,ctx:e}),t.$set(a);const r={};131072&s&&(r.$$scope={dirty:s,ctx:e}),n.$set(r)},i(e){r||(p(t.$$.fragment,e),p(n.$$.fragment,e),r=!0)},o(e){E(t.$$.fragment,e),E(n.$$.fragment,e),r=!1},d(e){e&&h(s),z(t),z(n)}}}function pe(e){let s;return{c(){s=T("Show Results")},l(e){s=H(e,"Show Results")},m(e,t){k(e,s,t)},d(e){e&&h(s)}}}function ke(e){let s;return{c(){s=T("Hide Results")},l(e){s=H(e,"Hide Results")},m(e,t){k(e,s,t)},d(e){e&&h(s)}}}function ye(e){let s;function t(e,s){return e[1]?ke:pe}let a=t(e),n=a(e);return{c(){n.c(),s=N()},l(e){n.l(e),s=N()},m(e,t){n.m(e,t),k(e,s,t)},p(e,r){a!==(a=t(e))&&(n.d(1),n=a(e),n&&(n.c(),n.m(s.parentNode,s)))},d(e){e&&h(s),n.d(e)}}}function we(e){let s;return{c(){s=T("Submit")},l(e){s=H(e,"Submit")},m(e,t){k(e,s,t)},d(e){e&&h(s)}}}function U(e){let s,t,a=e[9].error+"";return{c(){s=R("pre"),t=T(a),this.h()},l(e){s=S(e,"PRE",{class:!0});var n=q(s);t=H(n,a),n.forEach(h),this.h()},h(){C(s,"class","text-negative text-xs font-mono")},m(e,a){k(e,s,a),Q(s,t)},p(e,s){512&s&&a!==(a=e[9].error+"")&&oe(t,a)},d(e){e&&h(s)}}}function X(e){let s,t,a,n;return t=new ge({props:{data:e[9]}}),{c(){s=R("div"),P(t.$$.fragment)},l(e){s=S(e,"DIV",{});var a=q(s);I(t.$$.fragment,a),a.forEach(h)},m(e,a){k(e,s,a),A(t,s,null),n=!0},p(e,s){const a={};512&s&&(a.data=e[9]),t.$set(a)},i(e){n||(p(t.$$.fragment,e),e&&ue((()=>{n&&(a||(a=L(s,G,{},!0)),a.run(1))})),n=!0)},o(e){E(t.$$.fragment,e),e&&(a||(a=L(s,G,{},!1)),a.run(0)),n=!1},d(e){e&&h(s),z(t),e&&a&&a.end()}}}function Ee(e){let s,t,a,n,r,l,o,i,c,u,d="SQL Console",m=e[9].error&&!e[2]&&!!e[4],$=!e[3]&&K(e),f=m&&U(e),g=e[1]&&X(e);return{c(){s=R("h1"),s.textContent=d,t=D(),a=R("section"),n=R("div"),$&&$.c(),l=D(),f&&f.c(),o=D(),g&&g.c(),this.h()},l(e){s=S(e,"H1",{class:!0,"data-svelte-h":!0}),"svelte-7ylf69"!==le(s)&&(s.textContent=d),t=B(e),a=S(e,"SECTION",{class:!0,role:!0});var r=q(a);n=S(r,"DIV",{class:!0});var i=q(n);$&&$.l(i),i.forEach(h),l=B(r),f&&f.l(r),o=B(r),g&&g.l(r),r.forEach(h),this.h()},h(){C(s,"class","markdown"),C(n,"class","w-full relative rounded-sm border border-base-300 min-h-[8rem] cursor-text **:[&.cm-editor]:min-h-[8rem] **:[&.cm-editor]:rounded-sm"),C(a,"class","px-0 py-2 flex flex-col gap-2 min-h-[8rem]"),C(a,"role","none")},m(d,h){k(d,s,h),k(d,t,h),k(d,a,h),Q(a,n),$&&$.m(n,null),e[14](n),Q(a,l),f&&f.m(a,null),Q(a,o),g&&g.m(a,null),i=!0,c||(u=[re(r=de.call(null,n,{...e[5],theme:e[8]})),V(a,"click",e[15]),V(a,"keydown",e[16])],c=!0)},p(e,[s]){e[3]?$&&(O(),E($,1,1,(()=>{$=null})),j()):$?($.p(e,s),8&s&&p($,1)):($=K(e),$.c(),p($,1),$.m(n,null)),r&&me(r.update)&&288&s&&r.update.call(null,{...e[5],theme:e[8]}),532&s&&(m=e[9].error&&!e[2]&&!!e[4]),m?f?f.p(e,s):(f=U(e),f.c(),f.m(a,o)):f&&(f.d(1),f=null),e[1]?g?(g.p(e,s),2&s&&p(g,1)):(g=X(e),g.c(),p(g,1),g.m(a,null)):g&&(O(),E(g,1,1,(()=>{g=null})),j())},i(e){i||(p($),p(g),i=!0)},o(e){E($),E(g),i=!1},d(n){n&&(h(s),h(t),h(a)),$&&$.d(),e[14](null),f&&f.d(),g&&g.d(),c=!1,ne(u)}}}function Ce(e,s,t){let a,n,r=Z,l=()=>(r(),r=fe(p,(e=>t(9,n=e))),p);e.$$.on_destroy.push((()=>r()));let o,i,{hideErrors:c=!1}=s,{initialQuery:u="select 'ABC' as category, 123 as num, 26400000 as sales_usd"}=s,{showResults:d=!0}=s,{disabled:h=!1}=s,m=u,$=m,{data:p=M(m)}=s;l(),ie((async()=>{p&&p.fetch(),t(5,i={initialState:u,disabled:h,schema:await _e(),onChange:e=>{e.docChanged&&$.trim()!==e.state.doc.toString().trim()&&t(6,$=e.state.doc.toString())},onSubmit:()=>(t(4,m=$.trim()),m.endsWith(";")&&t(4,m=m.substring(0,m.length-1)),t(1,d=!0),!0)})}));const{theme:f}=he();return ae(e,f,(e=>t(8,a=e))),e.$$set=e=>{"hideErrors"in e&&t(2,c=e.hideErrors),"initialQuery"in e&&t(11,u=e.initialQuery),"showResults"in e&&t(1,d=e.showResults),"disabled"in e&&t(3,h=e.disabled),"data"in e&&l(t(0,p=e.data))},e.$$.update=()=>{17&e.$$.dirty&&m&&(l(t(0,p=M(m))),p.fetch()),40&e.$$.dirty&&i&&t(5,i.disabled=h,i)},[p,d,c,h,m,i,$,o,a,n,f,u,()=>t(1,d=!d),()=>{t(4,m=$),t(1,d=!0)},function(e){ce[e?"unshift":"push"]((()=>{o=e,t(7,o)}))},()=>null==o?void 0:o.focus(),e=>"Enter"===e.key&&(null==o?void 0:o.focus())]}class Se extends ${constructor(e){super(),x(this,e,Ce,Ee,Y,{hideErrors:2,initialQuery:11,showResults:1,disabled:3,data:0})}}function Re(e){let s,t;return s=new Se({}),{c(){P(s.$$.fragment)},l(e){I(s.$$.fragment,e)},m(e,a){A(s,e,a),t=!0},p:Z,i(e){t||(p(s.$$.fragment,e),t=!0)},o(e){E(s.$$.fragment,e),t=!1},d(e){z(s,e)}}}class ze extends ${constructor(e){super(),x(this,e,null,Re,Y,{})}}export{ze as component};
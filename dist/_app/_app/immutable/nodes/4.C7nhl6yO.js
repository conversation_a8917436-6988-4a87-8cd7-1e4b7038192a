import{s as tt,v as de,d as m,b as h,i as S,e as D,h as N,j as K,a7 as Ll,m as F,a8 as kl,C as Tt,a9 as Zl,p as ll,l as yt,N as v,k as Z,n as J,w as Qe,x as Ce,y as Te,z as Vl,aa as Bl,ab as Un,r as pe,ac as Hn,ad as vn,q as Se,t as Nt,J as jt,ae as qn,af as Li,D as Vn,ag as Wn,a1 as Ul,A as il,E as nl,a2 as Wi,ah as jn,a3 as Lt,a4 as Ht,I as Yn,a0 as ki,c as sl,u as rl,g as al,a as ol,ai as In,aj as Xn,ak as Qn,B as Kn}from"../chunks/scheduler.DQwIXrE4.js";import{S as lt,i as it,d as se,t as U,f as Qt,j as wi,a as M,m as re,b as ae,e as oe,g as <PERSON>,c as Ze,k as Zn}from"../chunks/index.BEt_7cXZ.js";import{g as Pt,h as Yt,Y as Oi,e as Kt,Z as Mn,_ as El,$ as Jn,a0 as ut,P as ji,a1 as xn,a2 as Yi,a3 as Hl,a4 as pn,a5 as $n,a6 as Ii,a7 as Mi,a8 as Dn,a9 as es,f as ts,aa as ls,ab as el,ac as is,ad as ns,ae as Ti,af as Si,ag as Ei,V as qe,ah as Al,ai as ss,aj as Ot,ak as Xt,al as Kl,am as Di,an as Ni,ao as Fi,ap as Pi,aq as Jl,ar as It,O as xl,S as pl,as as $l,at as rs,au as as,av as os,aw as fs,X as Nn,ax as us,W as cs,R as Sl,ay as Ol,az as ds,aA as ms,aB as hs,aC as ys,aD as bs,aE as Xi,aF as gs}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.ebVGE6DV.js";import{w as Ai}from"../chunks/entry.DSMgDpC3.js";import{h as Qi,p as _s}from"../chunks/setTrackProxy.DjIbdjlZ.js";import{p as Fn}from"../chunks/stores.DZ5VrdAm.js";function Ki(e){return t=>t.map((t=>{var s;const i={},n=Object.keys(t);for(const a of n)i[null!=(s=e[a])?s:a]=t[a];return i}))}function Cs(e,t){if(0===e.length||0===t.length)return{};const s=Object.keys(e[0]),i=Object.keys(t[0]),n={};for(const e of s)i.includes(e)&&(n[e]=e);return n}function Ts(e,t,s){for(const i in s)if(e[s[i]]!==t[i])return!1;return!0}function Ls(e,t){return t=>{if(!e.length)return t;const s=Cs(t,e),i=Object.keys(e[0]);return t.flatMap((t=>{const n=e.filter((e=>Ts(t,e,s)));if(n.length)return n.map((e=>({...t,...e})));const a=Object.fromEntries(i.filter((e=>null==t[e])).map((e=>[e,void 0])));return{...t,...a}}))}}function Zi(e){return t=>{const s=t.map((e=>({...e})));for(const i in e){const n=e[i],a="function"==typeof n?n(s):n,l=null!=a&&a[Symbol.iterator]&&"string"!=typeof a?a:t.map((()=>a));let r=-1;for(const e of s)e[i]=l[++r]}return s}}function ks(e){return t=>{const s=Es(e),i=[];for(const e in s){const n=s[e];let a;a="function"==typeof n?n(t):Array.isArray(n)?n:Array.from(new Set(t.map((t=>t[e])))),i.push(a.map((t=>({[e]:t}))))}return Ss(i)}}function Ss(e){const t=[];return function e(t,s,i){if(!i.length&&null!=s)return void t.push(s);const n=i[0],a=i.slice(1);for(const i of n)e(t,{...s,...i},a)}(t,null,e),t}function Es(e){if(Array.isArray(e)){const t={};for(const s of e)t[s]=s;return t}return"object"==typeof e?e:{[e]:e}}function As(e){return t=>{const s=[];for(const i of t){const t={...i};for(const s in e)null==t[s]&&(t[s]=e[s]);s.push(t)}return s}}function Ji(e,t){return s=>{const i=ks(e)(s),n=Ls(s)(i);return t?As(t)(n):n}}function xi(e,t,s){return null==e||null==t?void 0:0===t&&0===e?0:s||0!==t?e/t:void 0}function pi(e,t,s){const i="function"==typeof e?e:t=>t[e],n=e=>e[t],{predicate:a,allowDivideByZero:l}={};return null==a?(e,t,s)=>{const a=n(e);return xi(i(e,t,s),a,l)}:(e,t,s)=>{if(!a(e,t,s))return;const r=n(e);return xi(i(e,t,s),r,l)}}function ws(e){let t,s,i;return{c(){t=F("span"),s=kl("svg"),i=kl("path"),this.h()},l(e){t=N(e,"SPAN",{"aria-expanded":!0,class:!0});var n=K(t);s=Ll(n,"svg",{viewBox:!0,width:!0,height:!0,class:!0});var a=K(s);i=Ll(a,"path",{fill:!0,"fill-rule":!0,d:!0}),K(i).forEach(m),a.forEach(m),n.forEach(m),this.h()},h(){h(i,"fill",e[3]),h(i,"fill-rule","evenodd"),h(i,"d","M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"),h(s,"viewBox","0 0 16 16"),h(s,"width",e[1]),h(s,"height",e[1]),h(s,"class","svelte-lqleyo"),h(t,"aria-expanded",e[0]),h(t,"class","svelte-lqleyo")},m(e,n){S(e,t,n),D(t,s),D(s,i)},p(e,[n]){8&n&&h(i,"fill",e[3]),2&n&&h(s,"width",e[1]),2&n&&h(s,"height",e[1]),1&n&&h(t,"aria-expanded",e[0])},i:de,o:de,d(e){e&&m(t)}}}function Os(e,t,s){let i,n,a=de;e.$$.on_destroy.push((()=>a()));const{resolveColor:l}=Pt();let{toggled:r=!1}=t,{color:o="base-content"}=t,{size:c=10}=t;return e.$$set=e=>{"toggled"in e&&s(0,r=e.toggled),"color"in e&&s(4,o=e.color),"size"in e&&s(1,c=e.size)},e.$$.update=()=>{16&e.$$.dirty&&(s(2,i=l(o)),a(),a=Tt(i,(e=>s(3,n=e))))},[r,c,i,n,o]}class Pn extends lt{constructor(e){super(),it(this,e,Os,ws,tt,{toggled:0,color:4,size:1})}}function $i(e,t,s){const i=e.slice();return i[12]=t[s],i[14]=s,i}function en(e,t,s){const i=e.slice();return i[15]=t[s],i[17]=s,i}function tn(e,t,s){const i=e.slice();return i[15]=t[s],i}function ln(e,t,s){const i=e.slice();return i[15]=t[s],i}function nn(e){let t,s,i,n,a,l=e[15].id+"";return{c(){t=F("th"),s=Te(l),this.h()},l(e){t=N(e,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var i=K(t);s=Ce(i,l),i.forEach(m),this.h()},h(){var s,l;h(t,"class",i="py-0 px-2 font-medium "+e[15].type+" svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"evidencetype",n=(null==(s=e[15].evidenceColumnType)?void 0:s.evidenceType)||"unavailable"),h(t,"evidencetypefidelity",a=(null==(l=e[15].evidenceColumnType)?void 0:l.typeFidelity)||"unavailable")},m(e,i){S(e,t,i),D(t,s)},p(e,r){var o,c;8&r&&l!==(l=e[15].id+"")&&Qe(s,l),8&r&&i!==(i="py-0 px-2 font-medium "+e[15].type+" svelte-ghf30y")&&h(t,"class",i),64&r&&v(t,"width",e[6]+"%"),8&r&&n!==(n=(null==(o=e[15].evidenceColumnType)?void 0:o.evidenceType)||"unavailable")&&h(t,"evidencetype",n),8&r&&a!==(a=(null==(c=e[15].evidenceColumnType)?void 0:c.typeFidelity)||"unavailable")&&h(t,"evidencetypefidelity",a)},d(e){e&&m(t)}}}function sn(e){let t,s,i,n,a,l=e[15].type+"";return{c(){t=F("th"),s=Te(l),this.h()},l(e){t=N(e,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var i=K(t);s=Ce(i,l),i.forEach(m),this.h()},h(){var s,l;h(t,"class",i=e[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"evidencetype",n=(null==(s=e[15].evidenceColumnType)?void 0:s.evidenceType)||"unavailable"),h(t,"evidencetypefidelity",a=(null==(l=e[15].evidenceColumnType)?void 0:l.typeFidelity)||"unavailable")},m(e,i){S(e,t,i),D(t,s)},p(e,r){var o,c;8&r&&l!==(l=e[15].type+"")&&Qe(s,l),8&r&&i!==(i=e[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y")&&h(t,"class",i),64&r&&v(t,"width",e[6]+"%"),8&r&&n!==(n=(null==(o=e[15].evidenceColumnType)?void 0:o.evidenceType)||"unavailable")&&h(t,"evidencetype",n),8&r&&a!==(a=(null==(c=e[15].evidenceColumnType)?void 0:c.typeFidelity)||"unavailable")&&h(t,"evidencetypefidelity",a)},d(e){e&&m(t)}}}function Is(e){let t,s=(e[2]+e[14]+1).toLocaleString()+"";return{c(){t=Te(s)},l(e){t=Ce(e,s)},m(e,s){S(e,t,s)},p(e,i){4&i&&s!==(s=(e[2]+e[14]+1).toLocaleString()+"")&&Qe(t,s)},d(e){e&&m(t)}}}function Ms(e){let t,s=(e[2]+e[14]+1).toLocaleString()+"";return{c(){t=Te(s)},l(e){t=Ce(e,s)},m(e,s){S(e,t,s)},p(e,i){4&i&&s!==(s=(e[2]+e[14]+1).toLocaleString()+"")&&Qe(t,s)},d(e){e&&m(t)}}}function Ds(e){let t,s,i=(e[12][e[15].id]||"Ø")+"";return{c(){t=F("td"),s=Te(i),this.h()},l(e){t=N(e,"TD",{class:!0,style:!0});var n=K(t);s=Ce(n,i),n.forEach(m),this.h()},h(){h(t,"class","other svelte-ghf30y"),v(t,"width",e[6]+"%")},m(e,i){S(e,t,i),D(t,s)},p(e,n){40&n&&i!==(i=(e[12][e[15].id]||"Ø")+"")&&Qe(s,i),64&n&&v(t,"width",e[6]+"%")},d(e){e&&m(t)}}}function Ns(e){let t,s,i,n,a=(e[12][e[15].id]??"Ø")+"";return{c(){t=F("td"),s=F("div"),i=Te(a),this.h()},l(e){t=N(e,"TD",{class:!0,style:!0,title:!0});var n=K(t);s=N(n,"DIV",{class:!0});var l=K(s);i=Ce(l,a),l.forEach(m),n.forEach(m),this.h()},h(){h(s,"class","svelte-ghf30y"),h(t,"class","boolean svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"title",n=e[12][e[15].id])},m(e,n){S(e,t,n),D(t,s),D(s,i)},p(e,s){40&s&&a!==(a=(e[12][e[15].id]??"Ø")+"")&&Qe(i,a),64&s&&v(t,"width",e[6]+"%"),40&s&&n!==(n=e[12][e[15].id])&&h(t,"title",n)},d(e){e&&m(t)}}}function Fs(e){let t,s,i,n,a=(e[12][e[15].id]||"Ø")+"";return{c(){t=F("td"),s=F("div"),i=Te(a),this.h()},l(e){t=N(e,"TD",{class:!0,style:!0,title:!0});var n=K(t);s=N(n,"DIV",{class:!0});var l=K(s);i=Ce(l,a),l.forEach(m),n.forEach(m),this.h()},h(){h(s,"class","svelte-ghf30y"),h(t,"class","string svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"title",n=e[12][e[15].id])},m(e,n){S(e,t,n),D(t,s),D(s,i)},p(e,s){40&s&&a!==(a=(e[12][e[15].id]||"Ø")+"")&&Qe(i,a),64&s&&v(t,"width",e[6]+"%"),40&s&&n!==(n=e[12][e[15].id])&&h(t,"title",n)},d(e){e&&m(t)}}}function Ps(e){let t,s,i,n,a=ut(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"";return{c(){t=F("td"),s=F("div"),i=Te(a),this.h()},l(e){t=N(e,"TD",{class:!0,style:!0,title:!0});var n=K(t);s=N(n,"DIV",{class:!0});var l=K(s);i=Ce(l,a),l.forEach(m),n.forEach(m),this.h()},h(){h(s,"class","svelte-ghf30y"),h(t,"class","string svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"title",n=ut(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary))},m(e,n){S(e,t,n),D(t,s),D(s,i)},p(e,s){40&s&&a!==(a=ut(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"")&&Qe(i,a),64&s&&v(t,"width",e[6]+"%"),40&s&&n!==(n=ut(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary))&&h(t,"title",n)},d(e){e&&m(t)}}}function Rs(e){let t,s,i=ut(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"";return{c(){t=F("td"),s=Te(i),this.h()},l(e){t=N(e,"TD",{class:!0,style:!0});var n=K(t);s=Ce(n,i),n.forEach(m),this.h()},h(){h(t,"class","number svelte-ghf30y"),v(t,"width",e[6]+"%")},m(e,i){S(e,t,i),D(t,s)},p(e,n){40&n&&i!==(i=ut(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"")&&Qe(s,i),64&n&&v(t,"width",e[6]+"%")},d(e){e&&m(t)}}}function Bs(e){let t,s,i;return{c(){t=F("td"),s=Te("Ø"),this.h()},l(e){t=N(e,"TD",{class:!0,style:!0});var i=K(t);s=Ce(i,"Ø"),i.forEach(m),this.h()},h(){h(t,"class",i="text-base-content-muted "+e[3][e[17]].type+" svelte-ghf30y"),v(t,"width",e[6]+"%")},m(e,i){S(e,t,i),D(t,s)},p(e,s){8&s&&i!==(i="text-base-content-muted "+e[3][e[17]].type+" svelte-ghf30y")&&h(t,"class",i),64&s&&v(t,"width",e[6]+"%")},d(e){e&&m(t)}}}function rn(e){let t;function s(e,t){return null==e[12][e[15].id]?Bs:"number"===e[3][e[17]].type?Rs:"date"===e[3][e[17]].type?Ps:"string"===e[3][e[17]].type?Fs:"boolean"===e[3][e[17]].type?Ns:Ds}let i=s(e),n=i(e);return{c(){n.c(),t=pe()},l(e){n.l(e),t=pe()},m(e,s){n.m(e,s),S(e,t,s)},p(e,a){i===(i=s(e))&&n?n.p(e,a):(n.d(1),n=i(e),n&&(n.c(),n.m(t.parentNode,t)))},d(e){e&&m(t),n.d(e)}}}function an(e){let t,s,i,n,a=(0===e[14]?Ms:Is)(e),l=Yt(e[3]),r=[];for(let t=0;t<l.length;t+=1)r[t]=rn(en(e,l,t));return{c(){t=F("tr"),s=F("td"),a.c(),i=J();for(let e=0;e<r.length;e+=1)r[e].c();n=J(),this.h()},l(e){t=N(e,"TR",{});var l=K(t);s=N(l,"TD",{class:!0,style:!0});var o=K(s);a.l(o),o.forEach(m),i=Z(l);for(let e=0;e<r.length;e+=1)r[e].l(l);n=Z(l),l.forEach(m),this.h()},h(){h(s,"class","index text-base-content-muted svelte-ghf30y"),v(s,"width","10%")},m(e,l){S(e,t,l),D(t,s),a.m(s,null),D(t,i);for(let e=0;e<r.length;e+=1)r[e]&&r[e].m(t,null);D(t,n)},p(e,s){if(a.p(e,s),104&s){let i;for(l=Yt(e[3]),i=0;i<l.length;i+=1){const a=en(e,l,i);r[i]?r[i].p(a,s):(r[i]=rn(a),r[i].c(),r[i].m(t,n))}for(;i<r.length;i+=1)r[i].d(1);r.length=l.length}},d(e){e&&m(t),a.d(),Zl(r,e)}}}function on(e){let t,s,i,n,a,l,r,o,c,d=(e[2]+tl).toLocaleString()+"",p=(e[4]+tl).toLocaleString()+"";return{c(){t=F("div"),s=F("input"),i=J(),n=F("span"),a=Te(d),l=Te(" of "),r=Te(p),this.h()},l(e){t=N(e,"DIV",{class:!0});var o=K(t);s=N(o,"INPUT",{type:!0,max:!0,step:!0,class:!0}),i=Z(o),n=N(o,"SPAN",{class:!0});var c=K(n);a=Ce(c,d),l=Ce(c," of "),r=Ce(c,p),c.forEach(m),o.forEach(m),this.h()},h(){h(s,"type","range"),h(s,"max",e[4]),h(s,"step","1"),h(s,"class","slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"),h(n,"class","text-xs svelte-ghf30y"),h(t,"class","pagination svelte-ghf30y")},m(d,m){S(d,t,m),D(t,s),Bl(s,e[2]),D(t,i),D(t,n),D(n,a),D(n,l),D(n,r),o||(c=[yt(s,"change",e[9]),yt(s,"input",e[9]),yt(s,"input",e[7])],o=!0)},p(e,t){16&t&&h(s,"max",e[4]),4&t&&Bl(s,e[2]),4&t&&d!==(d=(e[2]+tl).toLocaleString()+"")&&Qe(a,d),16&t&&p!==(p=(e[4]+tl).toLocaleString()+"")&&Qe(r,p)},d(e){e&&m(t),o=!1,Vl(c)}}}function zs(e){let t,s,i,n,a,l,r,o,c,d,p,u,y,f,$,g,x,b,T,L,w,E,A,C,O,I,k=Yt(e[3]),P=[];for(let t=0;t<k.length;t+=1)P[t]=nn(ln(e,k,t));let R=Yt(e[3]),z=[];for(let t=0;t<R.length;t+=1)z[t]=sn(tn(e,R,t));let B=Yt(e[5]),G=[];for(let t=0;t<B.length;t+=1)G[t]=an($i(e,B,t));let H=e[4]>0&&on(e);return E=new Oi({props:{class:"download-button",data:e[1],queryID:e[0],display:!0}}),{c(){t=F("div"),s=F("div"),i=F("table"),n=F("thead"),a=F("tr"),l=F("th"),r=J();for(let e=0;e<P.length;e+=1)P[e].c();o=J(),c=F("tr"),d=J(),p=F("tr"),u=F("th"),y=J();for(let e=0;e<z.length;e+=1)z[e].c();f=J(),$=F("tr"),g=J(),x=F("tbody");for(let e=0;e<G.length;e+=1)G[e].c();T=J(),H&&H.c(),L=J(),w=F("div"),oe(E.$$.fragment),this.h()},l(e){t=N(e,"DIV",{class:!0});var h=K(t);s=N(h,"DIV",{class:!0});var v=K(s);i=N(v,"TABLE",{class:!0});var b=K(i);n=N(b,"THEAD",{});var S=K(n);a=N(S,"TR",{});var A=K(a);l=N(A,"TH",{class:!0,style:!0}),K(l).forEach(m),r=Z(A);for(let e=0;e<P.length;e+=1)P[e].l(A);o=Z(A),A.forEach(m),c=N(S,"TR",{}),K(c).forEach(m),d=Z(S),p=N(S,"TR",{class:!0});var C=K(p);u=N(C,"TH",{class:!0,style:!0}),K(u).forEach(m),y=Z(C);for(let e=0;e<z.length;e+=1)z[e].l(C);f=Z(C),C.forEach(m),$=N(S,"TR",{}),K($).forEach(m),S.forEach(m),g=Z(b),x=N(b,"TBODY",{});var M=K(x);for(let e=0;e<G.length;e+=1)G[e].l(M);M.forEach(m),b.forEach(m),v.forEach(m),T=Z(h),H&&H.l(h),L=Z(h),w=N(h,"DIV",{class:!0});var O=K(w);ae(E.$$.fragment,O),O.forEach(m),h.forEach(m),this.h()},h(){h(l,"class","py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y"),v(l,"width","10%"),h(u,"class","py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y"),v(u,"width","10%"),h(p,"class","type-indicator svelte-ghf30y"),h(i,"class","text-xs svelte-ghf30y"),h(s,"class","scrollbox pretty-scrollbar svelte-ghf30y"),h(w,"class","footer svelte-ghf30y"),h(t,"class","results-pane py-1 svelte-ghf30y")},m(m,h){S(m,t,h),D(t,s),D(s,i),D(i,n),D(n,a),D(a,l),D(a,r);for(let e=0;e<P.length;e+=1)P[e]&&P[e].m(a,null);D(a,o),D(n,c),D(n,d),D(n,p),D(p,u),D(p,y);for(let e=0;e<z.length;e+=1)z[e]&&z[e].m(p,null);D(p,f),D(n,$),D(i,g),D(i,x);for(let e=0;e<G.length;e+=1)G[e]&&G[e].m(x,null);D(t,T),H&&H.m(t,null),D(t,L),D(t,w),re(E,w,null),C=!0,O||(I=yt(x,"wheel",e[8]),O=!0)},p(e,[s]){if(72&s){let t;for(k=Yt(e[3]),t=0;t<k.length;t+=1){const i=ln(e,k,t);P[t]?P[t].p(i,s):(P[t]=nn(i),P[t].c(),P[t].m(a,o))}for(;t<P.length;t+=1)P[t].d(1);P.length=k.length}if(72&s){let t;for(R=Yt(e[3]),t=0;t<R.length;t+=1){const i=tn(e,R,t);z[t]?z[t].p(i,s):(z[t]=sn(i),z[t].c(),z[t].m(p,f))}for(;t<z.length;t+=1)z[t].d(1);z.length=R.length}if(108&s){let t;for(B=Yt(e[5]),t=0;t<B.length;t+=1){const i=$i(e,B,t);G[t]?G[t].p(i,s):(G[t]=an(i),G[t].c(),G[t].m(x,null))}for(;t<G.length;t+=1)G[t].d(1);G.length=B.length}e[4]>0?H?H.p(e,s):(H=on(e),H.c(),H.m(t,L)):H&&(H.d(1),H=null);const i={};2&s&&(i.data=e[1]),1&s&&(i.queryID=e[0]),E.$set(i)},i(e){C||(e&&(b||ll((()=>{b=wi(i,Mn,{}),b.start()}))),M(E.$$.fragment,e),e&&ll((()=>{C&&(A||(A=Qt(t,Kt,{},!0)),A.run(1))})),C=!0)},o(e){U(E.$$.fragment,e),e&&(A||(A=Qt(t,Kt,{},!1)),A.run(0)),C=!1},d(e){e&&m(t),Zl(P,e),Zl(z,e),Zl(G,e),H&&H.d(),se(E),e&&A&&A.end(),O=!1,I()}}}let tl=5;function Gs(e,t,s){let i,n,a,l,r,{queryID:o}=t,{data:c}=t,d=0;function m(){r=c.slice(d,d+tl),s(5,l=r)}const p=Jn((e=>{s(2,d=Math.min(Math.max(0,d+Math.floor(e.deltaY/Math.abs(e.deltaY))),a)),m()}),60);return e.$$set=e=>{"queryID"in e&&s(0,o=e.queryID),"data"in e&&s(1,c=e.data)},e.$$.update=()=>{2&e.$$.dirty&&s(3,i=El(c,"array")),8&e.$$.dirty&&s(6,n=90/(i.length+1)),2&e.$$.dirty&&s(4,a=Math.max(c.length-tl,0)),6&e.$$.dirty&&s(5,l=c.slice(d,d+tl))},[o,c,d,i,a,l,n,m,function(e){if(Math.abs(e.deltaX)>=Math.abs(e.deltaY))return;const t=e.deltaY<0&&0===d,s=e.deltaY>0&&d===a;t||s||(e.preventDefault(),p(e))},function(){d=Un(this.value),s(2,d)}]}class Us extends lt{constructor(e){super(),it(this,e,Gs,zs,tt,{queryID:0,data:1})}}const fn={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/};function Hs(e){let t,s,i,n,a,l=ji.highlight(e[0],fn)+"";return{c(){t=F("pre"),s=Te("  "),i=F("code"),n=new vn(!1),a=Te("\n"),this.h()},l(e){t=N(e,"PRE",{class:!0});var l=K(t);s=Ce(l,"  "),i=N(l,"CODE",{class:!0});var r=K(i);n=Hn(r,!1),r.forEach(m),a=Ce(l,"\n"),l.forEach(m),this.h()},h(){n.a=null,h(i,"class","language-sql svelte-re3fhx"),h(t,"class","text-xs max-h-56 overflow-auto pretty-scrollbar")},m(e,r){S(e,t,r),D(t,s),D(t,i),n.m(l,i),D(t,a)},p(e,[t]){1&t&&l!==(l=ji.highlight(e[0],fn)+"")&&n.p(l)},i:de,o:de,d(e){e&&m(t)}}}function vs(e,t,s){let{code:i=""}=t;return e.$$set=e=>{"code"in e&&s(0,i=e.code)},[i]}class Rn extends lt{constructor(e){super(),it(this,e,vs,Hs,tt,{code:0})}}function qs(e){let t,s,i,n,a,l="Compiled",r="Written";return{c(){t=F("button"),t.textContent=l,s=J(),i=F("button"),i.textContent=r,this.h()},l(e){t=N(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-1vzm9jy"!==Se(t)&&(t.textContent=l),s=Z(e),i=N(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-qu81ez"!==Se(i)&&(i.textContent=r),this.h()},h(){h(t,"class","off svelte-ska6l4"),h(i,"class","text-info bg-info/10 border border-info svelte-ska6l4")},m(l,r){S(l,t,r),S(l,s,r),S(l,i,r),n||(a=yt(t,"click",e[1]),n=!0)},p:de,d(e){e&&(m(t),m(s),m(i)),n=!1,a()}}}function Vs(e){let t,s,i,n,a,l="Compiled",r="Written";return{c(){t=F("button"),t.textContent=l,s=J(),i=F("button"),i.textContent=r,this.h()},l(e){t=N(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-wrfleh"!==Se(t)&&(t.textContent=l),s=Z(e),i=N(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-v36xno"!==Se(i)&&(i.textContent=r),this.h()},h(){h(t,"class","text-info bg-info/10 border border-info svelte-ska6l4"),h(i,"class","off svelte-ska6l4")},m(l,r){S(l,t,r),S(l,s,r),S(l,i,r),n||(a=yt(i,"click",e[1]),n=!0)},p:de,d(e){e&&(m(t),m(s),m(i)),n=!1,a()}}}function Ws(e){let t,s,i;function n(e,t){return e[0]?Vs:qs}let a=n(e),l=a(e);return{c(){t=F("div"),l.c(),this.h()},l(e){t=N(e,"DIV",{class:!0});var s=K(t);l.l(s),s.forEach(m),this.h()},h(){h(t,"class","toggle svelte-ska6l4")},m(e,s){S(e,t,s),l.m(t,null),i=!0},p(e,[s]){a===(a=n(e))&&l?l.p(e,s):(l.d(1),l=a(e),l&&(l.c(),l.m(t,null)))},i(e){i||(e&&ll((()=>{i&&(s||(s=Qt(t,Kt,{},!0)),s.run(1))})),i=!0)},o(e){e&&(s||(s=Qt(t,Kt,{},!1)),s.run(0)),i=!1},d(e){e&&m(t),l.d(),e&&s&&s.end()}}}function js(e,t,s){let{showCompiled:i}=t;return e.$$set=e=>{"showCompiled"in e&&s(0,i=e.showCompiled)},[i,function(){s(0,i=!i)}]}class Ys extends lt{constructor(e){super(),it(this,e,js,Ws,tt,{showCompiled:0})}}function un(e){let t,s,i,n,a,l,r,o,c,d,p,u,y,f,$,g,v;n=new Pn({props:{toggled:e[10]}});let x=e[10]&&e[4]&&cn(e),b=e[10]&&dn(e);const T=[xs,Js,Zs,Ks],L=[];function w(e,t){return e[6]?0:e[8]?1:e[2].loading?2:3}p=w(e),u=L[p]=T[p](e);let E=e[8]>0&&!e[6]&&e[9]&&mn(e);return{c(){t=F("div"),s=F("div"),i=F("button"),oe(n.$$.fragment),a=J(),l=Te(e[0]),r=J(),x&&x.c(),o=J(),b&&b.c(),c=J(),d=F("button"),u.c(),y=J(),E&&E.c(),this.h()},l(p){t=N(p,"DIV",{class:!0});var h=K(t);s=N(h,"DIV",{class:!0});var f=K(s);i=N(f,"BUTTON",{type:!0,"aria-label":!0,class:!0});var $=K(i);ae(n.$$.fragment,$),a=Z($),l=Ce($,e[0]),$.forEach(m),r=Z(f),x&&x.l(f),o=Z(f),b&&b.l(f),f.forEach(m),c=Z(h),d=N(h,"BUTTON",{type:!0,"aria-label":!0,class:!0});var g=K(d);u.l(g),g.forEach(m),y=Z(h),E&&E.l(h),h.forEach(m),this.h()},h(){h(i,"type","button"),h(i,"aria-label","show-sql"),h(i,"class","title svelte-1ursthx"),h(s,"class","container-a svelte-1ursthx"),h(d,"type","button"),h(d,"aria-label","view-query"),h(d,"class",qn("status-bar")+" svelte-1ursthx"),jt(d,"error",e[6]),jt(d,"success",!e[6]),jt(d,"open",e[9]),jt(d,"closed",!e[9]),h(t,"class","scrollbox my-3 svelte-1ursthx")},m(m,h){S(m,t,h),D(t,s),D(s,i),re(n,i,null),D(i,a),D(i,l),D(s,r),x&&x.m(s,null),D(s,o),b&&b.m(s,null),D(t,c),D(t,d),L[p].m(d,null),D(t,y),E&&E.m(t,null),$=!0,g||(v=[yt(i,"click",e[15]),yt(d,"click",e[16])],g=!0)},p(e,i){const a={};1024&i&&(a.toggled=e[10]),n.$set(a),(!$||1&i)&&Qe(l,e[0]),e[10]&&e[4]?x?(x.p(e,i),1040&i&&M(x,1)):(x=cn(e),x.c(),M(x,1),x.m(s,o)):x&&(Ke(),U(x,1,1,(()=>{x=null})),Ze()),e[10]?b?(b.p(e,i),1024&i&&M(b,1)):(b=dn(e),b.c(),M(b,1),b.m(s,null)):b&&(Ke(),U(b,1,1,(()=>{b=null})),Ze());let r=p;p=w(e),p===r?L[p].p(e,i):(Ke(),U(L[r],1,1,(()=>{L[r]=null})),Ze(),u=L[p],u?u.p(e,i):(u=L[p]=T[p](e),u.c()),M(u,1),u.m(d,null)),(!$||64&i)&&jt(d,"error",e[6]),(!$||64&i)&&jt(d,"success",!e[6]),(!$||512&i)&&jt(d,"open",e[9]),(!$||512&i)&&jt(d,"closed",!e[9]),e[8]>0&&!e[6]&&e[9]?E?(E.p(e,i),832&i&&M(E,1)):(E=mn(e),E.c(),M(E,1),E.m(t,null)):E&&(Ke(),U(E,1,1,(()=>{E=null})),Ze())},i(e){$||(M(n.$$.fragment,e),M(x),M(b),M(u),M(E),e&&ll((()=>{$&&(f||(f=Qt(t,Kt,{},!0)),f.run(1))})),$=!0)},o(e){U(n.$$.fragment,e),U(x),U(b),U(u),U(E),e&&(f||(f=Qt(t,Kt,{},!1)),f.run(0)),$=!1},d(e){e&&m(t),se(n),x&&x.d(),b&&b.d(),L[p].d(),E&&E.d(),e&&f&&f.end(),g=!1,Vl(v)}}}function cn(e){let t,s,i;function n(t){e[20](t)}let a={};return void 0!==e[5]&&(a.showCompiled=e[5]),t=new Ys({props:a}),Vn.push((()=>Zn(t,"showCompiled",n))),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,s){re(t,e,s),i=!0},p(e,i){const n={};!s&&32&i&&(s=!0,n.showCompiled=e[5],Wn((()=>s=!1))),t.$set(n)},i(e){i||(M(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){se(t,e)}}}function dn(e){let t,s,i,n,a;const l=[Qs,Xs],r=[];function o(e,t){return e[5]?0:1}return s=o(e),i=r[s]=l[s](e),{c(){t=F("div"),i.c(),this.h()},l(e){t=N(e,"DIV",{class:!0});var s=K(t);i.l(s),s.forEach(m),this.h()},h(){h(t,"class","code-container svelte-1ursthx")},m(e,i){S(e,t,i),r[s].m(t,null),a=!0},p(e,n){let a=s;s=o(e),s===a?r[s].p(e,n):(Ke(),U(r[a],1,1,(()=>{r[a]=null})),Ze(),i=r[s],i?i.p(e,n):(i=r[s]=l[s](e),i.c()),M(i,1),i.m(t,null))},i(e){a||(M(i),e&&ll((()=>{a&&(n||(n=Qt(t,Kt,{},!0)),n.run(1))})),a=!0)},o(e){U(i),e&&(n||(n=Qt(t,Kt,{},!1)),n.run(0)),a=!1},d(e){e&&m(t),r[s].d(),e&&n&&n.end()}}}function Xs(e){let t,s;return t=new Rn({props:{code:e[3]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};8&s&&(i.code=e[3]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Qs(e){let t,s;return t=new Rn({props:{code:e[1].originalText}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};2&s&&(i.code=e[1].originalText),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Ks(e){let t;return{c(){t=Te("ran successfully but no data was returned")},l(e){t=Ce(e,"ran successfully but no data was returned")},m(e,s){S(e,t,s)},p:de,i:de,o:de,d(e){e&&m(t)}}}function Zs(e){let t;return{c(){t=Te("loading...")},l(e){t=Ce(e,"loading...")},m(e,s){S(e,t,s)},p:de,i:de,o:de,d(e){e&&m(t)}}}function Js(e){let t,s,i,n,a,l,r,o,c,d,p=e[8].toLocaleString()+"",h=e[8]>1?"records":"record",u=e[7].toLocaleString()+"",y=e[7]>1?"properties":"property";return t=new Pn({props:{toggled:e[9],color:e[12].colors.info}}),{c(){oe(t.$$.fragment),s=J(),i=Te(p),n=J(),a=Te(h),l=Te(" with "),r=Te(u),o=J(),c=Te(y)},l(e){ae(t.$$.fragment,e),s=Z(e),i=Ce(e,p),n=Z(e),a=Ce(e,h),l=Ce(e," with "),r=Ce(e,u),o=Z(e),c=Ce(e,y)},m(e,m){re(t,e,m),S(e,s,m),S(e,i,m),S(e,n,m),S(e,a,m),S(e,l,m),S(e,r,m),S(e,o,m),S(e,c,m),d=!0},p(e,s){const n={};512&s&&(n.toggled=e[9]),4096&s&&(n.color=e[12].colors.info),t.$set(n),(!d||256&s)&&p!==(p=e[8].toLocaleString()+"")&&Qe(i,p),(!d||256&s)&&h!==(h=e[8]>1?"records":"record")&&Qe(a,h),(!d||128&s)&&u!==(u=e[7].toLocaleString()+"")&&Qe(r,u),(!d||128&s)&&y!==(y=e[7]>1?"properties":"property")&&Qe(c,y)},i(e){d||(M(t.$$.fragment,e),d=!0)},o(e){U(t.$$.fragment,e),d=!1},d(e){e&&(m(s),m(i),m(n),m(a),m(l),m(r),m(o),m(c)),se(t,e)}}}function xs(e){let t,s=e[6].message+"";return{c(){t=Te(s)},l(e){t=Ce(e,s)},m(e,s){S(e,t,s)},p(e,i){64&i&&s!==(s=e[6].message+"")&&Qe(t,s)},i:de,o:de,d(e){e&&m(t)}}}function mn(e){let t,s;return t=new Us({props:{data:e[1],queryID:e[0]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};2&s&&(i.data=e[1]),1&s&&(i.queryID=e[0]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function ps(e){let t,s,i,n=e[11]&&un(e);return{c(){t=F("div"),n&&n.c(),this.h()},l(e){t=N(e,"DIV",{class:!0});var s=K(t);n&&n.l(s),s.forEach(m),this.h()},h(){h(t,"class","over-container svelte-1ursthx")},m(e,s){S(e,t,s),n&&n.m(t,null),i=!0},p(e,[s]){e[11]?n?(n.p(e,s),2048&s&&M(n,1)):(n=un(e),n.c(),M(n,1),n.m(t,null)):n&&(Ke(),U(n,1,1,(()=>{n=null})),Ze())},i(e){i||(M(n),e&&(s||ll((()=>{s=wi(t,Mn,{}),s.start()}))),i=!0)},o(e){U(n),i=!1},d(e){e&&m(t),n&&n.d()}}}function $s(e,t,s){let i,n,a,l,r,o,c,d,m,p=de,h=()=>(p(),p=Tt(y,(e=>s(2,l=e))),y);Nt(e,Fn,(e=>s(19,c=e))),Nt(e,xn,(e=>s(11,d=e))),e.$$.on_destroy.push((()=>p()));let{queryID:u}=t,{queryResult:y}=t;h();let f=Yi("showSQL_".concat(u),!1);Nt(e,f,(e=>s(10,o=e)));let $=Yi(`showResults_${u}`);Nt(e,$,(e=>s(9,r=e)));let g,v,x,b=!0;const{theme:T}=Pt();return Nt(e,T,(e=>s(12,m=e))),e.$$set=e=>{"queryID"in e&&s(0,u=e.queryID),"queryResult"in e&&h(s(1,y=e.queryResult))},e.$$.update=()=>{if(524288&e.$$.dirty&&s(18,i=c.data.evidencemeta.queries),4&e.$$.dirty&&s(6,x=l?l.error:new Error("queryResult is undefined")),4&e.$$.dirty&&s(8,n=(null==l?void 0:l.length)??0),4&e.$$.dirty&&s(7,a=l.columns.length??(null==l?void 0:l._evidenceColumnTypes.length)??0),262145&e.$$.dirty){let e=null==i?void 0:i.find((e=>e.id===u));e&&(s(3,g=e.inputQueryString),s(4,v=e.compiled&&void 0===e.compileError))}},[u,y,l,g,v,b,x,a,n,r,o,d,m,f,$,function(){Li(f,o=!o,o)},function(){!x&&l.length>0&&Li($,r=!r,r)},T,i,c,function(e){b=e,s(5,b)}]}class er extends lt{constructor(e){super(),it(this,e,$s,ps,tt,{queryID:0,queryResult:1})}}const wl=Symbol.for("__evidence-chart-window-debug__"),tr=(e,t)=>{window[wl]||(window[wl]={}),window[wl][e]=t},lr=e=>{window[wl]||(window[wl]={}),delete window[wl][e]},Tl=500,ir=(e,t)=>{var s;const i=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)&&3*e.clientWidth*e.clientHeight*3>16777215;let n;Hl("light",Mi),Hl("dark",Dn);const a=()=>{n=Ii(e,t.theme,{renderer:i?"svg":t.renderer??"canvas"})};a(),tr(n.id,n),t.connectGroup&&(n.group=t.connectGroup,pn(t.connectGroup));const l=()=>{if(t.seriesColors){const e=n.getOption();if(!e)return;const s={...e};for(const i of Object.keys(t.seriesColors)){const n=e.series.findIndex((e=>e.name===i));-1!==n&&(s.series[n]={...s.series[n],itemStyle:{...s.series[n].itemStyle,color:t.seriesColors[i]}})}n.setOption(s)}},r=()=>{t.echartsOptions&&n.setOption({...t.echartsOptions})},o=()=>{let e=[];if(t.seriesOptions){const s=t.config.series.reduce(((e,{evidenceSeriesType:t},s)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(s),e)),[]);for(let i=0;i<t.config.series.length;i++)s.includes(i)?e.push({}):e.push({...t.seriesOptions});n.setOption({series:e})}};n.setOption({...t.config,animationDuration:Tl,animationDurationUpdate:Tl}),l(),r(),o();const c=t.dispatch;n.on("click",(function(e){c("click",e)}));const d=e.parentElement,m=$n((()=>{n.resize({animation:{duration:Tl}}),h()}),100);let p;window.ResizeObserver&&d?(p=new ResizeObserver(m),p.observe(d)):window.addEventListener("resize",m);const h=()=>{if(t.showAllXAxisLabels){const s=n.getOption();if(!s)return;const i=new Set(s.series.flatMap((e=>{var t;return null==(t=e.data)?void 0:t.map((e=>e[0]))}))),a=.8,l=(null==e?void 0:e.clientWidth)??0;if(!t.swapXY){const e={xAxis:{axisLabel:{interval:0,overflow:t.xAxisLabelOverflow,width:l*a/i.size}}};n.setOption(e)}}};return m(),window[s=Symbol.for("chart renders")]??(window[s]=0),window[Symbol.for("chart renders")]++,{update(e){window[Symbol.for("chart renders")]++,(e=>{e.theme!==t.theme&&(n.dispose(),t=e,a()),t=e,n.setOption({...t.config,animationDuration:Tl,animationDurationUpdate:Tl},!0),l(),r(),o(),n.resize({animation:{duration:Tl}}),h()})(e)},destroy(){p?p.unobserve(d):window.removeEventListener("resize",m),n.dispose(),lr(n.id)}}},nr=(e,t)=>{Hl("light",Mi),Hl("dark",Dn),console.log("echartsCanvasDownloadAction",t.theme);const s=Ii(e,t.theme,{renderer:"canvas"});t.config.animation=!1,s.setOption(t.config),t.echartsOptions&&s.setOption({...t.echartsOptions}),(()=>{if(t.seriesColors){const e=s.getOption();if(!e)return;const i={...e};for(const s of Object.keys(t.seriesColors)){const n=e.series.findIndex((e=>e.name===s));-1!==n&&(i.series[n]={...i.series[n],itemStyle:{...i.series[n].itemStyle,color:t.seriesColors[s]}})}s.setOption(i)}})(),(()=>{let e=[];if(t.seriesOptions){const i=t.config.series.reduce(((e,{evidenceSeriesType:t},s)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(s),e)),[]);for(let s=0;s<t.config.series.length;s++)i.includes(s)?e.push({}):e.push({...t.seriesOptions});s.setOption({series:e})}})();let i=s.getConnectedDataURL({type:"png",pixelRatio:3,backgroundColor:t.backgroundColor,excludeComponents:["toolbox"]});const n=new Date,a=new Date(n.getTime()-6e4*n.getTimezoneOffset()).toISOString().slice(0,19).replaceAll(":","-");return es(i,(t.evidenceChartTitle??t.queryID??"evidence-chart")+`_${a}.png`),s.dispose(),{destroy(){s.dispose()}}},vl=(e,t)=>{Hl("evidence-light",Mi);const{config:s,ratio:i,echartsOptions:n,seriesOptions:a,seriesColors:l,isMap:r,extraHeight:o,width:c}=t;let d={renderer:"canvas"};r&&(d.height=.5*c+o,e&&e.parentNode&&(e.style.height=d.height+"px",e.parentNode.style.height=d.height+"px"));const m=Ii(e,"evidence-light",d);s.animation=!1,m.setOption(s),n&&m.setOption(n),n&&m.setOption({...n}),(()=>{if(l){const e=m.getOption();if(!e)return;const t={...e};for(const s of Object.keys(l)){const i=e.series.findIndex((e=>e.name===s));-1!==i&&(t.series[i]={...t.series[i],itemStyle:{...t.series[i].itemStyle,color:l[s]}})}m.setOption(t)}})(),(()=>{let e=[];if(a){const t=s.series.reduce(((e,{evidenceSeriesType:t},s)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(s),e)),[]);for(let i=0;i<s.series.length;i++)t.includes(i)?e.push({}):e.push({...a});m.setOption({series:e})}})();let p=m.getConnectedDataURL({type:"jpeg",pixelRatio:i,backgroundColor:"#fff",excludeComponents:["toolbox"]});e.innerHTML=`<img src=${p} width="100%" style="\n        position: absolute; \n        top: 0;\n        user-select: all;\n        -webkit-user-select: all;\n        -moz-user-select: all;\n        -ms-user-select: all;\n    " />`,t.config.animation=!0};function sr(e){let t;function s(e,t){return e[9]?or:ar}let i=s(e),n=i(e);return{c(){n.c(),t=pe()},l(e){n.l(e),t=pe()},m(e,s){n.m(e,s),S(e,t,s)},p(e,a){i===(i=s(e))&&n?n.p(e,a):(n.d(1),n=i(e),n&&(n.c(),n.m(t.parentNode,t)))},d(e){e&&m(t),n.d(e)}}}function rr(e){let t,s,i,n;return{c(){t=F("div"),this.h()},l(e){t=N(e,"DIV",{class:!0,style:!0}),K(t).forEach(m),this.h()},h(){h(t,"class","chart"),v(t,"height",e[1]),v(t,"width",e[2]),v(t,"margin-left","0"),v(t,"margin-top","15px"),v(t,"margin-bottom","10px"),v(t,"overflow","visible"),v(t,"break-inside","avoid")},m(a,l){S(a,t,l),i||(n=il(s=vl.call(null,t,{config:e[0],ratio:2,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13]})),i=!0)},p(e,i){2&i&&v(t,"height",e[1]),4&i&&v(t,"width",e[2]),s&&nl(s.update)&&8289&i&&s.update.call(null,{config:e[0],ratio:2,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13]})},d(e){e&&m(t),i=!1,n()}}}function ar(e){let t,s,i,n,a,l,r;return{c(){t=F("div"),i=J(),n=F("div"),this.h()},l(e){t=N(e,"DIV",{class:!0,style:!0}),K(t).forEach(m),i=Z(e),n=N(e,"DIV",{class:!0,style:!0}),K(n).forEach(m),this.h()},h(){h(t,"class","chart md:hidden"),v(t,"height",e[1]),v(t,"width","650px"),v(t,"margin-left","0"),v(t,"margin-top","15px"),v(t,"margin-bottom","10px"),v(t,"overflow","visible"),v(t,"break-inside","avoid"),h(n,"class","chart hidden md:block"),v(n,"height",e[1]),v(n,"width","841px"),v(n,"margin-left","0"),v(n,"margin-top","15px"),v(n,"margin-bottom","10px"),v(n,"overflow","visible"),v(n,"break-inside","avoid")},m(o,c){S(o,t,c),S(o,i,c),S(o,n,c),l||(r=[il(s=vl.call(null,t,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:650})),il(a=vl.call(null,n,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:841}))],l=!0)},p(e,i){2&i&&v(t,"height",e[1]),s&&nl(s.update)&&8673&i&&s.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:650}),2&i&&v(n,"height",e[1]),a&&nl(a.update)&&8673&i&&a.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:841})},d(e){e&&(m(t),m(i),m(n)),l=!1,Vl(r)}}}function or(e){let t,s,i,n,a,l,r;return{c(){t=F("div"),i=J(),n=F("div"),this.h()},l(e){t=N(e,"DIV",{class:!0,style:!0}),K(t).forEach(m),i=Z(e),n=N(e,"DIV",{class:!0,style:!0}),K(n).forEach(m),this.h()},h(){h(t,"class","chart md:hidden"),v(t,"height",e[1]),v(t,"width",e[11]+"px"),v(t,"margin-left","0"),v(t,"margin-top","15px"),v(t,"margin-bottom","10px"),v(t,"overflow","visible"),v(t,"break-inside","avoid"),h(n,"class","chart hidden md:block"),v(n,"height",e[1]),v(n,"width",e[10]+"px"),v(n,"margin-left","0"),v(n,"margin-top","15px"),v(n,"margin-bottom","10px"),v(n,"overflow","visible"),v(n,"break-inside","avoid")},m(o,c){S(o,t,c),S(o,i,c),S(o,n,c),l||(r=[il(s=vl.call(null,t,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[11]})),il(a=vl.call(null,n,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[10]}))],l=!0)},p(e,i){2&i&&v(t,"height",e[1]),2048&i&&v(t,"width",e[11]+"px"),s&&nl(s.update)&&10721&i&&s.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[11]}),2&i&&v(n,"height",e[1]),1024&i&&v(n,"width",e[10]+"px"),a&&nl(a.update)&&9697&i&&a.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[10]})},d(e){e&&(m(t),m(i),m(n)),l=!1,Vl(r)}}}function fr(e){let t;function s(e,t){return e[3]?rr:e[4]?sr:void 0}let i=s(e),n=i&&i(e);return{c(){n&&n.c(),t=pe()},l(e){n&&n.l(e),t=pe()},m(e,s){n&&n.m(e,s),S(e,t,s)},p(e,[a]){i===(i=s(e))&&n?n.p(e,a):(n&&n.d(1),n=i&&i(e),n&&(n.c(),n.m(t.parentNode,t)))},i:de,o:de,d(e){e&&m(t),n&&n.d(e)}}}function ur(e,t,s){let i,n,a,l,r,o,c=de;e.$$.on_destroy.push((()=>c()));const{resolveColorsObject:d}=Pt();let m,p,{config:h}=t,{height:u="291px"}=t,{width:y="100%"}=t,{copying:f=!1}=t,{printing:$=!1}=t,{echartsOptions:g}=t,{seriesOptions:v}=t,{seriesColors:x}=t,{isMap:b=!1}=t,{extraHeight:T}=t,S=!1;const L=Ul("gridConfig");return L&&(S=!0,({cols:m,gapWidth:p}=L)),e.$$set=e=>{"config"in e&&s(0,h=e.config),"height"in e&&s(1,u=e.height),"width"in e&&s(2,y=e.width),"copying"in e&&s(3,f=e.copying),"printing"in e&&s(4,$=e.printing),"echartsOptions"in e&&s(5,g=e.echartsOptions),"seriesOptions"in e&&s(6,v=e.seriesOptions),"seriesColors"in e&&s(14,x=e.seriesColors),"isMap"in e&&s(7,b=e.isMap),"extraHeight"in e&&s(8,T=e.extraHeight)},e.$$.update=()=>{16384&e.$$.dirty&&(s(12,i=d(x)),c(),c=Tt(i,(e=>s(13,o=e)))),32768&e.$$.dirty&&s(18,n=Math.min(Number(m),2)),327680&e.$$.dirty&&s(11,a=(650-Number(p)*(n-1))/n),32768&e.$$.dirty&&s(17,l=Math.min(Number(m),3)),196608&e.$$.dirty&&s(10,r=(841-Number(p)*(l-1))/l)},[h,u,y,f,$,g,v,b,T,S,r,a,i,o,x,m,p,l,n]}class cr extends lt{constructor(e){super(),it(this,e,ur,fr,tt,{config:0,height:1,width:2,copying:3,printing:4,echartsOptions:5,seriesOptions:6,seriesColors:14,isMap:7,extraHeight:8})}}function dr(e){let t,s,i,n,a,l="Loading...";return{c(){t=F("div"),s=F("span"),s.textContent=l,i=J(),n=F("div"),this.h()},l(e){t=N(e,"DIV",{role:!0,class:!0});var a=K(t);s=N(a,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-1wtojot"!==Se(s)&&(s.textContent=l),i=Z(a),n=N(a,"DIV",{class:!0,style:!0}),K(n).forEach(m),a.forEach(m),this.h()},h(){h(s,"class","sr-only"),h(n,"class","bg-base-100 rounded-md max-w-[100%]"),v(n,"height",e[0]),v(n,"margin-top","15px"),v(n,"margin-bottom","31px"),h(t,"role","status"),h(t,"class","animate-pulse")},m(e,a){S(e,t,a),D(t,s),D(t,i),D(t,n)},p(e,[t]){1&t&&v(n,"height",e[0])},i(e){e&&(a||ll((()=>{a=wi(t,ts,{}),a.start()})))},o:de,d(e){e&&m(t)}}}function mr(e,t,s){let{height:i="231px"}=t;return e.$$set=e=>{"height"in e&&s(0,i=e.height)},[i]}class hr extends lt{constructor(e){super(),it(this,e,mr,dr,tt,{height:0})}}function hn(e){let t,s,i,n;const a=[br,yr],l=[];return t=1,s=l[1]=a[1](e),{c(){s.c(),i=pe()},l(e){s.l(e),i=pe()},m(e,t){l[1].m(e,t),S(e,i,t),n=!0},p(e,t){s.p(e,t)},i(e){n||(M(s),n=!0)},o(e){U(s),n=!1},d(e){e&&m(i),l[1].d(e)}}}function yr(e){let t,s,i,n;return{c(){t=F("div"),this.h()},l(e){t=N(e,"DIV",{class:!0,style:!0}),K(t).forEach(m),this.h()},h(){h(t,"class","chart svelte-db4qxn"),v(t,"height",e[3]),v(t,"width",e[4]),v(t,"overflow","visible"),v(t,"display",e[15]?"none":"inherit")},m(a,l){S(a,t,l),i||(n=il(s=ir.call(null,t,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],dispatch:e[24],renderer:e[6],connectGroup:e[12],xAxisLabelOverflow:e[13],seriesColors:e[19],theme:e[20]})),i=!0)},p(e,i){8&i[0]&&v(t,"height",e[3]),16&i[0]&&v(t,"width",e[4]),32768&i[0]&&v(t,"display",e[15]?"none":"inherit"),s&&nl(s.update)&&35141185&i[0]&&s.update.call(null,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],dispatch:e[24],renderer:e[6],connectGroup:e[12],xAxisLabelOverflow:e[13],seriesColors:e[19],theme:e[20]})},i:de,o:de,d(e){e&&m(t),i=!1,n()}}}function br(e){let t,s;return t=new hr({props:{height:e[3]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};8&s[0]&&(i.height=e[3]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function yn(e){let t,s,i,n=e[8]&&bn(e),a=e[5]&&e[7]&&gn(e);return{c(){t=F("div"),n&&n.c(),s=J(),a&&a.c(),this.h()},l(e){t=N(e,"DIV",{class:!0});var i=K(t);n&&n.l(i),s=Z(i),a&&a.l(i),i.forEach(m),this.h()},h(){h(t,"class","chart-footer svelte-db4qxn")},m(e,l){S(e,t,l),n&&n.m(t,null),D(t,s),a&&a.m(t,null),i=!0},p(e,i){e[8]?n?(n.p(e,i),256&i[0]&&M(n,1)):(n=bn(e),n.c(),M(n,1),n.m(t,s)):n&&(Ke(),U(n,1,1,(()=>{n=null})),Ze()),e[5]&&e[7]?a?(a.p(e,i),160&i[0]&&M(a,1)):(a=gn(e),a.c(),M(a,1),a.m(t,null)):a&&(Ke(),U(a,1,1,(()=>{a=null})),Ze())},i(e){i||(M(n),M(a),i=!0)},o(e){U(n),U(a),i=!1},d(e){e&&m(t),n&&n.d(),a&&a.d()}}}function bn(e){let t,s;return t=new Oi({props:{text:"Save Image",class:"download-button",downloadData:e[32],display:e[17],queryID:e[1],$$slots:{default:[gr]},$$scope:{ctx:e}}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};16384&s[0]&&(i.downloadData=e[32]),131072&s[0]&&(i.display=e[17]),2&s[0]&&(i.queryID=e[1]),32&s[1]&&(i.$$scope={dirty:s,ctx:e}),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function gr(e){let t,s,i,n;return{c(){t=kl("svg"),s=kl("rect"),i=kl("circle"),n=kl("path"),this.h()},l(e){t=Ll(e,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var a=K(t);s=Ll(a,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0}),K(s).forEach(m),i=Ll(a,"circle",{cx:!0,cy:!0,r:!0}),K(i).forEach(m),n=Ll(a,"path",{d:!0}),K(n).forEach(m),a.forEach(m),this.h()},h(){h(s,"x","3"),h(s,"y","3"),h(s,"width","18"),h(s,"height","18"),h(s,"rx","2"),h(i,"cx","8.5"),h(i,"cy","8.5"),h(i,"r","1.5"),h(n,"d","M20.4 14.5L16 10 4 20"),h(t,"xmlns","http://www.w3.org/2000/svg"),h(t,"width","12"),h(t,"height","12"),h(t,"viewBox","0 0 24 24"),h(t,"fill","none"),h(t,"stroke","#000"),h(t,"stroke-width","2"),h(t,"stroke-linecap","round"),h(t,"stroke-linejoin","round")},m(e,a){S(e,t,a),D(t,s),D(t,i),D(t,n)},p:de,d(e){e&&m(t)}}}function gn(e){let t,s;return t=new Oi({props:{text:"Download Data",data:e[5],queryID:e[1],class:"download-button",display:e[17]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};32&s[0]&&(i.data=e[5]),2&s[0]&&(i.queryID=e[1]),131072&s[0]&&(i.display=e[17]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function _n(e){let t,s;return t=new ls({props:{source:JSON.stringify(e[0],void 0,3),copyToClipboard:!0,$$slots:{default:[_r]},$$scope:{ctx:e}}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};1&s[0]&&(i.source=JSON.stringify(e[0],void 0,3)),1&s[0]|32&s[1]&&(i.$$scope={dirty:s,ctx:e}),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function _r(e){let t,s=JSON.stringify(e[0],void 0,3)+"";return{c(){t=Te(s)},l(e){t=Ce(e,s)},m(e,s){S(e,t,s)},p(e,i){1&i[0]&&s!==(s=JSON.stringify(e[0],void 0,3)+"")&&Qe(t,s)},d(e){e&&m(t)}}}function Cn(e){let t,s,i,n;return{c(){t=F("div"),this.h()},l(e){t=N(e,"DIV",{class:!0,style:!0}),K(t).forEach(m),this.h()},h(){h(t,"class","chart svelte-db4qxn"),v(t,"display","none"),v(t,"visibility","visible"),v(t,"height",e[3]),v(t,"width","666px"),v(t,"margin-left","0"),v(t,"margin-top","15px"),v(t,"margin-bottom","15px"),v(t,"overflow","visible")},m(a,l){S(a,t,l),i||(n=il(s=nr.call(null,t,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[19],queryID:e[1],evidenceChartTitle:e[2],theme:e[20],backgroundColor:e[21].colors["base-100"]})),i=!0)},p(e,i){8&i[0]&&v(t,"height",e[3]),s&&nl(s.update)&&37225991&i[0]&&s.update.call(null,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[19],queryID:e[1],evidenceChartTitle:e[2],theme:e[20],backgroundColor:e[21].colors["base-100"]})},d(e){e&&m(t),i=!1,n()}}}function Cr(e){let t,s,i,n,a,l,r,o,c,d,p=!e[16]&&hn(e);i=new cr({props:{config:e[0],height:e[3],width:e[4],copying:e[15],printing:e[16],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[18]}});let u=(e[7]||e[8])&&yn(e),y=e[11]&&!e[16]&&_n(e),f=e[14]&&Cn(e);return{c(){t=F("div"),p&&p.c(),s=J(),oe(i.$$.fragment),n=J(),u&&u.c(),a=J(),y&&y.c(),l=J(),f&&f.c(),r=pe(),this.h()},l(e){t=N(e,"DIV",{role:!0,class:!0});var o=K(t);p&&p.l(o),s=Z(o),ae(i.$$.fragment,o),n=Z(o),u&&u.l(o),a=Z(o),y&&y.l(o),o.forEach(m),l=Z(e),f&&f.l(e),r=pe(),this.h()},h(){h(t,"role","none"),h(t,"class","chart-container mt-2 mb-3 svelte-db4qxn")},m(m,h){S(m,t,h),p&&p.m(t,null),D(t,s),re(i,t,null),D(t,n),u&&u.m(t,null),D(t,a),y&&y.m(t,null),S(m,l,h),f&&f.m(m,h),S(m,r,h),o=!0,c||(d=[yt(window,"copy",e[27]),yt(window,"beforeprint",e[28]),yt(window,"afterprint",e[29]),yt(window,"export-beforeprint",e[30]),yt(window,"export-afterprint",e[31]),yt(t,"mouseenter",e[33]),yt(t,"mouseleave",e[34])],c=!0)},p(e,n){e[16]?p&&(Ke(),U(p,1,1,(()=>{p=null})),Ze()):p?(p.p(e,n),65536&n[0]&&M(p,1)):(p=hn(e),p.c(),M(p,1),p.m(t,s));const l={};1&n[0]&&(l.config=e[0]),8&n[0]&&(l.height=e[3]),16&n[0]&&(l.width=e[4]),32768&n[0]&&(l.copying=e[15]),65536&n[0]&&(l.printing=e[16]),512&n[0]&&(l.echartsOptions=e[9]),1024&n[0]&&(l.seriesOptions=e[10]),262144&n[0]&&(l.seriesColors=e[18]),i.$set(l),e[7]||e[8]?u?(u.p(e,n),384&n[0]&&M(u,1)):(u=yn(e),u.c(),M(u,1),u.m(t,a)):u&&(Ke(),U(u,1,1,(()=>{u=null})),Ze()),e[11]&&!e[16]?y?(y.p(e,n),67584&n[0]&&M(y,1)):(y=_n(e),y.c(),M(y,1),y.m(t,null)):y&&(Ke(),U(y,1,1,(()=>{y=null})),Ze()),e[14]?f?f.p(e,n):(f=Cn(e),f.c(),f.m(r.parentNode,r)):f&&(f.d(1),f=null)},i(e){o||(M(p),M(i.$$.fragment,e),M(u),M(y),o=!0)},o(e){U(p),U(i.$$.fragment,e),U(u),U(y),o=!1},d(e){e&&(m(t),m(l),m(r)),p&&p.d(),se(i),u&&u.d(),y&&y.d(),f&&f.d(e),c=!1,Vl(d)}}}function Tr(e,t,s){let i;const n=["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"];let a,l,r,o=Wi(t,n),c=de;e.$$.on_destroy.push((()=>c()));const{activeAppearance:d,theme:m,resolveColorsObject:p}=Pt();Nt(e,d,(e=>s(20,l=e))),Nt(e,m,(e=>s(21,r=e)));let{config:h}=t,{queryID:u}=t,{evidenceChartTitle:y}=t,{height:f="291px"}=t,{width:$="100%"}=t,{data:g}=t,{renderer:v}=t,{downloadableData:x}=t,{downloadableImage:b}=t,{echartsOptions:T}=t,{seriesOptions:S}=t,{printEchartsConfig:L}=t,{seriesColors:w}=t,{connectGroup:E}=t,{xAxisLabelOverflow:A}=t;const C=jn();let M=!1,O=!1,N=!1,D=!1;return e.$$set=e=>{t=Lt(Lt({},t),Ht(e)),s(25,o=Wi(t,n)),"config"in e&&s(0,h=e.config),"queryID"in e&&s(1,u=e.queryID),"evidenceChartTitle"in e&&s(2,y=e.evidenceChartTitle),"height"in e&&s(3,f=e.height),"width"in e&&s(4,$=e.width),"data"in e&&s(5,g=e.data),"renderer"in e&&s(6,v=e.renderer),"downloadableData"in e&&s(7,x=e.downloadableData),"downloadableImage"in e&&s(8,b=e.downloadableImage),"echartsOptions"in e&&s(9,T=e.echartsOptions),"seriesOptions"in e&&s(10,S=e.seriesOptions),"printEchartsConfig"in e&&s(11,L=e.printEchartsConfig),"seriesColors"in e&&s(26,w=e.seriesColors),"connectGroup"in e&&s(12,E=e.connectGroup),"xAxisLabelOverflow"in e&&s(13,A=e.xAxisLabelOverflow)},e.$$.update=()=>{67108864&e.$$.dirty[0]&&(s(18,i=p(w)),c(),c=Tt(i,(e=>s(19,a=e))))},[h,u,y,f,$,g,v,x,b,T,S,L,E,A,M,O,N,D,i,a,l,r,d,m,C,o,w,()=>{s(15,O=!0),Yn(),setTimeout((()=>{s(15,O=!1)}),0)},()=>s(16,N=!0),()=>s(16,N=!1),()=>s(16,N=!0),()=>s(16,N=!1),()=>{s(14,M=!0),setTimeout((()=>{s(14,M=!1)}),0)},()=>s(17,D=!0),()=>s(17,D=!1)]}class Lr extends lt{constructor(e){super(),it(this,e,Tr,Cr,tt,{config:0,queryID:1,evidenceChartTitle:2,height:3,width:4,data:5,renderer:6,downloadableData:7,downloadableImage:8,echartsOptions:9,seriesOptions:10,printEchartsConfig:11,seriesColors:26,connectGroup:12,xAxisLabelOverflow:13},null,[-1,-1])}}function ql(e,t){const s=new Set(e.map((e=>e[t])));return Array.from(s)}function kr(e,t){return el(e,is({count:ns(t)}))[0].count}function Sr(e,t,s){let i;if("object"!=typeof s)i=el(e,Si(t,Zi({xTotal:Ei(s)})),Ti({percentOfX:pi(s,"xTotal")}),Ki({percentOfX:s+"_pct"}));else{i=el(e,Ti({valueSum:0}));for(let e=0;e<i.length;e++){i[e].valueSum=0;for(let t=0;t<s.length;t++)i[e].valueSum=i[e].valueSum+i[e][s[t]]}i=el(i,Si(t,Zi({xTotal:Ei("valueSum")})));for(let e=0;e<s.length;e++)i=el(i,Ti({percentOfX:pi(s[e],"xTotal")}),Ki({percentOfX:s[e]+"_pct"}))}return i}function zl(e,t,s){return[...e].sort(((e,i)=>(e[t]<i[t]?-1:1)*(s?1:-1)))}function Ri(e,t,s){return e%(t+s)<t?0:1}function Er(e){let t,s;return t=new Pi({props:{error:e[14],title:e[8]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};16384&s[0]&&(i.error=e[14]),256&s[0]&&(i.title=e[8]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Ar(e){let t,s,i;const n=e[136].default,a=sl(n,e,e[135],null);return s=new Lr({props:{config:e[20],height:e[15],width:e[13],data:e[0],queryID:e[6],evidenceChartTitle:e[7],showAllXAxisLabels:e[1],swapXY:e[3],echartsOptions:e[9],seriesOptions:e[10],printEchartsConfig:e[2],renderer:e[11],downloadableData:e[4],downloadableImage:e[5],connectGroup:e[12],xAxisLabelOverflow:e[23],seriesColors:e[16]}}),{c(){a&&a.c(),t=J(),oe(s.$$.fragment)},l(e){a&&a.l(e),t=Z(e),ae(s.$$.fragment,e)},m(e,n){a&&a.m(e,n),S(e,t,n),re(s,e,n),i=!0},p(e,t){a&&a.p&&(!i||2048&t[4])&&rl(a,n,e,e[135],i?ol(n,e[135],t,null):al(e[135]),null);const l={};1048576&t[0]&&(l.config=e[20]),32768&t[0]&&(l.height=e[15]),8192&t[0]&&(l.width=e[13]),1&t[0]&&(l.data=e[0]),64&t[0]&&(l.queryID=e[6]),128&t[0]&&(l.evidenceChartTitle=e[7]),2&t[0]&&(l.showAllXAxisLabels=e[1]),8&t[0]&&(l.swapXY=e[3]),512&t[0]&&(l.echartsOptions=e[9]),1024&t[0]&&(l.seriesOptions=e[10]),4&t[0]&&(l.printEchartsConfig=e[2]),2048&t[0]&&(l.renderer=e[11]),16&t[0]&&(l.downloadableData=e[4]),32&t[0]&&(l.downloadableImage=e[5]),4096&t[0]&&(l.connectGroup=e[12]),65536&t[0]&&(l.seriesColors=e[16]),s.$set(l)},i(e){i||(M(a,e),M(s.$$.fragment,e),i=!0)},o(e){U(a,e),U(s.$$.fragment,e),i=!1},d(e){e&&m(t),a&&a.d(e),se(s,e)}}}function wr(e){let t,s,i,n;const a=[Ar,Er],l=[];function r(e,t){return e[14]?1:0}return t=r(e),s=l[t]=a[t](e),{c(){s.c(),i=pe()},l(e){s.l(e),i=pe()},m(e,s){l[t].m(e,s),S(e,i,s),n=!0},p(e,n){let o=t;t=r(e),t===o?l[t].p(e,n):(Ke(),U(l[o],1,1,(()=>{l[o]=null})),Ze(),s=l[t],s?s.p(e,n):(s=l[t]=a[t](e),s.c()),M(s,1),s.m(i.parentNode,i))},i(e){n||(M(s),n=!0)},o(e){U(s),n=!1},d(e){e&&m(i),l[t].d(e)}}}function Or(e,t,s){let i,n,a,l,r,o,c,d,m,p=de,h=de,u=de;e.$$.on_destroy.push((()=>p())),e.$$.on_destroy.push((()=>h())),e.$$.on_destroy.push((()=>u()));let{$$slots:y={},$$scope:f}=t,$=Ai({}),g=Ai({});Nt(e,g,(e=>s(20,m=e)));const{theme:v,resolveColor:x,resolveColorsObject:b,resolveColorPalette:T}=Pt();Nt(e,v,(e=>s(132,o=e)));let S,{data:L}=t,{queryID:w}=t,{x:E}=t,{y:A}=t,{y2:C}=t,{series:M}=t,{size:O}=t,{tooltipTitle:N}=t,{showAllXAxisLabels:D}=t,{printEchartsConfig:I=!1}=t,k=!!A,F=!!E,{swapXY:U=!1}=t,{title:P}=t,{subtitle:R}=t,{chartType:z="Chart"}=t,{bubble:B=!1}=t,{hist:G=!1}=t,{boxplot:K=!1}=t,{xType:H}=t,{xAxisTitle:Z="false"}=t,{xBaseline:q=!0}=t,{xTickMarks:_=!1}=t,{xGridlines:W=!1}=t,{xAxisLabels:J=!0}=t,{sort:j=!0}=t,{xFmt:V}=t,{xMin:Y}=t,{xMax:X}=t,{yLog:Q=!1}=t,{yType:ee=(!0===Q?"log":"value")}=t,{yLogBase:te=10}=t,{yAxisTitle:se="false"}=t,{yBaseline:ie=!1}=t,{yTickMarks:ne=!1}=t,{yGridlines:ae=!0}=t,{yAxisLabels:le=!0}=t,{yMin:re}=t,{yMax:oe}=t,{yScale:ce=!1}=t,{yFmt:me}=t,{yAxisColor:pe="true"}=t,{y2AxisTitle:he="false"}=t,{y2Baseline:ue=!1}=t,{y2TickMarks:ye=!1}=t,{y2Gridlines:fe=!0}=t,{y2AxisLabels:$e=!0}=t,{y2Min:ge}=t,{y2Max:ve}=t,{y2Scale:xe=!1}=t,{y2Fmt:be}=t,{y2AxisColor:Te="true"}=t,{sizeFmt:Se}=t,{colorPalette:Le="default"}=t,{legend:we}=t,{echartsOptions:Ee}=t,{seriesOptions:Ae}=t,{seriesColors:Ce}=t,{stackType:Me}=t,{stacked100:Oe=!1}=t,{chartAreaHeight:Ne}=t,{renderer:De}=t,{downloadableData:Ie=!0}=t,{downloadableImage:ke=!0}=t,{connectGroup:Fe}=t,{leftPadding:Ue}=t,{rightPadding:Pe}=t,{xLabelWrap:Re=!1}=t;const ze=Re?"break":"truncate";let Be,Ge,Ke,He,Ze,_e,We,Je,je,Ve,Ye,Xe,Qe,et,tt,st,it,nt,at,lt,rt,ot,ct,dt,mt,pt,ht,yt,ft,$t,gt,vt,xt,bt,St,Lt,wt,Et,At,Ct,Mt,Dt,It,kt,Ft,Ut,Rt,zt,Bt=[],Gt=[],Kt=[],Ht=!0,Zt=[],qt=[];return e.$$set=e=>{"data"in e&&s(0,L=e.data),"queryID"in e&&s(6,w=e.queryID),"x"in e&&s(24,E=e.x),"y"in e&&s(25,A=e.y),"y2"in e&&s(49,C=e.y2),"series"in e&&s(50,M=e.series),"size"in e&&s(51,O=e.size),"tooltipTitle"in e&&s(52,N=e.tooltipTitle),"showAllXAxisLabels"in e&&s(1,D=e.showAllXAxisLabels),"printEchartsConfig"in e&&s(2,I=e.printEchartsConfig),"swapXY"in e&&s(3,U=e.swapXY),"title"in e&&s(7,P=e.title),"subtitle"in e&&s(53,R=e.subtitle),"chartType"in e&&s(8,z=e.chartType),"bubble"in e&&s(54,B=e.bubble),"hist"in e&&s(55,G=e.hist),"boxplot"in e&&s(56,K=e.boxplot),"xType"in e&&s(26,H=e.xType),"xAxisTitle"in e&&s(27,Z=e.xAxisTitle),"xBaseline"in e&&s(28,q=e.xBaseline),"xTickMarks"in e&&s(29,_=e.xTickMarks),"xGridlines"in e&&s(30,W=e.xGridlines),"xAxisLabels"in e&&s(31,J=e.xAxisLabels),"sort"in e&&s(32,j=e.sort),"xFmt"in e&&s(57,V=e.xFmt),"xMin"in e&&s(58,Y=e.xMin),"xMax"in e&&s(59,X=e.xMax),"yLog"in e&&s(33,Q=e.yLog),"yType"in e&&s(60,ee=e.yType),"yLogBase"in e&&s(61,te=e.yLogBase),"yAxisTitle"in e&&s(34,se=e.yAxisTitle),"yBaseline"in e&&s(35,ie=e.yBaseline),"yTickMarks"in e&&s(36,ne=e.yTickMarks),"yGridlines"in e&&s(37,ae=e.yGridlines),"yAxisLabels"in e&&s(38,le=e.yAxisLabels),"yMin"in e&&s(62,re=e.yMin),"yMax"in e&&s(63,oe=e.yMax),"yScale"in e&&s(39,ce=e.yScale),"yFmt"in e&&s(64,me=e.yFmt),"yAxisColor"in e&&s(65,pe=e.yAxisColor),"y2AxisTitle"in e&&s(40,he=e.y2AxisTitle),"y2Baseline"in e&&s(41,ue=e.y2Baseline),"y2TickMarks"in e&&s(42,ye=e.y2TickMarks),"y2Gridlines"in e&&s(43,fe=e.y2Gridlines),"y2AxisLabels"in e&&s(44,$e=e.y2AxisLabels),"y2Min"in e&&s(66,ge=e.y2Min),"y2Max"in e&&s(67,ve=e.y2Max),"y2Scale"in e&&s(45,xe=e.y2Scale),"y2Fmt"in e&&s(68,be=e.y2Fmt),"y2AxisColor"in e&&s(69,Te=e.y2AxisColor),"sizeFmt"in e&&s(70,Se=e.sizeFmt),"colorPalette"in e&&s(71,Le=e.colorPalette),"legend"in e&&s(46,we=e.legend),"echartsOptions"in e&&s(9,Ee=e.echartsOptions),"seriesOptions"in e&&s(10,Ae=e.seriesOptions),"seriesColors"in e&&s(72,Ce=e.seriesColors),"stackType"in e&&s(73,Me=e.stackType),"stacked100"in e&&s(74,Oe=e.stacked100),"chartAreaHeight"in e&&s(47,Ne=e.chartAreaHeight),"renderer"in e&&s(11,De=e.renderer),"downloadableData"in e&&s(4,Ie=e.downloadableData),"downloadableImage"in e&&s(5,ke=e.downloadableImage),"connectGroup"in e&&s(12,Fe=e.connectGroup),"leftPadding"in e&&s(75,Ue=e.leftPadding),"rightPadding"in e&&s(76,Pe=e.rightPadding),"xLabelWrap"in e&&s(48,Re=e.xLabelWrap),"$$scope"in e&&s(135,f=e.$$scope)},e.$$.update=()=>{var t,m,y,f,v,w;if(4&e.$$.dirty[0]&&s(2,I=qe(I)),8&e.$$.dirty[0]&&s(3,U=qe(U)),268435456&e.$$.dirty[0]&&s(28,q=qe(q)),536870912&e.$$.dirty[0]&&s(29,_=qe(_)),1073741824&e.$$.dirty[0]&&s(30,W=qe(W)),1&e.$$.dirty[1]&&s(31,J=qe(J)),2&e.$$.dirty[1]&&s(32,j=qe(j)),4&e.$$.dirty[1]&&s(33,Q=qe(Q)),16&e.$$.dirty[1]&&s(35,ie=qe(ie)),32&e.$$.dirty[1]&&s(36,ne=qe(ne)),64&e.$$.dirty[1]&&s(37,ae=qe(ae)),128&e.$$.dirty[1]&&s(38,le=qe(le)),256&e.$$.dirty[1]&&s(39,ce=qe(ce)),8&e.$$.dirty[2]&&(s(19,i=x(pe)),u(),u=Tt(i,(e=>s(134,d=e)))),1024&e.$$.dirty[1]&&s(41,ue=qe(ue)),2048&e.$$.dirty[1]&&s(42,ye=qe(ye)),4096&e.$$.dirty[1]&&s(43,fe=qe(fe)),8192&e.$$.dirty[1]&&s(44,$e=qe($e)),16384&e.$$.dirty[1]&&s(45,xe=qe(xe)),128&e.$$.dirty[2]&&(s(18,n=x(Te)),h(),h=Tt(n,(e=>s(133,c=e)))),512&e.$$.dirty[2]&&(s(17,a=T(Le)),p(),p=Tt(a,(e=>s(131,r=e)))),1024&e.$$.dirty[2]&&s(16,l=b(Ce)),16&e.$$.dirty[0]&&s(4,Ie=qe(Ie)),32&e.$$.dirty[0]&&s(5,ke=qe(ke)),131072&e.$$.dirty[1]&&s(48,Re=qe(Re)),2130731403&e.$$.dirty[0]|2147352575&e.$$.dirty[1]|2147481975&e.$$.dirty[2]|2147483647&e.$$.dirty[3]|2047&e.$$.dirty[4])try{if(s(14,Ut=void 0),s(124,Kt=[]),s(83,Gt=[]),s(126,Zt=[]),s(127,qt=[]),s(85,He=[]),s(77,k=!!A),s(78,F=!!E),Al(L),s(80,Be=El(L)),s(81,Ge=Object.keys(Be)),F||s(24,E=Ge[0]),!k){s(82,Bt=Ge.filter((function(e){return![E,M,O].includes(e)})));for(let e=0;e<Bt.length;e++)s(85,He=Bt[e]),s(84,Ke=Be[He].type),"number"===Ke&&Gt.push(He);s(25,A=Gt.length>1?Gt:Gt[0])}s(79,S=B?{x:E,y:A,size:O}:G?{x:E}:K?{}:{x:E,y:A});for(let e in S)null==S[e]&&Kt.push(e);if(1===Kt.length)throw Error((new Intl.ListFormat).format(Kt)+" is required");if(Kt.length>1)throw Error((new Intl.ListFormat).format(Kt)+" are required");if(!0===Oe&&A.includes("_pct")&&!1===Ht)if("object"==typeof A){for(let e=0;e<A.length;e++)s(25,A[e]=A[e].replace("_pct",""),A);s(125,Ht=!1)}else s(25,A=A.replace("_pct","")),s(125,Ht=!1);if(E&&Zt.push(E),A)if("object"==typeof A)for(s(128,Ft=0);Ft<A.length;s(128,Ft++,Ft))Zt.push(A[Ft]);else Zt.push(A);if(C)if("object"==typeof C)for(s(128,Ft=0);Ft<C.length;s(128,Ft++,Ft))Zt.push(C[Ft]);else Zt.push(C);if(O&&Zt.push(O),M&&qt.push(M),N&&qt.push(N),Al(L,Zt,qt),!0===Oe){if(s(0,L=Sr(L,E,A)),"object"==typeof A){for(let e=0;e<A.length;e++)s(25,A[e]=A[e]+"_pct",A);s(125,Ht=!1)}else s(25,A+="_pct"),s(125,Ht=!1);s(80,Be=El(L))}switch(s(86,Ze=Be[E].type),Ze){case"number":s(86,Ze="value");break;case"string":s(86,Ze="category");break;case"date":s(86,Ze="time")}if(s(26,H="category"===H?"category":Ze),s(1,D=D?"true"===D||!0===D:"category"===H),U&&"category"!==H)throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(U&&C)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(U&&s(26,H="category"),s(87,_e="value"===Ze&&"category"===H),s(0,L=j?"category"===Ze?zl(L,A,!1):zl(L,E,!0):L),"time"===Ze&&s(0,L=zl(L,E,!0)),s(129,Rt=El(L,"array")),s(130,zt=Rt.filter((e=>"date"===e.type))),s(130,zt=zt.map((e=>e.id))),zt.length>0)for(let e=0;e<zt.length;e++)s(0,L=ss(L,zt[e]));s(88,We=V?Ot(V,null==(t=Be[E].format)?void 0:t.valueType):Be[E].format),s(89,Je=A?me?Ot(me,"object"==typeof A?null==(m=Be[A[0]].format)?void 0:m.valueType:null==(y=Be[A].format)?void 0:y.valueType):"object"==typeof A?Be[A[0]].format:Be[A].format:"str"),C&&s(90,je=be?Ot(be,"object"==typeof C?null==(f=Be[C[0]].format)?void 0:f.valueType:null==(v=Be[C].format)?void 0:v.valueType):"object"==typeof C?Be[C[0]].format:Be[C].format),O&&s(91,Ve=Se?Ot(Se,null==(w=Be[O].format)?void 0:w.valueType):Be[O].format),s(92,Ye=Be[E].columnUnitSummary),A&&s(93,Xe="object"==typeof A?Be[A[0]].columnUnitSummary:Be[A].columnUnitSummary),C&&s(94,Qe="object"==typeof C?Be[C[0]].columnUnitSummary:Be[C].columnUnitSummary),s(27,Z="true"===Z?Xt(E,We):"false"===Z?"":Z),s(34,se="true"===se?"object"==typeof A?"":Xt(A,Je):"false"===se?"":se),s(40,he="true"===he?"object"==typeof C?"":Xt(C,je):"false"===he?"":he);let e,i,n="object"==typeof A?A.length:1,a=M?kr(L,M):1,l=n*a,p="object"==typeof C?C.length:C?1:0,h=l+p;if(void 0!==we&&s(46,we="true"===we||!0===we),s(46,we=we??h>1),!0===Oe&&!0===Q)throw Error("Log axis cannot be used in a 100% stacked chart");if("stacked"===Me&&h>1&&!0===Q)throw Error("Log axis cannot be used in a stacked chart");if("object"==typeof A){e=Be[A[0]].columnUnitSummary.min;for(let t=0;t<A.length;t++)Be[A[t]].columnUnitSummary.min<e&&(e=Be[A[t]].columnUnitSummary.min)}else A&&(e=Be[A].columnUnitSummary.min);if(!0===Q&&e<=0&&null!==e)throw Error("Log axis cannot display values less than or equal to zero");if($.update((e=>({...e,data:L,x:E,y:A,y2:C,series:M,swapXY:U,sort:j,xType:H,xFormat:We,yFormat:Je,y2Format:je,sizeFormat:Ve,xMismatch:_e,size:O,yMin:re,y2Min:ge,columnSummary:Be,xAxisTitle:Z,yAxisTitle:se,y2AxisTitle:he,tooltipTitle:N,chartAreaHeight:Ne,chartType:z,yCount:n,y2Count:p}))),s(95,et=ql(L,E)),s(96,tt=U?{type:ee,logBase:te,position:"top",axisLabel:{show:le,hideOverlap:!0,showMaxLabel:!0,formatter:e=>Kl(e,Je,Xe),margin:4},min:re,max:oe,scale:ce,splitLine:{show:ae},axisLine:{show:ie,onZero:!1},axisTick:{show:ne},boundaryGap:!1,z:2}:{type:H,min:Y,max:X,tooltip:{show:!0,position:"inside",formatter(e){if(e.isTruncated())return e.name}},splitLine:{show:W},axisLine:{show:q},axisTick:{show:_},axisLabel:{show:J,hideOverlap:!0,showMaxLabel:"category"===H||"value"===H,formatter:"time"!==H&&"category"!==H&&function(e){return Kl(e,We,Ye)},margin:6},scale:!0,z:2}),U?s(97,st={type:H,inverse:"true",splitLine:{show:W},axisLine:{show:q},axisTick:{show:_},axisLabel:{show:J,hideOverlap:!0},scale:!0,min:Y,max:X,z:2}):(s(97,st={type:ee,logBase:te,splitLine:{show:ae},axisLine:{show:ie,onZero:!1},axisTick:{show:ne},axisLabel:{show:le,hideOverlap:!0,margin:4,formatter:e=>Kl(e,Je,Xe),color:C?"true"===d?r[0]:"false"!==d?d:void 0:void 0},name:se,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:C?"true"===d?r[0]:"false"!==d?d:void 0:void 0},nameGap:6,min:re,max:oe,scale:ce,boundaryGap:["0%","1%"],z:2}),i={type:"value",show:!1,alignTicks:!0,splitLine:{show:fe},axisLine:{show:ue,onZero:!1},axisTick:{show:ye},axisLabel:{show:$e,hideOverlap:!0,margin:4,formatter:e=>Kl(e,je,Qe),color:"true"===c?r[l]:"false"!==c?c:void 0},name:he,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:"true"===c?r[l]:"false"!==c?c:void 0},nameGap:6,min:ge,max:ve,scale:xe,boundaryGap:["0%","1%"],z:2},s(97,st=[st,i])),Ne){if(s(47,Ne=Number(Ne)),isNaN(Ne))throw Error("chartAreaHeight must be a number");if(Ne<=0)throw Error("chartAreaHeight must be a positive number")}else s(47,Ne=180);s(100,at=!!P),s(101,lt=!!R),s(102,rt=we*(null!==M||"object"==typeof A&&A.length>1)),s(103,ot=""!==se&&U),s(104,ct=""!==Z&&!U),s(105,dt=15),s(106,mt=13),s(107,pt=6*lt),s(108,ht=at*dt+lt*mt+pt*Math.max(at,lt)),s(109,yt=10),s(110,ft=10),s(111,$t=14),s(112,gt=14),s(113,vt=15),s(113,vt*=rt),s(114,xt=7),s(114,xt*=Math.max(at,lt)),s(115,bt=ht+xt),s(116,St=bt+vt+gt*ot+yt),s(117,Lt=ct*$t+ft),s(121,Ct=8),s(123,Dt=1),U&&(s(122,Mt=et.length),s(123,Dt=Math.max(1,Mt/Ct))),s(118,wt=Ne*Dt+St+Lt),s(119,Et=bt+vt+7),s(15,It=wt+"px"),s(13,kt="100%"),s(120,At=U?se:Z),""!==At&&s(120,At+=" →"),s(98,it={id:"horiz-axis-title",type:"text",style:{text:At,textAlign:"right",fill:o.colors["base-content-muted"]},cursor:"auto",right:U?"2%":"3%",top:U?Et:null,bottom:U?null:"2%"}),s(99,nt={title:{text:P,subtext:R,subtextStyle:{width:kt}},tooltip:{trigger:"axis",show:!0,formatter(e){let t,s,i,a;if(h>1){s=e[0].value[U?1:0],t=`<span id="tooltip" style='font-weight: 600;'>${ut(s,We)}</span>`;for(let s=e.length-1;s>=0;s--)"stackTotal"!==e[s].seriesName&&(i=e[s].value[U?0:1],t+=`<br> <span style='font-size: 11px;'>${e[s].marker} ${e[s].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${ut(i,0===Ri(e[s].componentIndex,n,p)?Je:je)}</span>`)}else"value"===H?(s=e[0].value[U?1:0],i=e[0].value[U?0:1],a=e[0].seriesName,t=`<span id="tooltip" style='font-weight: 600;'>${Xt(E,We)}: </span><span style='float:right; margin-left: 10px;'>${ut(s,We)}</span><br/><span style='font-weight: 600;'>${Xt(a,Je)}: </span><span style='float:right; margin-left: 10px;'>${ut(i,Je)}</span>`):(s=e[0].value[U?1:0],i=e[0].value[U?0:1],a=e[0].seriesName,t=`<span id="tooltip" style='font-weight: 600;'>${ut(s,We)}</span><br/><span>${Xt(a,Je)}: </span><span style='float:right; margin-left: 10px;'>${ut(i,Je)}</span>`);return t},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:we,type:"scroll",top:bt,padding:[0,0,0,0],data:[]},grid:{left:Ue??(U?"1%":"0.8%"),right:Pe??(U?"4%":"3%"),bottom:Lt,top:St,containLabel:!0},xAxis:tt,yAxis:st,series:[],animation:!0,graphic:it,color:r}),g.update((()=>nt))}catch(e){if(s(14,Ut=e.message),console.error("[31m%s[0m",`Error in ${z}: ${e.message}`),Di)throw Ut;$.update((e=>({...e,error:Ut})))}e.$$.dirty[0]},ki(Ni,$),ki(Fi,g),[L,D,I,U,Ie,ke,w,P,z,Ee,Ae,De,Fe,kt,Ut,It,l,a,n,i,m,g,v,ze,E,A,H,Z,q,_,W,J,j,Q,se,ie,ne,ae,le,ce,he,ue,ye,fe,$e,xe,we,Ne,Re,C,M,O,N,R,B,G,K,V,Y,X,ee,te,re,oe,me,pe,ge,ve,be,Te,Se,Le,Ce,Me,Oe,Ue,Pe,k,F,S,Be,Ge,Bt,Gt,Ke,He,Ze,_e,We,Je,je,Ve,Ye,Xe,Qe,et,tt,st,it,nt,at,lt,rt,ot,ct,dt,mt,pt,ht,yt,ft,$t,gt,vt,xt,bt,St,Lt,wt,Et,At,Ct,Mt,Dt,Kt,Ht,Zt,qt,Ft,Rt,zt,r,o,c,d,f,y]}class Ir extends lt{constructor(e){super(),it(this,e,Or,wr,tt,{data:0,queryID:6,x:24,y:25,y2:49,series:50,size:51,tooltipTitle:52,showAllXAxisLabels:1,printEchartsConfig:2,swapXY:3,title:7,subtitle:53,chartType:8,bubble:54,hist:55,boxplot:56,xType:26,xAxisTitle:27,xBaseline:28,xTickMarks:29,xGridlines:30,xAxisLabels:31,sort:32,xFmt:57,xMin:58,xMax:59,yLog:33,yType:60,yLogBase:61,yAxisTitle:34,yBaseline:35,yTickMarks:36,yGridlines:37,yAxisLabels:38,yMin:62,yMax:63,yScale:39,yFmt:64,yAxisColor:65,y2AxisTitle:40,y2Baseline:41,y2TickMarks:42,y2Gridlines:43,y2AxisLabels:44,y2Min:66,y2Max:67,y2Scale:45,y2Fmt:68,y2AxisColor:69,sizeFmt:70,colorPalette:71,legend:46,echartsOptions:9,seriesOptions:10,seriesColors:72,stackType:73,stacked100:74,chartAreaHeight:47,renderer:11,downloadableData:4,downloadableImage:5,connectGroup:12,leftPadding:75,rightPadding:76,xLabelWrap:48},null,[-1,-1,-1,-1,-1])}}function Mr(e){let t;const s=e[7].default,i=sl(s,e,e[8],null);return{c(){i&&i.c()},l(e){i&&i.l(e)},m(e,s){i&&i.m(e,s),t=!0},p(e,n){i&&i.p&&(!t||256&n)&&rl(i,s,e,e[8],t?ol(s,e[8],n,null):al(e[8]),null)},i(e){t||(M(i,e),t=!0)},o(e){U(i,e),t=!1},d(e){i&&i.d(e)}}}function Dr(e){let t,s;const i=[e[5],{data:It.isQuery(e[11])?Array.from(e[11]):e[11]},{queryID:e[6]}];let n={$$slots:{default:[Mr]},$$scope:{ctx:e}};for(let e=0;e<i.length;e+=1)n=Lt(n,i[e]);return t=new Ir({props:n}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const n=2144&s?xl(i,[32&s&&pl(e[5]),2048&s&&{data:It.isQuery(e[11])?Array.from(e[11]):e[11]},64&s&&{queryID:e[6]}]):{};256&s&&(n.$$scope={dirty:s,ctx:e}),t.$set(n)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Nr(e){let t,s;return t=new $l({props:{slot:"empty",emptyMessage:e[2],emptySet:e[1],chartType:e[5].chartType,isInitial:e[4]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};4&s&&(i.emptyMessage=e[2]),2&s&&(i.emptySet=e[1]),32&s&&(i.chartType=e[5].chartType),16&s&&(i.isInitial=e[4]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Fr(e){let t,s;return t=new Pi({props:{slot:"error",title:e[5].chartType,error:e[11].error.message}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};32&s&&(i.title=e[5].chartType),2048&s&&(i.error=e[11].error.message),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Pr(e){let t,s;return t=new Jl({props:{data:e[0],height:e[3],$$slots:{error:[Fr,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0],empty:[Nr],default:[Dr,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0]},$$scope:{ctx:e}}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,[s]){const i={};1&s&&(i.data=e[0]),8&s&&(i.height=e[3]),2358&s&&(i.$$scope={dirty:s,ctx:e}),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Rr(e,t,s){let i,{$$slots:n={},$$scope:a}=t,{data:l}=t;const r=It.isQuery(l)?l.hash:void 0;let o=(null==l?void 0:l.hash)===r,{emptySet:c}=t,{emptyMessage:d}=t,{height:m=200}=t,p=null==l?void 0:l.id;return e.$$set=e=>{s(10,t=Lt(Lt({},t),Ht(e))),"data"in e&&s(0,l=e.data),"emptySet"in e&&s(1,c=e.emptySet),"emptyMessage"in e&&s(2,d=e.emptyMessage),"height"in e&&s(3,m=e.height),"$$scope"in e&&s(8,a=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&s(4,o=(null==l?void 0:l.hash)===r),s(5,i={...Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e)))})},t=Ht(t),[l,c,d,m,o,i,p,n,a]}class Bn extends lt{constructor(e){super(),it(this,e,Rr,Pr,tt,{data:0,emptySet:1,emptyMessage:2,height:3})}}function zn(e,t,s,i,n,a,l,r,o,c,d=void 0,m=void 0,p=void 0,h=void 0){function u(e,t,s,i){let n={name:t,data:e,yAxisIndex:s};return n={...i,...n},n}let y,f,$,g,v,x,b,T,S=[],L=function(e,t){const s=[];function i(e,t){(function(e){return typeof e>"u"})(e)||(Array.isArray(e)?e.forEach((e=>s.push([e,t]))):s.push([e,t]))}return i(e,0),i(t,1),s}(s,p);if(null!=i&&1===L.length)for(b=ql(e,i),y=0;y<b.length;y++){if(v=e.filter((e=>e[i]===b[y])),g=n?v.map((e=>[e[L[0][0]],r?e[t].toString():e[t]])):v.map((e=>[r?e[t].toString():e[t],e[L[0][0]]])),d){let e=v.map((e=>e[d]));g.forEach(((t,s)=>t.push(e[s])))}if(m){let e=v.map((e=>e[m]));g.forEach(((t,s)=>t.push(e[s])))}x=b[y]??"null",T=L[0][1],$=u(g,x,T,a),S.push($)}if(null!=i&&L.length>1)for(b=ql(e,i),y=0;y<b.length;y++)for(v=e.filter((e=>e[i]===b[y])),f=0;f<L.length;f++){if(g=n?v.map((e=>[e[L[f][0]],r?e[t].toString():e[t]])):v.map((e=>[r?e[t].toString():e[t],e[L[f][0]]])),d){let e=v.map((e=>e[d]));g.forEach(((t,s)=>t.push(e[s])))}if(m){let e=v.map((e=>e[m]));g.forEach(((t,s)=>t.push(e[s])))}x=(b[y]??"null")+" - "+o[L[f][0]].title,T=L[f][1],$=u(g,x,T,a),S.push($)}if(null==i&&L.length>1)for(y=0;y<L.length;y++){if(g=n?e.map((e=>[e[L[y][0]],r?e[t].toString():e[t]])):e.map((e=>[r?e[t].toString():e[t],e[L[y][0]]])),d){let t=e.map((e=>e[d]));g.forEach(((e,s)=>e.push(t[s])))}if(m){let t=e.map((e=>e[m]));g.forEach(((e,s)=>e.push(t[s])))}x=o[L[y][0]].title,T=L[y][1],$=u(g,x,T,a),S.push($)}if(null==i&&1===L.length){if(g=n?e.map((e=>[e[L[0][0]],r?e[t].toString():e[t]])):e.map((e=>[r?e[t].toString():e[t],e[L[0][0]]])),d){let t=e.map((e=>e[d]));g.forEach(((e,s)=>e.push(t[s])))}if(m){let t=e.map((e=>e[m]));g.forEach(((e,s)=>e.push(t[s])))}x=o[L[0][0]].title,T=L[0][1],$=u(g,x,T,a),S.push($)}return c&&S.sort(((e,t)=>c.indexOf(e.name)-c.indexOf(t.name))),h&&S.forEach((e=>{e.name=rs(e.name,h)})),S}function Br(e){let t=[];for(let s=1;s<e.length;s++)t.push(e[s]-e[s-1]);return t}function Gn(e,t){return("number"!=typeof e||isNaN(e))&&(e=0),("number"!=typeof t||isNaN(t))&&(t=0),e=Math.abs(e),(t=Math.abs(t))<=.01?e:Gn(t,e%t)}function zr(e,t){if(!Array.isArray(e))throw new TypeError("Cannot calculate extent of non-array value.");let s,i;for(const t of e)"number"==typeof t&&(void 0===s?t>=t&&(s=i=t):(s>t&&(s=t),i<t&&(i=t)));return[s,i]}function Gr(e,t){let[s,i]=zr(e);const n=[];let a=s;for(;a<=i;)n.push(Math.round(1e8*(a+Number.EPSILON))/1e8),a+=t;return n}function Ur(e){if(e.length<=1)return;e.sort((function(e,t){return e-t}));let t=(e=Br(e=e.map((function(e){return 1e8*e})))).reduce(((e,t)=>Gn(e,t)))/1e8;return t=Math.round(1e8*(t+Number.EPSILON))/1e8,t}function Gl(e,t,s,i,n=!1,a=!1){var l;let r=!1;const o=e.map((e=>Object.assign({},e,{[t]:e[t]instanceof Date?(r=!0,e[t].toISOString()):e[t]}))).filter((e=>void 0!==e[t]&&null!==e[t])),c=Array.from(o).reduce(((e,s)=>(s[t]instanceof Date&&(s[t]=s[t].toISOString(),r=!0),i?(e[s[i]??"null"]||(e[s[i]??"null"]=[]),e[s[i]??"null"].push(s)):(e.default||(e.default=[]),e.default.push(s)),e)),{}),d={};let m;const p=(null==(l=o.find((e=>e&&null!==e[t]&&void 0!==e[t])))?void 0:l[t])??null;switch(typeof p){case"object":throw null===p?new Error(`Column '${t}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(m=ql(o,t),a){const e=Ur(m);d[t]=Gr(m,e)}break;case"string":m=ql(o,t),d[t]=m}const h=[];for(const e of Object.values(c)){const a=i?{[i]:null}:{};if(n)if(s instanceof Array)for(let e=0;e<s.length;e++)a[s[e]]=0;else a[s]=0;else if(s instanceof Array)for(let e=0;e<s.length;e++)a[s[e]]=null;else a[s]=null;i&&(d[i]=i);const l=[];0===Object.keys(d).length?l.push(Ji([t],a)):l.push(Ji(d,a)),h.push(el(e,...l))}return r?h.flat().map((e=>({...e,[t]:new Date(e[t])}))):h.flat()}function Tn(e,t,s){let i=el(e,Si(t,[as(s,Ei)]));if("object"==typeof s)for(let e=0;e<i.length;e++){i[e].stackTotal=0;for(let t=0;t<s.length;t++)i[e].stackTotal=i[e].stackTotal+i[e][s[t]]}return i}let Hr=60;function vr(e,t,s){let i,n,a,l,r,o,c,d,m,p,h,u,y,f,$,g,v,x,b,T,S,L,w,E=de,A=de,C=de,M=de;e.$$.on_destroy.push((()=>E())),e.$$.on_destroy.push((()=>A())),e.$$.on_destroy.push((()=>C())),e.$$.on_destroy.push((()=>M()));const{resolveColor:O}=Pt();let{y:N}=t;const D=!!N;let{y2:I}=t;const k=!!I;let{series:F}=t;const U=!!F;let P,{options:R}=t,{name:z}=t,{type:B="stacked"}=t,{stackName:G}=t,{fillColor:K}=t,{fillOpacity:H}=t,{outlineColor:Z}=t,{outlineWidth:q}=t,{labels:_=!1}=t,{seriesLabels:W=!0}=t,{labelSize:J=11}=t,{labelPosition:j}=t,{labelColor:V}=t,{labelFmt:Y}=t;Y&&(P=Ot(Y));let X,{yLabelFmt:Q}=t;Q&&(X=Ot(Q));let ee,{y2LabelFmt:te}=t;te&&(ee=Ot(te));let se,ie,ne,ae,{y2SeriesType:le="bar"}=t,{stackTotalLabel:re=!0}=t,{showAllLabels:oe=!1}=t,{seriesOrder:ce}=t;const me={outside:"top",inside:"inside"},pe={outside:"right",inside:"inside"};let{seriesLabelFmt:he}=t;return In((()=>{R&&n.update((e=>({...e,...R}))),b&&n.update((e=>{if(B.includes("stacked")?e.tooltip={...e.tooltip,order:"seriesDesc"}:e.tooltip={...e.tooltip,order:"seriesAsc"},"stacked100"===B&&(u?e.xAxis={...e.xAxis,max:1}:e.yAxis[0]={...e.yAxis[0],max:1}),u)e.yAxis={...e.yAxis,...b.xAxis},e.xAxis={...e.xAxis,...b.yAxis};else if(e.yAxis[0]={...e.yAxis[0],...b.yAxis},e.xAxis={...e.xAxis,...b.xAxis},I&&(e.yAxis[1]={...e.yAxis[1],show:!0},["line","bar","scatter"].includes(le)))for(let t=0;t<h;t++)e.series[p+t].type=le,e.series[p+t].stack=void 0;return e}))})),e.$$set=e=>{"y"in e&&s(4,N=e.y),"y2"in e&&s(5,I=e.y2),"series"in e&&s(6,F=e.series),"options"in e&&s(13,R=e.options),"name"in e&&s(7,z=e.name),"type"in e&&s(14,B=e.type),"stackName"in e&&s(8,G=e.stackName),"fillColor"in e&&s(15,K=e.fillColor),"fillOpacity"in e&&s(16,H=e.fillOpacity),"outlineColor"in e&&s(17,Z=e.outlineColor),"outlineWidth"in e&&s(18,q=e.outlineWidth),"labels"in e&&s(9,_=e.labels),"seriesLabels"in e&&s(10,W=e.seriesLabels),"labelSize"in e&&s(19,J=e.labelSize),"labelPosition"in e&&s(11,j=e.labelPosition),"labelColor"in e&&s(20,V=e.labelColor),"labelFmt"in e&&s(21,Y=e.labelFmt),"yLabelFmt"in e&&s(22,Q=e.yLabelFmt),"y2LabelFmt"in e&&s(23,te=e.y2LabelFmt),"y2SeriesType"in e&&s(24,le=e.y2SeriesType),"stackTotalLabel"in e&&s(12,re=e.stackTotalLabel),"showAllLabels"in e&&s(25,oe=e.showAllLabels),"seriesOrder"in e&&s(26,ce=e.seriesOrder),"seriesLabelFmt"in e&&s(27,he=e.seriesLabelFmt)},e.$$.update=()=>{32768&e.$$.dirty[0]&&(s(2,a=O(K)),A(),A=Tt(a,(e=>s(50,S=e)))),131072&e.$$.dirty[0]&&(s(1,l=O(Z)),E(),E=Tt(l,(e=>s(49,T=e)))),512&e.$$.dirty[0]&&s(9,_="true"===_||!0===_),1024&e.$$.dirty[0]&&s(10,W="true"===W||!0===W),1048576&e.$$.dirty[0]&&(s(0,r=O(V)),C(),C=Tt(r,(e=>s(51,L=e)))),4096&e.$$.dirty[0]&&s(12,re="true"===re||!0===re),2097152&e.$$.dirty[1]&&s(46,o=w.data),2097152&e.$$.dirty[1]&&s(42,c=w.x),16&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&s(4,N=D?N:w.y),32&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&s(5,I=k?I:w.y2),2097152&e.$$.dirty[1]&&s(40,d=w.yFormat),2097152&e.$$.dirty[1]&&s(47,m=w.y2Format),2097152&e.$$.dirty[1]&&s(35,p=w.yCount),2097152&e.$$.dirty[1]&&s(36,h=w.y2Count),2097152&e.$$.dirty[1]&&s(37,u=w.swapXY),2097152&e.$$.dirty[1]&&s(39,y=w.xType),2097152&e.$$.dirty[1]&&s(43,f=w.xMismatch),2097152&e.$$.dirty[1]&&s(44,$=w.columnSummary),2097152&e.$$.dirty[1]&&s(48,g=w.sort),64&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&s(6,F=U?F:w.series),16848&e.$$.dirty[0]|174403&e.$$.dirty[1]&&(F||"object"==typeof N?(!0===g&&"category"===y&&(s(31,se=Tn(o,c,N)),s(31,se=zl(se,"object"==typeof N?"stackTotal":N,!1)),s(32,ie=se.map((e=>e[c]))),s(46,o=[...o].sort((function(e,t){return ie.indexOf(e[c])-ie.indexOf(t[c])})))),u||("value"===y||"category"===y)&&B.includes("stacked")?(s(46,o=Gl(o,c,N,F,!0,"value"===y)),s(39,y="category")):"time"===y&&B.includes("stacked")&&s(46,o=Gl(o,c,N,F,!0,!0)),B.includes("stacked")?(s(8,G=G??"stack1"),s(33,ne="inside")):(s(8,G=void 0),s(33,ne=u?"right":"top"))):(s(7,z=z??Xt(N,$[N].title)),u&&"category"!==y&&(s(46,o=Gl(o,c,N,F,!0,"time"!==y)),s(39,y="category")),s(8,G="stack1"),s(33,ne=u?"right":"top"))),16400&e.$$.dirty[0]|34816&e.$$.dirty[1]&&"stacked"===B&&s(34,ae=Tn(o,c,N)),2048&e.$$.dirty[0]|68&e.$$.dirty[1]&&s(11,j=(u?pe[j]:me[j])??ne),1913458432&e.$$.dirty[0]|1901168&e.$$.dirty[1]&&s(45,v={type:"bar",stack:G,label:{show:_&&W,formatter:e=>0===e.value[u?0:1]?"":ut(e.value[u?0:1],[X??P??d,ee??P??m][Ri(e.componentIndex,p,h)]),position:j,fontSize:J,color:L},labelLayout:{hideOverlap:!oe},emphasis:{focus:"series"},barMaxWidth:Hr,itemStyle:{color:S,opacity:H,borderColor:T,borderWidth:q}}),201326832&e.$$.dirty[0]|63552&e.$$.dirty[1]&&s(41,x=zn(o,c,N,F,u,v,z,f,$,ce,void 0,void 0,I,he)),268981072&e.$$.dirty[0]|7880&e.$$.dirty[1]&&n.update((e=>(e.series.push(...x),e.legend.data.push(...x.map((e=>e.name.toString()))),!0===_&&"stacked"===B&&"object"==typeof N|void 0!==F&&!0===re&&F!==c&&(e.series.push({type:"bar",stack:G,name:"stackTotal",color:"none",data:ae.map((e=>[u?0:f?e[c].toString():e[c],u?f?e[c].toString():e[c]:0])),label:{show:!0,position:u?"right":"top",formatter(e){let t=0;return x.forEach((s=>{t+=s.data[e.dataIndex][u?0:1]})),0===t?"":ut(t,P??d)},fontWeight:"bold",fontSize:J,padding:u?[0,0,0,5]:void 0}}),e.legend.selectedMode=!1),e))),256&e.$$.dirty[1]&&(b={xAxis:{boundaryGap:["1%","2%"],type:y}})},s(3,i=Ul(Ni)),M(),M=Tt(i,(e=>s(52,w=e))),s(38,n=Ul(Fi)),[r,l,a,i,N,I,F,z,G,_,W,j,re,R,B,K,H,Z,q,J,V,Y,Q,te,le,oe,ce,he,P,X,ee,se,ie,ne,ae,p,h,u,n,y,d,x,c,f,$,v,o,m,g,T,S,L,w]}class qr extends lt{constructor(e){super(),it(this,e,vr,null,tt,{y:4,y2:5,series:6,options:13,name:7,type:14,stackName:8,fillColor:15,fillOpacity:16,outlineColor:17,outlineWidth:18,labels:9,seriesLabels:10,labelSize:19,labelPosition:11,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,stackTotalLabel:12,showAllLabels:25,seriesOrder:26,seriesLabelFmt:27},null,[-1,-1])}}function Vr(e){let t,s,i;t=new qr({props:{type:e[38],fillColor:e[72],fillOpacity:e[39],outlineColor:e[71],outlineWidth:e[40],labels:e[43],labelSize:e[44],labelPosition:e[45],labelColor:e[69],labelFmt:e[46],yLabelFmt:e[47],y2LabelFmt:e[48],stackTotalLabel:e[49],seriesLabels:e[50],showAllLabels:e[51],y2SeriesType:e[9],seriesOrder:e[60],seriesLabelFmt:e[62]}});const n=e[81].default,a=sl(n,e,e[82],null);return{c(){oe(t.$$.fragment),s=J(),a&&a.c()},l(e){ae(t.$$.fragment,e),s=Z(e),a&&a.l(e)},m(e,n){re(t,e,n),S(e,s,n),a&&a.m(e,n),i=!0},p(e,s){const l={};128&s[1]&&(l.type=e[38]),1024&s[2]&&(l.fillColor=e[72]),256&s[1]&&(l.fillOpacity=e[39]),512&s[2]&&(l.outlineColor=e[71]),512&s[1]&&(l.outlineWidth=e[40]),4096&s[1]&&(l.labels=e[43]),8192&s[1]&&(l.labelSize=e[44]),16384&s[1]&&(l.labelPosition=e[45]),128&s[2]&&(l.labelColor=e[69]),32768&s[1]&&(l.labelFmt=e[46]),65536&s[1]&&(l.yLabelFmt=e[47]),131072&s[1]&&(l.y2LabelFmt=e[48]),262144&s[1]&&(l.stackTotalLabel=e[49]),524288&s[1]&&(l.seriesLabels=e[50]),1048576&s[1]&&(l.showAllLabels=e[51]),512&s[0]&&(l.y2SeriesType=e[9]),536870912&s[1]&&(l.seriesOrder=e[60]),1&s[2]&&(l.seriesLabelFmt=e[62]),t.$set(l),a&&a.p&&(!i||1048576&s[2])&&rl(a,n,e,e[82],i?ol(n,e[82],s,null):al(e[82]),null)},i(e){i||(M(t.$$.fragment,e),M(a,e),i=!0)},o(e){U(t.$$.fragment,e),U(a,e),i=!1},d(e){e&&m(s),se(t,e),a&&a.d(e)}}}function Wr(e){let t,s;return t=new Bn({props:{data:e[1],x:e[2],y:e[3],y2:e[4],xFmt:e[12],yFmt:e[10],y2Fmt:e[11],series:e[5],xType:e[6],yLog:e[7],yLogBase:e[8],legend:e[15],xAxisTitle:e[16],yAxisTitle:e[17],y2AxisTitle:e[18],xGridlines:e[19],yGridlines:e[20],y2Gridlines:e[21],xAxisLabels:e[22],yAxisLabels:e[23],y2AxisLabels:e[24],xBaseline:e[25],yBaseline:e[26],y2Baseline:e[27],xTickMarks:e[28],yTickMarks:e[29],y2TickMarks:e[30],yAxisColor:e[68],y2AxisColor:e[67],yMin:e[31],yMax:e[32],yScale:e[33],y2Min:e[34],y2Max:e[35],y2Scale:e[36],swapXY:e[0],title:e[13],subtitle:e[14],chartType:"Bar Chart",stackType:e[38],sort:e[42],stacked100:e[73],chartAreaHeight:e[41],showAllXAxisLabels:e[37],colorPalette:e[70],echartsOptions:e[52],seriesOptions:e[53],printEchartsConfig:e[54],emptySet:e[55],emptyMessage:e[56],renderer:e[57],downloadableData:e[58],downloadableImage:e[59],connectGroup:e[61],xLabelWrap:e[65],seriesColors:e[66],leftPadding:e[63],rightPadding:e[64],$$slots:{default:[Vr]},$$scope:{ctx:e}}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};2&s[0]&&(i.data=e[1]),4&s[0]&&(i.x=e[2]),8&s[0]&&(i.y=e[3]),16&s[0]&&(i.y2=e[4]),4096&s[0]&&(i.xFmt=e[12]),1024&s[0]&&(i.yFmt=e[10]),2048&s[0]&&(i.y2Fmt=e[11]),32&s[0]&&(i.series=e[5]),64&s[0]&&(i.xType=e[6]),128&s[0]&&(i.yLog=e[7]),256&s[0]&&(i.yLogBase=e[8]),32768&s[0]&&(i.legend=e[15]),65536&s[0]&&(i.xAxisTitle=e[16]),131072&s[0]&&(i.yAxisTitle=e[17]),262144&s[0]&&(i.y2AxisTitle=e[18]),524288&s[0]&&(i.xGridlines=e[19]),1048576&s[0]&&(i.yGridlines=e[20]),2097152&s[0]&&(i.y2Gridlines=e[21]),4194304&s[0]&&(i.xAxisLabels=e[22]),8388608&s[0]&&(i.yAxisLabels=e[23]),16777216&s[0]&&(i.y2AxisLabels=e[24]),33554432&s[0]&&(i.xBaseline=e[25]),67108864&s[0]&&(i.yBaseline=e[26]),134217728&s[0]&&(i.y2Baseline=e[27]),268435456&s[0]&&(i.xTickMarks=e[28]),536870912&s[0]&&(i.yTickMarks=e[29]),1073741824&s[0]&&(i.y2TickMarks=e[30]),64&s[2]&&(i.yAxisColor=e[68]),32&s[2]&&(i.y2AxisColor=e[67]),1&s[1]&&(i.yMin=e[31]),2&s[1]&&(i.yMax=e[32]),4&s[1]&&(i.yScale=e[33]),8&s[1]&&(i.y2Min=e[34]),16&s[1]&&(i.y2Max=e[35]),32&s[1]&&(i.y2Scale=e[36]),1&s[0]&&(i.swapXY=e[0]),8192&s[0]&&(i.title=e[13]),16384&s[0]&&(i.subtitle=e[14]),128&s[1]&&(i.stackType=e[38]),2048&s[1]&&(i.sort=e[42]),1024&s[1]&&(i.chartAreaHeight=e[41]),64&s[1]&&(i.showAllXAxisLabels=e[37]),256&s[2]&&(i.colorPalette=e[70]),2097152&s[1]&&(i.echartsOptions=e[52]),4194304&s[1]&&(i.seriesOptions=e[53]),8388608&s[1]&&(i.printEchartsConfig=e[54]),16777216&s[1]&&(i.emptySet=e[55]),33554432&s[1]&&(i.emptyMessage=e[56]),67108864&s[1]&&(i.renderer=e[57]),134217728&s[1]&&(i.downloadableData=e[58]),268435456&s[1]&&(i.downloadableImage=e[59]),1073741824&s[1]&&(i.connectGroup=e[61]),8&s[2]&&(i.xLabelWrap=e[65]),16&s[2]&&(i.seriesColors=e[66]),2&s[2]&&(i.leftPadding=e[63]),4&s[2]&&(i.rightPadding=e[64]),512&s[0]|538964864&s[1]|1050241&s[2]&&(i.$$scope={dirty:s,ctx:e}),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function jr(e,t,s){let i,n,a,l,r,o,c,{$$slots:d={},$$scope:m}=t;const{resolveColor:p,resolveColorsObject:h,resolveColorPalette:u}=Pt();let{data:y}=t,{x:f}=t,{y:$}=t,{y2:g}=t,{series:v}=t,{xType:x}=t,{yLog:b}=t,{yLogBase:T}=t,{y2SeriesType:S}=t,{yFmt:L}=t,{y2Fmt:w}=t,{xFmt:E}=t,{title:A}=t,{subtitle:C}=t,{legend:M}=t,{xAxisTitle:O}=t,{yAxisTitle:N=(g?"true":void 0)}=t,{y2AxisTitle:D=(g?"true":void 0)}=t,{xGridlines:I}=t,{yGridlines:k}=t,{y2Gridlines:F}=t,{xAxisLabels:U}=t,{yAxisLabels:P}=t,{y2AxisLabels:R}=t,{xBaseline:z}=t,{yBaseline:B}=t,{y2Baseline:G}=t,{xTickMarks:K}=t,{yTickMarks:H}=t,{y2TickMarks:Z}=t,{yMin:q}=t,{yMax:_}=t,{yScale:W}=t,{y2Min:J}=t,{y2Max:j}=t,{y2Scale:V}=t,{swapXY:Y=!1}=t,{showAllXAxisLabels:X}=t,{type:Q="stacked"}=t,ee="stacked100"===Q,{fillColor:te}=t,{fillOpacity:se}=t,{outlineColor:ie}=t,{outlineWidth:ne}=t,{chartAreaHeight:ae}=t,{sort:le}=t,{colorPalette:re="default"}=t,{labels:oe}=t,{labelSize:ce}=t,{labelPosition:de}=t,{labelColor:me}=t,{labelFmt:pe}=t,{yLabelFmt:he}=t,{y2LabelFmt:ue}=t,{stackTotalLabel:ye}=t,{seriesLabels:fe}=t,{showAllLabels:$e}=t,{yAxisColor:ge}=t,{y2AxisColor:ve}=t,{echartsOptions:xe}=t,{seriesOptions:be}=t,{printEchartsConfig:Te=!1}=t,{emptySet:Se}=t,{emptyMessage:Le}=t,{renderer:we}=t,{downloadableData:Ee}=t,{downloadableImage:Ae}=t,{seriesColors:Ce}=t,{seriesOrder:Me}=t,{connectGroup:Oe}=t,{seriesLabelFmt:Ne}=t,{leftPadding:De}=t,{rightPadding:Ie}=t,{xLabelWrap:ke}=t;return e.$$set=e=>{"data"in e&&s(1,y=e.data),"x"in e&&s(2,f=e.x),"y"in e&&s(3,$=e.y),"y2"in e&&s(4,g=e.y2),"series"in e&&s(5,v=e.series),"xType"in e&&s(6,x=e.xType),"yLog"in e&&s(7,b=e.yLog),"yLogBase"in e&&s(8,T=e.yLogBase),"y2SeriesType"in e&&s(9,S=e.y2SeriesType),"yFmt"in e&&s(10,L=e.yFmt),"y2Fmt"in e&&s(11,w=e.y2Fmt),"xFmt"in e&&s(12,E=e.xFmt),"title"in e&&s(13,A=e.title),"subtitle"in e&&s(14,C=e.subtitle),"legend"in e&&s(15,M=e.legend),"xAxisTitle"in e&&s(16,O=e.xAxisTitle),"yAxisTitle"in e&&s(17,N=e.yAxisTitle),"y2AxisTitle"in e&&s(18,D=e.y2AxisTitle),"xGridlines"in e&&s(19,I=e.xGridlines),"yGridlines"in e&&s(20,k=e.yGridlines),"y2Gridlines"in e&&s(21,F=e.y2Gridlines),"xAxisLabels"in e&&s(22,U=e.xAxisLabels),"yAxisLabels"in e&&s(23,P=e.yAxisLabels),"y2AxisLabels"in e&&s(24,R=e.y2AxisLabels),"xBaseline"in e&&s(25,z=e.xBaseline),"yBaseline"in e&&s(26,B=e.yBaseline),"y2Baseline"in e&&s(27,G=e.y2Baseline),"xTickMarks"in e&&s(28,K=e.xTickMarks),"yTickMarks"in e&&s(29,H=e.yTickMarks),"y2TickMarks"in e&&s(30,Z=e.y2TickMarks),"yMin"in e&&s(31,q=e.yMin),"yMax"in e&&s(32,_=e.yMax),"yScale"in e&&s(33,W=e.yScale),"y2Min"in e&&s(34,J=e.y2Min),"y2Max"in e&&s(35,j=e.y2Max),"y2Scale"in e&&s(36,V=e.y2Scale),"swapXY"in e&&s(0,Y=e.swapXY),"showAllXAxisLabels"in e&&s(37,X=e.showAllXAxisLabels),"type"in e&&s(38,Q=e.type),"fillColor"in e&&s(74,te=e.fillColor),"fillOpacity"in e&&s(39,se=e.fillOpacity),"outlineColor"in e&&s(75,ie=e.outlineColor),"outlineWidth"in e&&s(40,ne=e.outlineWidth),"chartAreaHeight"in e&&s(41,ae=e.chartAreaHeight),"sort"in e&&s(42,le=e.sort),"colorPalette"in e&&s(76,re=e.colorPalette),"labels"in e&&s(43,oe=e.labels),"labelSize"in e&&s(44,ce=e.labelSize),"labelPosition"in e&&s(45,de=e.labelPosition),"labelColor"in e&&s(77,me=e.labelColor),"labelFmt"in e&&s(46,pe=e.labelFmt),"yLabelFmt"in e&&s(47,he=e.yLabelFmt),"y2LabelFmt"in e&&s(48,ue=e.y2LabelFmt),"stackTotalLabel"in e&&s(49,ye=e.stackTotalLabel),"seriesLabels"in e&&s(50,fe=e.seriesLabels),"showAllLabels"in e&&s(51,$e=e.showAllLabels),"yAxisColor"in e&&s(78,ge=e.yAxisColor),"y2AxisColor"in e&&s(79,ve=e.y2AxisColor),"echartsOptions"in e&&s(52,xe=e.echartsOptions),"seriesOptions"in e&&s(53,be=e.seriesOptions),"printEchartsConfig"in e&&s(54,Te=e.printEchartsConfig),"emptySet"in e&&s(55,Se=e.emptySet),"emptyMessage"in e&&s(56,Le=e.emptyMessage),"renderer"in e&&s(57,we=e.renderer),"downloadableData"in e&&s(58,Ee=e.downloadableData),"downloadableImage"in e&&s(59,Ae=e.downloadableImage),"seriesColors"in e&&s(80,Ce=e.seriesColors),"seriesOrder"in e&&s(60,Me=e.seriesOrder),"connectGroup"in e&&s(61,Oe=e.connectGroup),"seriesLabelFmt"in e&&s(62,Ne=e.seriesLabelFmt),"leftPadding"in e&&s(63,De=e.leftPadding),"rightPadding"in e&&s(64,Ie=e.rightPadding),"xLabelWrap"in e&&s(65,ke=e.xLabelWrap),"$$scope"in e&&s(82,m=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty[0]&&s(0,Y="true"===Y||!0===Y),4096&e.$$.dirty[2]&&s(72,i=p(te)),8192&e.$$.dirty[2]&&s(71,n=p(ie)),16384&e.$$.dirty[2]&&s(70,a=u(re)),32768&e.$$.dirty[2]&&s(69,l=p(me)),65536&e.$$.dirty[2]&&s(68,r=p(ge)),131072&e.$$.dirty[2]&&s(67,o=p(ve)),262144&e.$$.dirty[2]&&s(66,c=h(Ce))},[Y,y,f,$,g,v,x,b,T,S,L,w,E,A,C,M,O,N,D,I,k,F,U,P,R,z,B,G,K,H,Z,q,_,W,J,j,V,X,Q,se,ne,ae,le,oe,ce,de,pe,he,ue,ye,fe,$e,xe,be,Te,Se,Le,we,Ee,Ae,Me,Oe,Ne,De,Ie,ke,c,o,r,l,a,n,i,ee,te,ie,re,me,ge,ve,Ce,d,m]}class Yr extends lt{constructor(e){super(),it(this,e,jr,Wr,tt,{data:1,x:2,y:3,y2:4,series:5,xType:6,yLog:7,yLogBase:8,y2SeriesType:9,yFmt:10,y2Fmt:11,xFmt:12,title:13,subtitle:14,legend:15,xAxisTitle:16,yAxisTitle:17,y2AxisTitle:18,xGridlines:19,yGridlines:20,y2Gridlines:21,xAxisLabels:22,yAxisLabels:23,y2AxisLabels:24,xBaseline:25,yBaseline:26,y2Baseline:27,xTickMarks:28,yTickMarks:29,y2TickMarks:30,yMin:31,yMax:32,yScale:33,y2Min:34,y2Max:35,y2Scale:36,swapXY:0,showAllXAxisLabels:37,type:38,fillColor:74,fillOpacity:39,outlineColor:75,outlineWidth:40,chartAreaHeight:41,sort:42,colorPalette:76,labels:43,labelSize:44,labelPosition:45,labelColor:77,labelFmt:46,yLabelFmt:47,y2LabelFmt:48,stackTotalLabel:49,seriesLabels:50,showAllLabels:51,yAxisColor:78,y2AxisColor:79,echartsOptions:52,seriesOptions:53,printEchartsConfig:54,emptySet:55,emptyMessage:56,renderer:57,downloadableData:58,downloadableImage:59,seriesColors:80,seriesOrder:60,connectGroup:61,seriesLabelFmt:62,leftPadding:63,rightPadding:64,xLabelWrap:65},null,[-1,-1,-1])}}function Xr(e){let t,s;return t=new fs({props:{error:e[3]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};8&s&&(i.error=e[3]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Qr(e){let t,s,i,n,a=ut(e[2],e[4])+"",l=e[1]&&Ln(e);return{c(){t=F("span"),s=Te(a),i=J(),l&&l.c(),this.h()},l(e){t=N(e,"SPAN",{style:!0});var n=K(t);s=Ce(n,a),i=Z(n),l&&l.l(n),n.forEach(m),this.h()},h(){v(t,"color",e[5])},m(e,a){S(e,t,a),D(t,s),D(t,i),l&&l.m(t,null),n=!0},p(e,i){(!n||20&i)&&a!==(a=ut(e[2],e[4])+"")&&Qe(s,a),e[1]?l?(l.p(e,i),2&i&&M(l,1)):(l=Ln(e),l.c(),M(l,1),l.m(t,null)):l&&(Ke(),U(l,1,1,(()=>{l=null})),Ze()),(!n||32&i)&&v(t,"color",e[5])},i(e){n||(M(l),n=!0)},o(e){U(l),n=!1},d(e){e&&m(t),l&&l.d()}}}function Kr(e){let t,s,i,n,a,l="Placeholder: no data currently referenced.";return{c(){t=F("span"),s=Te("["),i=Te(e[0]),n=Te("]"),a=F("span"),a.textContent=l,this.h()},l(r){t=N(r,"SPAN",{class:!0});var o=K(t);s=Ce(o,"["),i=Ce(o,e[0]),n=Ce(o,"]"),a=N(o,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-ddarzq"!==Se(a)&&(a.textContent=l),o.forEach(m),this.h()},h(){h(a,"class","error-msg svelte-1mb9o01"),h(t,"class","placeholder svelte-1mb9o01")},m(e,l){S(e,t,l),D(t,s),D(t,i),D(t,n),D(t,a)},p(e,t){1&t&&Qe(i,e[0])},i:de,o:de,d(e){e&&m(t)}}}function Ln(e){let t,s;return t=new Nn({props:{description:e[1]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};2&s&&(i.description=e[1]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Zr(e){let t,s,i,n;const a=[Kr,Qr,Xr],l=[];function r(e,t){return e[0]?0:e[3]?2:1}return t=r(e),s=l[t]=a[t](e),{c(){s.c(),i=pe()},l(e){s.l(e),i=pe()},m(e,s){l[t].m(e,s),S(e,i,s),n=!0},p(e,[n]){let o=t;t=r(e),t===o?l[t].p(e,n):(Ke(),U(l[o],1,1,(()=>{l[o]=null})),Ze(),s=l[t],s?s.p(e,n):(s=l[t]=a[t](e),s.c()),M(s,1),s.m(i.parentNode,i))},i(e){n||(M(s),n=!0)},o(e){U(s),n=!1},d(e){e&&m(i),l[t].d(e)}}}function Jr(e,t,s){let i,n,a=de;e.$$.on_destroy.push((()=>a()));const{resolveColor:l}=Pt();let r,o,c,d,{data:m=null}=t,{row:p=0}=t,{column:h=null}=t,{value:u=null}=t,{placeholder:y=null}=t,{description:f}=t,{fmt:$}=t,{color:g}=t,v="",{redNegatives:x=!1}=t;return e.$$set=e=>{"data"in e&&s(7,m=e.data),"row"in e&&s(10,p=e.row),"column"in e&&s(8,h=e.column),"value"in e&&s(11,u=e.value),"placeholder"in e&&s(0,y=e.placeholder),"description"in e&&s(1,f=e.description),"fmt"in e&&s(12,$=e.fmt),"color"in e&&s(13,g=e.color),"redNegatives"in e&&s(9,x=e.redNegatives)},e.$$.update=()=>{var t;if(2304&e.$$.dirty&&s(8,h=h??u),21897&e.$$.dirty)try{if(s(3,c=void 0),!y){if(!m)throw Error("No data provided. If you referenced a query result, check that the name is correct.");{if("string"==typeof m)throw Error(`Received: data=${m}, expected: data={${m}}`);if(Array.isArray(m)||s(7,m=[m]),isNaN(p))throw Error("row must be a number (row="+p+")");try{Object.keys(m[p])[0]}catch{throw Error("Row "+p+" does not exist in the dataset")}s(8,h=h??Object.keys(m[p])[0]),Al(m,[h]),s(14,d=El(m,"array"));const e=d.filter((e=>{var t;return"date"===e.type&&!((null==(t=m[0])?void 0:t[e.id])instanceof Date)})).map((e=>e.id));for(let t=0;t<e.length;t++)s(7,m=os(m,e[t]));s(2,o=m[p][h]),s(14,d=d.filter((e=>e.id===h))),s(4,r=$?Ot($,null==(t=d[0].format)?void 0:t.valueType):d[0].format)}}}catch(e){if(s(3,c=e.message),console.error("[31m%s[0m",`Error in Value: ${c}`),Di)throw c}2304&e.$$.dirty&&u&&h&&console.warn('Both "value" and "column" were supplied as props to Value. "value" will be ignored.'),8192&e.$$.dirty&&(s(6,i=l(g)),a(),a=Tt(i,(e=>s(15,n=e)))),512&e.$$.dirty&&s(9,x="true"===x||!0===x),33284&e.$$.dirty&&(x||n)&&(x&&o<0?s(5,v="rgb(220 38 38)"):n&&s(5,v=n))},[y,f,o,c,r,v,i,m,h,x,p,u,$,g,d,n]}class xr extends lt{constructor(e){super(),it(this,e,Jr,Zr,tt,{data:7,row:10,column:8,value:11,placeholder:0,description:1,fmt:12,color:13,redNegatives:9})}}function pr(e){let t;const s=e[7].default,i=sl(s,e,e[8],null);return{c(){i&&i.c()},l(e){i&&i.l(e)},m(e,s){i&&i.m(e,s),t=!0},p(e,n){i&&i.p&&(!t||256&n)&&rl(i,s,e,e[8],t?ol(s,e[8],n,null):al(e[8]),null)},i(e){t||(M(i,e),t=!0)},o(e){U(i,e),t=!1},d(e){i&&i.d(e)}}}function $r(e){let t,s;const i=[e[4],{data:It.isQuery(e[11])?Array.from(e[11]):e[11]}];let n={$$slots:{default:[pr]},$$scope:{ctx:e}};for(let e=0;e<i.length;e+=1)n=Lt(n,i[e]);return t=new xr({props:n}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const n=2064&s?xl(i,[16&s&&pl(e[4]),2048&s&&{data:It.isQuery(e[11])?Array.from(e[11]):e[11]}]):{};256&s&&(n.$$scope={dirty:s,ctx:e}),t.$set(n)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function kn(e){let t,s;return t=new $l({props:{emptyMessage:e[2],emptySet:e[1],chartType:ia,isInitial:e[3]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};4&s&&(i.emptyMessage=e[2]),2&s&&(i.emptySet=e[1]),8&s&&(i.isInitial=e[3]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function ea(e){let t,s,i=!e[4].placeholder&&kn(e);return{c(){t=F("span"),i&&i.c(),this.h()},l(e){t=N(e,"SPAN",{slot:!0});var s=K(t);i&&i.l(s),s.forEach(m),this.h()},h(){h(t,"slot","empty")},m(e,n){S(e,t,n),i&&i.m(t,null),s=!0},p(e,s){e[4].placeholder?i&&(Ke(),U(i,1,1,(()=>{i=null})),Ze()):i?(i.p(e,s),16&s&&M(i,1)):(i=kn(e),i.c(),M(i,1),i.m(t,null))},i(e){s||(M(i),s=!0)},o(e){U(i),s=!1},d(e){e&&m(t),i&&i.d()}}}function ta(e){let t,s="Loading...";return{c(){t=F("span"),t.textContent=s,this.h()},l(e){t=N(e,"SPAN",{slot:!0,class:!0,"data-svelte-h":!0}),"svelte-89gxhc"!==Se(t)&&(t.textContent=s),this.h()},h(){h(t,"slot","skeleton"),h(t,"class","text-base-content-muted")},m(e,s){S(e,t,s)},p:de,d(e){e&&m(t)}}}function la(e){let t,s;return t=new Jl({props:{data:e[0],$$slots:{skeleton:[ta],empty:[ea],default:[$r,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0]},$$scope:{ctx:e}}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,[s]){const i={};1&s&&(i.data=e[0]),2334&s&&(i.$$scope={dirty:s,ctx:e}),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}let ia="Value";function na(e,t,s){let i,{$$slots:n={},$$scope:a}=t,{data:l}=t,{column:r}=t,{agg:o}=t;const c=It.isQuery(l)?l.hash:void 0;let d=(null==l?void 0:l.hash)===c,{emptySet:m}=t,{emptyMessage:p}=t;return e.$$set=e=>{s(10,t=Lt(Lt({},t),Ht(e))),"data"in e&&s(0,l=e.data),"column"in e&&s(5,r=e.column),"agg"in e&&s(6,o=e.agg),"emptySet"in e&&s(1,m=e.emptySet),"emptyMessage"in e&&s(2,p=e.emptyMessage),"$$scope"in e&&s(8,a=e.$$scope)},e.$$.update=()=>{97&e.$$.dirty&&o&&s(0,l=l.groupBy(void 0).agg({[o]:{col:r,as:r}})),1&e.$$.dirty&&s(3,d=(null==l?void 0:l.hash)===c),s(4,i=Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e))))},t=Ht(t),[l,m,p,d,i,r,o,n,a]}class Wl extends lt{constructor(e){super(),it(this,e,na,la,tt,{data:0,column:5,agg:6,emptySet:1,emptyMessage:2})}}function sa(e){let t;const s=e[6].default,i=sl(s,e,e[7],null);return{c(){i&&i.c()},l(e){i&&i.l(e)},m(e,s){i&&i.m(e,s),t=!0},p(e,n){i&&i.p&&(!t||128&n)&&rl(i,s,e,e[7],t?ol(s,e[7],n,null):al(e[7]),null)},i(e){t||(M(i,e),t=!0)},o(e){U(i,e),t=!1},d(e){i&&i.d(e)}}}function ra(e){let t,s;const i=[e[4],{data:It.isQuery(e[10])?Array.from(e[10]):e[10]},{queryID:e[5]}];let n={$$slots:{default:[sa]},$$scope:{ctx:e}};for(let e=0;e<i.length;e+=1)n=Lt(n,i[e]);return t=new us({props:n}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const n=1072&s?xl(i,[16&s&&pl(e[4]),1024&s&&{data:It.isQuery(e[10])?Array.from(e[10]):e[10]},32&s&&{queryID:e[5]}]):{};128&s&&(n.$$scope={dirty:s,ctx:e}),t.$set(n)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function aa(e){let t,s;return t=new $l({props:{slot:"empty",emptyMessage:e[2],emptySet:e[1],chartType:e[4].chartType,isInitial:e[3]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};4&s&&(i.emptyMessage=e[2]),2&s&&(i.emptySet=e[1]),16&s&&(i.chartType=e[4].chartType),8&s&&(i.isInitial=e[3]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function oa(e){let t,s;return t=new Pi({props:{slot:"error",title:ua,error:e[10].error.message}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};1024&s&&(i.error=e[10].error.message),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function fa(e){let t,s;return t=new Jl({props:{data:e[0],$$slots:{error:[oa,({loaded:e})=>({10:e}),({loaded:e})=>e?1024:0],empty:[aa],default:[ra,({loaded:e})=>({10:e}),({loaded:e})=>e?1024:0]},$$scope:{ctx:e}}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,[s]){const i={};1&s&&(i.data=e[0]),1182&s&&(i.$$scope={dirty:s,ctx:e}),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}let ua="Sparkline";function ca(e,t,s){let i,{$$slots:n={},$$scope:a}=t,{data:l}=t;const r=It.isQuery(l)?l.hash:void 0;let o=(null==l?void 0:l.hash)===r,{emptySet:c}=t,{emptyMessage:d}=t,m=null==l?void 0:l.id;return e.$$set=e=>{s(9,t=Lt(Lt({},t),Ht(e))),"data"in e&&s(0,l=e.data),"emptySet"in e&&s(1,c=e.emptySet),"emptyMessage"in e&&s(2,d=e.emptyMessage),"$$scope"in e&&s(7,a=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&s(3,o=(null==l?void 0:l.hash)===r),s(4,i={...Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e)))})},t=Ht(t),[l,c,d,o,i,m,n,a]}class da extends lt{constructor(e){super(),it(this,e,ca,fa,tt,{data:0,emptySet:1,emptyMessage:2})}}function ma(e){let t,s,i,n,a,l,r,o,c,d,p,u,y,f=e[23]&&Sn(e);const $=[ba,ya],g=[];function v(e,t){return e[22]?0:1}r=v(e),o=g[r]=$[r](e);let x=e[8]&&En(e),b=e[7]&&An(e);return{c(){t=F("p"),s=Te(e[3]),i=J(),f&&f.c(),a=J(),l=F("div"),o.c(),c=J(),x&&x.c(),p=J(),b&&b.c(),u=pe(),this.h()},l(n){t=N(n,"P",{class:!0});var r=K(t);s=Ce(r,e[3]),i=Z(r),f&&f.l(r),r.forEach(m),a=Z(n),l=N(n,"DIV",{class:!0});var d=K(l);o.l(d),c=Z(d),x&&x.l(d),d.forEach(m),p=Z(n),b&&b.l(n),u=pe(),this.h()},h(){h(t,"class",n=Sl("text-sm align-top leading-none",e[19])),h(l,"class",d=Sl("relative text-xl font-medium mt-1.5",e[20]))},m(e,n){S(e,t,n),D(t,s),D(t,i),f&&f.m(t,null),S(e,a,n),S(e,l,n),g[r].m(l,null),D(l,c),x&&x.m(l,null),S(e,p,n),b&&b.m(e,n),S(e,u,n),y=!0},p(e,i){(!y||8&i)&&Qe(s,e[3]),e[23]?f?(f.p(e,i),8388608&i&&M(f,1)):(f=Sn(e),f.c(),M(f,1),f.m(t,null)):f&&(Ke(),U(f,1,1,(()=>{f=null})),Ze()),(!y||524288&i&&n!==(n=Sl("text-sm align-top leading-none",e[19])))&&h(t,"class",n);let a=r;r=v(e),r===a?g[r].p(e,i):(Ke(),U(g[a],1,1,(()=>{g[a]=null})),Ze(),o=g[r],o?o.p(e,i):(o=g[r]=$[r](e),o.c()),M(o,1),o.m(l,c)),e[8]?x?(x.p(e,i),256&i&&M(x,1)):(x=En(e),x.c(),M(x,1),x.m(l,null)):x&&(Ke(),U(x,1,1,(()=>{x=null})),Ze()),(!y||1048576&i&&d!==(d=Sl("relative text-xl font-medium mt-1.5",e[20])))&&h(l,"class",d),e[7]?b?(b.p(e,i),128&i&&M(b,1)):(b=An(e),b.c(),M(b,1),b.m(u.parentNode,u)):b&&(Ke(),U(b,1,1,(()=>{b=null})),Ze())},i(e){y||(M(f),M(o),M(x),M(b),y=!0)},o(e){U(f),U(o),U(x),U(b),y=!1},d(e){e&&(m(t),m(a),m(l),m(p),m(u)),f&&f.d(),g[r].d(),x&&x.d(),b&&b.d(e)}}}function ha(e){let t,s;return t=new cs({props:{inputType:"BigValue",error:e[24],width:"148",height:"28"}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};16777216&s&&(i.error=e[24]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Sn(e){let t,s;return t=new Nn({props:{description:e[23],size:"3"}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};8388608&s&&(i.description=e[23]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function ya(e){let t,s;return t=new Wl({props:{data:e[0],column:e[6],fmt:e[13]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};1&s&&(i.data=e[0]),64&s&&(i.column=e[6]),8192&s&&(i.fmt=e[13]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function ba(e){let t,s,i,n;return s=new Wl({props:{data:e[0],column:e[6],fmt:e[13]}}),{c(){t=F("a"),oe(s.$$.fragment),this.h()},l(e){t=N(e,"A",{class:!0,href:!0});var i=K(t);ae(s.$$.fragment,i),i.forEach(m),this.h()},h(){h(t,"class","hover:bg-base-200"),h(t,"href",i=Ol(e[22]))},m(e,i){S(e,t,i),re(s,t,null),n=!0},p(e,a){const l={};1&a&&(l.data=e[0]),64&a&&(l.column=e[6]),8192&a&&(l.fmt=e[13]),s.$set(l),(!n||4194304&a&&i!==(i=Ol(e[22])))&&h(t,"href",i)},i(e){n||(M(s.$$.fragment,e),n=!0)},o(e){U(s.$$.fragment,e),n=!1},d(e){e&&m(t),se(s)}}}function En(e){let t,s;return t=new da({props:{height:"15",data:e[0],dateCol:e[8],valueCol:e[6],type:e[9],interactive:"true",color:e[25],valueFmt:e[13]??e[10],dateFmt:e[11],yScale:e[2],connectGroup:e[12]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};1&s&&(i.data=e[0]),256&s&&(i.dateCol=e[8]),64&s&&(i.valueCol=e[6]),512&s&&(i.type=e[9]),33554432&s&&(i.color=e[25]),9216&s&&(i.valueFmt=e[13]??e[10]),2048&s&&(i.dateFmt=e[11]),4&s&&(i.yScale=e[2]),4096&s&&(i.connectGroup=e[12]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function An(e){let t,s,i,n;const a=[_a,ga],l=[];function r(e,t){return e[1]?0:1}return t=r(e),s=l[t]=a[t](e),{c(){s.c(),i=pe()},l(e){s.l(e),i=pe()},m(e,s){l[t].m(e,s),S(e,i,s),n=!0},p(e,n){let o=t;t=r(e),t===o?l[t].p(e,n):(Ke(),U(l[o],1,1,(()=>{l[o]=null})),Ze(),s=l[t],s?s.p(e,n):(s=l[t]=a[t](e),s.c()),M(s,1),s.m(i.parentNode,i))},i(e){n||(M(s),n=!0)},o(e){U(s),n=!1},d(e){e&&m(i),l[t].d(e)}}}function ga(e){let t,s,i,n,a,l,r;const o=[Ta,Ca],c=[];function d(e,t){return e[22]?0:1}return s=d(e),i=c[s]=o[s](e),{c(){t=F("p"),i.c(),n=J(),a=F("span"),l=Te(e[4]),this.h()},l(s){t=N(s,"P",{class:!0});var r=K(t);i.l(r),n=Z(r),a=N(r,"SPAN",{});var o=K(a);l=Ce(o,e[4]),o.forEach(m),r.forEach(m),this.h()},h(){h(t,"class","text-xs font-sans /60 pt-[0.5px]")},m(e,i){S(e,t,i),c[s].m(t,null),D(t,n),D(t,a),D(a,l),r=!0},p(e,a){let m=s;s=d(e),s===m?c[s].p(e,a):(Ke(),U(c[m],1,1,(()=>{c[m]=null})),Ze(),i=c[s],i?i.p(e,a):(i=c[s]=o[s](e),i.c()),M(i,1),i.m(t,n)),(!r||16&a)&&Qe(l,e[4])},i(e){r||(M(i),r=!0)},o(e){U(i),r=!1},d(e){e&&m(t),c[s].d()}}}function _a(e){let t,s,i,n;return s=new ds({props:{data:e[0],column:e[7],fmt:e[14],fontClass:"text-xs",symbolPosition:"left",neutralMin:e[15],neutralMax:e[16],text:e[4],downIsGood:e[5]}}),{c(){t=F("p"),oe(s.$$.fragment),this.h()},l(e){t=N(e,"P",{class:!0});var i=K(t);ae(s.$$.fragment,i),i.forEach(m),this.h()},h(){h(t,"class",i=Sl("text-xs font-sans mt-1",e[21]))},m(e,i){S(e,t,i),re(s,t,null),n=!0},p(e,a){const l={};1&a&&(l.data=e[0]),128&a&&(l.column=e[7]),16384&a&&(l.fmt=e[14]),32768&a&&(l.neutralMin=e[15]),65536&a&&(l.neutralMax=e[16]),16&a&&(l.text=e[4]),32&a&&(l.downIsGood=e[5]),s.$set(l),(!n||2097152&a&&i!==(i=Sl("text-xs font-sans mt-1",e[21])))&&h(t,"class",i)},i(e){n||(M(s.$$.fragment,e),n=!0)},o(e){U(s.$$.fragment,e),n=!1},d(e){e&&m(t),se(s)}}}function Ca(e){let t,s;return t=new Wl({props:{data:e[0],column:e[7],fmt:e[14]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};1&s&&(i.data=e[0]),128&s&&(i.column=e[7]),16384&s&&(i.fmt=e[14]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function Ta(e){let t,s,i,n;return s=new Wl({props:{data:e[0],column:e[7],fmt:e[14]}}),{c(){t=F("a"),oe(s.$$.fragment),this.h()},l(e){t=N(e,"A",{class:!0,href:!0});var i=K(t);ae(s.$$.fragment,i),i.forEach(m),this.h()},h(){h(t,"class","hover:bg-base-200"),h(t,"href",i=Ol(e[22]))},m(e,i){S(e,t,i),re(s,t,null),n=!0},p(e,a){const l={};1&a&&(l.data=e[0]),128&a&&(l.column=e[7]),16384&a&&(l.fmt=e[14]),s.$set(l),(!n||4194304&a&&i!==(i=Ol(e[22])))&&h(t,"href",i)},i(e){n||(M(s.$$.fragment,e),n=!0)},o(e){U(s.$$.fragment,e),n=!1},d(e){e&&m(t),se(s)}}}function La(e){let t,s,i,n,a;const l=[ha,ma],r=[];function o(e,t){return e[24].length>0?0:1}return s=o(e),i=r[s]=l[s](e),{c(){t=F("div"),i.c(),this.h()},l(e){t=N(e,"DIV",{class:!0,style:!0});var s=K(t);i.l(s),s.forEach(m),this.h()},h(){h(t,"class","inline-block font-sans pt-2 pb-3 pl-0 mr-3 items-center align-top"),h(t,"style",n=`\n        min-width: ${e[18]};\n        max-width: ${e[17]};\n\t\t`)},m(e,i){S(e,t,i),r[s].m(t,null),a=!0},p(e,[c]){let d=s;s=o(e),s===d?r[s].p(e,c):(Ke(),U(r[d],1,1,(()=>{r[d]=null})),Ze(),i=r[s],i?i.p(e,c):(i=r[s]=l[s](e),i.c()),M(i,1),i.m(t,null)),(!a||393216&c&&n!==(n=`\n        min-width: ${e[18]};\n        max-width: ${e[17]};\n\t\t`))&&h(t,"style",n)},i(e){a||(M(i),a=!0)},o(e){U(i),a=!1},d(e){e&&m(t),r[s].d()}}}function ka(e,t,s){let i;const{resolveColor:n}=Pt();let{data:a}=t,{value:l=null}=t,{comparison:r=null}=t,{comparisonDelta:o=!0}=t,{sparkline:c=null}=t,{sparklineType:d="line"}=t,{sparklineColor:m}=t,{sparklineValueFmt:p}=t,{sparklineDateFmt:h}=t,{sparklineYScale:u=!1}=t,{connectGroup:y}=t,{fmt:f}=t,{comparisonFmt:$}=t,{title:g=null}=t,{comparisonTitle:v=null}=t,{downIsGood:x=!1}=t,{neutralMin:b=0}=t,{neutralMax:T=0}=t,{maxWidth:S="none"}=t,{minWidth:L="18%"}=t,{titleClass:w}=t,{valueClass:E}=t,{comparisonClass:A}=t,{link:C=null}=t,{description:M}=t,O=[];return e.$$set=e=>{"data"in e&&s(0,a=e.data),"value"in e&&s(6,l=e.value),"comparison"in e&&s(7,r=e.comparison),"comparisonDelta"in e&&s(1,o=e.comparisonDelta),"sparkline"in e&&s(8,c=e.sparkline),"sparklineType"in e&&s(9,d=e.sparklineType),"sparklineColor"in e&&s(26,m=e.sparklineColor),"sparklineValueFmt"in e&&s(10,p=e.sparklineValueFmt),"sparklineDateFmt"in e&&s(11,h=e.sparklineDateFmt),"sparklineYScale"in e&&s(2,u=e.sparklineYScale),"connectGroup"in e&&s(12,y=e.connectGroup),"fmt"in e&&s(13,f=e.fmt),"comparisonFmt"in e&&s(14,$=e.comparisonFmt),"title"in e&&s(3,g=e.title),"comparisonTitle"in e&&s(4,v=e.comparisonTitle),"downIsGood"in e&&s(5,x=e.downIsGood),"neutralMin"in e&&s(15,b=e.neutralMin),"neutralMax"in e&&s(16,T=e.neutralMax),"maxWidth"in e&&s(17,S=e.maxWidth),"minWidth"in e&&s(18,L=e.minWidth),"titleClass"in e&&s(19,w=e.titleClass),"valueClass"in e&&s(20,E=e.valueClass),"comparisonClass"in e&&s(21,A=e.comparisonClass),"link"in e&&s(22,C=e.link),"description"in e&&s(23,M=e.description)},e.$$.update=()=>{if(2&e.$$.dirty&&s(1,o="true"===o||!0===o),67108864&e.$$.dirty&&s(25,i=n(m)),4&e.$$.dirty&&s(2,u="true"===u||!0===u),32&e.$$.dirty&&s(5,x="true"===x||!0===x),16777689&e.$$.dirty)try{Array.isArray(a)||s(0,a=[a]),Al(a,[l]);let e=El(a,"array"),t=e.find((e=>e.id===l));if(s(3,g=g??(t?t.title:null)),null!==r){Al(a,[r]);let t=e.find((e=>e.id===r));s(4,v=v??(t?t.title:null))}null!==c&&Al(a,[c])}catch(e){if(s(24,O=[...O,e]),Di)throw O}},[a,o,u,g,v,x,l,r,c,d,p,h,y,f,$,b,T,S,L,w,E,A,C,M,O,i,m]}let Sa=class extends lt{constructor(e){super(),it(this,e,ka,La,tt,{data:0,value:6,comparison:7,comparisonDelta:1,sparkline:8,sparklineType:9,sparklineColor:26,sparklineValueFmt:10,sparklineDateFmt:11,sparklineYScale:2,connectGroup:12,fmt:13,comparisonFmt:14,title:3,comparisonTitle:4,downIsGood:5,neutralMin:15,neutralMax:16,maxWidth:17,minWidth:18,titleClass:19,valueClass:20,comparisonClass:21,link:22,description:23})}};function Ea(e){let t;const s=e[6].default,i=sl(s,e,e[7],null);return{c(){i&&i.c()},l(e){i&&i.l(e)},m(e,s){i&&i.m(e,s),t=!0},p(e,n){i&&i.p&&(!t||128&n)&&rl(i,s,e,e[7],t?ol(s,e[7],n,null):al(e[7]),null)},i(e){t||(M(i,e),t=!0)},o(e){U(i,e),t=!1},d(e){i&&i.d(e)}}}function Aa(e){let t,s;const i=[e[4],{data:It.isQuery(e[9])?Array.from(e[9]):e[9]}];let n={$$slots:{default:[Ea]},$$scope:{ctx:e}};for(let e=0;e<i.length;e+=1)n=Lt(n,i[e]);return t=new Sa({props:n}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const n=528&s?xl(i,[16&s&&pl(e[4]),512&s&&{data:It.isQuery(e[9])?Array.from(e[9]):e[9]}]):{};128&s&&(n.$$scope={dirty:s,ctx:e}),t.$set(n)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function wa(e){let t,s,i,n;return s=new ms({props:{error:e[9].error.message}}),{c(){t=F("div"),oe(s.$$.fragment),this.h()},l(e){t=N(e,"DIV",{slot:!0,class:!0,style:!0});var i=K(t);ae(s.$$.fragment,i),i.forEach(m),this.h()},h(){h(t,"slot","error"),h(t,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(t,"style",i=`\n\t\t\t\tmin-width: ${e[5].minWidth};\n\t\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`)},m(e,i){S(e,t,i),re(s,t,null),n=!0},p(e,a){const l={};512&a&&(l.error=e[9].error.message),s.$set(l),(!n||32&a&&i!==(i=`\n\t\t\t\tmin-width: ${e[5].minWidth};\n\t\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`))&&h(t,"style",i)},i(e){n||(M(s.$$.fragment,e),n=!0)},o(e){U(s.$$.fragment,e),n=!1},d(e){e&&m(t),se(s)}}}function Oa(e){let t,s,i,n;return s=new $l({props:{emptyMessage:e[2],emptySet:e[1],chartType:Da,isInitial:e[3]}}),{c(){t=F("div"),oe(s.$$.fragment),this.h()},l(e){t=N(e,"DIV",{slot:!0,class:!0,style:!0});var i=K(t);ae(s.$$.fragment,i),i.forEach(m),this.h()},h(){h(t,"slot","empty"),h(t,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(t,"style",i=`\n\t\t\t\tmin-width: ${e[5].minWidth};\n\t\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`)},m(e,i){S(e,t,i),re(s,t,null),n=!0},p(e,a){const l={};4&a&&(l.emptyMessage=e[2]),2&a&&(l.emptySet=e[1]),8&a&&(l.isInitial=e[3]),s.$set(l),(!n||32&a&&i!==(i=`\n\t\t\t\tmin-width: ${e[5].minWidth};\n\t\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`))&&h(t,"style",i)},i(e){n||(M(s.$$.fragment,e),n=!0)},o(e){U(s.$$.fragment,e),n=!1},d(e){e&&m(t),se(s)}}}function Ia(e){let t,s,i,n,a,l,r,o=(e[5].title??" ")+"";return a=new Wl({props:{column:e[5].value,fmt:e[5].fmt,data:e[9]}}),{c(){t=F("div"),s=F("p"),i=Te(o),n=J(),oe(a.$$.fragment),this.h()},l(e){t=N(e,"DIV",{class:!0,style:!0,slot:!0});var l=K(t);s=N(l,"P",{class:!0});var r=K(s);i=Ce(r,o),r.forEach(m),n=Z(l),ae(a.$$.fragment,l),l.forEach(m),this.h()},h(){h(s,"class","text-sm"),h(t,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(t,"style",l=`\n\t\t\tmin-width: ${e[5].minWidth};\n\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`),h(t,"slot","skeleton")},m(e,l){S(e,t,l),D(t,s),D(s,i),D(t,n),re(a,t,null),r=!0},p(e,s){(!r||32&s)&&o!==(o=(e[5].title??" ")+"")&&Qe(i,o);const n={};32&s&&(n.column=e[5].value),32&s&&(n.fmt=e[5].fmt),512&s&&(n.data=e[9]),a.$set(n),(!r||32&s&&l!==(l=`\n\t\t\tmin-width: ${e[5].minWidth};\n\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`))&&h(t,"style",l)},i(e){r||(M(a.$$.fragment,e),r=!0)},o(e){U(a.$$.fragment,e),r=!1},d(e){e&&m(t),se(a)}}}function Ma(e){let t,s;return t=new Jl({props:{data:e[0],$$slots:{skeleton:[Ia,({loaded:e})=>({9:e}),({loaded:e})=>e?512:0],empty:[Oa],error:[wa,({loaded:e})=>({9:e}),({loaded:e})=>e?512:0],default:[Aa,({loaded:e})=>({9:e}),({loaded:e})=>e?512:0]},$$scope:{ctx:e}}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,[s]){const i={};1&s&&(i.data=e[0]),702&s&&(i.$$scope={dirty:s,ctx:e}),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}let Da="Big Value";function Na(e,t,s){let i,{$$slots:n={},$$scope:a}=t,{data:l}=t;const r=It.isQuery(l)?l.hash:void 0;let o=(null==l?void 0:l.hash)===r,{emptySet:c}=t,{emptyMessage:d}=t;return e.$$set=e=>{s(5,t=Lt(Lt({},t),Ht(e))),"data"in e&&s(0,l=e.data),"emptySet"in e&&s(1,c=e.emptySet),"emptyMessage"in e&&s(2,d=e.emptyMessage),"$$scope"in e&&s(7,a=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&s(3,o=(null==l?void 0:l.hash)===r),s(4,i=Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e))))},t=Ht(t),[l,c,d,o,i,t,n,a]}class wn extends lt{constructor(e){super(),it(this,e,Na,Ma,tt,{data:0,emptySet:1,emptyMessage:2})}}function Fa(e,t,s){let i,n,a,l,r,o,c,d,m,p,h,u,y,f,$,g,v,x,b=de,T=de;e.$$.on_destroy.push((()=>b())),e.$$.on_destroy.push((()=>T()));let S=Ul(Ni);Nt(e,S,(e=>s(46,x=e)));let L=Ul(Fi);const{resolveColor:w}=Pt();let{y:E}=t;const A=!!E;let{y2:C}=t;const M=!!C;let{series:O}=t;const N=!!O;let D,{options:I}=t,{name:k}=t,{lineColor:F}=t,{lineWidth:U=2}=t,{lineType:P="solid"}=t,{lineOpacity:R}=t,{markers:z=!1}=t,{markerShape:B="circle"}=t,{markerSize:G=8}=t,{labels:K=!1}=t,{labelSize:H=11}=t,{labelPosition:Z="top"}=t,{labelColor:q}=t,{labelFmt:_}=t;_&&(D=Ot(_));let W,{yLabelFmt:J}=t;J&&(W=Ot(J));let j,{y2LabelFmt:V}=t;V&&(j=Ot(V));let{y2SeriesType:Y}=t,{showAllLabels:X=!1}=t,{handleMissing:Q="gap"}=t,{step:ee=!1}=t,{stepPosition:te="end"}=t,{seriesOrder:se}=t,{seriesLabelFmt:ie}=t;const ne={above:"top",below:"bottom",middle:"inside"},ae={above:"right",below:"left",middle:"inside"};let le=r?"right":"top";return In((()=>{L.update((e=>{if(r)e.yAxis={...e.yAxis,...$.xAxis},e.xAxis={...e.xAxis,...$.yAxis};else if(e.yAxis[0]={...e.yAxis[0],...$.yAxis},e.xAxis={...e.xAxis,...$.xAxis},C&&(e.yAxis[1]={...e.yAxis[1],show:!0},["line","bar","scatter"].includes(Y)))for(let t=0;t<m;t++)e.series[d+t].type=Y;return K&&(e.axisPointer={triggerEmphasis:!1}),e}))})),e.$$set=e=>{"y"in e&&s(3,E=e.y),"y2"in e&&s(4,C=e.y2),"series"in e&&s(5,O=e.series),"options"in e&&s(12,I=e.options),"name"in e&&s(6,k=e.name),"lineColor"in e&&s(13,F=e.lineColor),"lineWidth"in e&&s(14,U=e.lineWidth),"lineType"in e&&s(15,P=e.lineType),"lineOpacity"in e&&s(16,R=e.lineOpacity),"markers"in e&&s(7,z=e.markers),"markerShape"in e&&s(17,B=e.markerShape),"markerSize"in e&&s(18,G=e.markerSize),"labels"in e&&s(8,K=e.labels),"labelSize"in e&&s(19,H=e.labelSize),"labelPosition"in e&&s(9,Z=e.labelPosition),"labelColor"in e&&s(20,q=e.labelColor),"labelFmt"in e&&s(21,_=e.labelFmt),"yLabelFmt"in e&&s(22,J=e.yLabelFmt),"y2LabelFmt"in e&&s(23,V=e.y2LabelFmt),"y2SeriesType"in e&&s(24,Y=e.y2SeriesType),"showAllLabels"in e&&s(10,X=e.showAllLabels),"handleMissing"in e&&s(25,Q=e.handleMissing),"step"in e&&s(11,ee=e.step),"stepPosition"in e&&s(26,te=e.stepPosition),"seriesOrder"in e&&s(27,se=e.seriesOrder),"seriesLabelFmt"in e&&s(28,ie=e.seriesLabelFmt)},e.$$.update=()=>{if(8192&e.$$.dirty[0]&&(s(1,i=w(F)),b(),b=Tt(i,(e=>s(44,g=e)))),128&e.$$.dirty[0]&&s(7,z=qe(z)),256&e.$$.dirty[0]&&s(8,K=qe(K)),1048576&e.$$.dirty[0]&&(s(0,n=w(q)),T(),T=Tt(n,(e=>s(45,v=e)))),1024&e.$$.dirty[0]&&s(10,X=qe(X)),2048&e.$$.dirty[0]&&s(11,ee=qe(ee)),32768&e.$$.dirty[1]&&s(41,a=x.data),32768&e.$$.dirty[1]&&s(40,l=x.x),8&e.$$.dirty[0]|32768&e.$$.dirty[1]&&s(3,E=A?E:x.y),16&e.$$.dirty[0]|32768&e.$$.dirty[1]&&s(4,C=M?C:x.y2),32768&e.$$.dirty[1]&&s(34,r=x.swapXY),32768&e.$$.dirty[1]&&s(43,o=x.yFormat),32768&e.$$.dirty[1]&&s(42,c=x.y2Format),32768&e.$$.dirty[1]&&s(32,d=x.yCount),32768&e.$$.dirty[1]&&s(33,m=x.y2Count),32768&e.$$.dirty[1]&&s(35,p=x.xType),32768&e.$$.dirty[1]&&s(38,h=x.xMismatch),32768&e.$$.dirty[1]&&s(37,u=x.columnSummary),32&e.$$.dirty[0]|32768&e.$$.dirty[1]&&s(5,O=N?O:x.series),104&e.$$.dirty[0]|1600&e.$$.dirty[1])if(O||"object"==typeof E)try{s(41,a=Gl(a,l,E,O))}catch(e){console.warn("Failed to complete data",{e}),s(41,a=[])}else s(6,k=k??Xt(E,u[E].title));if(33554472&e.$$.dirty[0]|1536&e.$$.dirty[1]&&"zero"===Q)try{s(41,a=Gl(a,l,E,O,!0))}catch(e){console.warn("Failed to complete data",{e}),s(41,a=[])}512&e.$$.dirty[0]|8&e.$$.dirty[1]&&s(9,Z=(r?ae[Z]:ne[Z])??le),1712312192&e.$$.dirty[0]|30735&e.$$.dirty[1]&&s(39,y={type:"line",label:{show:K,formatter:e=>0===e.value[r?0:1]?"":ut(e.value[r?0:1],[W??D??o,j??D??c][Ri(e.componentIndex,d,m)]),fontSize:H,color:v,position:Z,padding:3},labelLayout:{hideOverlap:!X},connectNulls:"connect"===Q,emphasis:{focus:"series",endLabel:{show:!1},lineStyle:{opacity:1,width:3}},lineStyle:{width:parseInt(U),type:P,opacity:R},itemStyle:{color:g,opacity:R},showSymbol:K||z,symbol:B,symbolSize:K&&!z?0:G,step:!!ee&&te}),402653304&e.$$.dirty[0]|1992&e.$$.dirty[1]&&s(36,f=zn(a,l,E,O,r,y,k,h,u,se,void 0,void 0,C,ie)),32&e.$$.dirty[1]&&L.update((e=>(e.series.push(...f),e.legend.data.push(...f.map((e=>e.name.toString()))),e))),4096&e.$$.dirty[0]&&I&&L.update((e=>({...e,...I}))),16&e.$$.dirty[1]&&($={yAxis:{boundaryGap:["0%","1%"]},xAxis:{boundaryGap:["time"===p?"2%":"0%","2%"]}})},[n,i,S,E,C,O,k,z,K,Z,X,ee,I,F,U,P,R,B,G,H,q,_,J,V,Y,Q,te,se,ie,D,W,j,d,m,r,p,f,u,h,y,l,a,c,o,g,v,x]}class Pa extends lt{constructor(e){super(),it(this,e,Fa,null,tt,{y:3,y2:4,series:5,options:12,name:6,lineColor:13,lineWidth:14,lineType:15,lineOpacity:16,markers:7,markerShape:17,markerSize:18,labels:8,labelSize:19,labelPosition:9,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,showAllLabels:10,handleMissing:25,step:11,stepPosition:26,seriesOrder:27,seriesLabelFmt:28},null,[-1,-1])}}function Ra(e){let t,s,i;t=new Pa({props:{lineColor:e[73],lineWidth:e[38],lineOpacity:e[37],lineType:e[36],markers:e[40],markerShape:e[41],markerSize:e[42],handleMissing:e[43],step:e[44],stepPosition:e[45],labels:e[47],labelSize:e[48],labelPosition:e[49],labelColor:e[71],labelFmt:e[50],yLabelFmt:e[51],y2LabelFmt:e[52],showAllLabels:e[53],y2SeriesType:e[8],seriesOrder:e[62],seriesLabelFmt:e[64]}});const n=e[80].default,a=sl(n,e,e[81],null);return{c(){oe(t.$$.fragment),s=J(),a&&a.c()},l(e){ae(t.$$.fragment,e),s=Z(e),a&&a.l(e)},m(e,n){re(t,e,n),S(e,s,n),a&&a.m(e,n),i=!0},p(e,s){const l={};2048&s[2]&&(l.lineColor=e[73]),128&s[1]&&(l.lineWidth=e[38]),64&s[1]&&(l.lineOpacity=e[37]),32&s[1]&&(l.lineType=e[36]),512&s[1]&&(l.markers=e[40]),1024&s[1]&&(l.markerShape=e[41]),2048&s[1]&&(l.markerSize=e[42]),4096&s[1]&&(l.handleMissing=e[43]),8192&s[1]&&(l.step=e[44]),16384&s[1]&&(l.stepPosition=e[45]),65536&s[1]&&(l.labels=e[47]),131072&s[1]&&(l.labelSize=e[48]),262144&s[1]&&(l.labelPosition=e[49]),512&s[2]&&(l.labelColor=e[71]),524288&s[1]&&(l.labelFmt=e[50]),1048576&s[1]&&(l.yLabelFmt=e[51]),2097152&s[1]&&(l.y2LabelFmt=e[52]),4194304&s[1]&&(l.showAllLabels=e[53]),256&s[0]&&(l.y2SeriesType=e[8]),1&s[2]&&(l.seriesOrder=e[62]),4&s[2]&&(l.seriesLabelFmt=e[64]),t.$set(l),a&&a.p&&(!i||524288&s[2])&&rl(a,n,e,e[81],i?ol(n,e[81],s,null):al(e[81]),null)},i(e){i||(M(t.$$.fragment,e),M(a,e),i=!0)},o(e){U(t.$$.fragment,e),U(a,e),i=!1},d(e){e&&m(s),se(t,e),a&&a.d(e)}}}function Ba(e){let t,s;return t=new Bn({props:{data:e[0],x:e[1],y:e[2],y2:e[3],xFmt:e[10],yFmt:e[9],y2Fmt:e[11],series:e[4],xType:e[5],yLog:e[6],yLogBase:e[7],legend:e[14],xAxisTitle:e[15],yAxisTitle:e[16],y2AxisTitle:e[17],xGridlines:e[18],yGridlines:e[19],y2Gridlines:e[20],xAxisLabels:e[21],yAxisLabels:e[22],y2AxisLabels:e[23],xBaseline:e[24],yBaseline:e[25],y2Baseline:e[26],xTickMarks:e[27],yTickMarks:e[28],y2TickMarks:e[29],yAxisColor:e[70],y2AxisColor:e[69],yMin:e[30],yMax:e[31],yScale:e[32],y2Min:e[33],y2Max:e[34],y2Scale:e[35],title:e[12],subtitle:e[13],chartType:"Line Chart",sort:e[46],chartAreaHeight:e[39],colorPalette:e[72],echartsOptions:e[54],seriesOptions:e[55],printEchartsConfig:e[56],emptySet:e[57],emptyMessage:e[58],renderer:e[59],downloadableData:e[60],downloadableImage:e[61],connectGroup:e[63],seriesColors:e[68],leftPadding:e[65],rightPadding:e[66],xLabelWrap:e[67],$$slots:{default:[Ra]},$$scope:{ctx:e}}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};1&s[0]&&(i.data=e[0]),2&s[0]&&(i.x=e[1]),4&s[0]&&(i.y=e[2]),8&s[0]&&(i.y2=e[3]),1024&s[0]&&(i.xFmt=e[10]),512&s[0]&&(i.yFmt=e[9]),2048&s[0]&&(i.y2Fmt=e[11]),16&s[0]&&(i.series=e[4]),32&s[0]&&(i.xType=e[5]),64&s[0]&&(i.yLog=e[6]),128&s[0]&&(i.yLogBase=e[7]),16384&s[0]&&(i.legend=e[14]),32768&s[0]&&(i.xAxisTitle=e[15]),65536&s[0]&&(i.yAxisTitle=e[16]),131072&s[0]&&(i.y2AxisTitle=e[17]),262144&s[0]&&(i.xGridlines=e[18]),524288&s[0]&&(i.yGridlines=e[19]),1048576&s[0]&&(i.y2Gridlines=e[20]),2097152&s[0]&&(i.xAxisLabels=e[21]),4194304&s[0]&&(i.yAxisLabels=e[22]),8388608&s[0]&&(i.y2AxisLabels=e[23]),16777216&s[0]&&(i.xBaseline=e[24]),33554432&s[0]&&(i.yBaseline=e[25]),67108864&s[0]&&(i.y2Baseline=e[26]),134217728&s[0]&&(i.xTickMarks=e[27]),268435456&s[0]&&(i.yTickMarks=e[28]),536870912&s[0]&&(i.y2TickMarks=e[29]),256&s[2]&&(i.yAxisColor=e[70]),128&s[2]&&(i.y2AxisColor=e[69]),1073741824&s[0]&&(i.yMin=e[30]),1&s[1]&&(i.yMax=e[31]),2&s[1]&&(i.yScale=e[32]),4&s[1]&&(i.y2Min=e[33]),8&s[1]&&(i.y2Max=e[34]),16&s[1]&&(i.y2Scale=e[35]),4096&s[0]&&(i.title=e[12]),8192&s[0]&&(i.subtitle=e[13]),32768&s[1]&&(i.sort=e[46]),256&s[1]&&(i.chartAreaHeight=e[39]),1024&s[2]&&(i.colorPalette=e[72]),8388608&s[1]&&(i.echartsOptions=e[54]),16777216&s[1]&&(i.seriesOptions=e[55]),33554432&s[1]&&(i.printEchartsConfig=e[56]),67108864&s[1]&&(i.emptySet=e[57]),134217728&s[1]&&(i.emptyMessage=e[58]),268435456&s[1]&&(i.renderer=e[59]),536870912&s[1]&&(i.downloadableData=e[60]),1073741824&s[1]&&(i.downloadableImage=e[61]),2&s[2]&&(i.connectGroup=e[63]),64&s[2]&&(i.seriesColors=e[68]),8&s[2]&&(i.leftPadding=e[65]),16&s[2]&&(i.rightPadding=e[66]),32&s[2]&&(i.xLabelWrap=e[67]),256&s[0]|8355552&s[1]|526853&s[2]&&(i.$$scope={dirty:s,ctx:e}),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function za(e,t,s){let i,n,a,l,r,o,{$$slots:c={},$$scope:d}=t;const{resolveColor:m,resolveColorsObject:p,resolveColorPalette:h}=Pt();let{data:u}=t,{x:y}=t,{y:f}=t,{y2:$}=t,{series:g}=t,{xType:v}=t,{yLog:x}=t,{yLogBase:b}=t,{y2SeriesType:T}=t,{yFmt:S}=t,{xFmt:L}=t,{y2Fmt:w}=t,{title:E}=t,{subtitle:A}=t,{legend:C}=t,{xAxisTitle:M}=t,{yAxisTitle:O=($?"true":void 0)}=t,{y2AxisTitle:N=($?"true":void 0)}=t,{xGridlines:D}=t,{yGridlines:I}=t,{y2Gridlines:k}=t,{xAxisLabels:F}=t,{yAxisLabels:U}=t,{y2AxisLabels:P}=t,{xBaseline:R}=t,{yBaseline:z}=t,{y2Baseline:B}=t,{xTickMarks:G}=t,{yTickMarks:K}=t,{y2TickMarks:H}=t,{yMin:Z}=t,{yMax:q}=t,{yScale:_}=t,{y2Min:W}=t,{y2Max:J}=t,{y2Scale:j}=t,{lineColor:V}=t,{lineType:Y}=t,{lineOpacity:X}=t,{lineWidth:Q}=t,{chartAreaHeight:ee}=t,{markers:te}=t,{markerShape:se}=t,{markerSize:ie}=t,{handleMissing:ne}=t,{step:ae}=t,{stepPosition:le}=t,{sort:re}=t,{colorPalette:oe="default"}=t,{labels:ce}=t,{labelSize:de}=t,{labelPosition:me}=t,{labelColor:pe}=t,{labelFmt:he}=t,{yLabelFmt:ue}=t,{y2LabelFmt:ye}=t,{showAllLabels:fe}=t,{yAxisColor:$e}=t,{y2AxisColor:ge}=t,{echartsOptions:ve}=t,{seriesOptions:xe}=t,{printEchartsConfig:be=!1}=t,{emptySet:Te}=t,{emptyMessage:Se}=t,{renderer:Le}=t,{downloadableData:we}=t,{downloadableImage:Ee}=t,{seriesColors:Ae}=t,{seriesOrder:Ce}=t,{connectGroup:Me}=t,{seriesLabelFmt:Oe}=t,{leftPadding:Ne}=t,{rightPadding:De}=t,{xLabelWrap:Ie}=t;return e.$$set=e=>{"data"in e&&s(0,u=e.data),"x"in e&&s(1,y=e.x),"y"in e&&s(2,f=e.y),"y2"in e&&s(3,$=e.y2),"series"in e&&s(4,g=e.series),"xType"in e&&s(5,v=e.xType),"yLog"in e&&s(6,x=e.yLog),"yLogBase"in e&&s(7,b=e.yLogBase),"y2SeriesType"in e&&s(8,T=e.y2SeriesType),"yFmt"in e&&s(9,S=e.yFmt),"xFmt"in e&&s(10,L=e.xFmt),"y2Fmt"in e&&s(11,w=e.y2Fmt),"title"in e&&s(12,E=e.title),"subtitle"in e&&s(13,A=e.subtitle),"legend"in e&&s(14,C=e.legend),"xAxisTitle"in e&&s(15,M=e.xAxisTitle),"yAxisTitle"in e&&s(16,O=e.yAxisTitle),"y2AxisTitle"in e&&s(17,N=e.y2AxisTitle),"xGridlines"in e&&s(18,D=e.xGridlines),"yGridlines"in e&&s(19,I=e.yGridlines),"y2Gridlines"in e&&s(20,k=e.y2Gridlines),"xAxisLabels"in e&&s(21,F=e.xAxisLabels),"yAxisLabels"in e&&s(22,U=e.yAxisLabels),"y2AxisLabels"in e&&s(23,P=e.y2AxisLabels),"xBaseline"in e&&s(24,R=e.xBaseline),"yBaseline"in e&&s(25,z=e.yBaseline),"y2Baseline"in e&&s(26,B=e.y2Baseline),"xTickMarks"in e&&s(27,G=e.xTickMarks),"yTickMarks"in e&&s(28,K=e.yTickMarks),"y2TickMarks"in e&&s(29,H=e.y2TickMarks),"yMin"in e&&s(30,Z=e.yMin),"yMax"in e&&s(31,q=e.yMax),"yScale"in e&&s(32,_=e.yScale),"y2Min"in e&&s(33,W=e.y2Min),"y2Max"in e&&s(34,J=e.y2Max),"y2Scale"in e&&s(35,j=e.y2Scale),"lineColor"in e&&s(74,V=e.lineColor),"lineType"in e&&s(36,Y=e.lineType),"lineOpacity"in e&&s(37,X=e.lineOpacity),"lineWidth"in e&&s(38,Q=e.lineWidth),"chartAreaHeight"in e&&s(39,ee=e.chartAreaHeight),"markers"in e&&s(40,te=e.markers),"markerShape"in e&&s(41,se=e.markerShape),"markerSize"in e&&s(42,ie=e.markerSize),"handleMissing"in e&&s(43,ne=e.handleMissing),"step"in e&&s(44,ae=e.step),"stepPosition"in e&&s(45,le=e.stepPosition),"sort"in e&&s(46,re=e.sort),"colorPalette"in e&&s(75,oe=e.colorPalette),"labels"in e&&s(47,ce=e.labels),"labelSize"in e&&s(48,de=e.labelSize),"labelPosition"in e&&s(49,me=e.labelPosition),"labelColor"in e&&s(76,pe=e.labelColor),"labelFmt"in e&&s(50,he=e.labelFmt),"yLabelFmt"in e&&s(51,ue=e.yLabelFmt),"y2LabelFmt"in e&&s(52,ye=e.y2LabelFmt),"showAllLabels"in e&&s(53,fe=e.showAllLabels),"yAxisColor"in e&&s(77,$e=e.yAxisColor),"y2AxisColor"in e&&s(78,ge=e.y2AxisColor),"echartsOptions"in e&&s(54,ve=e.echartsOptions),"seriesOptions"in e&&s(55,xe=e.seriesOptions),"printEchartsConfig"in e&&s(56,be=e.printEchartsConfig),"emptySet"in e&&s(57,Te=e.emptySet),"emptyMessage"in e&&s(58,Se=e.emptyMessage),"renderer"in e&&s(59,Le=e.renderer),"downloadableData"in e&&s(60,we=e.downloadableData),"downloadableImage"in e&&s(61,Ee=e.downloadableImage),"seriesColors"in e&&s(79,Ae=e.seriesColors),"seriesOrder"in e&&s(62,Ce=e.seriesOrder),"connectGroup"in e&&s(63,Me=e.connectGroup),"seriesLabelFmt"in e&&s(64,Oe=e.seriesLabelFmt),"leftPadding"in e&&s(65,Ne=e.leftPadding),"rightPadding"in e&&s(66,De=e.rightPadding),"xLabelWrap"in e&&s(67,Ie=e.xLabelWrap),"$$scope"in e&&s(81,d=e.$$scope)},e.$$.update=()=>{4096&e.$$.dirty[2]&&s(73,i=m(V)),8192&e.$$.dirty[2]&&s(72,n=h(oe)),16384&e.$$.dirty[2]&&s(71,a=m(pe)),32768&e.$$.dirty[2]&&s(70,l=m($e)),65536&e.$$.dirty[2]&&s(69,r=m(ge)),131072&e.$$.dirty[2]&&s(68,o=p(Ae))},[u,y,f,$,g,v,x,b,T,S,L,w,E,A,C,M,O,N,D,I,k,F,U,P,R,z,B,G,K,H,Z,q,_,W,J,j,Y,X,Q,ee,te,se,ie,ne,ae,le,re,ce,de,me,he,ue,ye,fe,ve,xe,be,Te,Se,Le,we,Ee,Ce,Me,Oe,Ne,De,Ie,o,r,l,a,n,i,V,oe,pe,$e,ge,Ae,c,d]}class Ga extends lt{constructor(e){super(),it(this,e,za,Ba,tt,{data:0,x:1,y:2,y2:3,series:4,xType:5,yLog:6,yLogBase:7,y2SeriesType:8,yFmt:9,xFmt:10,y2Fmt:11,title:12,subtitle:13,legend:14,xAxisTitle:15,yAxisTitle:16,y2AxisTitle:17,xGridlines:18,yGridlines:19,y2Gridlines:20,xAxisLabels:21,yAxisLabels:22,y2AxisLabels:23,xBaseline:24,yBaseline:25,y2Baseline:26,xTickMarks:27,yTickMarks:28,y2TickMarks:29,yMin:30,yMax:31,yScale:32,y2Min:33,y2Max:34,y2Scale:35,lineColor:74,lineType:36,lineOpacity:37,lineWidth:38,chartAreaHeight:39,markers:40,markerShape:41,markerSize:42,handleMissing:43,step:44,stepPosition:45,sort:46,colorPalette:75,labels:47,labelSize:48,labelPosition:49,labelColor:76,labelFmt:50,yLabelFmt:51,y2LabelFmt:52,showAllLabels:53,yAxisColor:77,y2AxisColor:78,echartsOptions:54,seriesOptions:55,printEchartsConfig:56,emptySet:57,emptyMessage:58,renderer:59,downloadableData:60,downloadableImage:61,seriesColors:79,seriesOrder:62,connectGroup:63,seriesLabelFmt:64,leftPadding:65,rightPadding:66,xLabelWrap:67},null,[-1,-1,-1])}}function Ua(e){let t,s,i=we.title+"";return{c(){t=F("h1"),s=Te(i),this.h()},l(e){t=N(e,"H1",{class:!0});var n=K(t);s=Ce(n,i),n.forEach(m),this.h()},h(){h(t,"class","title")},m(e,i){S(e,t,i),D(t,s)},p:de,d(e){e&&m(t)}}}function Ha(e){return{c(){this.h()},l(e){this.h()},h(){document.title="Evidence"},m:de,p:de,d:de}}function va(e){let t,s,i,n,a;return document.title=t=we.title,{c(){s=J(),i=F("meta"),n=J(),a=F("meta"),this.h()},l(e){s=Z(e),i=N(e,"META",{property:!0,content:!0}),n=Z(e),a=N(e,"META",{name:!0,content:!0}),this.h()},h(){var e,t;h(i,"property","og:title"),h(i,"content",(null==(e=we.og)?void 0:e.title)??we.title),h(a,"name","twitter:title"),h(a,"content",(null==(t=we.og)?void 0:t.title)??we.title)},m(e,t){S(e,s,t),S(e,i,t),S(e,n,t),S(e,a,t)},p(e,s){0&s&&t!==(t=we.title)&&(document.title=t)},d(e){e&&(m(s),m(i),m(n),m(a))}}}function qa(e){var t,s;let i,n,a=(we.description||(null==(t=we.og)?void 0:t.description))&&Va(),l=(null==(s=we.og)?void 0:s.image)&&Wa();return{c(){a&&a.c(),i=J(),l&&l.c(),n=pe()},l(e){a&&a.l(e),i=Z(e),l&&l.l(e),n=pe()},m(e,t){a&&a.m(e,t),S(e,i,t),l&&l.m(e,t),S(e,n,t)},p(e,t){var s,i;(we.description||null!=(s=we.og)&&s.description)&&a.p(e,t),null!=(i=we.og)&&i.image&&l.p(e,t)},d(e){e&&(m(i),m(n)),a&&a.d(e),l&&l.d(e)}}}function Va(e){let t,s,i,n,a;return{c(){t=F("meta"),s=J(),i=F("meta"),n=J(),a=F("meta"),this.h()},l(e){t=N(e,"META",{name:!0,content:!0}),s=Z(e),i=N(e,"META",{property:!0,content:!0}),n=Z(e),a=N(e,"META",{name:!0,content:!0}),this.h()},h(){var e,s,n;h(t,"name","description"),h(t,"content",we.description??(null==(e=we.og)?void 0:e.description)),h(i,"property","og:description"),h(i,"content",(null==(s=we.og)?void 0:s.description)??we.description),h(a,"name","twitter:description"),h(a,"content",(null==(n=we.og)?void 0:n.description)??we.description)},m(e,l){S(e,t,l),S(e,s,l),S(e,i,l),S(e,n,l),S(e,a,l)},p:de,d(e){e&&(m(t),m(s),m(i),m(n),m(a))}}}function Wa(e){let t,s,i;return{c(){t=F("meta"),s=J(),i=F("meta"),this.h()},l(e){t=N(e,"META",{property:!0,content:!0}),s=Z(e),i=N(e,"META",{name:!0,content:!0}),this.h()},h(){var e,s;h(t,"property","og:image"),h(t,"content",Ol(null==(e=we.og)?void 0:e.image)),h(i,"name","twitter:image"),h(i,"content",Ol(null==(s=we.og)?void 0:s.image))},m(e,n){S(e,t,n),S(e,s,n),S(e,i,n)},p:de,d(e){e&&(m(t),m(s),m(i))}}}function On(e){let t,s;return t=new er({props:{queryID:"sample_sales",queryResult:e[0]}}),{c(){oe(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,i){re(t,e,i),s=!0},p(e,s){const i={};1&s&&(i.queryResult=e[0]),t.$set(i)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){se(t,e)}}}function ja(e){let t,s,i,n,a,l,r,o,c,d,p,u,y,f,$,g,x,b,T,L,w,E,A,C,O,I,k,P,R,z,B,G,H,q,_,W,j,V,Y,X,Q,ee,te,ie,ne,le,ce,de,me,he,ue,ye,fe,$e,ge,ve,xe,be,Te,Le,Ee,Ae,Ce,Me,Oe,Ne,De,Ie,ke,Fe,Ue,Pe,Re,ze='<a href="#domo-data-loader">Domo Data Loader</a>',Be="Load datasets from Domo into DuckDB for analysis with Evidence.",Ge='<h3 class="svelte-16omzez">🚀 Enhanced Domo DDX Integration</h3> <p>This app now supports real DuckDB integration, multiple dataset loading, iframe compatibility, and data visualizations!</p>',He='<label for="dataset-search" class="svelte-16omzez">Search Datasets:</label> <input id="dataset-search" type="text" placeholder="Type to search datasets..." class="dataset-search svelte-16omzez"/> <div class="dataset-stats" id="dataset-stats"><span id="dataset-count">Loading datasets...</span></div>',qe="Select Dataset:",_e="Choose a dataset...",We='<h4>📊 Dataset Information</h4> <div id="preview-content" class="preview-content"><div id="dataset-info" class="dataset-info-grid"></div> <div class="dataset-tabs"><button class="tab-button active" data-tab="schema">Schema</button> <button class="tab-button" data-tab="sample">Sample Data</button> <button class="tab-button" data-tab="metadata">Metadata</button></div> <div id="schema-tab" class="tab-content active"><div id="schema-table" class="schema-table"></div></div> <div id="sample-tab" class="tab-content"><div class="preview-actions svelte-16omzez"><button id="preview-btn" class="btn btn-secondary svelte-16omzez">🔍 Load Sample Data</button> <span class="preview-note">Shows first 10 rows</span></div> <div id="data-preview" class="data-preview svelte-16omzez" style="display: none;"></div></div> <div id="metadata-tab" class="tab-content"><div id="dataset-metadata" class="dataset-metadata"></div></div></div>',Je="⚙️ Loading Configuration",je='<label for="table-name" class="svelte-16omzez">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-16omzez"/> <small class="field-help">Use lowercase letters, numbers, and underscores only</small>',Ve="Refresh Mode:",Ye="Replace existing data",Xe="Append to existing data",Qe="Choose how to handle existing data",et='<label for="row-limit" class="svelte-16omzez">Row Limit (optional):</label> <input id="row-limit" type="number" placeholder="Leave empty for all rows" min="1" max="1000000" class="svelte-16omzez"/> <small class="field-help">Limit rows for testing (max 1M)</small>',tt='<label class="svelte-16omzez"><input type="checkbox" id="create-index" checked="" class="svelte-16omzez"/>\n            Create indexes for better performance</label>',st='<div class="action-buttons"><button id="validate-config-btn" class="btn btn-secondary svelte-16omzez">✅ Validate Configuration</button> <button id="load-dataset-btn" class="btn btn-primary svelte-16omzez">📊 Load Dataset into DuckDB</button></div> <div id="validation-results" class="validation-results" style="display: none;"></div>',it='<div class="loading-spinner svelte-16omzez"></div> <p id="loading-message" class="svelte-16omzez">Loading...</p> <div class="progress-bar"><div class="progress-fill" id="progress-fill"></div></div>',nt='<h2 class="markdown">📚 Loaded Datasets</h2> <div class="loaded-datasets-header"><p>Datasets currently available in DuckDB for analysis:</p> <button id="refresh-loaded-btn" class="btn btn-secondary btn-small svelte-16omzez">🔄 Refresh</button></div> <div id="loaded-datasets-list" class="loaded-datasets-grid"></div>',at='<h2 class="markdown">🔍 Query Your Data</h2> <div class="query-interface"><div class="query-input"><label for="sql-query">SQL Query:</label> <textarea id="sql-query" placeholder="SELECT * FROM your_table_name LIMIT 10;" rows="4"></textarea> <div class="query-actions"><button id="run-query-btn" class="btn btn-primary svelte-16omzez">▶️ Run Query</button> <button id="clear-query-btn" class="btn btn-secondary svelte-16omzez">🗑️ Clear</button></div></div> <div id="query-results" class="query-results" style="display: none;"></div></div>',lt='<a href="#sample-evidence-visualizations">Sample Evidence Visualizations</a>',rt="Once you load data, you can create Evidence-style visualizations. Here are some examples:",ot='<a href="#sales-performance-dashboard">Sales Performance Dashboard</a>',ct='<a href="#key-metrics">Key Metrics</a>',dt='<strong class="markdown">Next Steps:</strong>',mt='<li class="markdown">Select a dataset from the dropdown above</li> <li class="markdown">Configure loading options</li> <li class="markdown">Load the data into DuckDB</li> <li class="markdown">Run SQL queries to explore your data</li> <li class="markdown">Create Evidence visualizations with your results</li>',pt=typeof we<"u"&&we.title&&!0!==we.hide_title&&Ua(),ht=(typeof we<"u"&&we.title?va:Ha)(e),ut="object"==typeof we&&qa(),yt=e[0]&&On(e);return be=new Yr({props:{data:e[0],x:"product",y:"revenue",title:"Revenue by Product"}}),Le=new Ga({props:{data:e[0],x:"month",y:"units_sold",series:"product",title:"Units Sold Trend"}}),Me=new wn({props:{data:e[0],value:"revenue",title:"Total Revenue",fmt:"$#,##0"}}),Ne=new wn({props:{data:e[0],value:"units_sold",title:"Total Units",fmt:"#,##0"}}),{c(){pt&&pt.c(),t=J(),ht.c(),s=F("meta"),i=F("meta"),ut&&ut.c(),n=pe(),a=J(),l=F("h1"),l.innerHTML=ze,r=J(),o=F("p"),o.textContent=Be,c=J(),d=F("div"),d.innerHTML=Ge,p=J(),u=F("div"),y=F("div"),f=F("div"),f.innerHTML=He,$=J(),g=F("div"),x=F("label"),x.textContent=qe,b=J(),T=F("select"),L=F("option"),L.textContent=_e,w=J(),E=F("div"),E.innerHTML=We,A=J(),C=F("div"),O=F("h4"),O.textContent=Je,I=J(),k=F("div"),P=F("div"),P.innerHTML=je,R=J(),z=F("div"),B=F("label"),B.textContent=Ve,G=J(),H=F("select"),q=F("option"),q.textContent=Ye,_=F("option"),_.textContent=Xe,W=J(),j=F("small"),j.textContent=Qe,V=J(),Y=F("div"),Y.innerHTML=et,X=J(),Q=F("div"),Q.innerHTML=tt,ee=J(),te=F("div"),te.innerHTML=st,ie=J(),ne=F("div"),ne.innerHTML=it,le=J(),ce=F("div"),ce.innerHTML=nt,de=J(),me=F("div"),me.innerHTML=at,he=J(),ue=F("h2"),ue.innerHTML=lt,ye=J(),fe=F("p"),fe.textContent=rt,$e=J(),ge=F("h3"),ge.innerHTML=ot,ve=J(),yt&&yt.c(),xe=J(),oe(be.$$.fragment),Te=J(),oe(Le.$$.fragment),Ee=J(),Ae=F("h3"),Ae.innerHTML=ct,Ce=J(),oe(Me.$$.fragment),Oe=J(),oe(Ne.$$.fragment),De=J(),Ie=F("hr"),ke=J(),Fe=F("p"),Fe.innerHTML=dt,Ue=J(),Pe=F("ol"),Pe.innerHTML=mt,this.h()},l(e){pt&&pt.l(e),t=Z(e);const h=Xn("svelte-2igo1p",document.head);ht.l(h),s=N(h,"META",{name:!0,content:!0}),i=N(h,"META",{name:!0,content:!0}),ut&&ut.l(h),n=pe(),h.forEach(m),a=Z(e),l=N(e,"H1",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-94lco6"!==Se(l)&&(l.innerHTML=ze),r=Z(e),o=N(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1s6ws79"!==Se(o)&&(o.textContent=Be),c=Z(e),d=N(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1top7fi"!==Se(d)&&(d.innerHTML=Ge),p=Z(e),u=N(e,"DIV",{class:!0});var v=K(u);y=N(v,"DIV",{id:!0,class:!0});var S=K(y);f=N(S,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-qgc35d"!==Se(f)&&(f.innerHTML=He),$=Z(S),g=N(S,"DIV",{class:!0});var M=K(g);x=N(M,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-1fci9ty"!==Se(x)&&(x.textContent=qe),b=Z(M),T=N(M,"SELECT",{id:!0,class:!0});var D=K(T);L=N(D,"OPTION",{"data-svelte-h":!0}),"svelte-59d9xk"!==Se(L)&&(L.textContent=_e),D.forEach(m),M.forEach(m),w=Z(S),E=N(S,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-19tcyh0"!==Se(E)&&(E.innerHTML=We),A=Z(S),C=N(S,"DIV",{id:!0,class:!0,style:!0});var F=K(C);O=N(F,"H4",{"data-svelte-h":!0}),"svelte-6xztzo"!==Se(O)&&(O.textContent=Je),I=Z(F),k=N(F,"DIV",{class:!0});var U=K(k);P=N(U,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-199xsoc"!==Se(P)&&(P.innerHTML=je),R=Z(U),z=N(U,"DIV",{class:!0});var J=K(z);B=N(J,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-p1qydn"!==Se(B)&&(B.textContent=Ve),G=Z(J),H=N(J,"SELECT",{id:!0,class:!0});var se=K(H);q=N(se,"OPTION",{"data-svelte-h":!0}),"svelte-qvzdub"!==Se(q)&&(q.textContent=Ye),_=N(se,"OPTION",{"data-svelte-h":!0}),"svelte-idsvi6"!==Se(_)&&(_.textContent=Xe),se.forEach(m),W=Z(J),j=N(J,"SMALL",{class:!0,"data-svelte-h":!0}),"svelte-fpi9b6"!==Se(j)&&(j.textContent=Qe),J.forEach(m),V=Z(U),Y=N(U,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-15v44ck"!==Se(Y)&&(Y.innerHTML=et),X=Z(U),Q=N(U,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-5ivc8q"!==Se(Q)&&(Q.innerHTML=tt),U.forEach(m),F.forEach(m),ee=Z(S),te=N(S,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-68tw8y"!==Se(te)&&(te.innerHTML=st),ie=Z(S),ne=N(S,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-1oq3jl0"!==Se(ne)&&(ne.innerHTML=it),S.forEach(m),v.forEach(m),le=Z(e),ce=N(e,"DIV",{id:!0,style:!0,"data-svelte-h":!0}),"svelte-n8w8em"!==Se(ce)&&(ce.innerHTML=nt),de=Z(e),me=N(e,"DIV",{id:!0,style:!0,"data-svelte-h":!0}),"svelte-1i72rmk"!==Se(me)&&(me.innerHTML=at),he=Z(e),ue=N(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1t5nmyw"!==Se(ue)&&(ue.innerHTML=lt),ye=Z(e),fe=N(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-nbtkwk"!==Se(fe)&&(fe.textContent=rt),$e=Z(e),ge=N(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-bguwp4"!==Se(ge)&&(ge.innerHTML=ot),ve=Z(e),yt&&yt.l(e),xe=Z(e),ae(be.$$.fragment,e),Te=Z(e),ae(Le.$$.fragment,e),Ee=Z(e),Ae=N(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-aivela"!==Se(Ae)&&(Ae.innerHTML=ct),Ce=Z(e),ae(Me.$$.fragment,e),Oe=Z(e),ae(Ne.$$.fragment,e),De=Z(e),Ie=N(e,"HR",{class:!0}),ke=Z(e),Fe=N(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1mg5bp7"!==Se(Fe)&&(Fe.innerHTML=dt),Ue=Z(e),Pe=N(e,"OL",{class:!0,"data-svelte-h":!0}),"svelte-1ei1c0u"!==Se(Pe)&&(Pe.innerHTML=mt),this.h()},h(){h(s,"name","twitter:card"),h(s,"content","summary_large_image"),h(i,"name","twitter:site"),h(i,"content","@evidence_dev"),h(l,"class","markdown"),h(l,"id","domo-data-loader"),h(o,"class","markdown"),h(d,"class","dev-banner svelte-16omzez"),h(f,"class","workflow-step svelte-16omzez"),h(x,"for","dataset-selector"),h(x,"class","svelte-16omzez"),L.__value="",Bl(L,L.__value),h(T,"id","dataset-selector"),h(T,"class","dataset-dropdown svelte-16omzez"),h(g,"class","workflow-step svelte-16omzez"),h(E,"id","dataset-preview"),h(E,"class","dataset-preview svelte-16omzez"),v(E,"display","none"),h(P,"class","config-item"),h(B,"for","refresh-mode"),h(B,"class","svelte-16omzez"),q.__value="replace",Bl(q,q.__value),_.__value="append",Bl(_,_.__value),h(H,"id","refresh-mode"),h(H,"class","svelte-16omzez"),h(j,"class","field-help"),h(z,"class","config-item"),h(Y,"class","config-item"),h(Q,"class","config-item"),h(k,"class","config-grid svelte-16omzez"),h(C,"id","loading-config"),h(C,"class","workflow-step svelte-16omzez"),v(C,"display","none"),h(te,"id","workflow-actions"),h(te,"class","workflow-actions svelte-16omzez"),v(te,"display","none"),h(ne,"id","loading-status"),h(ne,"class","loading-status svelte-16omzez"),v(ne,"display","none"),h(y,"id","domo-workflow-picker"),h(y,"class","workflow-picker svelte-16omzez"),h(u,"class","workflow-picker-section svelte-16omzez"),h(ce,"id","loaded-datasets-section"),v(ce,"display","none"),h(me,"id","query-section"),v(me,"display","none"),h(ue,"class","markdown"),h(ue,"id","sample-evidence-visualizations"),h(fe,"class","markdown"),h(ge,"class","markdown"),h(ge,"id","sales-performance-dashboard"),h(Ae,"class","markdown"),h(Ae,"id","key-metrics"),h(Ie,"class","markdown"),h(Fe,"class","markdown"),h(Pe,"class","markdown")},m(e,m){pt&&pt.m(e,m),S(e,t,m),ht.m(document.head,null),D(document.head,s),D(document.head,i),ut&&ut.m(document.head,null),D(document.head,n),S(e,a,m),S(e,l,m),S(e,r,m),S(e,o,m),S(e,c,m),S(e,d,m),S(e,p,m),S(e,u,m),D(u,y),D(y,f),D(y,$),D(y,g),D(g,x),D(g,b),D(g,T),D(T,L),D(y,w),D(y,E),D(y,A),D(y,C),D(C,O),D(C,I),D(C,k),D(k,P),D(k,R),D(k,z),D(z,B),D(z,G),D(z,H),D(H,q),D(H,_),D(z,W),D(z,j),D(k,V),D(k,Y),D(k,X),D(k,Q),D(y,ee),D(y,te),D(y,ie),D(y,ne),S(e,le,m),S(e,ce,m),S(e,de,m),S(e,me,m),S(e,he,m),S(e,ue,m),S(e,ye,m),S(e,fe,m),S(e,$e,m),S(e,ge,m),S(e,ve,m),yt&&yt.m(e,m),S(e,xe,m),re(be,e,m),S(e,Te,m),re(Le,e,m),S(e,Ee,m),S(e,Ae,m),S(e,Ce,m),re(Me,e,m),S(e,Oe,m),re(Ne,e,m),S(e,De,m),S(e,Ie,m),S(e,ke,m),S(e,Fe,m),S(e,Ue,m),S(e,Pe,m),Re=!0},p(e,[t]){typeof we<"u"&&we.title&&!0!==we.hide_title&&pt.p(e,t),ht.p(e,t),"object"==typeof we&&ut.p(e,t),e[0]?yt?(yt.p(e,t),1&t&&M(yt,1)):(yt=On(e),yt.c(),M(yt,1),yt.m(xe.parentNode,xe)):yt&&(Ke(),U(yt,1,1,(()=>{yt=null})),Ze());const s={};1&t&&(s.data=e[0]),be.$set(s);const i={};1&t&&(i.data=e[0]),Le.$set(i);const n={};1&t&&(n.data=e[0]),Me.$set(n);const a={};1&t&&(a.data=e[0]),Ne.$set(a)},i(e){Re||(M(yt),M(be.$$.fragment,e),M(Le.$$.fragment,e),M(Me.$$.fragment,e),M(Ne.$$.fragment,e),Re=!0)},o(e){U(yt),U(be.$$.fragment,e),U(Le.$$.fragment,e),U(Me.$$.fragment,e),U(Ne.$$.fragment,e),Re=!1},d(e){e&&(m(t),m(a),m(l),m(r),m(o),m(c),m(d),m(p),m(u),m(le),m(ce),m(de),m(me),m(he),m(ue),m(ye),m(fe),m($e),m(ge),m(ve),m(xe),m(Te),m(Ee),m(Ae),m(Ce),m(Oe),m(De),m(Ie),m(ke),m(Fe),m(Ue),m(Pe)),pt&&pt.d(e),ht.d(e),m(s),m(i),ut&&ut.d(e),m(n),yt&&yt.d(e),se(be,e),se(Le,e),se(Me,e),se(Ne,e)}}}const we={title:"Domo Data Loader"};function Ya(e,t,s){let i,n;Nt(e,Fn,(e=>s(7,i=e))),Nt(e,Xi,(e=>s(13,n=e)));let{data:a}=t,{data:l={},customFormattingSettings:r,__db:o,inputs:c}=a;Li(Xi,n="6666cd76f96956469e7be39d750cc7d9",n);let d=hs(Ai(c));Qn(d.subscribe((e=>c=e))),ki(gs,{getCustomFormats:()=>r.customFormats||[]});const m=(e,t)=>_s(o.query,e,{query_name:t});ys(m),i.params,Kn((()=>!0));let p={initialData:void 0,initialError:void 0},h=Qi`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`,u="SELECT\n  'Product A' as product,\n  150000 as revenue,\n  1250 as units_sold,\n  '2024-01' as month\nUNION ALL\nSELECT 'Product B', 125000, 980, '2024-01'\nUNION ALL\nSELECT 'Product C', 180000, 1450, '2024-01'\nUNION ALL\nSELECT 'Product A', 165000, 1380, '2024-02'\nUNION ALL\nSELECT 'Product B', 140000, 1120, '2024-02'\nUNION ALL\nSELECT 'Product C', 195000, 1580, '2024-02'";l.sample_sales_data&&(l.sample_sales_data instanceof Error?p.initialError=l.sample_sales_data:p.initialData=l.sample_sales_data,l.sample_sales_columns&&(p.knownColumns=l.sample_sales_columns));let y,f=!1;const $=It.createReactive({callback:e=>{s(0,y=e)},execFn:m},{id:"sample_sales",...p});return $(u,{noResolve:h,...p}),globalThis[Symbol.for("sample_sales")]={get value(){return y}},e.$$set=e=>{"data"in e&&s(1,a=e.data)},e.$$.update=()=>{2&e.$$.dirty&&s(2,({data:l={},customFormattingSettings:r,__db:o}=a),l),4&e.$$.dirty&&bs.set(Object.keys(l).length>0),128&e.$$.dirty&&i.params,120&e.$$.dirty&&(h||!f?h||($(u,{noResolve:h,...p}),s(6,f=!0)):$(u,{noResolve:h}))},s(4,h=Qi`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`),s(5,u="SELECT\n  'Product A' as product,\n  150000 as revenue,\n  1250 as units_sold,\n  '2024-01' as month\nUNION ALL\nSELECT 'Product B', 125000, 980, '2024-01'\nUNION ALL\nSELECT 'Product C', 180000, 1450, '2024-01'\nUNION ALL\nSELECT 'Product A', 165000, 1380, '2024-02'\nUNION ALL\nSELECT 'Product B', 140000, 1120, '2024-02'\nUNION ALL\nSELECT 'Product C', 195000, 1580, '2024-02'"),[y,a,l,p,h,u,f,i]}class $a extends lt{constructor(e){super(),it(this,e,Ya,ja,tt,{data:1})}}export{$a as component};
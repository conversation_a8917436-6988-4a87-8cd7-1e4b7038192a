import{s as nt,v as ce,d,b as h,i as E,e as I,h as M,j as Q,a7 as yl,m as D,a8 as gl,C as St,a9 as Xl,p as xt,l as gt,N as v,k as J,n as x,w as Ze,x as <PERSON>,y as <PERSON>,z as Gl,aa as Dl,ab as Un,r as $e,ac as Hn,ad as Vn,q as Oe,t as Dt,J as Ht,ae as Wn,af as Ti,D as qn,ag as vn,a1 as Pl,A as pt,E as $t,a2 as vi,ah as jn,a3 as Et,a4 as Gt,I as Yn,a0 as Li,c as el,u as tl,g as ll,a as il,ai as In,aj as Xn,ak as Qn,B as Kn}from"../chunks/scheduler.DQwIXrE4.js";import{S as st,i as rt,d as ae,t as U,f as qt,j as Ai,a as O,m as fe,b as oe,e as ue,g as Je,c as xe,k as Zn}from"../chunks/index.BEt_7cXZ.js";import{g as Bt,h as Vt,Y as wi,e as vt,Z as Mn,_ as _l,$ as Jn,a0 as ct,P as ji,a1 as xn,a2 as Yi,a3 as Rl,a4 as pn,a5 as $n,a6 as Oi,a7 as Ii,a8 as Dn,a9 as es,f as ts,aa as ls,ab as Zt,ac as is,ad as ns,ae as Ci,af as ki,ag as Si,V as Ve,ah as Cl,ai as ss,aj as It,ak as Wt,al as Yl,am as Mi,an as Di,ao as Ni,ap as Fi,aq as Ql,ar as Mt,O as Kl,S as Zl,as as Jl,at as rs,au as as,av as fs,aw as os,X as Nn,ax as us,W as cs,R as bl,ay as Ll,az as ms,aA as ds,aB as hs,aC as ys,aD as gs,aE as Xi,aF as bs}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.ZfA2TvHf.js";import{w as Ei}from"../chunks/entry.B1WPoaSj.js";import{h as Qi,p as _s}from"../chunks/setTrackProxy.DjIbdjlZ.js";import{p as Fn}from"../chunks/stores.CN6mJNFq.js";function Ki(e){return t=>t.map((t=>{var i;const s={},n=Object.keys(t);for(const a of n)s[null!=(i=e[a])?i:a]=t[a];return s}))}function Cs(e,t){if(0===e.length||0===t.length)return{};const i=Object.keys(e[0]),s=Object.keys(t[0]),n={};for(const e of i)s.includes(e)&&(n[e]=e);return n}function Ts(e,t,i){for(const s in i)if(e[i[s]]!==t[s])return!1;return!0}function Ls(e,t){return t=>{if(!e.length)return t;const i=Cs(t,e),s=Object.keys(e[0]);return t.flatMap((t=>{const n=e.filter((e=>Ts(t,e,i)));if(n.length)return n.map((e=>({...t,...e})));const a=Object.fromEntries(s.filter((e=>null==t[e])).map((e=>[e,void 0])));return{...t,...a}}))}}function Zi(e){return t=>{const i=t.map((e=>({...e})));for(const s in e){const n=e[s],a="function"==typeof n?n(i):n,l=null!=a&&a[Symbol.iterator]&&"string"!=typeof a?a:t.map((()=>a));let r=-1;for(const e of i)e[s]=l[++r]}return i}}function ks(e){return t=>{const i=Es(e),s=[];for(const e in i){const n=i[e];let a;a="function"==typeof n?n(t):Array.isArray(n)?n:Array.from(new Set(t.map((t=>t[e])))),s.push(a.map((t=>({[e]:t}))))}return Ss(s)}}function Ss(e){const t=[];return function e(t,i,s){if(!s.length&&null!=i)return void t.push(i);const n=s[0],a=s.slice(1);for(const s of n)e(t,{...i,...s},a)}(t,null,e),t}function Es(e){if(Array.isArray(e)){const t={};for(const i of e)t[i]=i;return t}return"object"==typeof e?e:{[e]:e}}function As(e){return t=>{const i=[];for(const s of t){const t={...s};for(const i in e)null==t[i]&&(t[i]=e[i]);i.push(t)}return i}}function Ji(e,t){return i=>{const s=ks(e)(i),n=Ls(i)(s);return t?As(t)(n):n}}function xi(e,t,i){return null==e||null==t?void 0:0===t&&0===e?0:i||0!==t?e/t:void 0}function pi(e,t,i){const s="function"==typeof e?e:t=>t[e],n=e=>e[t],{predicate:a,allowDivideByZero:l}={};return null==a?(e,t,i)=>{const a=n(e);return xi(s(e,t,i),a,l)}:(e,t,i)=>{if(!a(e,t,i))return;const r=n(e);return xi(s(e,t,i),r,l)}}function ws(e){let t,i,s;return{c(){t=D("span"),i=gl("svg"),s=gl("path"),this.h()},l(e){t=M(e,"SPAN",{"aria-expanded":!0,class:!0});var n=Q(t);i=yl(n,"svg",{viewBox:!0,width:!0,height:!0,class:!0});var a=Q(i);s=yl(a,"path",{fill:!0,"fill-rule":!0,d:!0}),Q(s).forEach(d),a.forEach(d),n.forEach(d),this.h()},h(){h(s,"fill",e[3]),h(s,"fill-rule","evenodd"),h(s,"d","M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"),h(i,"viewBox","0 0 16 16"),h(i,"width",e[1]),h(i,"height",e[1]),h(i,"class","svelte-lqleyo"),h(t,"aria-expanded",e[0]),h(t,"class","svelte-lqleyo")},m(e,n){E(e,t,n),I(t,i),I(i,s)},p(e,[n]){8&n&&h(s,"fill",e[3]),2&n&&h(i,"width",e[1]),2&n&&h(i,"height",e[1]),1&n&&h(t,"aria-expanded",e[0])},i:ce,o:ce,d(e){e&&d(t)}}}function Os(e,t,i){let s,n,a=ce;e.$$.on_destroy.push((()=>a()));const{resolveColor:l}=Bt();let{toggled:r=!1}=t,{color:o="base-content"}=t,{size:c=10}=t;return e.$$set=e=>{"toggled"in e&&i(0,r=e.toggled),"color"in e&&i(4,o=e.color),"size"in e&&i(1,c=e.size)},e.$$.update=()=>{16&e.$$.dirty&&(i(2,s=l(o)),a(),a=St(s,(e=>i(3,n=e))))},[r,c,s,n,o]}class Pn extends st{constructor(e){super(),rt(this,e,Os,ws,nt,{toggled:0,color:4,size:1})}}function $i(e,t,i){const s=e.slice();return s[12]=t[i],s[14]=i,s}function en(e,t,i){const s=e.slice();return s[15]=t[i],s[17]=i,s}function tn(e,t,i){const s=e.slice();return s[15]=t[i],s}function ln(e,t,i){const s=e.slice();return s[15]=t[i],s}function nn(e){let t,i,s,n,a,l=e[15].id+"";return{c(){t=D("th"),i=Le(l),this.h()},l(e){t=M(e,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var s=Q(t);i=Te(s,l),s.forEach(d),this.h()},h(){var i,l;h(t,"class",s="py-0 px-2 font-medium "+e[15].type+" svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"evidencetype",n=(null==(i=e[15].evidenceColumnType)?void 0:i.evidenceType)||"unavailable"),h(t,"evidencetypefidelity",a=(null==(l=e[15].evidenceColumnType)?void 0:l.typeFidelity)||"unavailable")},m(e,s){E(e,t,s),I(t,i)},p(e,r){var o,c;8&r&&l!==(l=e[15].id+"")&&Ze(i,l),8&r&&s!==(s="py-0 px-2 font-medium "+e[15].type+" svelte-ghf30y")&&h(t,"class",s),64&r&&v(t,"width",e[6]+"%"),8&r&&n!==(n=(null==(o=e[15].evidenceColumnType)?void 0:o.evidenceType)||"unavailable")&&h(t,"evidencetype",n),8&r&&a!==(a=(null==(c=e[15].evidenceColumnType)?void 0:c.typeFidelity)||"unavailable")&&h(t,"evidencetypefidelity",a)},d(e){e&&d(t)}}}function sn(e){let t,i,s,n,a,l=e[15].type+"";return{c(){t=D("th"),i=Le(l),this.h()},l(e){t=M(e,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var s=Q(t);i=Te(s,l),s.forEach(d),this.h()},h(){var i,l;h(t,"class",s=e[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"evidencetype",n=(null==(i=e[15].evidenceColumnType)?void 0:i.evidenceType)||"unavailable"),h(t,"evidencetypefidelity",a=(null==(l=e[15].evidenceColumnType)?void 0:l.typeFidelity)||"unavailable")},m(e,s){E(e,t,s),I(t,i)},p(e,r){var o,c;8&r&&l!==(l=e[15].type+"")&&Ze(i,l),8&r&&s!==(s=e[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y")&&h(t,"class",s),64&r&&v(t,"width",e[6]+"%"),8&r&&n!==(n=(null==(o=e[15].evidenceColumnType)?void 0:o.evidenceType)||"unavailable")&&h(t,"evidencetype",n),8&r&&a!==(a=(null==(c=e[15].evidenceColumnType)?void 0:c.typeFidelity)||"unavailable")&&h(t,"evidencetypefidelity",a)},d(e){e&&d(t)}}}function Is(e){let t,i=(e[2]+e[14]+1).toLocaleString()+"";return{c(){t=Le(i)},l(e){t=Te(e,i)},m(e,i){E(e,t,i)},p(e,s){4&s&&i!==(i=(e[2]+e[14]+1).toLocaleString()+"")&&Ze(t,i)},d(e){e&&d(t)}}}function Ms(e){let t,i=(e[2]+e[14]+1).toLocaleString()+"";return{c(){t=Le(i)},l(e){t=Te(e,i)},m(e,i){E(e,t,i)},p(e,s){4&s&&i!==(i=(e[2]+e[14]+1).toLocaleString()+"")&&Ze(t,i)},d(e){e&&d(t)}}}function Ds(e){let t,i,s=(e[12][e[15].id]||"Ø")+"";return{c(){t=D("td"),i=Le(s),this.h()},l(e){t=M(e,"TD",{class:!0,style:!0});var n=Q(t);i=Te(n,s),n.forEach(d),this.h()},h(){h(t,"class","other svelte-ghf30y"),v(t,"width",e[6]+"%")},m(e,s){E(e,t,s),I(t,i)},p(e,n){40&n&&s!==(s=(e[12][e[15].id]||"Ø")+"")&&Ze(i,s),64&n&&v(t,"width",e[6]+"%")},d(e){e&&d(t)}}}function Ns(e){let t,i,s,n,a=(e[12][e[15].id]??"Ø")+"";return{c(){t=D("td"),i=D("div"),s=Le(a),this.h()},l(e){t=M(e,"TD",{class:!0,style:!0,title:!0});var n=Q(t);i=M(n,"DIV",{class:!0});var l=Q(i);s=Te(l,a),l.forEach(d),n.forEach(d),this.h()},h(){h(i,"class","svelte-ghf30y"),h(t,"class","boolean svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"title",n=e[12][e[15].id])},m(e,n){E(e,t,n),I(t,i),I(i,s)},p(e,i){40&i&&a!==(a=(e[12][e[15].id]??"Ø")+"")&&Ze(s,a),64&i&&v(t,"width",e[6]+"%"),40&i&&n!==(n=e[12][e[15].id])&&h(t,"title",n)},d(e){e&&d(t)}}}function Fs(e){let t,i,s,n,a=(e[12][e[15].id]||"Ø")+"";return{c(){t=D("td"),i=D("div"),s=Le(a),this.h()},l(e){t=M(e,"TD",{class:!0,style:!0,title:!0});var n=Q(t);i=M(n,"DIV",{class:!0});var l=Q(i);s=Te(l,a),l.forEach(d),n.forEach(d),this.h()},h(){h(i,"class","svelte-ghf30y"),h(t,"class","string svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"title",n=e[12][e[15].id])},m(e,n){E(e,t,n),I(t,i),I(i,s)},p(e,i){40&i&&a!==(a=(e[12][e[15].id]||"Ø")+"")&&Ze(s,a),64&i&&v(t,"width",e[6]+"%"),40&i&&n!==(n=e[12][e[15].id])&&h(t,"title",n)},d(e){e&&d(t)}}}function Ps(e){let t,i,s,n,a=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"";return{c(){t=D("td"),i=D("div"),s=Le(a),this.h()},l(e){t=M(e,"TD",{class:!0,style:!0,title:!0});var n=Q(t);i=M(n,"DIV",{class:!0});var l=Q(i);s=Te(l,a),l.forEach(d),n.forEach(d),this.h()},h(){h(i,"class","svelte-ghf30y"),h(t,"class","string svelte-ghf30y"),v(t,"width",e[6]+"%"),h(t,"title",n=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary))},m(e,n){E(e,t,n),I(t,i),I(i,s)},p(e,i){40&i&&a!==(a=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"")&&Ze(s,a),64&i&&v(t,"width",e[6]+"%"),40&i&&n!==(n=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary))&&h(t,"title",n)},d(e){e&&d(t)}}}function Rs(e){let t,i,s=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"";return{c(){t=D("td"),i=Le(s),this.h()},l(e){t=M(e,"TD",{class:!0,style:!0});var n=Q(t);i=Te(n,s),n.forEach(d),this.h()},h(){h(t,"class","number svelte-ghf30y"),v(t,"width",e[6]+"%")},m(e,s){E(e,t,s),I(t,i)},p(e,n){40&n&&s!==(s=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"")&&Ze(i,s),64&n&&v(t,"width",e[6]+"%")},d(e){e&&d(t)}}}function Bs(e){let t,i,s;return{c(){t=D("td"),i=Le("Ø"),this.h()},l(e){t=M(e,"TD",{class:!0,style:!0});var s=Q(t);i=Te(s,"Ø"),s.forEach(d),this.h()},h(){h(t,"class",s="text-base-content-muted "+e[3][e[17]].type+" svelte-ghf30y"),v(t,"width",e[6]+"%")},m(e,s){E(e,t,s),I(t,i)},p(e,i){8&i&&s!==(s="text-base-content-muted "+e[3][e[17]].type+" svelte-ghf30y")&&h(t,"class",s),64&i&&v(t,"width",e[6]+"%")},d(e){e&&d(t)}}}function rn(e){let t;function i(e,t){return null==e[12][e[15].id]?Bs:"number"===e[3][e[17]].type?Rs:"date"===e[3][e[17]].type?Ps:"string"===e[3][e[17]].type?Fs:"boolean"===e[3][e[17]].type?Ns:Ds}let s=i(e),n=s(e);return{c(){n.c(),t=$e()},l(e){n.l(e),t=$e()},m(e,i){n.m(e,i),E(e,t,i)},p(e,a){s===(s=i(e))&&n?n.p(e,a):(n.d(1),n=s(e),n&&(n.c(),n.m(t.parentNode,t)))},d(e){e&&d(t),n.d(e)}}}function an(e){let t,i,s,n,a=(0===e[14]?Ms:Is)(e),l=Vt(e[3]),r=[];for(let t=0;t<l.length;t+=1)r[t]=rn(en(e,l,t));return{c(){t=D("tr"),i=D("td"),a.c(),s=x();for(let e=0;e<r.length;e+=1)r[e].c();n=x(),this.h()},l(e){t=M(e,"TR",{});var l=Q(t);i=M(l,"TD",{class:!0,style:!0});var o=Q(i);a.l(o),o.forEach(d),s=J(l);for(let e=0;e<r.length;e+=1)r[e].l(l);n=J(l),l.forEach(d),this.h()},h(){h(i,"class","index text-base-content-muted svelte-ghf30y"),v(i,"width","10%")},m(e,l){E(e,t,l),I(t,i),a.m(i,null),I(t,s);for(let e=0;e<r.length;e+=1)r[e]&&r[e].m(t,null);I(t,n)},p(e,i){if(a.p(e,i),104&i){let s;for(l=Vt(e[3]),s=0;s<l.length;s+=1){const a=en(e,l,s);r[s]?r[s].p(a,i):(r[s]=rn(a),r[s].c(),r[s].m(t,n))}for(;s<r.length;s+=1)r[s].d(1);r.length=l.length}},d(e){e&&d(t),a.d(),Xl(r,e)}}}function fn(e){let t,i,s,n,a,l,r,o,c,p=(e[2]+Jt).toLocaleString()+"",u=(e[4]+Jt).toLocaleString()+"";return{c(){t=D("div"),i=D("input"),s=x(),n=D("span"),a=Le(p),l=Le(" of "),r=Le(u),this.h()},l(e){t=M(e,"DIV",{class:!0});var o=Q(t);i=M(o,"INPUT",{type:!0,max:!0,step:!0,class:!0}),s=J(o),n=M(o,"SPAN",{class:!0});var c=Q(n);a=Te(c,p),l=Te(c," of "),r=Te(c,u),c.forEach(d),o.forEach(d),this.h()},h(){h(i,"type","range"),h(i,"max",e[4]),h(i,"step","1"),h(i,"class","slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"),h(n,"class","text-xs svelte-ghf30y"),h(t,"class","pagination svelte-ghf30y")},m(d,p){E(d,t,p),I(t,i),Dl(i,e[2]),I(t,s),I(t,n),I(n,a),I(n,l),I(n,r),o||(c=[gt(i,"change",e[9]),gt(i,"input",e[9]),gt(i,"input",e[7])],o=!0)},p(e,t){16&t&&h(i,"max",e[4]),4&t&&Dl(i,e[2]),4&t&&p!==(p=(e[2]+Jt).toLocaleString()+"")&&Ze(a,p),16&t&&u!==(u=(e[4]+Jt).toLocaleString()+"")&&Ze(r,u)},d(e){e&&d(t),o=!1,Gl(c)}}}function zs(e){let t,i,s,n,a,l,r,o,c,p,u,m,y,f,$,g,b,T,L,A,w,C,S,k,N,F,P=Vt(e[3]),R=[];for(let t=0;t<P.length;t+=1)R[t]=nn(ln(e,P,t));let B=Vt(e[3]),G=[];for(let t=0;t<B.length;t+=1)G[t]=sn(tn(e,B,t));let z=Vt(e[5]),H=[];for(let t=0;t<z.length;t+=1)H[t]=an($i(e,z,t));let V=e[4]>0&&fn(e);return C=new wi({props:{class:"download-button",data:e[1],queryID:e[0],display:!0}}),{c(){t=D("div"),i=D("div"),s=D("table"),n=D("thead"),a=D("tr"),l=D("th"),r=x();for(let e=0;e<R.length;e+=1)R[e].c();o=x(),c=D("tr"),p=x(),u=D("tr"),m=D("th"),y=x();for(let e=0;e<G.length;e+=1)G[e].c();f=x(),$=D("tr"),g=x(),b=D("tbody");for(let e=0;e<H.length;e+=1)H[e].c();L=x(),V&&V.c(),A=x(),w=D("div"),ue(C.$$.fragment),this.h()},l(e){t=M(e,"DIV",{class:!0});var h=Q(t);i=M(h,"DIV",{class:!0});var x=Q(i);s=M(x,"TABLE",{class:!0});var v=Q(s);n=M(v,"THEAD",{});var E=Q(n);a=M(E,"TR",{});var T=Q(a);l=M(T,"TH",{class:!0,style:!0}),Q(l).forEach(d),r=J(T);for(let e=0;e<R.length;e+=1)R[e].l(T);o=J(T),T.forEach(d),c=M(E,"TR",{}),Q(c).forEach(d),p=J(E),u=M(E,"TR",{class:!0});var O=Q(u);m=M(O,"TH",{class:!0,style:!0}),Q(m).forEach(d),y=J(O);for(let e=0;e<G.length;e+=1)G[e].l(O);f=J(O),O.forEach(d),$=M(E,"TR",{}),Q($).forEach(d),E.forEach(d),g=J(v),b=M(v,"TBODY",{});var I=Q(b);for(let e=0;e<H.length;e+=1)H[e].l(I);I.forEach(d),v.forEach(d),x.forEach(d),L=J(h),V&&V.l(h),A=J(h),w=M(h,"DIV",{class:!0});var S=Q(w);oe(C.$$.fragment,S),S.forEach(d),h.forEach(d),this.h()},h(){h(l,"class","py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y"),v(l,"width","10%"),h(m,"class","py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y"),v(m,"width","10%"),h(u,"class","type-indicator svelte-ghf30y"),h(s,"class","text-xs svelte-ghf30y"),h(i,"class","scrollbox pretty-scrollbar svelte-ghf30y"),h(w,"class","footer svelte-ghf30y"),h(t,"class","results-pane py-1 svelte-ghf30y")},m(d,h){E(d,t,h),I(t,i),I(i,s),I(s,n),I(n,a),I(a,l),I(a,r);for(let e=0;e<R.length;e+=1)R[e]&&R[e].m(a,null);I(a,o),I(n,c),I(n,p),I(n,u),I(u,m),I(u,y);for(let e=0;e<G.length;e+=1)G[e]&&G[e].m(u,null);I(u,f),I(n,$),I(s,g),I(s,b);for(let e=0;e<H.length;e+=1)H[e]&&H[e].m(b,null);I(t,L),V&&V.m(t,null),I(t,A),I(t,w),fe(C,w,null),k=!0,N||(F=gt(b,"wheel",e[8]),N=!0)},p(e,[i]){if(72&i){let t;for(P=Vt(e[3]),t=0;t<P.length;t+=1){const s=ln(e,P,t);R[t]?R[t].p(s,i):(R[t]=nn(s),R[t].c(),R[t].m(a,o))}for(;t<R.length;t+=1)R[t].d(1);R.length=P.length}if(72&i){let t;for(B=Vt(e[3]),t=0;t<B.length;t+=1){const s=tn(e,B,t);G[t]?G[t].p(s,i):(G[t]=sn(s),G[t].c(),G[t].m(u,f))}for(;t<G.length;t+=1)G[t].d(1);G.length=B.length}if(108&i){let t;for(z=Vt(e[5]),t=0;t<z.length;t+=1){const s=$i(e,z,t);H[t]?H[t].p(s,i):(H[t]=an(s),H[t].c(),H[t].m(b,null))}for(;t<H.length;t+=1)H[t].d(1);H.length=z.length}e[4]>0?V?V.p(e,i):(V=fn(e),V.c(),V.m(t,A)):V&&(V.d(1),V=null);const s={};2&i&&(s.data=e[1]),1&i&&(s.queryID=e[0]),C.$set(s)},i(e){k||(e&&(T||xt((()=>{T=Ai(s,Mn,{}),T.start()}))),O(C.$$.fragment,e),e&&xt((()=>{k&&(S||(S=qt(t,vt,{},!0)),S.run(1))})),k=!0)},o(e){U(C.$$.fragment,e),e&&(S||(S=qt(t,vt,{},!1)),S.run(0)),k=!1},d(e){e&&d(t),Xl(R,e),Xl(G,e),Xl(H,e),V&&V.d(),ae(C),e&&S&&S.end(),N=!1,F()}}}let Jt=5;function Gs(e,t,i){let s,n,a,l,r,{queryID:o}=t,{data:c}=t,d=0;function p(){r=c.slice(d,d+Jt),i(5,l=r)}const h=Jn((e=>{i(2,d=Math.min(Math.max(0,d+Math.floor(e.deltaY/Math.abs(e.deltaY))),a)),p()}),60);return e.$$set=e=>{"queryID"in e&&i(0,o=e.queryID),"data"in e&&i(1,c=e.data)},e.$$.update=()=>{2&e.$$.dirty&&i(3,s=_l(c,"array")),8&e.$$.dirty&&i(6,n=90/(s.length+1)),2&e.$$.dirty&&i(4,a=Math.max(c.length-Jt,0)),6&e.$$.dirty&&i(5,l=c.slice(d,d+Jt))},[o,c,d,s,a,l,n,p,function(e){if(Math.abs(e.deltaX)>=Math.abs(e.deltaY))return;const t=e.deltaY<0&&0===d,i=e.deltaY>0&&d===a;t||i||(e.preventDefault(),h(e))},function(){d=Un(this.value),i(2,d)}]}class Us extends st{constructor(e){super(),rt(this,e,Gs,zs,nt,{queryID:0,data:1})}}const on={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/};function Hs(e){let t,i,s,n,a,l=ji.highlight(e[0],on)+"";return{c(){t=D("pre"),i=Le("  "),s=D("code"),n=new Vn(!1),a=Le("\n"),this.h()},l(e){t=M(e,"PRE",{class:!0});var l=Q(t);i=Te(l,"  "),s=M(l,"CODE",{class:!0});var r=Q(s);n=Hn(r,!1),r.forEach(d),a=Te(l,"\n"),l.forEach(d),this.h()},h(){n.a=null,h(s,"class","language-sql svelte-re3fhx"),h(t,"class","text-xs max-h-56 overflow-auto pretty-scrollbar")},m(e,r){E(e,t,r),I(t,i),I(t,s),n.m(l,s),I(t,a)},p(e,[t]){1&t&&l!==(l=ji.highlight(e[0],on)+"")&&n.p(l)},i:ce,o:ce,d(e){e&&d(t)}}}function Vs(e,t,i){let{code:s=""}=t;return e.$$set=e=>{"code"in e&&i(0,s=e.code)},[s]}class Rn extends st{constructor(e){super(),rt(this,e,Vs,Hs,nt,{code:0})}}function Ws(e){let t,i,s,n,a,l="Compiled",r="Written";return{c(){t=D("button"),t.textContent=l,i=x(),s=D("button"),s.textContent=r,this.h()},l(e){t=M(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-1vzm9jy"!==Oe(t)&&(t.textContent=l),i=J(e),s=M(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-qu81ez"!==Oe(s)&&(s.textContent=r),this.h()},h(){h(t,"class","off svelte-ska6l4"),h(s,"class","text-info bg-info/10 border border-info svelte-ska6l4")},m(l,r){E(l,t,r),E(l,i,r),E(l,s,r),n||(a=gt(t,"click",e[1]),n=!0)},p:ce,d(e){e&&(d(t),d(i),d(s)),n=!1,a()}}}function qs(e){let t,i,s,n,a,l="Compiled",r="Written";return{c(){t=D("button"),t.textContent=l,i=x(),s=D("button"),s.textContent=r,this.h()},l(e){t=M(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-wrfleh"!==Oe(t)&&(t.textContent=l),i=J(e),s=M(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-v36xno"!==Oe(s)&&(s.textContent=r),this.h()},h(){h(t,"class","text-info bg-info/10 border border-info svelte-ska6l4"),h(s,"class","off svelte-ska6l4")},m(l,r){E(l,t,r),E(l,i,r),E(l,s,r),n||(a=gt(s,"click",e[1]),n=!0)},p:ce,d(e){e&&(d(t),d(i),d(s)),n=!1,a()}}}function vs(e){let t,i,s;function n(e,t){return e[0]?qs:Ws}let a=n(e),l=a(e);return{c(){t=D("div"),l.c(),this.h()},l(e){t=M(e,"DIV",{class:!0});var i=Q(t);l.l(i),i.forEach(d),this.h()},h(){h(t,"class","toggle svelte-ska6l4")},m(e,i){E(e,t,i),l.m(t,null),s=!0},p(e,[i]){a===(a=n(e))&&l?l.p(e,i):(l.d(1),l=a(e),l&&(l.c(),l.m(t,null)))},i(e){s||(e&&xt((()=>{s&&(i||(i=qt(t,vt,{},!0)),i.run(1))})),s=!0)},o(e){e&&(i||(i=qt(t,vt,{},!1)),i.run(0)),s=!1},d(e){e&&d(t),l.d(),e&&i&&i.end()}}}function js(e,t,i){let{showCompiled:s}=t;return e.$$set=e=>{"showCompiled"in e&&i(0,s=e.showCompiled)},[s,function(){i(0,s=!s)}]}class Ys extends st{constructor(e){super(),rt(this,e,js,vs,nt,{showCompiled:0})}}function un(e){let t,i,s,n,a,l,r,o,c,p,u,m,y,f,$,g,v;n=new Pn({props:{toggled:e[10]}});let b=e[10]&&e[4]&&cn(e),T=e[10]&&mn(e);const L=[xs,Js,Zs,Ks],A=[];function w(e,t){return e[6]?0:e[8]?1:e[2].loading?2:3}u=w(e),m=A[u]=L[u](e);let C=e[8]>0&&!e[6]&&e[9]&&dn(e);return{c(){t=D("div"),i=D("div"),s=D("button"),ue(n.$$.fragment),a=x(),l=Le(e[0]),r=x(),b&&b.c(),o=x(),T&&T.c(),c=x(),p=D("button"),m.c(),y=x(),C&&C.c(),this.h()},l(h){t=M(h,"DIV",{class:!0});var u=Q(t);i=M(u,"DIV",{class:!0});var f=Q(i);s=M(f,"BUTTON",{type:!0,"aria-label":!0,class:!0});var $=Q(s);oe(n.$$.fragment,$),a=J($),l=Te($,e[0]),$.forEach(d),r=J(f),b&&b.l(f),o=J(f),T&&T.l(f),f.forEach(d),c=J(u),p=M(u,"BUTTON",{type:!0,"aria-label":!0,class:!0});var g=Q(p);m.l(g),g.forEach(d),y=J(u),C&&C.l(u),u.forEach(d),this.h()},h(){h(s,"type","button"),h(s,"aria-label","show-sql"),h(s,"class","title svelte-1ursthx"),h(i,"class","container-a svelte-1ursthx"),h(p,"type","button"),h(p,"aria-label","view-query"),h(p,"class",Wn("status-bar")+" svelte-1ursthx"),Ht(p,"error",e[6]),Ht(p,"success",!e[6]),Ht(p,"open",e[9]),Ht(p,"closed",!e[9]),h(t,"class","scrollbox my-3 svelte-1ursthx")},m(d,h){E(d,t,h),I(t,i),I(i,s),fe(n,s,null),I(s,a),I(s,l),I(i,r),b&&b.m(i,null),I(i,o),T&&T.m(i,null),I(t,c),I(t,p),A[u].m(p,null),I(t,y),C&&C.m(t,null),$=!0,g||(v=[gt(s,"click",e[15]),gt(p,"click",e[16])],g=!0)},p(e,s){const a={};1024&s&&(a.toggled=e[10]),n.$set(a),(!$||1&s)&&Ze(l,e[0]),e[10]&&e[4]?b?(b.p(e,s),1040&s&&O(b,1)):(b=cn(e),b.c(),O(b,1),b.m(i,o)):b&&(Je(),U(b,1,1,(()=>{b=null})),xe()),e[10]?T?(T.p(e,s),1024&s&&O(T,1)):(T=mn(e),T.c(),O(T,1),T.m(i,null)):T&&(Je(),U(T,1,1,(()=>{T=null})),xe());let r=u;u=w(e),u===r?A[u].p(e,s):(Je(),U(A[r],1,1,(()=>{A[r]=null})),xe(),m=A[u],m?m.p(e,s):(m=A[u]=L[u](e),m.c()),O(m,1),m.m(p,null)),(!$||64&s)&&Ht(p,"error",e[6]),(!$||64&s)&&Ht(p,"success",!e[6]),(!$||512&s)&&Ht(p,"open",e[9]),(!$||512&s)&&Ht(p,"closed",!e[9]),e[8]>0&&!e[6]&&e[9]?C?(C.p(e,s),832&s&&O(C,1)):(C=dn(e),C.c(),O(C,1),C.m(t,null)):C&&(Je(),U(C,1,1,(()=>{C=null})),xe())},i(e){$||(O(n.$$.fragment,e),O(b),O(T),O(m),O(C),e&&xt((()=>{$&&(f||(f=qt(t,vt,{},!0)),f.run(1))})),$=!0)},o(e){U(n.$$.fragment,e),U(b),U(T),U(m),U(C),e&&(f||(f=qt(t,vt,{},!1)),f.run(0)),$=!1},d(e){e&&d(t),ae(n),b&&b.d(),T&&T.d(),A[u].d(),C&&C.d(),e&&f&&f.end(),g=!1,Gl(v)}}}function cn(e){let t,i,s;function n(t){e[20](t)}let a={};return void 0!==e[5]&&(a.showCompiled=e[5]),t=new Ys({props:a}),qn.push((()=>Zn(t,"showCompiled",n))),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,i){fe(t,e,i),s=!0},p(e,s){const n={};!i&&32&s&&(i=!0,n.showCompiled=e[5],vn((()=>i=!1))),t.$set(n)},i(e){s||(O(t.$$.fragment,e),s=!0)},o(e){U(t.$$.fragment,e),s=!1},d(e){ae(t,e)}}}function mn(e){let t,i,s,n,a;const l=[Qs,Xs],r=[];function o(e,t){return e[5]?0:1}return i=o(e),s=r[i]=l[i](e),{c(){t=D("div"),s.c(),this.h()},l(e){t=M(e,"DIV",{class:!0});var i=Q(t);s.l(i),i.forEach(d),this.h()},h(){h(t,"class","code-container svelte-1ursthx")},m(e,s){E(e,t,s),r[i].m(t,null),a=!0},p(e,n){let a=i;i=o(e),i===a?r[i].p(e,n):(Je(),U(r[a],1,1,(()=>{r[a]=null})),xe(),s=r[i],s?s.p(e,n):(s=r[i]=l[i](e),s.c()),O(s,1),s.m(t,null))},i(e){a||(O(s),e&&xt((()=>{a&&(n||(n=qt(t,vt,{},!0)),n.run(1))})),a=!0)},o(e){U(s),e&&(n||(n=qt(t,vt,{},!1)),n.run(0)),a=!1},d(e){e&&d(t),r[i].d(),e&&n&&n.end()}}}function Xs(e){let t,i;return t=new Rn({props:{code:e[3]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};8&i&&(s.code=e[3]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Qs(e){let t,i;return t=new Rn({props:{code:e[1].originalText}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};2&i&&(s.code=e[1].originalText),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Ks(e){let t;return{c(){t=Le("ran successfully but no data was returned")},l(e){t=Te(e,"ran successfully but no data was returned")},m(e,i){E(e,t,i)},p:ce,i:ce,o:ce,d(e){e&&d(t)}}}function Zs(e){let t;return{c(){t=Le("loading...")},l(e){t=Te(e,"loading...")},m(e,i){E(e,t,i)},p:ce,i:ce,o:ce,d(e){e&&d(t)}}}function Js(e){let t,i,s,n,a,l,r,o,c,p,h=e[8].toLocaleString()+"",u=e[8]>1?"records":"record",m=e[7].toLocaleString()+"",y=e[7]>1?"properties":"property";return t=new Pn({props:{toggled:e[9],color:e[12].colors.info}}),{c(){ue(t.$$.fragment),i=x(),s=Le(h),n=x(),a=Le(u),l=Le(" with "),r=Le(m),o=x(),c=Le(y)},l(e){oe(t.$$.fragment,e),i=J(e),s=Te(e,h),n=J(e),a=Te(e,u),l=Te(e," with "),r=Te(e,m),o=J(e),c=Te(e,y)},m(e,d){fe(t,e,d),E(e,i,d),E(e,s,d),E(e,n,d),E(e,a,d),E(e,l,d),E(e,r,d),E(e,o,d),E(e,c,d),p=!0},p(e,i){const n={};512&i&&(n.toggled=e[9]),4096&i&&(n.color=e[12].colors.info),t.$set(n),(!p||256&i)&&h!==(h=e[8].toLocaleString()+"")&&Ze(s,h),(!p||256&i)&&u!==(u=e[8]>1?"records":"record")&&Ze(a,u),(!p||128&i)&&m!==(m=e[7].toLocaleString()+"")&&Ze(r,m),(!p||128&i)&&y!==(y=e[7]>1?"properties":"property")&&Ze(c,y)},i(e){p||(O(t.$$.fragment,e),p=!0)},o(e){U(t.$$.fragment,e),p=!1},d(e){e&&(d(i),d(s),d(n),d(a),d(l),d(r),d(o),d(c)),ae(t,e)}}}function xs(e){let t,i=e[6].message+"";return{c(){t=Le(i)},l(e){t=Te(e,i)},m(e,i){E(e,t,i)},p(e,s){64&s&&i!==(i=e[6].message+"")&&Ze(t,i)},i:ce,o:ce,d(e){e&&d(t)}}}function dn(e){let t,i;return t=new Us({props:{data:e[1],queryID:e[0]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};2&i&&(s.data=e[1]),1&i&&(s.queryID=e[0]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function ps(e){let t,i,s,n=e[11]&&un(e);return{c(){t=D("div"),n&&n.c(),this.h()},l(e){t=M(e,"DIV",{class:!0});var i=Q(t);n&&n.l(i),i.forEach(d),this.h()},h(){h(t,"class","over-container svelte-1ursthx")},m(e,i){E(e,t,i),n&&n.m(t,null),s=!0},p(e,[i]){e[11]?n?(n.p(e,i),2048&i&&O(n,1)):(n=un(e),n.c(),O(n,1),n.m(t,null)):n&&(Je(),U(n,1,1,(()=>{n=null})),xe())},i(e){s||(O(n),e&&(i||xt((()=>{i=Ai(t,Mn,{}),i.start()}))),s=!0)},o(e){U(n),s=!1},d(e){e&&d(t),n&&n.d()}}}function $s(e,t,i){let s,n,a,l,r,o,c,d,p,h=ce,u=()=>(h(),h=St(y,(e=>i(2,l=e))),y);Dt(e,Fn,(e=>i(19,c=e))),Dt(e,xn,(e=>i(11,d=e))),e.$$.on_destroy.push((()=>h()));let{queryID:m}=t,{queryResult:y}=t;u();let f=Yi("showSQL_".concat(m),!1);Dt(e,f,(e=>i(10,o=e)));let $=Yi(`showResults_${m}`);Dt(e,$,(e=>i(9,r=e)));let g,x,v,b=!0;const{theme:E}=Bt();return Dt(e,E,(e=>i(12,p=e))),e.$$set=e=>{"queryID"in e&&i(0,m=e.queryID),"queryResult"in e&&u(i(1,y=e.queryResult))},e.$$.update=()=>{if(524288&e.$$.dirty&&i(18,s=c.data.evidencemeta.queries),4&e.$$.dirty&&i(6,v=l?l.error:new Error("queryResult is undefined")),4&e.$$.dirty&&i(8,n=(null==l?void 0:l.length)??0),4&e.$$.dirty&&i(7,a=l.columns.length??(null==l?void 0:l._evidenceColumnTypes.length)??0),262145&e.$$.dirty){let e=null==s?void 0:s.find((e=>e.id===m));e&&(i(3,g=e.inputQueryString),i(4,x=e.compiled&&void 0===e.compileError))}},[m,y,l,g,x,b,v,a,n,r,o,d,p,f,$,function(){Ti(f,o=!o,o)},function(){!v&&l.length>0&&Ti($,r=!r,r)},E,s,c,function(e){b=e,i(5,b)}]}class er extends st{constructor(e){super(),rt(this,e,$s,ps,nt,{queryID:0,queryResult:1})}}const Tl=Symbol.for("__evidence-chart-window-debug__"),tr=(e,t)=>{window[Tl]||(window[Tl]={}),window[Tl][e]=t},lr=e=>{window[Tl]||(window[Tl]={}),delete window[Tl][e]},hl=500,ir=(e,t)=>{var i;const s=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)&&3*e.clientWidth*e.clientHeight*3>16777215;let n;Rl("light",Ii),Rl("dark",Dn);const a=()=>{n=Oi(e,t.theme,{renderer:s?"svg":t.renderer??"canvas"})};a(),tr(n.id,n),t.connectGroup&&(n.group=t.connectGroup,pn(t.connectGroup));const l=()=>{if(t.seriesColors){const e=n.getOption();if(!e)return;const i={...e};for(const s of Object.keys(t.seriesColors)){const n=e.series.findIndex((e=>e.name===s));-1!==n&&(i.series[n]={...i.series[n],itemStyle:{...i.series[n].itemStyle,color:t.seriesColors[s]}})}n.setOption(i)}},r=()=>{t.echartsOptions&&n.setOption({...t.echartsOptions})},o=()=>{let e=[];if(t.seriesOptions){const i=t.config.series.reduce(((e,{evidenceSeriesType:t},i)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(i),e)),[]);for(let s=0;s<t.config.series.length;s++)i.includes(s)?e.push({}):e.push({...t.seriesOptions});n.setOption({series:e})}};n.setOption({...t.config,animationDuration:hl,animationDurationUpdate:hl}),l(),r(),o();const c=t.dispatch;n.on("click",(function(e){c("click",e)}));const d=e.parentElement,p=$n((()=>{n.resize({animation:{duration:hl}}),u()}),100);let h;window.ResizeObserver&&d?(h=new ResizeObserver(p),h.observe(d)):window.addEventListener("resize",p);const u=()=>{if(t.showAllXAxisLabels){const i=n.getOption();if(!i)return;const s=new Set(i.series.flatMap((e=>{var t;return null==(t=e.data)?void 0:t.map((e=>e[0]))}))),a=.8,l=(null==e?void 0:e.clientWidth)??0;if(!t.swapXY){const e={xAxis:{axisLabel:{interval:0,overflow:t.xAxisLabelOverflow,width:l*a/s.size}}};n.setOption(e)}}};return p(),window[i=Symbol.for("chart renders")]??(window[i]=0),window[Symbol.for("chart renders")]++,{update(e){window[Symbol.for("chart renders")]++,(e=>{e.theme!==t.theme&&(n.dispose(),t=e,a()),t=e,n.setOption({...t.config,animationDuration:hl,animationDurationUpdate:hl},!0),l(),r(),o(),n.resize({animation:{duration:hl}}),u()})(e)},destroy(){h?h.unobserve(d):window.removeEventListener("resize",p),n.dispose(),lr(n.id)}}},nr=(e,t)=>{Rl("light",Ii),Rl("dark",Dn),console.log("echartsCanvasDownloadAction",t.theme);const i=Oi(e,t.theme,{renderer:"canvas"});t.config.animation=!1,i.setOption(t.config),t.echartsOptions&&i.setOption({...t.echartsOptions}),(()=>{if(t.seriesColors){const e=i.getOption();if(!e)return;const s={...e};for(const i of Object.keys(t.seriesColors)){const n=e.series.findIndex((e=>e.name===i));-1!==n&&(s.series[n]={...s.series[n],itemStyle:{...s.series[n].itemStyle,color:t.seriesColors[i]}})}i.setOption(s)}})(),(()=>{let e=[];if(t.seriesOptions){const s=t.config.series.reduce(((e,{evidenceSeriesType:t},i)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(i),e)),[]);for(let i=0;i<t.config.series.length;i++)s.includes(i)?e.push({}):e.push({...t.seriesOptions});i.setOption({series:e})}})();let s=i.getConnectedDataURL({type:"png",pixelRatio:3,backgroundColor:t.backgroundColor,excludeComponents:["toolbox"]});const n=new Date,a=new Date(n.getTime()-6e4*n.getTimezoneOffset()).toISOString().slice(0,19).replaceAll(":","-");return es(s,(t.evidenceChartTitle??t.queryID??"evidence-chart")+`_${a}.png`),i.dispose(),{destroy(){i.dispose()}}},Bl=(e,t)=>{Rl("evidence-light",Ii);const{config:i,ratio:s,echartsOptions:n,seriesOptions:a,seriesColors:l,isMap:r,extraHeight:o,width:c}=t;let d={renderer:"canvas"};r&&(d.height=.5*c+o,e&&e.parentNode&&(e.style.height=d.height+"px",e.parentNode.style.height=d.height+"px"));const p=Oi(e,"evidence-light",d);i.animation=!1,p.setOption(i),n&&p.setOption(n),n&&p.setOption({...n}),(()=>{if(l){const e=p.getOption();if(!e)return;const t={...e};for(const i of Object.keys(l)){const s=e.series.findIndex((e=>e.name===i));-1!==s&&(t.series[s]={...t.series[s],itemStyle:{...t.series[s].itemStyle,color:l[i]}})}p.setOption(t)}})(),(()=>{let e=[];if(a){const t=i.series.reduce(((e,{evidenceSeriesType:t},i)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(i),e)),[]);for(let s=0;s<i.series.length;s++)t.includes(s)?e.push({}):e.push({...a});p.setOption({series:e})}})();let h=p.getConnectedDataURL({type:"jpeg",pixelRatio:s,backgroundColor:"#fff",excludeComponents:["toolbox"]});e.innerHTML=`<img src=${h} width="100%" style="\n        position: absolute; \n        top: 0;\n        user-select: all;\n        -webkit-user-select: all;\n        -moz-user-select: all;\n        -ms-user-select: all;\n    " />`,t.config.animation=!0};function sr(e){let t;function i(e,t){return e[9]?fr:ar}let s=i(e),n=s(e);return{c(){n.c(),t=$e()},l(e){n.l(e),t=$e()},m(e,i){n.m(e,i),E(e,t,i)},p(e,a){s===(s=i(e))&&n?n.p(e,a):(n.d(1),n=s(e),n&&(n.c(),n.m(t.parentNode,t)))},d(e){e&&d(t),n.d(e)}}}function rr(e){let t,i,s,n;return{c(){t=D("div"),this.h()},l(e){t=M(e,"DIV",{class:!0,style:!0}),Q(t).forEach(d),this.h()},h(){h(t,"class","chart"),v(t,"height",e[1]),v(t,"width",e[2]),v(t,"margin-left","0"),v(t,"margin-top","15px"),v(t,"margin-bottom","10px"),v(t,"overflow","visible"),v(t,"break-inside","avoid")},m(a,l){E(a,t,l),s||(n=pt(i=Bl.call(null,t,{config:e[0],ratio:2,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13]})),s=!0)},p(e,s){2&s&&v(t,"height",e[1]),4&s&&v(t,"width",e[2]),i&&$t(i.update)&&8289&s&&i.update.call(null,{config:e[0],ratio:2,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13]})},d(e){e&&d(t),s=!1,n()}}}function ar(e){let t,i,s,n,a,l,r;return{c(){t=D("div"),s=x(),n=D("div"),this.h()},l(e){t=M(e,"DIV",{class:!0,style:!0}),Q(t).forEach(d),s=J(e),n=M(e,"DIV",{class:!0,style:!0}),Q(n).forEach(d),this.h()},h(){h(t,"class","chart md:hidden"),v(t,"height",e[1]),v(t,"width","650px"),v(t,"margin-left","0"),v(t,"margin-top","15px"),v(t,"margin-bottom","10px"),v(t,"overflow","visible"),v(t,"break-inside","avoid"),h(n,"class","chart hidden md:block"),v(n,"height",e[1]),v(n,"width","841px"),v(n,"margin-left","0"),v(n,"margin-top","15px"),v(n,"margin-bottom","10px"),v(n,"overflow","visible"),v(n,"break-inside","avoid")},m(o,c){E(o,t,c),E(o,s,c),E(o,n,c),l||(r=[pt(i=Bl.call(null,t,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:650})),pt(a=Bl.call(null,n,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:841}))],l=!0)},p(e,s){2&s&&v(t,"height",e[1]),i&&$t(i.update)&&8673&s&&i.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:650}),2&s&&v(n,"height",e[1]),a&&$t(a.update)&&8673&s&&a.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:841})},d(e){e&&(d(t),d(s),d(n)),l=!1,Gl(r)}}}function fr(e){let t,i,s,n,a,l,r;return{c(){t=D("div"),s=x(),n=D("div"),this.h()},l(e){t=M(e,"DIV",{class:!0,style:!0}),Q(t).forEach(d),s=J(e),n=M(e,"DIV",{class:!0,style:!0}),Q(n).forEach(d),this.h()},h(){h(t,"class","chart md:hidden"),v(t,"height",e[1]),v(t,"width",e[11]+"px"),v(t,"margin-left","0"),v(t,"margin-top","15px"),v(t,"margin-bottom","10px"),v(t,"overflow","visible"),v(t,"break-inside","avoid"),h(n,"class","chart hidden md:block"),v(n,"height",e[1]),v(n,"width",e[10]+"px"),v(n,"margin-left","0"),v(n,"margin-top","15px"),v(n,"margin-bottom","10px"),v(n,"overflow","visible"),v(n,"break-inside","avoid")},m(o,c){E(o,t,c),E(o,s,c),E(o,n,c),l||(r=[pt(i=Bl.call(null,t,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[11]})),pt(a=Bl.call(null,n,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[10]}))],l=!0)},p(e,s){2&s&&v(t,"height",e[1]),2048&s&&v(t,"width",e[11]+"px"),i&&$t(i.update)&&10721&s&&i.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[11]}),2&s&&v(n,"height",e[1]),1024&s&&v(n,"width",e[10]+"px"),a&&$t(a.update)&&9697&s&&a.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[10]})},d(e){e&&(d(t),d(s),d(n)),l=!1,Gl(r)}}}function or(e){let t;function i(e,t){return e[3]?rr:e[4]?sr:void 0}let s=i(e),n=s&&s(e);return{c(){n&&n.c(),t=$e()},l(e){n&&n.l(e),t=$e()},m(e,i){n&&n.m(e,i),E(e,t,i)},p(e,[a]){s===(s=i(e))&&n?n.p(e,a):(n&&n.d(1),n=s&&s(e),n&&(n.c(),n.m(t.parentNode,t)))},i:ce,o:ce,d(e){e&&d(t),n&&n.d(e)}}}function ur(e,t,i){let s,n,a,l,r,o,c=ce;e.$$.on_destroy.push((()=>c()));const{resolveColorsObject:d}=Bt();let p,h,{config:u}=t,{height:m="291px"}=t,{width:y="100%"}=t,{copying:f=!1}=t,{printing:$=!1}=t,{echartsOptions:g}=t,{seriesOptions:x}=t,{seriesColors:v}=t,{isMap:b=!1}=t,{extraHeight:E}=t,T=!1;const L=Pl("gridConfig");return L&&(T=!0,({cols:p,gapWidth:h}=L)),e.$$set=e=>{"config"in e&&i(0,u=e.config),"height"in e&&i(1,m=e.height),"width"in e&&i(2,y=e.width),"copying"in e&&i(3,f=e.copying),"printing"in e&&i(4,$=e.printing),"echartsOptions"in e&&i(5,g=e.echartsOptions),"seriesOptions"in e&&i(6,x=e.seriesOptions),"seriesColors"in e&&i(14,v=e.seriesColors),"isMap"in e&&i(7,b=e.isMap),"extraHeight"in e&&i(8,E=e.extraHeight)},e.$$.update=()=>{16384&e.$$.dirty&&(i(12,s=d(v)),c(),c=St(s,(e=>i(13,o=e)))),32768&e.$$.dirty&&i(18,n=Math.min(Number(p),2)),327680&e.$$.dirty&&i(11,a=(650-Number(h)*(n-1))/n),32768&e.$$.dirty&&i(17,l=Math.min(Number(p),3)),196608&e.$$.dirty&&i(10,r=(841-Number(h)*(l-1))/l)},[u,m,y,f,$,g,x,b,E,T,r,a,s,o,v,p,h,l,n]}class cr extends st{constructor(e){super(),rt(this,e,ur,or,nt,{config:0,height:1,width:2,copying:3,printing:4,echartsOptions:5,seriesOptions:6,seriesColors:14,isMap:7,extraHeight:8})}}function mr(e){let t,i,s,n,a,l="Loading...";return{c(){t=D("div"),i=D("span"),i.textContent=l,s=x(),n=D("div"),this.h()},l(e){t=M(e,"DIV",{role:!0,class:!0});var a=Q(t);i=M(a,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-1wtojot"!==Oe(i)&&(i.textContent=l),s=J(a),n=M(a,"DIV",{class:!0,style:!0}),Q(n).forEach(d),a.forEach(d),this.h()},h(){h(i,"class","sr-only"),h(n,"class","bg-base-100 rounded-md max-w-[100%]"),v(n,"height",e[0]),v(n,"margin-top","15px"),v(n,"margin-bottom","31px"),h(t,"role","status"),h(t,"class","animate-pulse")},m(e,a){E(e,t,a),I(t,i),I(t,s),I(t,n)},p(e,[t]){1&t&&v(n,"height",e[0])},i(e){e&&(a||xt((()=>{a=Ai(t,ts,{}),a.start()})))},o:ce,d(e){e&&d(t)}}}function dr(e,t,i){let{height:s="231px"}=t;return e.$$set=e=>{"height"in e&&i(0,s=e.height)},[s]}class hr extends st{constructor(e){super(),rt(this,e,dr,mr,nt,{height:0})}}function hn(e){let t,i,s,n;const a=[gr,yr],l=[];return t=1,i=l[1]=a[1](e),{c(){i.c(),s=$e()},l(e){i.l(e),s=$e()},m(e,t){l[1].m(e,t),E(e,s,t),n=!0},p(e,t){i.p(e,t)},i(e){n||(O(i),n=!0)},o(e){U(i),n=!1},d(e){e&&d(s),l[1].d(e)}}}function yr(e){let t,i,s,n;return{c(){t=D("div"),this.h()},l(e){t=M(e,"DIV",{class:!0,style:!0}),Q(t).forEach(d),this.h()},h(){h(t,"class","chart svelte-db4qxn"),v(t,"height",e[3]),v(t,"width",e[4]),v(t,"overflow","visible"),v(t,"display",e[15]?"none":"inherit")},m(a,l){E(a,t,l),s||(n=pt(i=ir.call(null,t,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],dispatch:e[24],renderer:e[6],connectGroup:e[12],xAxisLabelOverflow:e[13],seriesColors:e[19],theme:e[20]})),s=!0)},p(e,s){8&s[0]&&v(t,"height",e[3]),16&s[0]&&v(t,"width",e[4]),32768&s[0]&&v(t,"display",e[15]?"none":"inherit"),i&&$t(i.update)&&35141185&s[0]&&i.update.call(null,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],dispatch:e[24],renderer:e[6],connectGroup:e[12],xAxisLabelOverflow:e[13],seriesColors:e[19],theme:e[20]})},i:ce,o:ce,d(e){e&&d(t),s=!1,n()}}}function gr(e){let t,i;return t=new hr({props:{height:e[3]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};8&i[0]&&(s.height=e[3]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function yn(e){let t,i,s,n=e[8]&&gn(e),a=e[5]&&e[7]&&bn(e);return{c(){t=D("div"),n&&n.c(),i=x(),a&&a.c(),this.h()},l(e){t=M(e,"DIV",{class:!0});var s=Q(t);n&&n.l(s),i=J(s),a&&a.l(s),s.forEach(d),this.h()},h(){h(t,"class","chart-footer svelte-db4qxn")},m(e,l){E(e,t,l),n&&n.m(t,null),I(t,i),a&&a.m(t,null),s=!0},p(e,s){e[8]?n?(n.p(e,s),256&s[0]&&O(n,1)):(n=gn(e),n.c(),O(n,1),n.m(t,i)):n&&(Je(),U(n,1,1,(()=>{n=null})),xe()),e[5]&&e[7]?a?(a.p(e,s),160&s[0]&&O(a,1)):(a=bn(e),a.c(),O(a,1),a.m(t,null)):a&&(Je(),U(a,1,1,(()=>{a=null})),xe())},i(e){s||(O(n),O(a),s=!0)},o(e){U(n),U(a),s=!1},d(e){e&&d(t),n&&n.d(),a&&a.d()}}}function gn(e){let t,i;return t=new wi({props:{text:"Save Image",class:"download-button",downloadData:e[32],display:e[17],queryID:e[1],$$slots:{default:[br]},$$scope:{ctx:e}}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};16384&i[0]&&(s.downloadData=e[32]),131072&i[0]&&(s.display=e[17]),2&i[0]&&(s.queryID=e[1]),32&i[1]&&(s.$$scope={dirty:i,ctx:e}),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function br(e){let t,i,s,n;return{c(){t=gl("svg"),i=gl("rect"),s=gl("circle"),n=gl("path"),this.h()},l(e){t=yl(e,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var a=Q(t);i=yl(a,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0}),Q(i).forEach(d),s=yl(a,"circle",{cx:!0,cy:!0,r:!0}),Q(s).forEach(d),n=yl(a,"path",{d:!0}),Q(n).forEach(d),a.forEach(d),this.h()},h(){h(i,"x","3"),h(i,"y","3"),h(i,"width","18"),h(i,"height","18"),h(i,"rx","2"),h(s,"cx","8.5"),h(s,"cy","8.5"),h(s,"r","1.5"),h(n,"d","M20.4 14.5L16 10 4 20"),h(t,"xmlns","http://www.w3.org/2000/svg"),h(t,"width","12"),h(t,"height","12"),h(t,"viewBox","0 0 24 24"),h(t,"fill","none"),h(t,"stroke","#000"),h(t,"stroke-width","2"),h(t,"stroke-linecap","round"),h(t,"stroke-linejoin","round")},m(e,a){E(e,t,a),I(t,i),I(t,s),I(t,n)},p:ce,d(e){e&&d(t)}}}function bn(e){let t,i;return t=new wi({props:{text:"Download Data",data:e[5],queryID:e[1],class:"download-button",display:e[17]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};32&i[0]&&(s.data=e[5]),2&i[0]&&(s.queryID=e[1]),131072&i[0]&&(s.display=e[17]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function _n(e){let t,i;return t=new ls({props:{source:JSON.stringify(e[0],void 0,3),copyToClipboard:!0,$$slots:{default:[_r]},$$scope:{ctx:e}}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};1&i[0]&&(s.source=JSON.stringify(e[0],void 0,3)),1&i[0]|32&i[1]&&(s.$$scope={dirty:i,ctx:e}),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function _r(e){let t,i=JSON.stringify(e[0],void 0,3)+"";return{c(){t=Le(i)},l(e){t=Te(e,i)},m(e,i){E(e,t,i)},p(e,s){1&s[0]&&i!==(i=JSON.stringify(e[0],void 0,3)+"")&&Ze(t,i)},d(e){e&&d(t)}}}function Cn(e){let t,i,s,n;return{c(){t=D("div"),this.h()},l(e){t=M(e,"DIV",{class:!0,style:!0}),Q(t).forEach(d),this.h()},h(){h(t,"class","chart svelte-db4qxn"),v(t,"display","none"),v(t,"visibility","visible"),v(t,"height",e[3]),v(t,"width","666px"),v(t,"margin-left","0"),v(t,"margin-top","15px"),v(t,"margin-bottom","15px"),v(t,"overflow","visible")},m(a,l){E(a,t,l),s||(n=pt(i=nr.call(null,t,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[19],queryID:e[1],evidenceChartTitle:e[2],theme:e[20],backgroundColor:e[21].colors["base-100"]})),s=!0)},p(e,s){8&s[0]&&v(t,"height",e[3]),i&&$t(i.update)&&37225991&s[0]&&i.update.call(null,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[19],queryID:e[1],evidenceChartTitle:e[2],theme:e[20],backgroundColor:e[21].colors["base-100"]})},d(e){e&&d(t),s=!1,n()}}}function Cr(e){let t,i,s,n,a,l,r,o,c,p,u=!e[16]&&hn(e);s=new cr({props:{config:e[0],height:e[3],width:e[4],copying:e[15],printing:e[16],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[18]}});let m=(e[7]||e[8])&&yn(e),y=e[11]&&!e[16]&&_n(e),f=e[14]&&Cn(e);return{c(){t=D("div"),u&&u.c(),i=x(),ue(s.$$.fragment),n=x(),m&&m.c(),a=x(),y&&y.c(),l=x(),f&&f.c(),r=$e(),this.h()},l(e){t=M(e,"DIV",{role:!0,class:!0});var o=Q(t);u&&u.l(o),i=J(o),oe(s.$$.fragment,o),n=J(o),m&&m.l(o),a=J(o),y&&y.l(o),o.forEach(d),l=J(e),f&&f.l(e),r=$e(),this.h()},h(){h(t,"role","none"),h(t,"class","chart-container mt-2 mb-3 svelte-db4qxn")},m(d,h){E(d,t,h),u&&u.m(t,null),I(t,i),fe(s,t,null),I(t,n),m&&m.m(t,null),I(t,a),y&&y.m(t,null),E(d,l,h),f&&f.m(d,h),E(d,r,h),o=!0,c||(p=[gt(window,"copy",e[27]),gt(window,"beforeprint",e[28]),gt(window,"afterprint",e[29]),gt(window,"export-beforeprint",e[30]),gt(window,"export-afterprint",e[31]),gt(t,"mouseenter",e[33]),gt(t,"mouseleave",e[34])],c=!0)},p(e,n){e[16]?u&&(Je(),U(u,1,1,(()=>{u=null})),xe()):u?(u.p(e,n),65536&n[0]&&O(u,1)):(u=hn(e),u.c(),O(u,1),u.m(t,i));const l={};1&n[0]&&(l.config=e[0]),8&n[0]&&(l.height=e[3]),16&n[0]&&(l.width=e[4]),32768&n[0]&&(l.copying=e[15]),65536&n[0]&&(l.printing=e[16]),512&n[0]&&(l.echartsOptions=e[9]),1024&n[0]&&(l.seriesOptions=e[10]),262144&n[0]&&(l.seriesColors=e[18]),s.$set(l),e[7]||e[8]?m?(m.p(e,n),384&n[0]&&O(m,1)):(m=yn(e),m.c(),O(m,1),m.m(t,a)):m&&(Je(),U(m,1,1,(()=>{m=null})),xe()),e[11]&&!e[16]?y?(y.p(e,n),67584&n[0]&&O(y,1)):(y=_n(e),y.c(),O(y,1),y.m(t,null)):y&&(Je(),U(y,1,1,(()=>{y=null})),xe()),e[14]?f?f.p(e,n):(f=Cn(e),f.c(),f.m(r.parentNode,r)):f&&(f.d(1),f=null)},i(e){o||(O(u),O(s.$$.fragment,e),O(m),O(y),o=!0)},o(e){U(u),U(s.$$.fragment,e),U(m),U(y),o=!1},d(e){e&&(d(t),d(l),d(r)),u&&u.d(),ae(s),m&&m.d(),y&&y.d(),f&&f.d(e),c=!1,Gl(p)}}}function Tr(e,t,i){let s;const n=["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"];let a,l,r,o=vi(t,n),c=ce;e.$$.on_destroy.push((()=>c()));const{activeAppearance:d,theme:p,resolveColorsObject:h}=Bt();Dt(e,d,(e=>i(20,l=e))),Dt(e,p,(e=>i(21,r=e)));let{config:u}=t,{queryID:m}=t,{evidenceChartTitle:y}=t,{height:f="291px"}=t,{width:$="100%"}=t,{data:g}=t,{renderer:x}=t,{downloadableData:v}=t,{downloadableImage:b}=t,{echartsOptions:E}=t,{seriesOptions:T}=t,{printEchartsConfig:L}=t,{seriesColors:O}=t,{connectGroup:A}=t,{xAxisLabelOverflow:w}=t;const I=jn();let M=!1,C=!1,S=!1,D=!1;return e.$$set=e=>{t=Et(Et({},t),Gt(e)),i(25,o=vi(t,n)),"config"in e&&i(0,u=e.config),"queryID"in e&&i(1,m=e.queryID),"evidenceChartTitle"in e&&i(2,y=e.evidenceChartTitle),"height"in e&&i(3,f=e.height),"width"in e&&i(4,$=e.width),"data"in e&&i(5,g=e.data),"renderer"in e&&i(6,x=e.renderer),"downloadableData"in e&&i(7,v=e.downloadableData),"downloadableImage"in e&&i(8,b=e.downloadableImage),"echartsOptions"in e&&i(9,E=e.echartsOptions),"seriesOptions"in e&&i(10,T=e.seriesOptions),"printEchartsConfig"in e&&i(11,L=e.printEchartsConfig),"seriesColors"in e&&i(26,O=e.seriesColors),"connectGroup"in e&&i(12,A=e.connectGroup),"xAxisLabelOverflow"in e&&i(13,w=e.xAxisLabelOverflow)},e.$$.update=()=>{67108864&e.$$.dirty[0]&&(i(18,s=h(O)),c(),c=St(s,(e=>i(19,a=e))))},[u,m,y,f,$,g,x,v,b,E,T,L,A,w,M,C,S,D,s,a,l,r,d,p,I,o,O,()=>{i(15,C=!0),Yn(),setTimeout((()=>{i(15,C=!1)}),0)},()=>i(16,S=!0),()=>i(16,S=!1),()=>i(16,S=!0),()=>i(16,S=!1),()=>{i(14,M=!0),setTimeout((()=>{i(14,M=!1)}),0)},()=>i(17,D=!0),()=>i(17,D=!1)]}class Lr extends st{constructor(e){super(),rt(this,e,Tr,Cr,nt,{config:0,queryID:1,evidenceChartTitle:2,height:3,width:4,data:5,renderer:6,downloadableData:7,downloadableImage:8,echartsOptions:9,seriesOptions:10,printEchartsConfig:11,seriesColors:26,connectGroup:12,xAxisLabelOverflow:13},null,[-1,-1])}}function zl(e,t){const i=new Set(e.map((e=>e[t])));return Array.from(i)}function kr(e,t){return Zt(e,is({count:ns(t)}))[0].count}function Sr(e,t,i){let s;if("object"!=typeof i)s=Zt(e,ki(t,Zi({xTotal:Si(i)})),Ci({percentOfX:pi(i,"xTotal")}),Ki({percentOfX:i+"_pct"}));else{s=Zt(e,Ci({valueSum:0}));for(let e=0;e<s.length;e++){s[e].valueSum=0;for(let t=0;t<i.length;t++)s[e].valueSum=s[e].valueSum+s[e][i[t]]}s=Zt(s,ki(t,Zi({xTotal:Si("valueSum")})));for(let e=0;e<i.length;e++)s=Zt(s,Ci({percentOfX:pi(i[e],"xTotal")}),Ki({percentOfX:i[e]+"_pct"}))}return s}function Nl(e,t,i){return[...e].sort(((e,s)=>(e[t]<s[t]?-1:1)*(i?1:-1)))}function Pi(e,t,i){return e%(t+i)<t?0:1}function Er(e){let t,i;return t=new Fi({props:{error:e[14],title:e[8]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};16384&i[0]&&(s.error=e[14]),256&i[0]&&(s.title=e[8]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Ar(e){let t,i,s;const n=e[136].default,a=el(n,e,e[135],null);return i=new Lr({props:{config:e[20],height:e[15],width:e[13],data:e[0],queryID:e[6],evidenceChartTitle:e[7],showAllXAxisLabels:e[1],swapXY:e[3],echartsOptions:e[9],seriesOptions:e[10],printEchartsConfig:e[2],renderer:e[11],downloadableData:e[4],downloadableImage:e[5],connectGroup:e[12],xAxisLabelOverflow:e[23],seriesColors:e[16]}}),{c(){a&&a.c(),t=x(),ue(i.$$.fragment)},l(e){a&&a.l(e),t=J(e),oe(i.$$.fragment,e)},m(e,n){a&&a.m(e,n),E(e,t,n),fe(i,e,n),s=!0},p(e,t){a&&a.p&&(!s||2048&t[4])&&tl(a,n,e,e[135],s?il(n,e[135],t,null):ll(e[135]),null);const l={};1048576&t[0]&&(l.config=e[20]),32768&t[0]&&(l.height=e[15]),8192&t[0]&&(l.width=e[13]),1&t[0]&&(l.data=e[0]),64&t[0]&&(l.queryID=e[6]),128&t[0]&&(l.evidenceChartTitle=e[7]),2&t[0]&&(l.showAllXAxisLabels=e[1]),8&t[0]&&(l.swapXY=e[3]),512&t[0]&&(l.echartsOptions=e[9]),1024&t[0]&&(l.seriesOptions=e[10]),4&t[0]&&(l.printEchartsConfig=e[2]),2048&t[0]&&(l.renderer=e[11]),16&t[0]&&(l.downloadableData=e[4]),32&t[0]&&(l.downloadableImage=e[5]),4096&t[0]&&(l.connectGroup=e[12]),65536&t[0]&&(l.seriesColors=e[16]),i.$set(l)},i(e){s||(O(a,e),O(i.$$.fragment,e),s=!0)},o(e){U(a,e),U(i.$$.fragment,e),s=!1},d(e){e&&d(t),a&&a.d(e),ae(i,e)}}}function wr(e){let t,i,s,n;const a=[Ar,Er],l=[];function r(e,t){return e[14]?1:0}return t=r(e),i=l[t]=a[t](e),{c(){i.c(),s=$e()},l(e){i.l(e),s=$e()},m(e,i){l[t].m(e,i),E(e,s,i),n=!0},p(e,n){let o=t;t=r(e),t===o?l[t].p(e,n):(Je(),U(l[o],1,1,(()=>{l[o]=null})),xe(),i=l[t],i?i.p(e,n):(i=l[t]=a[t](e),i.c()),O(i,1),i.m(s.parentNode,s))},i(e){n||(O(i),n=!0)},o(e){U(i),n=!1},d(e){e&&d(s),l[t].d(e)}}}function Or(e,t,i){let s,n,a,l,r,o,c,d,p,h=ce,u=ce,m=ce;e.$$.on_destroy.push((()=>h())),e.$$.on_destroy.push((()=>u())),e.$$.on_destroy.push((()=>m()));let{$$slots:y={},$$scope:f}=t,$=Ei({}),g=Ei({});Dt(e,g,(e=>i(20,p=e)));const{theme:x,resolveColor:v,resolveColorsObject:b,resolveColorPalette:E}=Bt();Dt(e,x,(e=>i(132,o=e)));let T,{data:L}=t,{queryID:O}=t,{x:A}=t,{y:w}=t,{y2:I}=t,{series:M}=t,{size:C}=t,{tooltipTitle:S}=t,{showAllXAxisLabels:D}=t,{printEchartsConfig:k=!1}=t,N=!!w,U=!!A,{swapXY:F=!1}=t,{title:P}=t,{subtitle:R}=t,{chartType:B="Chart"}=t,{bubble:G=!1}=t,{hist:z=!1}=t,{boxplot:J=!1}=t,{xType:H}=t,{xAxisTitle:V="false"}=t,{xBaseline:Q=!0}=t,{xTickMarks:_=!1}=t,{xGridlines:W=!1}=t,{xAxisLabels:q=!0}=t,{sort:j=!0}=t,{xFmt:Y}=t,{xMin:X}=t,{xMax:Z}=t,{yLog:K=!1}=t,{yType:ee=(!0===K?"log":"value")}=t,{yLogBase:te=10}=t,{yAxisTitle:ie="false"}=t,{yBaseline:se=!1}=t,{yTickMarks:ne=!1}=t,{yGridlines:ae=!0}=t,{yAxisLabels:le=!0}=t,{yMin:re}=t,{yMax:oe}=t,{yScale:de=!1}=t,{yFmt:pe}=t,{yAxisColor:he="true"}=t,{y2AxisTitle:ue="false"}=t,{y2Baseline:me=!1}=t,{y2TickMarks:ye=!1}=t,{y2Gridlines:fe=!0}=t,{y2AxisLabels:$e=!0}=t,{y2Min:ge}=t,{y2Max:xe}=t,{y2Scale:ve=!1}=t,{y2Fmt:be}=t,{y2AxisColor:Ee="true"}=t,{sizeFmt:Te}=t,{colorPalette:Le="default"}=t,{legend:Oe}=t,{echartsOptions:Ae}=t,{seriesOptions:we}=t,{seriesColors:Ie}=t,{stackType:Me}=t,{stacked100:Ce=!1}=t,{chartAreaHeight:Se}=t,{renderer:De}=t,{downloadableData:ke=!0}=t,{downloadableImage:Ne=!0}=t,{connectGroup:Ue}=t,{leftPadding:Fe}=t,{rightPadding:Pe}=t,{xLabelWrap:Re=!1}=t;const Be=Re?"break":"truncate";let Ge,ze,Je,He,Qe,_e,We,qe,je,Ye,Xe,Ze,Ke,et,tt,it,st,nt,at,lt,rt,ot,dt,pt,ht,ut,mt,yt,ft,$t,gt,xt,vt,bt,Et,Tt,Lt,Ot,At,wt,Mt,Ct,kt,Nt,Ut,Ft,Pt,Rt,Gt=[],zt=[],Jt=[],Ht=!0,Vt=[],Qt=[];return e.$$set=e=>{"data"in e&&i(0,L=e.data),"queryID"in e&&i(6,O=e.queryID),"x"in e&&i(24,A=e.x),"y"in e&&i(25,w=e.y),"y2"in e&&i(49,I=e.y2),"series"in e&&i(50,M=e.series),"size"in e&&i(51,C=e.size),"tooltipTitle"in e&&i(52,S=e.tooltipTitle),"showAllXAxisLabels"in e&&i(1,D=e.showAllXAxisLabels),"printEchartsConfig"in e&&i(2,k=e.printEchartsConfig),"swapXY"in e&&i(3,F=e.swapXY),"title"in e&&i(7,P=e.title),"subtitle"in e&&i(53,R=e.subtitle),"chartType"in e&&i(8,B=e.chartType),"bubble"in e&&i(54,G=e.bubble),"hist"in e&&i(55,z=e.hist),"boxplot"in e&&i(56,J=e.boxplot),"xType"in e&&i(26,H=e.xType),"xAxisTitle"in e&&i(27,V=e.xAxisTitle),"xBaseline"in e&&i(28,Q=e.xBaseline),"xTickMarks"in e&&i(29,_=e.xTickMarks),"xGridlines"in e&&i(30,W=e.xGridlines),"xAxisLabels"in e&&i(31,q=e.xAxisLabels),"sort"in e&&i(32,j=e.sort),"xFmt"in e&&i(57,Y=e.xFmt),"xMin"in e&&i(58,X=e.xMin),"xMax"in e&&i(59,Z=e.xMax),"yLog"in e&&i(33,K=e.yLog),"yType"in e&&i(60,ee=e.yType),"yLogBase"in e&&i(61,te=e.yLogBase),"yAxisTitle"in e&&i(34,ie=e.yAxisTitle),"yBaseline"in e&&i(35,se=e.yBaseline),"yTickMarks"in e&&i(36,ne=e.yTickMarks),"yGridlines"in e&&i(37,ae=e.yGridlines),"yAxisLabels"in e&&i(38,le=e.yAxisLabels),"yMin"in e&&i(62,re=e.yMin),"yMax"in e&&i(63,oe=e.yMax),"yScale"in e&&i(39,de=e.yScale),"yFmt"in e&&i(64,pe=e.yFmt),"yAxisColor"in e&&i(65,he=e.yAxisColor),"y2AxisTitle"in e&&i(40,ue=e.y2AxisTitle),"y2Baseline"in e&&i(41,me=e.y2Baseline),"y2TickMarks"in e&&i(42,ye=e.y2TickMarks),"y2Gridlines"in e&&i(43,fe=e.y2Gridlines),"y2AxisLabels"in e&&i(44,$e=e.y2AxisLabels),"y2Min"in e&&i(66,ge=e.y2Min),"y2Max"in e&&i(67,xe=e.y2Max),"y2Scale"in e&&i(45,ve=e.y2Scale),"y2Fmt"in e&&i(68,be=e.y2Fmt),"y2AxisColor"in e&&i(69,Ee=e.y2AxisColor),"sizeFmt"in e&&i(70,Te=e.sizeFmt),"colorPalette"in e&&i(71,Le=e.colorPalette),"legend"in e&&i(46,Oe=e.legend),"echartsOptions"in e&&i(9,Ae=e.echartsOptions),"seriesOptions"in e&&i(10,we=e.seriesOptions),"seriesColors"in e&&i(72,Ie=e.seriesColors),"stackType"in e&&i(73,Me=e.stackType),"stacked100"in e&&i(74,Ce=e.stacked100),"chartAreaHeight"in e&&i(47,Se=e.chartAreaHeight),"renderer"in e&&i(11,De=e.renderer),"downloadableData"in e&&i(4,ke=e.downloadableData),"downloadableImage"in e&&i(5,Ne=e.downloadableImage),"connectGroup"in e&&i(12,Ue=e.connectGroup),"leftPadding"in e&&i(75,Fe=e.leftPadding),"rightPadding"in e&&i(76,Pe=e.rightPadding),"xLabelWrap"in e&&i(48,Re=e.xLabelWrap),"$$scope"in e&&i(135,f=e.$$scope)},e.$$.update=()=>{var t,p,y,f,x,O;if(4&e.$$.dirty[0]&&i(2,k=Ve(k)),8&e.$$.dirty[0]&&i(3,F=Ve(F)),268435456&e.$$.dirty[0]&&i(28,Q=Ve(Q)),536870912&e.$$.dirty[0]&&i(29,_=Ve(_)),1073741824&e.$$.dirty[0]&&i(30,W=Ve(W)),1&e.$$.dirty[1]&&i(31,q=Ve(q)),2&e.$$.dirty[1]&&i(32,j=Ve(j)),4&e.$$.dirty[1]&&i(33,K=Ve(K)),16&e.$$.dirty[1]&&i(35,se=Ve(se)),32&e.$$.dirty[1]&&i(36,ne=Ve(ne)),64&e.$$.dirty[1]&&i(37,ae=Ve(ae)),128&e.$$.dirty[1]&&i(38,le=Ve(le)),256&e.$$.dirty[1]&&i(39,de=Ve(de)),8&e.$$.dirty[2]&&(i(19,s=v(he)),m(),m=St(s,(e=>i(134,d=e)))),1024&e.$$.dirty[1]&&i(41,me=Ve(me)),2048&e.$$.dirty[1]&&i(42,ye=Ve(ye)),4096&e.$$.dirty[1]&&i(43,fe=Ve(fe)),8192&e.$$.dirty[1]&&i(44,$e=Ve($e)),16384&e.$$.dirty[1]&&i(45,ve=Ve(ve)),128&e.$$.dirty[2]&&(i(18,n=v(Ee)),u(),u=St(n,(e=>i(133,c=e)))),512&e.$$.dirty[2]&&(i(17,a=E(Le)),h(),h=St(a,(e=>i(131,r=e)))),1024&e.$$.dirty[2]&&i(16,l=b(Ie)),16&e.$$.dirty[0]&&i(4,ke=Ve(ke)),32&e.$$.dirty[0]&&i(5,Ne=Ve(Ne)),131072&e.$$.dirty[1]&&i(48,Re=Ve(Re)),2130731403&e.$$.dirty[0]|2147352575&e.$$.dirty[1]|2147481975&e.$$.dirty[2]|2147483647&e.$$.dirty[3]|2047&e.$$.dirty[4])try{if(i(14,Ft=void 0),i(124,Jt=[]),i(83,zt=[]),i(126,Vt=[]),i(127,Qt=[]),i(85,He=[]),i(77,N=!!w),i(78,U=!!A),Cl(L),i(80,Ge=_l(L)),i(81,ze=Object.keys(Ge)),U||i(24,A=ze[0]),!N){i(82,Gt=ze.filter((function(e){return![A,M,C].includes(e)})));for(let e=0;e<Gt.length;e++)i(85,He=Gt[e]),i(84,Je=Ge[He].type),"number"===Je&&zt.push(He);i(25,w=zt.length>1?zt:zt[0])}i(79,T=G?{x:A,y:w,size:C}:z?{x:A}:J?{}:{x:A,y:w});for(let e in T)null==T[e]&&Jt.push(e);if(1===Jt.length)throw Error((new Intl.ListFormat).format(Jt)+" is required");if(Jt.length>1)throw Error((new Intl.ListFormat).format(Jt)+" are required");if(!0===Ce&&w.includes("_pct")&&!1===Ht)if("object"==typeof w){for(let e=0;e<w.length;e++)i(25,w[e]=w[e].replace("_pct",""),w);i(125,Ht=!1)}else i(25,w=w.replace("_pct","")),i(125,Ht=!1);if(A&&Vt.push(A),w)if("object"==typeof w)for(i(128,Ut=0);Ut<w.length;i(128,Ut++,Ut))Vt.push(w[Ut]);else Vt.push(w);if(I)if("object"==typeof I)for(i(128,Ut=0);Ut<I.length;i(128,Ut++,Ut))Vt.push(I[Ut]);else Vt.push(I);if(C&&Vt.push(C),M&&Qt.push(M),S&&Qt.push(S),Cl(L,Vt,Qt),!0===Ce){if(i(0,L=Sr(L,A,w)),"object"==typeof w){for(let e=0;e<w.length;e++)i(25,w[e]=w[e]+"_pct",w);i(125,Ht=!1)}else i(25,w+="_pct"),i(125,Ht=!1);i(80,Ge=_l(L))}switch(i(86,Qe=Ge[A].type),Qe){case"number":i(86,Qe="value");break;case"string":i(86,Qe="category");break;case"date":i(86,Qe="time")}if(i(26,H="category"===H?"category":Qe),i(1,D=D?"true"===D||!0===D:"category"===H),F&&"category"!==H)throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(F&&I)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(F&&i(26,H="category"),i(87,_e="value"===Qe&&"category"===H),i(0,L=j?"category"===Qe?Nl(L,w,!1):Nl(L,A,!0):L),"time"===Qe&&i(0,L=Nl(L,A,!0)),i(129,Pt=_l(L,"array")),i(130,Rt=Pt.filter((e=>"date"===e.type))),i(130,Rt=Rt.map((e=>e.id))),Rt.length>0)for(let e=0;e<Rt.length;e++)i(0,L=ss(L,Rt[e]));i(88,We=Y?It(Y,null==(t=Ge[A].format)?void 0:t.valueType):Ge[A].format),i(89,qe=w?pe?It(pe,"object"==typeof w?null==(p=Ge[w[0]].format)?void 0:p.valueType:null==(y=Ge[w].format)?void 0:y.valueType):"object"==typeof w?Ge[w[0]].format:Ge[w].format:"str"),I&&i(90,je=be?It(be,"object"==typeof I?null==(f=Ge[I[0]].format)?void 0:f.valueType:null==(x=Ge[I].format)?void 0:x.valueType):"object"==typeof I?Ge[I[0]].format:Ge[I].format),C&&i(91,Ye=Te?It(Te,null==(O=Ge[C].format)?void 0:O.valueType):Ge[C].format),i(92,Xe=Ge[A].columnUnitSummary),w&&i(93,Ze="object"==typeof w?Ge[w[0]].columnUnitSummary:Ge[w].columnUnitSummary),I&&i(94,Ke="object"==typeof I?Ge[I[0]].columnUnitSummary:Ge[I].columnUnitSummary),i(27,V="true"===V?Wt(A,We):"false"===V?"":V),i(34,ie="true"===ie?"object"==typeof w?"":Wt(w,qe):"false"===ie?"":ie),i(40,ue="true"===ue?"object"==typeof I?"":Wt(I,je):"false"===ue?"":ue);let e,s,n="object"==typeof w?w.length:1,a=M?kr(L,M):1,l=n*a,h="object"==typeof I?I.length:I?1:0,u=l+h;if(void 0!==Oe&&i(46,Oe="true"===Oe||!0===Oe),i(46,Oe=Oe??u>1),!0===Ce&&!0===K)throw Error("Log axis cannot be used in a 100% stacked chart");if("stacked"===Me&&u>1&&!0===K)throw Error("Log axis cannot be used in a stacked chart");if("object"==typeof w){e=Ge[w[0]].columnUnitSummary.min;for(let t=0;t<w.length;t++)Ge[w[t]].columnUnitSummary.min<e&&(e=Ge[w[t]].columnUnitSummary.min)}else w&&(e=Ge[w].columnUnitSummary.min);if(!0===K&&e<=0&&null!==e)throw Error("Log axis cannot display values less than or equal to zero");if($.update((e=>({...e,data:L,x:A,y:w,y2:I,series:M,swapXY:F,sort:j,xType:H,xFormat:We,yFormat:qe,y2Format:je,sizeFormat:Ye,xMismatch:_e,size:C,yMin:re,y2Min:ge,columnSummary:Ge,xAxisTitle:V,yAxisTitle:ie,y2AxisTitle:ue,tooltipTitle:S,chartAreaHeight:Se,chartType:B,yCount:n,y2Count:h}))),i(95,et=zl(L,A)),i(96,tt=F?{type:ee,logBase:te,position:"top",axisLabel:{show:le,hideOverlap:!0,showMaxLabel:!0,formatter:e=>Yl(e,qe,Ze),margin:4},min:re,max:oe,scale:de,splitLine:{show:ae},axisLine:{show:se,onZero:!1},axisTick:{show:ne},boundaryGap:!1,z:2}:{type:H,min:X,max:Z,tooltip:{show:!0,position:"inside",formatter(e){if(e.isTruncated())return e.name}},splitLine:{show:W},axisLine:{show:Q},axisTick:{show:_},axisLabel:{show:q,hideOverlap:!0,showMaxLabel:"category"===H||"value"===H,formatter:"time"!==H&&"category"!==H&&function(e){return Yl(e,We,Xe)},margin:6},scale:!0,z:2}),F?i(97,it={type:H,inverse:"true",splitLine:{show:W},axisLine:{show:Q},axisTick:{show:_},axisLabel:{show:q,hideOverlap:!0},scale:!0,min:X,max:Z,z:2}):(i(97,it={type:ee,logBase:te,splitLine:{show:ae},axisLine:{show:se,onZero:!1},axisTick:{show:ne},axisLabel:{show:le,hideOverlap:!0,margin:4,formatter:e=>Yl(e,qe,Ze),color:I?"true"===d?r[0]:"false"!==d?d:void 0:void 0},name:ie,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:I?"true"===d?r[0]:"false"!==d?d:void 0:void 0},nameGap:6,min:re,max:oe,scale:de,boundaryGap:["0%","1%"],z:2}),s={type:"value",show:!1,alignTicks:!0,splitLine:{show:fe},axisLine:{show:me,onZero:!1},axisTick:{show:ye},axisLabel:{show:$e,hideOverlap:!0,margin:4,formatter:e=>Yl(e,je,Ke),color:"true"===c?r[l]:"false"!==c?c:void 0},name:ue,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:"true"===c?r[l]:"false"!==c?c:void 0},nameGap:6,min:ge,max:xe,scale:ve,boundaryGap:["0%","1%"],z:2},i(97,it=[it,s])),Se){if(i(47,Se=Number(Se)),isNaN(Se))throw Error("chartAreaHeight must be a number");if(Se<=0)throw Error("chartAreaHeight must be a positive number")}else i(47,Se=180);i(100,at=!!P),i(101,lt=!!R),i(102,rt=Oe*(null!==M||"object"==typeof w&&w.length>1)),i(103,ot=""!==ie&&F),i(104,dt=""!==V&&!F),i(105,pt=15),i(106,ht=13),i(107,ut=6*lt),i(108,mt=at*pt+lt*ht+ut*Math.max(at,lt)),i(109,yt=10),i(110,ft=10),i(111,$t=14),i(112,gt=14),i(113,xt=15),i(113,xt*=rt),i(114,vt=7),i(114,vt*=Math.max(at,lt)),i(115,bt=mt+vt),i(116,Et=bt+xt+gt*ot+yt),i(117,Tt=dt*$t+ft),i(121,wt=8),i(123,Ct=1),F&&(i(122,Mt=et.length),i(123,Ct=Math.max(1,Mt/wt))),i(118,Lt=Se*Ct+Et+Tt),i(119,Ot=bt+xt+7),i(15,kt=Lt+"px"),i(13,Nt="100%"),i(120,At=F?ie:V),""!==At&&i(120,At+=" →"),i(98,st={id:"horiz-axis-title",type:"text",style:{text:At,textAlign:"right",fill:o.colors["base-content-muted"]},cursor:"auto",right:F?"2%":"3%",top:F?Ot:null,bottom:F?null:"2%"}),i(99,nt={title:{text:P,subtext:R,subtextStyle:{width:Nt}},tooltip:{trigger:"axis",show:!0,formatter(e){let t,i,s,a;if(u>1){i=e[0].value[F?1:0],t=`<span id="tooltip" style='font-weight: 600;'>${ct(i,We)}</span>`;for(let i=e.length-1;i>=0;i--)"stackTotal"!==e[i].seriesName&&(s=e[i].value[F?0:1],t+=`<br> <span style='font-size: 11px;'>${e[i].marker} ${e[i].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${ct(s,0===Pi(e[i].componentIndex,n,h)?qe:je)}</span>`)}else"value"===H?(i=e[0].value[F?1:0],s=e[0].value[F?0:1],a=e[0].seriesName,t=`<span id="tooltip" style='font-weight: 600;'>${Wt(A,We)}: </span><span style='float:right; margin-left: 10px;'>${ct(i,We)}</span><br/><span style='font-weight: 600;'>${Wt(a,qe)}: </span><span style='float:right; margin-left: 10px;'>${ct(s,qe)}</span>`):(i=e[0].value[F?1:0],s=e[0].value[F?0:1],a=e[0].seriesName,t=`<span id="tooltip" style='font-weight: 600;'>${ct(i,We)}</span><br/><span>${Wt(a,qe)}: </span><span style='float:right; margin-left: 10px;'>${ct(s,qe)}</span>`);return t},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:Oe,type:"scroll",top:bt,padding:[0,0,0,0],data:[]},grid:{left:Fe??(F?"1%":"0.8%"),right:Pe??(F?"4%":"3%"),bottom:Tt,top:Et,containLabel:!0},xAxis:tt,yAxis:it,series:[],animation:!0,graphic:st,color:r}),g.update((()=>nt))}catch(e){if(i(14,Ft=e.message),console.error("[31m%s[0m",`Error in ${B}: ${e.message}`),Mi)throw Ft;$.update((e=>({...e,error:Ft})))}e.$$.dirty[0]},Li(Di,$),Li(Ni,g),[L,D,k,F,ke,Ne,O,P,B,Ae,we,De,Ue,Nt,Ft,kt,l,a,n,s,p,g,x,Be,A,w,H,V,Q,_,W,q,j,K,ie,se,ne,ae,le,de,ue,me,ye,fe,$e,ve,Oe,Se,Re,I,M,C,S,R,G,z,J,Y,X,Z,ee,te,re,oe,pe,he,ge,xe,be,Ee,Te,Le,Ie,Me,Ce,Fe,Pe,N,U,T,Ge,ze,Gt,zt,Je,He,Qe,_e,We,qe,je,Ye,Xe,Ze,Ke,et,tt,it,st,nt,at,lt,rt,ot,dt,pt,ht,ut,mt,yt,ft,$t,gt,xt,vt,bt,Et,Tt,Lt,Ot,At,wt,Mt,Ct,Jt,Ht,Vt,Qt,Ut,Pt,Rt,r,o,c,d,f,y]}class Ir extends st{constructor(e){super(),rt(this,e,Or,wr,nt,{data:0,queryID:6,x:24,y:25,y2:49,series:50,size:51,tooltipTitle:52,showAllXAxisLabels:1,printEchartsConfig:2,swapXY:3,title:7,subtitle:53,chartType:8,bubble:54,hist:55,boxplot:56,xType:26,xAxisTitle:27,xBaseline:28,xTickMarks:29,xGridlines:30,xAxisLabels:31,sort:32,xFmt:57,xMin:58,xMax:59,yLog:33,yType:60,yLogBase:61,yAxisTitle:34,yBaseline:35,yTickMarks:36,yGridlines:37,yAxisLabels:38,yMin:62,yMax:63,yScale:39,yFmt:64,yAxisColor:65,y2AxisTitle:40,y2Baseline:41,y2TickMarks:42,y2Gridlines:43,y2AxisLabels:44,y2Min:66,y2Max:67,y2Scale:45,y2Fmt:68,y2AxisColor:69,sizeFmt:70,colorPalette:71,legend:46,echartsOptions:9,seriesOptions:10,seriesColors:72,stackType:73,stacked100:74,chartAreaHeight:47,renderer:11,downloadableData:4,downloadableImage:5,connectGroup:12,leftPadding:75,rightPadding:76,xLabelWrap:48},null,[-1,-1,-1,-1,-1])}}function Mr(e){let t;const i=e[7].default,s=el(i,e,e[8],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,i){s&&s.m(e,i),t=!0},p(e,n){s&&s.p&&(!t||256&n)&&tl(s,i,e,e[8],t?il(i,e[8],n,null):ll(e[8]),null)},i(e){t||(O(s,e),t=!0)},o(e){U(s,e),t=!1},d(e){s&&s.d(e)}}}function Dr(e){let t,i;const s=[e[5],{data:Mt.isQuery(e[11])?Array.from(e[11]):e[11]},{queryID:e[6]}];let n={$$slots:{default:[Mr]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)n=Et(n,s[e]);return t=new Ir({props:n}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const n=2144&i?Kl(s,[32&i&&Zl(e[5]),2048&i&&{data:Mt.isQuery(e[11])?Array.from(e[11]):e[11]},64&i&&{queryID:e[6]}]):{};256&i&&(n.$$scope={dirty:i,ctx:e}),t.$set(n)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Nr(e){let t,i;return t=new Jl({props:{slot:"empty",emptyMessage:e[2],emptySet:e[1],chartType:e[5].chartType,isInitial:e[4]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};4&i&&(s.emptyMessage=e[2]),2&i&&(s.emptySet=e[1]),32&i&&(s.chartType=e[5].chartType),16&i&&(s.isInitial=e[4]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Fr(e){let t,i;return t=new Fi({props:{slot:"error",title:e[5].chartType,error:e[11].error.message}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};32&i&&(s.title=e[5].chartType),2048&i&&(s.error=e[11].error.message),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Pr(e){let t,i;return t=new Ql({props:{data:e[0],height:e[3],$$slots:{error:[Fr,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0],empty:[Nr],default:[Dr,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0]},$$scope:{ctx:e}}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,[i]){const s={};1&i&&(s.data=e[0]),8&i&&(s.height=e[3]),2358&i&&(s.$$scope={dirty:i,ctx:e}),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Rr(e,t,i){let s,{$$slots:n={},$$scope:a}=t,{data:l}=t;const r=Mt.isQuery(l)?l.hash:void 0;let o=(null==l?void 0:l.hash)===r,{emptySet:c}=t,{emptyMessage:d}=t,{height:p=200}=t,h=null==l?void 0:l.id;return e.$$set=e=>{i(10,t=Et(Et({},t),Gt(e))),"data"in e&&i(0,l=e.data),"emptySet"in e&&i(1,c=e.emptySet),"emptyMessage"in e&&i(2,d=e.emptyMessage),"height"in e&&i(3,p=e.height),"$$scope"in e&&i(8,a=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&i(4,o=(null==l?void 0:l.hash)===r),i(5,s={...Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e)))})},t=Gt(t),[l,c,d,p,o,s,h,n,a]}class Bn extends st{constructor(e){super(),rt(this,e,Rr,Pr,nt,{data:0,emptySet:1,emptyMessage:2,height:3})}}function zn(e,t,i,s,n,a,l,r,o,c,d=void 0,p=void 0,h=void 0,u=void 0){function m(e,t,i,s){let n={name:t,data:e,yAxisIndex:i};return n={...s,...n},n}let y,f,$,g,x,v,b,E,T=[],L=function(e,t){const i=[];function s(e,t){(function(e){return typeof e>"u"})(e)||(Array.isArray(e)?e.forEach((e=>i.push([e,t]))):i.push([e,t]))}return s(e,0),s(t,1),i}(i,h);if(null!=s&&1===L.length)for(b=zl(e,s),y=0;y<b.length;y++){if(x=e.filter((e=>e[s]===b[y])),g=n?x.map((e=>[e[L[0][0]],r?e[t].toString():e[t]])):x.map((e=>[r?e[t].toString():e[t],e[L[0][0]]])),d){let e=x.map((e=>e[d]));g.forEach(((t,i)=>t.push(e[i])))}if(p){let e=x.map((e=>e[p]));g.forEach(((t,i)=>t.push(e[i])))}v=b[y]??"null",E=L[0][1],$=m(g,v,E,a),T.push($)}if(null!=s&&L.length>1)for(b=zl(e,s),y=0;y<b.length;y++)for(x=e.filter((e=>e[s]===b[y])),f=0;f<L.length;f++){if(g=n?x.map((e=>[e[L[f][0]],r?e[t].toString():e[t]])):x.map((e=>[r?e[t].toString():e[t],e[L[f][0]]])),d){let e=x.map((e=>e[d]));g.forEach(((t,i)=>t.push(e[i])))}if(p){let e=x.map((e=>e[p]));g.forEach(((t,i)=>t.push(e[i])))}v=(b[y]??"null")+" - "+o[L[f][0]].title,E=L[f][1],$=m(g,v,E,a),T.push($)}if(null==s&&L.length>1)for(y=0;y<L.length;y++){if(g=n?e.map((e=>[e[L[y][0]],r?e[t].toString():e[t]])):e.map((e=>[r?e[t].toString():e[t],e[L[y][0]]])),d){let t=e.map((e=>e[d]));g.forEach(((e,i)=>e.push(t[i])))}if(p){let t=e.map((e=>e[p]));g.forEach(((e,i)=>e.push(t[i])))}v=o[L[y][0]].title,E=L[y][1],$=m(g,v,E,a),T.push($)}if(null==s&&1===L.length){if(g=n?e.map((e=>[e[L[0][0]],r?e[t].toString():e[t]])):e.map((e=>[r?e[t].toString():e[t],e[L[0][0]]])),d){let t=e.map((e=>e[d]));g.forEach(((e,i)=>e.push(t[i])))}if(p){let t=e.map((e=>e[p]));g.forEach(((e,i)=>e.push(t[i])))}v=o[L[0][0]].title,E=L[0][1],$=m(g,v,E,a),T.push($)}return c&&T.sort(((e,t)=>c.indexOf(e.name)-c.indexOf(t.name))),u&&T.forEach((e=>{e.name=rs(e.name,u)})),T}function Br(e){let t=[];for(let i=1;i<e.length;i++)t.push(e[i]-e[i-1]);return t}function Gn(e,t){return("number"!=typeof e||isNaN(e))&&(e=0),("number"!=typeof t||isNaN(t))&&(t=0),e=Math.abs(e),(t=Math.abs(t))<=.01?e:Gn(t,e%t)}function zr(e,t){if(!Array.isArray(e))throw new TypeError("Cannot calculate extent of non-array value.");let i,s;for(const t of e)"number"==typeof t&&(void 0===i?t>=t&&(i=s=t):(i>t&&(i=t),s<t&&(s=t)));return[i,s]}function Gr(e,t){let[i,s]=zr(e);const n=[];let a=i;for(;a<=s;)n.push(Math.round(1e8*(a+Number.EPSILON))/1e8),a+=t;return n}function Ur(e){if(e.length<=1)return;e.sort((function(e,t){return e-t}));let t=(e=Br(e=e.map((function(e){return 1e8*e})))).reduce(((e,t)=>Gn(e,t)))/1e8;return t=Math.round(1e8*(t+Number.EPSILON))/1e8,t}function Fl(e,t,i,s,n=!1,a=!1){var l;let r=!1;const o=e.map((e=>Object.assign({},e,{[t]:e[t]instanceof Date?(r=!0,e[t].toISOString()):e[t]}))).filter((e=>void 0!==e[t]&&null!==e[t])),c=Array.from(o).reduce(((e,i)=>(i[t]instanceof Date&&(i[t]=i[t].toISOString(),r=!0),s?(e[i[s]??"null"]||(e[i[s]??"null"]=[]),e[i[s]??"null"].push(i)):(e.default||(e.default=[]),e.default.push(i)),e)),{}),d={};let p;const h=(null==(l=o.find((e=>e&&null!==e[t]&&void 0!==e[t])))?void 0:l[t])??null;switch(typeof h){case"object":throw null===h?new Error(`Column '${t}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(p=zl(o,t),a){const e=Ur(p);d[t]=Gr(p,e)}break;case"string":p=zl(o,t),d[t]=p}const u=[];for(const e of Object.values(c)){const a=s?{[s]:null}:{};if(n)if(i instanceof Array)for(let e=0;e<i.length;e++)a[i[e]]=0;else a[i]=0;else if(i instanceof Array)for(let e=0;e<i.length;e++)a[i[e]]=null;else a[i]=null;s&&(d[s]=s);const l=[];0===Object.keys(d).length?l.push(Ji([t],a)):l.push(Ji(d,a)),u.push(Zt(e,...l))}return r?u.flat().map((e=>({...e,[t]:new Date(e[t])}))):u.flat()}function Tn(e,t,i){let s=Zt(e,ki(t,[as(i,Si)]));if("object"==typeof i)for(let e=0;e<s.length;e++){s[e].stackTotal=0;for(let t=0;t<i.length;t++)s[e].stackTotal=s[e].stackTotal+s[e][i[t]]}return s}let Hr=60;function Vr(e,t,i){let s,n,a,l,r,o,c,d,p,h,u,m,y,f,$,g,x,v,b,E,T,L,O,A=ce,w=ce,I=ce,M=ce;e.$$.on_destroy.push((()=>A())),e.$$.on_destroy.push((()=>w())),e.$$.on_destroy.push((()=>I())),e.$$.on_destroy.push((()=>M()));const{resolveColor:C}=Bt();let{y:S}=t;const D=!!S;let{y2:k}=t;const N=!!k;let{series:U}=t;const F=!!U;let P,{options:R}=t,{name:B}=t,{type:G="stacked"}=t,{stackName:z}=t,{fillColor:J}=t,{fillOpacity:H}=t,{outlineColor:V}=t,{outlineWidth:Q}=t,{labels:_=!1}=t,{seriesLabels:W=!0}=t,{labelSize:q=11}=t,{labelPosition:j}=t,{labelColor:Y}=t,{labelFmt:X}=t;X&&(P=It(X));let Z,{yLabelFmt:K}=t;K&&(Z=It(K));let ee,{y2LabelFmt:te}=t;te&&(ee=It(te));let ie,se,ne,ae,{y2SeriesType:le="bar"}=t,{stackTotalLabel:re=!0}=t,{showAllLabels:oe=!1}=t,{seriesOrder:de}=t;const pe={outside:"top",inside:"inside"},he={outside:"right",inside:"inside"};let{seriesLabelFmt:ue}=t;return In((()=>{R&&n.update((e=>({...e,...R}))),b&&n.update((e=>{if(G.includes("stacked")?e.tooltip={...e.tooltip,order:"seriesDesc"}:e.tooltip={...e.tooltip,order:"seriesAsc"},"stacked100"===G&&(m?e.xAxis={...e.xAxis,max:1}:e.yAxis[0]={...e.yAxis[0],max:1}),m)e.yAxis={...e.yAxis,...b.xAxis},e.xAxis={...e.xAxis,...b.yAxis};else if(e.yAxis[0]={...e.yAxis[0],...b.yAxis},e.xAxis={...e.xAxis,...b.xAxis},k&&(e.yAxis[1]={...e.yAxis[1],show:!0},["line","bar","scatter"].includes(le)))for(let t=0;t<u;t++)e.series[h+t].type=le,e.series[h+t].stack=void 0;return e}))})),e.$$set=e=>{"y"in e&&i(4,S=e.y),"y2"in e&&i(5,k=e.y2),"series"in e&&i(6,U=e.series),"options"in e&&i(13,R=e.options),"name"in e&&i(7,B=e.name),"type"in e&&i(14,G=e.type),"stackName"in e&&i(8,z=e.stackName),"fillColor"in e&&i(15,J=e.fillColor),"fillOpacity"in e&&i(16,H=e.fillOpacity),"outlineColor"in e&&i(17,V=e.outlineColor),"outlineWidth"in e&&i(18,Q=e.outlineWidth),"labels"in e&&i(9,_=e.labels),"seriesLabels"in e&&i(10,W=e.seriesLabels),"labelSize"in e&&i(19,q=e.labelSize),"labelPosition"in e&&i(11,j=e.labelPosition),"labelColor"in e&&i(20,Y=e.labelColor),"labelFmt"in e&&i(21,X=e.labelFmt),"yLabelFmt"in e&&i(22,K=e.yLabelFmt),"y2LabelFmt"in e&&i(23,te=e.y2LabelFmt),"y2SeriesType"in e&&i(24,le=e.y2SeriesType),"stackTotalLabel"in e&&i(12,re=e.stackTotalLabel),"showAllLabels"in e&&i(25,oe=e.showAllLabels),"seriesOrder"in e&&i(26,de=e.seriesOrder),"seriesLabelFmt"in e&&i(27,ue=e.seriesLabelFmt)},e.$$.update=()=>{32768&e.$$.dirty[0]&&(i(2,a=C(J)),w(),w=St(a,(e=>i(50,T=e)))),131072&e.$$.dirty[0]&&(i(1,l=C(V)),A(),A=St(l,(e=>i(49,E=e)))),512&e.$$.dirty[0]&&i(9,_="true"===_||!0===_),1024&e.$$.dirty[0]&&i(10,W="true"===W||!0===W),1048576&e.$$.dirty[0]&&(i(0,r=C(Y)),I(),I=St(r,(e=>i(51,L=e)))),4096&e.$$.dirty[0]&&i(12,re="true"===re||!0===re),2097152&e.$$.dirty[1]&&i(46,o=O.data),2097152&e.$$.dirty[1]&&i(42,c=O.x),16&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&i(4,S=D?S:O.y),32&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&i(5,k=N?k:O.y2),2097152&e.$$.dirty[1]&&i(40,d=O.yFormat),2097152&e.$$.dirty[1]&&i(47,p=O.y2Format),2097152&e.$$.dirty[1]&&i(35,h=O.yCount),2097152&e.$$.dirty[1]&&i(36,u=O.y2Count),2097152&e.$$.dirty[1]&&i(37,m=O.swapXY),2097152&e.$$.dirty[1]&&i(39,y=O.xType),2097152&e.$$.dirty[1]&&i(43,f=O.xMismatch),2097152&e.$$.dirty[1]&&i(44,$=O.columnSummary),2097152&e.$$.dirty[1]&&i(48,g=O.sort),64&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&i(6,U=F?U:O.series),16848&e.$$.dirty[0]|174403&e.$$.dirty[1]&&(U||"object"==typeof S?(!0===g&&"category"===y&&(i(31,ie=Tn(o,c,S)),i(31,ie=Nl(ie,"object"==typeof S?"stackTotal":S,!1)),i(32,se=ie.map((e=>e[c]))),i(46,o=[...o].sort((function(e,t){return se.indexOf(e[c])-se.indexOf(t[c])})))),m||("value"===y||"category"===y)&&G.includes("stacked")?(i(46,o=Fl(o,c,S,U,!0,"value"===y)),i(39,y="category")):"time"===y&&G.includes("stacked")&&i(46,o=Fl(o,c,S,U,!0,!0)),G.includes("stacked")?(i(8,z=z??"stack1"),i(33,ne="inside")):(i(8,z=void 0),i(33,ne=m?"right":"top"))):(i(7,B=B??Wt(S,$[S].title)),m&&"category"!==y&&(i(46,o=Fl(o,c,S,U,!0,"time"!==y)),i(39,y="category")),i(8,z="stack1"),i(33,ne=m?"right":"top"))),16400&e.$$.dirty[0]|34816&e.$$.dirty[1]&&"stacked"===G&&i(34,ae=Tn(o,c,S)),2048&e.$$.dirty[0]|68&e.$$.dirty[1]&&i(11,j=(m?he[j]:pe[j])??ne),1913458432&e.$$.dirty[0]|1901168&e.$$.dirty[1]&&i(45,x={type:"bar",stack:z,label:{show:_&&W,formatter:e=>0===e.value[m?0:1]?"":ct(e.value[m?0:1],[Z??P??d,ee??P??p][Pi(e.componentIndex,h,u)]),position:j,fontSize:q,color:L},labelLayout:{hideOverlap:!oe},emphasis:{focus:"series"},barMaxWidth:Hr,itemStyle:{color:T,opacity:H,borderColor:E,borderWidth:Q}}),201326832&e.$$.dirty[0]|63552&e.$$.dirty[1]&&i(41,v=zn(o,c,S,U,m,x,B,f,$,de,void 0,void 0,k,ue)),268981072&e.$$.dirty[0]|7880&e.$$.dirty[1]&&n.update((e=>(e.series.push(...v),e.legend.data.push(...v.map((e=>e.name.toString()))),!0===_&&"stacked"===G&&"object"==typeof S|void 0!==U&&!0===re&&U!==c&&(e.series.push({type:"bar",stack:z,name:"stackTotal",color:"none",data:ae.map((e=>[m?0:f?e[c].toString():e[c],m?f?e[c].toString():e[c]:0])),label:{show:!0,position:m?"right":"top",formatter(e){let t=0;return v.forEach((i=>{t+=i.data[e.dataIndex][m?0:1]})),0===t?"":ct(t,P??d)},fontWeight:"bold",fontSize:q,padding:m?[0,0,0,5]:void 0}}),e.legend.selectedMode=!1),e))),256&e.$$.dirty[1]&&(b={xAxis:{boundaryGap:["1%","2%"],type:y}})},i(3,s=Pl(Di)),M(),M=St(s,(e=>i(52,O=e))),i(38,n=Pl(Ni)),[r,l,a,s,S,k,U,B,z,_,W,j,re,R,G,J,H,V,Q,q,Y,X,K,te,le,oe,de,ue,P,Z,ee,ie,se,ne,ae,h,u,m,n,y,d,v,c,f,$,x,o,p,g,E,T,L,O]}class Wr extends st{constructor(e){super(),rt(this,e,Vr,null,nt,{y:4,y2:5,series:6,options:13,name:7,type:14,stackName:8,fillColor:15,fillOpacity:16,outlineColor:17,outlineWidth:18,labels:9,seriesLabels:10,labelSize:19,labelPosition:11,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,stackTotalLabel:12,showAllLabels:25,seriesOrder:26,seriesLabelFmt:27},null,[-1,-1])}}function qr(e){let t,i,s;t=new Wr({props:{type:e[38],fillColor:e[72],fillOpacity:e[39],outlineColor:e[71],outlineWidth:e[40],labels:e[43],labelSize:e[44],labelPosition:e[45],labelColor:e[69],labelFmt:e[46],yLabelFmt:e[47],y2LabelFmt:e[48],stackTotalLabel:e[49],seriesLabels:e[50],showAllLabels:e[51],y2SeriesType:e[9],seriesOrder:e[60],seriesLabelFmt:e[62]}});const n=e[81].default,a=el(n,e,e[82],null);return{c(){ue(t.$$.fragment),i=x(),a&&a.c()},l(e){oe(t.$$.fragment,e),i=J(e),a&&a.l(e)},m(e,n){fe(t,e,n),E(e,i,n),a&&a.m(e,n),s=!0},p(e,i){const l={};128&i[1]&&(l.type=e[38]),1024&i[2]&&(l.fillColor=e[72]),256&i[1]&&(l.fillOpacity=e[39]),512&i[2]&&(l.outlineColor=e[71]),512&i[1]&&(l.outlineWidth=e[40]),4096&i[1]&&(l.labels=e[43]),8192&i[1]&&(l.labelSize=e[44]),16384&i[1]&&(l.labelPosition=e[45]),128&i[2]&&(l.labelColor=e[69]),32768&i[1]&&(l.labelFmt=e[46]),65536&i[1]&&(l.yLabelFmt=e[47]),131072&i[1]&&(l.y2LabelFmt=e[48]),262144&i[1]&&(l.stackTotalLabel=e[49]),524288&i[1]&&(l.seriesLabels=e[50]),1048576&i[1]&&(l.showAllLabels=e[51]),512&i[0]&&(l.y2SeriesType=e[9]),536870912&i[1]&&(l.seriesOrder=e[60]),1&i[2]&&(l.seriesLabelFmt=e[62]),t.$set(l),a&&a.p&&(!s||1048576&i[2])&&tl(a,n,e,e[82],s?il(n,e[82],i,null):ll(e[82]),null)},i(e){s||(O(t.$$.fragment,e),O(a,e),s=!0)},o(e){U(t.$$.fragment,e),U(a,e),s=!1},d(e){e&&d(i),ae(t,e),a&&a.d(e)}}}function vr(e){let t,i;return t=new Bn({props:{data:e[1],x:e[2],y:e[3],y2:e[4],xFmt:e[12],yFmt:e[10],y2Fmt:e[11],series:e[5],xType:e[6],yLog:e[7],yLogBase:e[8],legend:e[15],xAxisTitle:e[16],yAxisTitle:e[17],y2AxisTitle:e[18],xGridlines:e[19],yGridlines:e[20],y2Gridlines:e[21],xAxisLabels:e[22],yAxisLabels:e[23],y2AxisLabels:e[24],xBaseline:e[25],yBaseline:e[26],y2Baseline:e[27],xTickMarks:e[28],yTickMarks:e[29],y2TickMarks:e[30],yAxisColor:e[68],y2AxisColor:e[67],yMin:e[31],yMax:e[32],yScale:e[33],y2Min:e[34],y2Max:e[35],y2Scale:e[36],swapXY:e[0],title:e[13],subtitle:e[14],chartType:"Bar Chart",stackType:e[38],sort:e[42],stacked100:e[73],chartAreaHeight:e[41],showAllXAxisLabels:e[37],colorPalette:e[70],echartsOptions:e[52],seriesOptions:e[53],printEchartsConfig:e[54],emptySet:e[55],emptyMessage:e[56],renderer:e[57],downloadableData:e[58],downloadableImage:e[59],connectGroup:e[61],xLabelWrap:e[65],seriesColors:e[66],leftPadding:e[63],rightPadding:e[64],$$slots:{default:[qr]},$$scope:{ctx:e}}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};2&i[0]&&(s.data=e[1]),4&i[0]&&(s.x=e[2]),8&i[0]&&(s.y=e[3]),16&i[0]&&(s.y2=e[4]),4096&i[0]&&(s.xFmt=e[12]),1024&i[0]&&(s.yFmt=e[10]),2048&i[0]&&(s.y2Fmt=e[11]),32&i[0]&&(s.series=e[5]),64&i[0]&&(s.xType=e[6]),128&i[0]&&(s.yLog=e[7]),256&i[0]&&(s.yLogBase=e[8]),32768&i[0]&&(s.legend=e[15]),65536&i[0]&&(s.xAxisTitle=e[16]),131072&i[0]&&(s.yAxisTitle=e[17]),262144&i[0]&&(s.y2AxisTitle=e[18]),524288&i[0]&&(s.xGridlines=e[19]),1048576&i[0]&&(s.yGridlines=e[20]),2097152&i[0]&&(s.y2Gridlines=e[21]),4194304&i[0]&&(s.xAxisLabels=e[22]),8388608&i[0]&&(s.yAxisLabels=e[23]),16777216&i[0]&&(s.y2AxisLabels=e[24]),33554432&i[0]&&(s.xBaseline=e[25]),67108864&i[0]&&(s.yBaseline=e[26]),134217728&i[0]&&(s.y2Baseline=e[27]),268435456&i[0]&&(s.xTickMarks=e[28]),536870912&i[0]&&(s.yTickMarks=e[29]),1073741824&i[0]&&(s.y2TickMarks=e[30]),64&i[2]&&(s.yAxisColor=e[68]),32&i[2]&&(s.y2AxisColor=e[67]),1&i[1]&&(s.yMin=e[31]),2&i[1]&&(s.yMax=e[32]),4&i[1]&&(s.yScale=e[33]),8&i[1]&&(s.y2Min=e[34]),16&i[1]&&(s.y2Max=e[35]),32&i[1]&&(s.y2Scale=e[36]),1&i[0]&&(s.swapXY=e[0]),8192&i[0]&&(s.title=e[13]),16384&i[0]&&(s.subtitle=e[14]),128&i[1]&&(s.stackType=e[38]),2048&i[1]&&(s.sort=e[42]),1024&i[1]&&(s.chartAreaHeight=e[41]),64&i[1]&&(s.showAllXAxisLabels=e[37]),256&i[2]&&(s.colorPalette=e[70]),2097152&i[1]&&(s.echartsOptions=e[52]),4194304&i[1]&&(s.seriesOptions=e[53]),8388608&i[1]&&(s.printEchartsConfig=e[54]),16777216&i[1]&&(s.emptySet=e[55]),33554432&i[1]&&(s.emptyMessage=e[56]),67108864&i[1]&&(s.renderer=e[57]),134217728&i[1]&&(s.downloadableData=e[58]),268435456&i[1]&&(s.downloadableImage=e[59]),1073741824&i[1]&&(s.connectGroup=e[61]),8&i[2]&&(s.xLabelWrap=e[65]),16&i[2]&&(s.seriesColors=e[66]),2&i[2]&&(s.leftPadding=e[63]),4&i[2]&&(s.rightPadding=e[64]),512&i[0]|538964864&i[1]|1050241&i[2]&&(s.$$scope={dirty:i,ctx:e}),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function jr(e,t,i){let s,n,a,l,r,o,c,{$$slots:d={},$$scope:p}=t;const{resolveColor:h,resolveColorsObject:u,resolveColorPalette:m}=Bt();let{data:y}=t,{x:f}=t,{y:$}=t,{y2:g}=t,{series:x}=t,{xType:v}=t,{yLog:b}=t,{yLogBase:E}=t,{y2SeriesType:T}=t,{yFmt:L}=t,{y2Fmt:O}=t,{xFmt:A}=t,{title:w}=t,{subtitle:I}=t,{legend:M}=t,{xAxisTitle:C}=t,{yAxisTitle:S=(g?"true":void 0)}=t,{y2AxisTitle:D=(g?"true":void 0)}=t,{xGridlines:k}=t,{yGridlines:N}=t,{y2Gridlines:U}=t,{xAxisLabels:F}=t,{yAxisLabels:P}=t,{y2AxisLabels:R}=t,{xBaseline:B}=t,{yBaseline:G}=t,{y2Baseline:z}=t,{xTickMarks:J}=t,{yTickMarks:H}=t,{y2TickMarks:V}=t,{yMin:Q}=t,{yMax:_}=t,{yScale:W}=t,{y2Min:q}=t,{y2Max:j}=t,{y2Scale:Y}=t,{swapXY:X=!1}=t,{showAllXAxisLabels:Z}=t,{type:K="stacked"}=t,ee="stacked100"===K,{fillColor:te}=t,{fillOpacity:ie}=t,{outlineColor:se}=t,{outlineWidth:ne}=t,{chartAreaHeight:ae}=t,{sort:le}=t,{colorPalette:re="default"}=t,{labels:oe}=t,{labelSize:ce}=t,{labelPosition:de}=t,{labelColor:pe}=t,{labelFmt:he}=t,{yLabelFmt:ue}=t,{y2LabelFmt:me}=t,{stackTotalLabel:ye}=t,{seriesLabels:fe}=t,{showAllLabels:$e}=t,{yAxisColor:ge}=t,{y2AxisColor:xe}=t,{echartsOptions:ve}=t,{seriesOptions:be}=t,{printEchartsConfig:Ee=!1}=t,{emptySet:Te}=t,{emptyMessage:Le}=t,{renderer:Oe}=t,{downloadableData:Ae}=t,{downloadableImage:we}=t,{seriesColors:Ie}=t,{seriesOrder:Me}=t,{connectGroup:Ce}=t,{seriesLabelFmt:Se}=t,{leftPadding:De}=t,{rightPadding:ke}=t,{xLabelWrap:Ne}=t;return e.$$set=e=>{"data"in e&&i(1,y=e.data),"x"in e&&i(2,f=e.x),"y"in e&&i(3,$=e.y),"y2"in e&&i(4,g=e.y2),"series"in e&&i(5,x=e.series),"xType"in e&&i(6,v=e.xType),"yLog"in e&&i(7,b=e.yLog),"yLogBase"in e&&i(8,E=e.yLogBase),"y2SeriesType"in e&&i(9,T=e.y2SeriesType),"yFmt"in e&&i(10,L=e.yFmt),"y2Fmt"in e&&i(11,O=e.y2Fmt),"xFmt"in e&&i(12,A=e.xFmt),"title"in e&&i(13,w=e.title),"subtitle"in e&&i(14,I=e.subtitle),"legend"in e&&i(15,M=e.legend),"xAxisTitle"in e&&i(16,C=e.xAxisTitle),"yAxisTitle"in e&&i(17,S=e.yAxisTitle),"y2AxisTitle"in e&&i(18,D=e.y2AxisTitle),"xGridlines"in e&&i(19,k=e.xGridlines),"yGridlines"in e&&i(20,N=e.yGridlines),"y2Gridlines"in e&&i(21,U=e.y2Gridlines),"xAxisLabels"in e&&i(22,F=e.xAxisLabels),"yAxisLabels"in e&&i(23,P=e.yAxisLabels),"y2AxisLabels"in e&&i(24,R=e.y2AxisLabels),"xBaseline"in e&&i(25,B=e.xBaseline),"yBaseline"in e&&i(26,G=e.yBaseline),"y2Baseline"in e&&i(27,z=e.y2Baseline),"xTickMarks"in e&&i(28,J=e.xTickMarks),"yTickMarks"in e&&i(29,H=e.yTickMarks),"y2TickMarks"in e&&i(30,V=e.y2TickMarks),"yMin"in e&&i(31,Q=e.yMin),"yMax"in e&&i(32,_=e.yMax),"yScale"in e&&i(33,W=e.yScale),"y2Min"in e&&i(34,q=e.y2Min),"y2Max"in e&&i(35,j=e.y2Max),"y2Scale"in e&&i(36,Y=e.y2Scale),"swapXY"in e&&i(0,X=e.swapXY),"showAllXAxisLabels"in e&&i(37,Z=e.showAllXAxisLabels),"type"in e&&i(38,K=e.type),"fillColor"in e&&i(74,te=e.fillColor),"fillOpacity"in e&&i(39,ie=e.fillOpacity),"outlineColor"in e&&i(75,se=e.outlineColor),"outlineWidth"in e&&i(40,ne=e.outlineWidth),"chartAreaHeight"in e&&i(41,ae=e.chartAreaHeight),"sort"in e&&i(42,le=e.sort),"colorPalette"in e&&i(76,re=e.colorPalette),"labels"in e&&i(43,oe=e.labels),"labelSize"in e&&i(44,ce=e.labelSize),"labelPosition"in e&&i(45,de=e.labelPosition),"labelColor"in e&&i(77,pe=e.labelColor),"labelFmt"in e&&i(46,he=e.labelFmt),"yLabelFmt"in e&&i(47,ue=e.yLabelFmt),"y2LabelFmt"in e&&i(48,me=e.y2LabelFmt),"stackTotalLabel"in e&&i(49,ye=e.stackTotalLabel),"seriesLabels"in e&&i(50,fe=e.seriesLabels),"showAllLabels"in e&&i(51,$e=e.showAllLabels),"yAxisColor"in e&&i(78,ge=e.yAxisColor),"y2AxisColor"in e&&i(79,xe=e.y2AxisColor),"echartsOptions"in e&&i(52,ve=e.echartsOptions),"seriesOptions"in e&&i(53,be=e.seriesOptions),"printEchartsConfig"in e&&i(54,Ee=e.printEchartsConfig),"emptySet"in e&&i(55,Te=e.emptySet),"emptyMessage"in e&&i(56,Le=e.emptyMessage),"renderer"in e&&i(57,Oe=e.renderer),"downloadableData"in e&&i(58,Ae=e.downloadableData),"downloadableImage"in e&&i(59,we=e.downloadableImage),"seriesColors"in e&&i(80,Ie=e.seriesColors),"seriesOrder"in e&&i(60,Me=e.seriesOrder),"connectGroup"in e&&i(61,Ce=e.connectGroup),"seriesLabelFmt"in e&&i(62,Se=e.seriesLabelFmt),"leftPadding"in e&&i(63,De=e.leftPadding),"rightPadding"in e&&i(64,ke=e.rightPadding),"xLabelWrap"in e&&i(65,Ne=e.xLabelWrap),"$$scope"in e&&i(82,p=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty[0]&&i(0,X="true"===X||!0===X),4096&e.$$.dirty[2]&&i(72,s=h(te)),8192&e.$$.dirty[2]&&i(71,n=h(se)),16384&e.$$.dirty[2]&&i(70,a=m(re)),32768&e.$$.dirty[2]&&i(69,l=h(pe)),65536&e.$$.dirty[2]&&i(68,r=h(ge)),131072&e.$$.dirty[2]&&i(67,o=h(xe)),262144&e.$$.dirty[2]&&i(66,c=u(Ie))},[X,y,f,$,g,x,v,b,E,T,L,O,A,w,I,M,C,S,D,k,N,U,F,P,R,B,G,z,J,H,V,Q,_,W,q,j,Y,Z,K,ie,ne,ae,le,oe,ce,de,he,ue,me,ye,fe,$e,ve,be,Ee,Te,Le,Oe,Ae,we,Me,Ce,Se,De,ke,Ne,c,o,r,l,a,n,s,ee,te,se,re,pe,ge,xe,Ie,d,p]}class Yr extends st{constructor(e){super(),rt(this,e,jr,vr,nt,{data:1,x:2,y:3,y2:4,series:5,xType:6,yLog:7,yLogBase:8,y2SeriesType:9,yFmt:10,y2Fmt:11,xFmt:12,title:13,subtitle:14,legend:15,xAxisTitle:16,yAxisTitle:17,y2AxisTitle:18,xGridlines:19,yGridlines:20,y2Gridlines:21,xAxisLabels:22,yAxisLabels:23,y2AxisLabels:24,xBaseline:25,yBaseline:26,y2Baseline:27,xTickMarks:28,yTickMarks:29,y2TickMarks:30,yMin:31,yMax:32,yScale:33,y2Min:34,y2Max:35,y2Scale:36,swapXY:0,showAllXAxisLabels:37,type:38,fillColor:74,fillOpacity:39,outlineColor:75,outlineWidth:40,chartAreaHeight:41,sort:42,colorPalette:76,labels:43,labelSize:44,labelPosition:45,labelColor:77,labelFmt:46,yLabelFmt:47,y2LabelFmt:48,stackTotalLabel:49,seriesLabels:50,showAllLabels:51,yAxisColor:78,y2AxisColor:79,echartsOptions:52,seriesOptions:53,printEchartsConfig:54,emptySet:55,emptyMessage:56,renderer:57,downloadableData:58,downloadableImage:59,seriesColors:80,seriesOrder:60,connectGroup:61,seriesLabelFmt:62,leftPadding:63,rightPadding:64,xLabelWrap:65},null,[-1,-1,-1])}}function Xr(e){let t,i;return t=new os({props:{error:e[3]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};8&i&&(s.error=e[3]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Qr(e){let t,i,s,n,a=ct(e[2],e[4])+"",l=e[1]&&Ln(e);return{c(){t=D("span"),i=Le(a),s=x(),l&&l.c(),this.h()},l(e){t=M(e,"SPAN",{style:!0});var n=Q(t);i=Te(n,a),s=J(n),l&&l.l(n),n.forEach(d),this.h()},h(){v(t,"color",e[5])},m(e,a){E(e,t,a),I(t,i),I(t,s),l&&l.m(t,null),n=!0},p(e,s){(!n||20&s)&&a!==(a=ct(e[2],e[4])+"")&&Ze(i,a),e[1]?l?(l.p(e,s),2&s&&O(l,1)):(l=Ln(e),l.c(),O(l,1),l.m(t,null)):l&&(Je(),U(l,1,1,(()=>{l=null})),xe()),(!n||32&s)&&v(t,"color",e[5])},i(e){n||(O(l),n=!0)},o(e){U(l),n=!1},d(e){e&&d(t),l&&l.d()}}}function Kr(e){let t,i,s,n,a,l="Placeholder: no data currently referenced.";return{c(){t=D("span"),i=Le("["),s=Le(e[0]),n=Le("]"),a=D("span"),a.textContent=l,this.h()},l(r){t=M(r,"SPAN",{class:!0});var o=Q(t);i=Te(o,"["),s=Te(o,e[0]),n=Te(o,"]"),a=M(o,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-ddarzq"!==Oe(a)&&(a.textContent=l),o.forEach(d),this.h()},h(){h(a,"class","error-msg svelte-1mb9o01"),h(t,"class","placeholder svelte-1mb9o01")},m(e,l){E(e,t,l),I(t,i),I(t,s),I(t,n),I(t,a)},p(e,t){1&t&&Ze(s,e[0])},i:ce,o:ce,d(e){e&&d(t)}}}function Ln(e){let t,i;return t=new Nn({props:{description:e[1]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};2&i&&(s.description=e[1]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Zr(e){let t,i,s,n;const a=[Kr,Qr,Xr],l=[];function r(e,t){return e[0]?0:e[3]?2:1}return t=r(e),i=l[t]=a[t](e),{c(){i.c(),s=$e()},l(e){i.l(e),s=$e()},m(e,i){l[t].m(e,i),E(e,s,i),n=!0},p(e,[n]){let o=t;t=r(e),t===o?l[t].p(e,n):(Je(),U(l[o],1,1,(()=>{l[o]=null})),xe(),i=l[t],i?i.p(e,n):(i=l[t]=a[t](e),i.c()),O(i,1),i.m(s.parentNode,s))},i(e){n||(O(i),n=!0)},o(e){U(i),n=!1},d(e){e&&d(s),l[t].d(e)}}}function Jr(e,t,i){let s,n,a=ce;e.$$.on_destroy.push((()=>a()));const{resolveColor:l}=Bt();let r,o,c,d,{data:p=null}=t,{row:h=0}=t,{column:u=null}=t,{value:m=null}=t,{placeholder:y=null}=t,{description:f}=t,{fmt:$}=t,{color:g}=t,x="",{redNegatives:v=!1}=t;return e.$$set=e=>{"data"in e&&i(7,p=e.data),"row"in e&&i(10,h=e.row),"column"in e&&i(8,u=e.column),"value"in e&&i(11,m=e.value),"placeholder"in e&&i(0,y=e.placeholder),"description"in e&&i(1,f=e.description),"fmt"in e&&i(12,$=e.fmt),"color"in e&&i(13,g=e.color),"redNegatives"in e&&i(9,v=e.redNegatives)},e.$$.update=()=>{var t;if(2304&e.$$.dirty&&i(8,u=u??m),21897&e.$$.dirty)try{if(i(3,c=void 0),!y){if(!p)throw Error("No data provided. If you referenced a query result, check that the name is correct.");{if("string"==typeof p)throw Error(`Received: data=${p}, expected: data={${p}}`);if(Array.isArray(p)||i(7,p=[p]),isNaN(h))throw Error("row must be a number (row="+h+")");try{Object.keys(p[h])[0]}catch{throw Error("Row "+h+" does not exist in the dataset")}i(8,u=u??Object.keys(p[h])[0]),Cl(p,[u]),i(14,d=_l(p,"array"));const e=d.filter((e=>{var t;return"date"===e.type&&!((null==(t=p[0])?void 0:t[e.id])instanceof Date)})).map((e=>e.id));for(let t=0;t<e.length;t++)i(7,p=fs(p,e[t]));i(2,o=p[h][u]),i(14,d=d.filter((e=>e.id===u))),i(4,r=$?It($,null==(t=d[0].format)?void 0:t.valueType):d[0].format)}}}catch(e){if(i(3,c=e.message),console.error("[31m%s[0m",`Error in Value: ${c}`),Mi)throw c}2304&e.$$.dirty&&m&&u&&console.warn('Both "value" and "column" were supplied as props to Value. "value" will be ignored.'),8192&e.$$.dirty&&(i(6,s=l(g)),a(),a=St(s,(e=>i(15,n=e)))),512&e.$$.dirty&&i(9,v="true"===v||!0===v),33284&e.$$.dirty&&(v||n)&&(v&&o<0?i(5,x="rgb(220 38 38)"):n&&i(5,x=n))},[y,f,o,c,r,x,s,p,u,v,h,m,$,g,d,n]}class xr extends st{constructor(e){super(),rt(this,e,Jr,Zr,nt,{data:7,row:10,column:8,value:11,placeholder:0,description:1,fmt:12,color:13,redNegatives:9})}}function pr(e){let t;const i=e[7].default,s=el(i,e,e[8],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,i){s&&s.m(e,i),t=!0},p(e,n){s&&s.p&&(!t||256&n)&&tl(s,i,e,e[8],t?il(i,e[8],n,null):ll(e[8]),null)},i(e){t||(O(s,e),t=!0)},o(e){U(s,e),t=!1},d(e){s&&s.d(e)}}}function $r(e){let t,i;const s=[e[4],{data:Mt.isQuery(e[11])?Array.from(e[11]):e[11]}];let n={$$slots:{default:[pr]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)n=Et(n,s[e]);return t=new xr({props:n}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const n=2064&i?Kl(s,[16&i&&Zl(e[4]),2048&i&&{data:Mt.isQuery(e[11])?Array.from(e[11]):e[11]}]):{};256&i&&(n.$$scope={dirty:i,ctx:e}),t.$set(n)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function kn(e){let t,i;return t=new Jl({props:{emptyMessage:e[2],emptySet:e[1],chartType:ia,isInitial:e[3]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};4&i&&(s.emptyMessage=e[2]),2&i&&(s.emptySet=e[1]),8&i&&(s.isInitial=e[3]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function ea(e){let t,i,s=!e[4].placeholder&&kn(e);return{c(){t=D("span"),s&&s.c(),this.h()},l(e){t=M(e,"SPAN",{slot:!0});var i=Q(t);s&&s.l(i),i.forEach(d),this.h()},h(){h(t,"slot","empty")},m(e,n){E(e,t,n),s&&s.m(t,null),i=!0},p(e,i){e[4].placeholder?s&&(Je(),U(s,1,1,(()=>{s=null})),xe()):s?(s.p(e,i),16&i&&O(s,1)):(s=kn(e),s.c(),O(s,1),s.m(t,null))},i(e){i||(O(s),i=!0)},o(e){U(s),i=!1},d(e){e&&d(t),s&&s.d()}}}function ta(e){let t,i="Loading...";return{c(){t=D("span"),t.textContent=i,this.h()},l(e){t=M(e,"SPAN",{slot:!0,class:!0,"data-svelte-h":!0}),"svelte-89gxhc"!==Oe(t)&&(t.textContent=i),this.h()},h(){h(t,"slot","skeleton"),h(t,"class","text-base-content-muted")},m(e,i){E(e,t,i)},p:ce,d(e){e&&d(t)}}}function la(e){let t,i;return t=new Ql({props:{data:e[0],$$slots:{skeleton:[ta],empty:[ea],default:[$r,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0]},$$scope:{ctx:e}}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,[i]){const s={};1&i&&(s.data=e[0]),2334&i&&(s.$$scope={dirty:i,ctx:e}),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}let ia="Value";function na(e,t,i){let s,{$$slots:n={},$$scope:a}=t,{data:l}=t,{column:r}=t,{agg:o}=t;const c=Mt.isQuery(l)?l.hash:void 0;let d=(null==l?void 0:l.hash)===c,{emptySet:p}=t,{emptyMessage:h}=t;return e.$$set=e=>{i(10,t=Et(Et({},t),Gt(e))),"data"in e&&i(0,l=e.data),"column"in e&&i(5,r=e.column),"agg"in e&&i(6,o=e.agg),"emptySet"in e&&i(1,p=e.emptySet),"emptyMessage"in e&&i(2,h=e.emptyMessage),"$$scope"in e&&i(8,a=e.$$scope)},e.$$.update=()=>{97&e.$$.dirty&&o&&i(0,l=l.groupBy(void 0).agg({[o]:{col:r,as:r}})),1&e.$$.dirty&&i(3,d=(null==l?void 0:l.hash)===c),i(4,s=Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e))))},t=Gt(t),[l,p,h,d,s,r,o,n,a]}class Ul extends st{constructor(e){super(),rt(this,e,na,la,nt,{data:0,column:5,agg:6,emptySet:1,emptyMessage:2})}}function sa(e){let t;const i=e[6].default,s=el(i,e,e[7],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,i){s&&s.m(e,i),t=!0},p(e,n){s&&s.p&&(!t||128&n)&&tl(s,i,e,e[7],t?il(i,e[7],n,null):ll(e[7]),null)},i(e){t||(O(s,e),t=!0)},o(e){U(s,e),t=!1},d(e){s&&s.d(e)}}}function ra(e){let t,i;const s=[e[4],{data:Mt.isQuery(e[10])?Array.from(e[10]):e[10]},{queryID:e[5]}];let n={$$slots:{default:[sa]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)n=Et(n,s[e]);return t=new us({props:n}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const n=1072&i?Kl(s,[16&i&&Zl(e[4]),1024&i&&{data:Mt.isQuery(e[10])?Array.from(e[10]):e[10]},32&i&&{queryID:e[5]}]):{};128&i&&(n.$$scope={dirty:i,ctx:e}),t.$set(n)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function aa(e){let t,i;return t=new Jl({props:{slot:"empty",emptyMessage:e[2],emptySet:e[1],chartType:e[4].chartType,isInitial:e[3]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};4&i&&(s.emptyMessage=e[2]),2&i&&(s.emptySet=e[1]),16&i&&(s.chartType=e[4].chartType),8&i&&(s.isInitial=e[3]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function fa(e){let t,i;return t=new Fi({props:{slot:"error",title:ua,error:e[10].error.message}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};1024&i&&(s.error=e[10].error.message),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function oa(e){let t,i;return t=new Ql({props:{data:e[0],$$slots:{error:[fa,({loaded:e})=>({10:e}),({loaded:e})=>e?1024:0],empty:[aa],default:[ra,({loaded:e})=>({10:e}),({loaded:e})=>e?1024:0]},$$scope:{ctx:e}}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,[i]){const s={};1&i&&(s.data=e[0]),1182&i&&(s.$$scope={dirty:i,ctx:e}),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}let ua="Sparkline";function ca(e,t,i){let s,{$$slots:n={},$$scope:a}=t,{data:l}=t;const r=Mt.isQuery(l)?l.hash:void 0;let o=(null==l?void 0:l.hash)===r,{emptySet:c}=t,{emptyMessage:d}=t,p=null==l?void 0:l.id;return e.$$set=e=>{i(9,t=Et(Et({},t),Gt(e))),"data"in e&&i(0,l=e.data),"emptySet"in e&&i(1,c=e.emptySet),"emptyMessage"in e&&i(2,d=e.emptyMessage),"$$scope"in e&&i(7,a=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&i(3,o=(null==l?void 0:l.hash)===r),i(4,s={...Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e)))})},t=Gt(t),[l,c,d,o,s,p,n,a]}class ma extends st{constructor(e){super(),rt(this,e,ca,oa,nt,{data:0,emptySet:1,emptyMessage:2})}}function da(e){let t,i,s,n,a,l,r,o,c,p,u,m,y,f=e[23]&&Sn(e);const $=[ga,ya],g=[];function v(e,t){return e[22]?0:1}r=v(e),o=g[r]=$[r](e);let b=e[8]&&En(e),T=e[7]&&An(e);return{c(){t=D("p"),i=Le(e[3]),s=x(),f&&f.c(),a=x(),l=D("div"),o.c(),c=x(),b&&b.c(),u=x(),T&&T.c(),m=$e(),this.h()},l(n){t=M(n,"P",{class:!0});var r=Q(t);i=Te(r,e[3]),s=J(r),f&&f.l(r),r.forEach(d),a=J(n),l=M(n,"DIV",{class:!0});var p=Q(l);o.l(p),c=J(p),b&&b.l(p),p.forEach(d),u=J(n),T&&T.l(n),m=$e(),this.h()},h(){h(t,"class",n=bl("text-sm align-top leading-none",e[19])),h(l,"class",p=bl("relative text-xl font-medium mt-1.5",e[20]))},m(e,n){E(e,t,n),I(t,i),I(t,s),f&&f.m(t,null),E(e,a,n),E(e,l,n),g[r].m(l,null),I(l,c),b&&b.m(l,null),E(e,u,n),T&&T.m(e,n),E(e,m,n),y=!0},p(e,s){(!y||8&s)&&Ze(i,e[3]),e[23]?f?(f.p(e,s),8388608&s&&O(f,1)):(f=Sn(e),f.c(),O(f,1),f.m(t,null)):f&&(Je(),U(f,1,1,(()=>{f=null})),xe()),(!y||524288&s&&n!==(n=bl("text-sm align-top leading-none",e[19])))&&h(t,"class",n);let a=r;r=v(e),r===a?g[r].p(e,s):(Je(),U(g[a],1,1,(()=>{g[a]=null})),xe(),o=g[r],o?o.p(e,s):(o=g[r]=$[r](e),o.c()),O(o,1),o.m(l,c)),e[8]?b?(b.p(e,s),256&s&&O(b,1)):(b=En(e),b.c(),O(b,1),b.m(l,null)):b&&(Je(),U(b,1,1,(()=>{b=null})),xe()),(!y||1048576&s&&p!==(p=bl("relative text-xl font-medium mt-1.5",e[20])))&&h(l,"class",p),e[7]?T?(T.p(e,s),128&s&&O(T,1)):(T=An(e),T.c(),O(T,1),T.m(m.parentNode,m)):T&&(Je(),U(T,1,1,(()=>{T=null})),xe())},i(e){y||(O(f),O(o),O(b),O(T),y=!0)},o(e){U(f),U(o),U(b),U(T),y=!1},d(e){e&&(d(t),d(a),d(l),d(u),d(m)),f&&f.d(),g[r].d(),b&&b.d(),T&&T.d(e)}}}function ha(e){let t,i;return t=new cs({props:{inputType:"BigValue",error:e[24],width:"148",height:"28"}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};16777216&i&&(s.error=e[24]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Sn(e){let t,i;return t=new Nn({props:{description:e[23],size:"3"}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};8388608&i&&(s.description=e[23]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function ya(e){let t,i;return t=new Ul({props:{data:e[0],column:e[6],fmt:e[13]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};1&i&&(s.data=e[0]),64&i&&(s.column=e[6]),8192&i&&(s.fmt=e[13]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function ga(e){let t,i,s,n;return i=new Ul({props:{data:e[0],column:e[6],fmt:e[13]}}),{c(){t=D("a"),ue(i.$$.fragment),this.h()},l(e){t=M(e,"A",{class:!0,href:!0});var s=Q(t);oe(i.$$.fragment,s),s.forEach(d),this.h()},h(){h(t,"class","hover:bg-base-200"),h(t,"href",s=Ll(e[22]))},m(e,s){E(e,t,s),fe(i,t,null),n=!0},p(e,a){const l={};1&a&&(l.data=e[0]),64&a&&(l.column=e[6]),8192&a&&(l.fmt=e[13]),i.$set(l),(!n||4194304&a&&s!==(s=Ll(e[22])))&&h(t,"href",s)},i(e){n||(O(i.$$.fragment,e),n=!0)},o(e){U(i.$$.fragment,e),n=!1},d(e){e&&d(t),ae(i)}}}function En(e){let t,i;return t=new ma({props:{height:"15",data:e[0],dateCol:e[8],valueCol:e[6],type:e[9],interactive:"true",color:e[25],valueFmt:e[13]??e[10],dateFmt:e[11],yScale:e[2],connectGroup:e[12]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};1&i&&(s.data=e[0]),256&i&&(s.dateCol=e[8]),64&i&&(s.valueCol=e[6]),512&i&&(s.type=e[9]),33554432&i&&(s.color=e[25]),9216&i&&(s.valueFmt=e[13]??e[10]),2048&i&&(s.dateFmt=e[11]),4&i&&(s.yScale=e[2]),4096&i&&(s.connectGroup=e[12]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function An(e){let t,i,s,n;const a=[_a,ba],l=[];function r(e,t){return e[1]?0:1}return t=r(e),i=l[t]=a[t](e),{c(){i.c(),s=$e()},l(e){i.l(e),s=$e()},m(e,i){l[t].m(e,i),E(e,s,i),n=!0},p(e,n){let o=t;t=r(e),t===o?l[t].p(e,n):(Je(),U(l[o],1,1,(()=>{l[o]=null})),xe(),i=l[t],i?i.p(e,n):(i=l[t]=a[t](e),i.c()),O(i,1),i.m(s.parentNode,s))},i(e){n||(O(i),n=!0)},o(e){U(i),n=!1},d(e){e&&d(s),l[t].d(e)}}}function ba(e){let t,i,s,n,a,l,r;const o=[Ta,Ca],c=[];function p(e,t){return e[22]?0:1}return i=p(e),s=c[i]=o[i](e),{c(){t=D("p"),s.c(),n=x(),a=D("span"),l=Le(e[4]),this.h()},l(i){t=M(i,"P",{class:!0});var r=Q(t);s.l(r),n=J(r),a=M(r,"SPAN",{});var o=Q(a);l=Te(o,e[4]),o.forEach(d),r.forEach(d),this.h()},h(){h(t,"class","text-xs font-sans /60 pt-[0.5px]")},m(e,s){E(e,t,s),c[i].m(t,null),I(t,n),I(t,a),I(a,l),r=!0},p(e,a){let d=i;i=p(e),i===d?c[i].p(e,a):(Je(),U(c[d],1,1,(()=>{c[d]=null})),xe(),s=c[i],s?s.p(e,a):(s=c[i]=o[i](e),s.c()),O(s,1),s.m(t,n)),(!r||16&a)&&Ze(l,e[4])},i(e){r||(O(s),r=!0)},o(e){U(s),r=!1},d(e){e&&d(t),c[i].d()}}}function _a(e){let t,i,s,n;return i=new ms({props:{data:e[0],column:e[7],fmt:e[14],fontClass:"text-xs",symbolPosition:"left",neutralMin:e[15],neutralMax:e[16],text:e[4],downIsGood:e[5]}}),{c(){t=D("p"),ue(i.$$.fragment),this.h()},l(e){t=M(e,"P",{class:!0});var s=Q(t);oe(i.$$.fragment,s),s.forEach(d),this.h()},h(){h(t,"class",s=bl("text-xs font-sans mt-1",e[21]))},m(e,s){E(e,t,s),fe(i,t,null),n=!0},p(e,a){const l={};1&a&&(l.data=e[0]),128&a&&(l.column=e[7]),16384&a&&(l.fmt=e[14]),32768&a&&(l.neutralMin=e[15]),65536&a&&(l.neutralMax=e[16]),16&a&&(l.text=e[4]),32&a&&(l.downIsGood=e[5]),i.$set(l),(!n||2097152&a&&s!==(s=bl("text-xs font-sans mt-1",e[21])))&&h(t,"class",s)},i(e){n||(O(i.$$.fragment,e),n=!0)},o(e){U(i.$$.fragment,e),n=!1},d(e){e&&d(t),ae(i)}}}function Ca(e){let t,i;return t=new Ul({props:{data:e[0],column:e[7],fmt:e[14]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};1&i&&(s.data=e[0]),128&i&&(s.column=e[7]),16384&i&&(s.fmt=e[14]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function Ta(e){let t,i,s,n;return i=new Ul({props:{data:e[0],column:e[7],fmt:e[14]}}),{c(){t=D("a"),ue(i.$$.fragment),this.h()},l(e){t=M(e,"A",{class:!0,href:!0});var s=Q(t);oe(i.$$.fragment,s),s.forEach(d),this.h()},h(){h(t,"class","hover:bg-base-200"),h(t,"href",s=Ll(e[22]))},m(e,s){E(e,t,s),fe(i,t,null),n=!0},p(e,a){const l={};1&a&&(l.data=e[0]),128&a&&(l.column=e[7]),16384&a&&(l.fmt=e[14]),i.$set(l),(!n||4194304&a&&s!==(s=Ll(e[22])))&&h(t,"href",s)},i(e){n||(O(i.$$.fragment,e),n=!0)},o(e){U(i.$$.fragment,e),n=!1},d(e){e&&d(t),ae(i)}}}function La(e){let t,i,s,n,a;const l=[ha,da],r=[];function o(e,t){return e[24].length>0?0:1}return i=o(e),s=r[i]=l[i](e),{c(){t=D("div"),s.c(),this.h()},l(e){t=M(e,"DIV",{class:!0,style:!0});var i=Q(t);s.l(i),i.forEach(d),this.h()},h(){h(t,"class","inline-block font-sans pt-2 pb-3 pl-0 mr-3 items-center align-top"),h(t,"style",n=`\n        min-width: ${e[18]};\n        max-width: ${e[17]};\n\t\t`)},m(e,s){E(e,t,s),r[i].m(t,null),a=!0},p(e,[c]){let d=i;i=o(e),i===d?r[i].p(e,c):(Je(),U(r[d],1,1,(()=>{r[d]=null})),xe(),s=r[i],s?s.p(e,c):(s=r[i]=l[i](e),s.c()),O(s,1),s.m(t,null)),(!a||393216&c&&n!==(n=`\n        min-width: ${e[18]};\n        max-width: ${e[17]};\n\t\t`))&&h(t,"style",n)},i(e){a||(O(s),a=!0)},o(e){U(s),a=!1},d(e){e&&d(t),r[i].d()}}}function ka(e,t,i){let s;const{resolveColor:n}=Bt();let{data:a}=t,{value:l=null}=t,{comparison:r=null}=t,{comparisonDelta:o=!0}=t,{sparkline:c=null}=t,{sparklineType:d="line"}=t,{sparklineColor:p}=t,{sparklineValueFmt:h}=t,{sparklineDateFmt:u}=t,{sparklineYScale:m=!1}=t,{connectGroup:y}=t,{fmt:f}=t,{comparisonFmt:$}=t,{title:g=null}=t,{comparisonTitle:x=null}=t,{downIsGood:v=!1}=t,{neutralMin:b=0}=t,{neutralMax:E=0}=t,{maxWidth:T="none"}=t,{minWidth:L="18%"}=t,{titleClass:O}=t,{valueClass:A}=t,{comparisonClass:w}=t,{link:I=null}=t,{description:M}=t,C=[];return e.$$set=e=>{"data"in e&&i(0,a=e.data),"value"in e&&i(6,l=e.value),"comparison"in e&&i(7,r=e.comparison),"comparisonDelta"in e&&i(1,o=e.comparisonDelta),"sparkline"in e&&i(8,c=e.sparkline),"sparklineType"in e&&i(9,d=e.sparklineType),"sparklineColor"in e&&i(26,p=e.sparklineColor),"sparklineValueFmt"in e&&i(10,h=e.sparklineValueFmt),"sparklineDateFmt"in e&&i(11,u=e.sparklineDateFmt),"sparklineYScale"in e&&i(2,m=e.sparklineYScale),"connectGroup"in e&&i(12,y=e.connectGroup),"fmt"in e&&i(13,f=e.fmt),"comparisonFmt"in e&&i(14,$=e.comparisonFmt),"title"in e&&i(3,g=e.title),"comparisonTitle"in e&&i(4,x=e.comparisonTitle),"downIsGood"in e&&i(5,v=e.downIsGood),"neutralMin"in e&&i(15,b=e.neutralMin),"neutralMax"in e&&i(16,E=e.neutralMax),"maxWidth"in e&&i(17,T=e.maxWidth),"minWidth"in e&&i(18,L=e.minWidth),"titleClass"in e&&i(19,O=e.titleClass),"valueClass"in e&&i(20,A=e.valueClass),"comparisonClass"in e&&i(21,w=e.comparisonClass),"link"in e&&i(22,I=e.link),"description"in e&&i(23,M=e.description)},e.$$.update=()=>{if(2&e.$$.dirty&&i(1,o="true"===o||!0===o),67108864&e.$$.dirty&&i(25,s=n(p)),4&e.$$.dirty&&i(2,m="true"===m||!0===m),32&e.$$.dirty&&i(5,v="true"===v||!0===v),16777689&e.$$.dirty)try{Array.isArray(a)||i(0,a=[a]),Cl(a,[l]);let e=_l(a,"array"),t=e.find((e=>e.id===l));if(i(3,g=g??(t?t.title:null)),null!==r){Cl(a,[r]);let t=e.find((e=>e.id===r));i(4,x=x??(t?t.title:null))}null!==c&&Cl(a,[c])}catch(e){if(i(24,C=[...C,e]),Mi)throw C}},[a,o,m,g,x,v,l,r,c,d,h,u,y,f,$,b,E,T,L,O,A,w,I,M,C,s,p]}let Sa=class extends st{constructor(e){super(),rt(this,e,ka,La,nt,{data:0,value:6,comparison:7,comparisonDelta:1,sparkline:8,sparklineType:9,sparklineColor:26,sparklineValueFmt:10,sparklineDateFmt:11,sparklineYScale:2,connectGroup:12,fmt:13,comparisonFmt:14,title:3,comparisonTitle:4,downIsGood:5,neutralMin:15,neutralMax:16,maxWidth:17,minWidth:18,titleClass:19,valueClass:20,comparisonClass:21,link:22,description:23})}};function Ea(e){let t;const i=e[6].default,s=el(i,e,e[7],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,i){s&&s.m(e,i),t=!0},p(e,n){s&&s.p&&(!t||128&n)&&tl(s,i,e,e[7],t?il(i,e[7],n,null):ll(e[7]),null)},i(e){t||(O(s,e),t=!0)},o(e){U(s,e),t=!1},d(e){s&&s.d(e)}}}function Aa(e){let t,i;const s=[e[4],{data:Mt.isQuery(e[9])?Array.from(e[9]):e[9]}];let n={$$slots:{default:[Ea]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)n=Et(n,s[e]);return t=new Sa({props:n}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const n=528&i?Kl(s,[16&i&&Zl(e[4]),512&i&&{data:Mt.isQuery(e[9])?Array.from(e[9]):e[9]}]):{};128&i&&(n.$$scope={dirty:i,ctx:e}),t.$set(n)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function wa(e){let t,i,s,n;return i=new ds({props:{error:e[9].error.message}}),{c(){t=D("div"),ue(i.$$.fragment),this.h()},l(e){t=M(e,"DIV",{slot:!0,class:!0,style:!0});var s=Q(t);oe(i.$$.fragment,s),s.forEach(d),this.h()},h(){h(t,"slot","error"),h(t,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(t,"style",s=`\n\t\t\t\tmin-width: ${e[5].minWidth};\n\t\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`)},m(e,s){E(e,t,s),fe(i,t,null),n=!0},p(e,a){const l={};512&a&&(l.error=e[9].error.message),i.$set(l),(!n||32&a&&s!==(s=`\n\t\t\t\tmin-width: ${e[5].minWidth};\n\t\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`))&&h(t,"style",s)},i(e){n||(O(i.$$.fragment,e),n=!0)},o(e){U(i.$$.fragment,e),n=!1},d(e){e&&d(t),ae(i)}}}function Oa(e){let t,i,s,n;return i=new Jl({props:{emptyMessage:e[2],emptySet:e[1],chartType:Da,isInitial:e[3]}}),{c(){t=D("div"),ue(i.$$.fragment),this.h()},l(e){t=M(e,"DIV",{slot:!0,class:!0,style:!0});var s=Q(t);oe(i.$$.fragment,s),s.forEach(d),this.h()},h(){h(t,"slot","empty"),h(t,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(t,"style",s=`\n\t\t\t\tmin-width: ${e[5].minWidth};\n\t\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`)},m(e,s){E(e,t,s),fe(i,t,null),n=!0},p(e,a){const l={};4&a&&(l.emptyMessage=e[2]),2&a&&(l.emptySet=e[1]),8&a&&(l.isInitial=e[3]),i.$set(l),(!n||32&a&&s!==(s=`\n\t\t\t\tmin-width: ${e[5].minWidth};\n\t\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`))&&h(t,"style",s)},i(e){n||(O(i.$$.fragment,e),n=!0)},o(e){U(i.$$.fragment,e),n=!1},d(e){e&&d(t),ae(i)}}}function Ia(e){let t,i,s,n,a,l,r,o=(e[5].title??" ")+"";return a=new Ul({props:{column:e[5].value,fmt:e[5].fmt,data:e[9]}}),{c(){t=D("div"),i=D("p"),s=Le(o),n=x(),ue(a.$$.fragment),this.h()},l(e){t=M(e,"DIV",{class:!0,style:!0,slot:!0});var l=Q(t);i=M(l,"P",{class:!0});var r=Q(i);s=Te(r,o),r.forEach(d),n=J(l),oe(a.$$.fragment,l),l.forEach(d),this.h()},h(){h(i,"class","text-sm"),h(t,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(t,"style",l=`\n\t\t\tmin-width: ${e[5].minWidth};\n\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`),h(t,"slot","skeleton")},m(e,l){E(e,t,l),I(t,i),I(i,s),I(t,n),fe(a,t,null),r=!0},p(e,i){(!r||32&i)&&o!==(o=(e[5].title??" ")+"")&&Ze(s,o);const n={};32&i&&(n.column=e[5].value),32&i&&(n.fmt=e[5].fmt),512&i&&(n.data=e[9]),a.$set(n),(!r||32&i&&l!==(l=`\n\t\t\tmin-width: ${e[5].minWidth};\n\t\t\tmax-width: ${e[5].maxWidth};\n\t\t`))&&h(t,"style",l)},i(e){r||(O(a.$$.fragment,e),r=!0)},o(e){U(a.$$.fragment,e),r=!1},d(e){e&&d(t),ae(a)}}}function Ma(e){let t,i;return t=new Ql({props:{data:e[0],$$slots:{skeleton:[Ia,({loaded:e})=>({9:e}),({loaded:e})=>e?512:0],empty:[Oa],error:[wa,({loaded:e})=>({9:e}),({loaded:e})=>e?512:0],default:[Aa,({loaded:e})=>({9:e}),({loaded:e})=>e?512:0]},$$scope:{ctx:e}}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,[i]){const s={};1&i&&(s.data=e[0]),702&i&&(s.$$scope={dirty:i,ctx:e}),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}let Da="Big Value";function Na(e,t,i){let s,{$$slots:n={},$$scope:a}=t,{data:l}=t;const r=Mt.isQuery(l)?l.hash:void 0;let o=(null==l?void 0:l.hash)===r,{emptySet:c}=t,{emptyMessage:d}=t;return e.$$set=e=>{i(5,t=Et(Et({},t),Gt(e))),"data"in e&&i(0,l=e.data),"emptySet"in e&&i(1,c=e.emptySet),"emptyMessage"in e&&i(2,d=e.emptyMessage),"$$scope"in e&&i(7,a=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&i(3,o=(null==l?void 0:l.hash)===r),i(4,s=Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e))))},t=Gt(t),[l,c,d,o,s,t,n,a]}class wn extends st{constructor(e){super(),rt(this,e,Na,Ma,nt,{data:0,emptySet:1,emptyMessage:2})}}function Fa(e,t,i){let s,n,a,l,r,o,c,d,p,h,u,m,y,f,$,g,x,v,b=ce,E=ce;e.$$.on_destroy.push((()=>b())),e.$$.on_destroy.push((()=>E()));let T=Pl(Di);Dt(e,T,(e=>i(46,v=e)));let L=Pl(Ni);const{resolveColor:O}=Bt();let{y:A}=t;const w=!!A;let{y2:I}=t;const M=!!I;let{series:C}=t;const S=!!C;let D,{options:k}=t,{name:N}=t,{lineColor:U}=t,{lineWidth:F=2}=t,{lineType:P="solid"}=t,{lineOpacity:R}=t,{markers:B=!1}=t,{markerShape:G="circle"}=t,{markerSize:z=8}=t,{labels:J=!1}=t,{labelSize:H=11}=t,{labelPosition:V="top"}=t,{labelColor:Q}=t,{labelFmt:_}=t;_&&(D=It(_));let W,{yLabelFmt:q}=t;q&&(W=It(q));let j,{y2LabelFmt:Y}=t;Y&&(j=It(Y));let{y2SeriesType:X}=t,{showAllLabels:Z=!1}=t,{handleMissing:K="gap"}=t,{step:ee=!1}=t,{stepPosition:te="end"}=t,{seriesOrder:ie}=t,{seriesLabelFmt:se}=t;const ne={above:"top",below:"bottom",middle:"inside"},ae={above:"right",below:"left",middle:"inside"};let le=r?"right":"top";return In((()=>{L.update((e=>{if(r)e.yAxis={...e.yAxis,...$.xAxis},e.xAxis={...e.xAxis,...$.yAxis};else if(e.yAxis[0]={...e.yAxis[0],...$.yAxis},e.xAxis={...e.xAxis,...$.xAxis},I&&(e.yAxis[1]={...e.yAxis[1],show:!0},["line","bar","scatter"].includes(X)))for(let t=0;t<p;t++)e.series[d+t].type=X;return J&&(e.axisPointer={triggerEmphasis:!1}),e}))})),e.$$set=e=>{"y"in e&&i(3,A=e.y),"y2"in e&&i(4,I=e.y2),"series"in e&&i(5,C=e.series),"options"in e&&i(12,k=e.options),"name"in e&&i(6,N=e.name),"lineColor"in e&&i(13,U=e.lineColor),"lineWidth"in e&&i(14,F=e.lineWidth),"lineType"in e&&i(15,P=e.lineType),"lineOpacity"in e&&i(16,R=e.lineOpacity),"markers"in e&&i(7,B=e.markers),"markerShape"in e&&i(17,G=e.markerShape),"markerSize"in e&&i(18,z=e.markerSize),"labels"in e&&i(8,J=e.labels),"labelSize"in e&&i(19,H=e.labelSize),"labelPosition"in e&&i(9,V=e.labelPosition),"labelColor"in e&&i(20,Q=e.labelColor),"labelFmt"in e&&i(21,_=e.labelFmt),"yLabelFmt"in e&&i(22,q=e.yLabelFmt),"y2LabelFmt"in e&&i(23,Y=e.y2LabelFmt),"y2SeriesType"in e&&i(24,X=e.y2SeriesType),"showAllLabels"in e&&i(10,Z=e.showAllLabels),"handleMissing"in e&&i(25,K=e.handleMissing),"step"in e&&i(11,ee=e.step),"stepPosition"in e&&i(26,te=e.stepPosition),"seriesOrder"in e&&i(27,ie=e.seriesOrder),"seriesLabelFmt"in e&&i(28,se=e.seriesLabelFmt)},e.$$.update=()=>{if(8192&e.$$.dirty[0]&&(i(1,s=O(U)),b(),b=St(s,(e=>i(44,g=e)))),128&e.$$.dirty[0]&&i(7,B=Ve(B)),256&e.$$.dirty[0]&&i(8,J=Ve(J)),1048576&e.$$.dirty[0]&&(i(0,n=O(Q)),E(),E=St(n,(e=>i(45,x=e)))),1024&e.$$.dirty[0]&&i(10,Z=Ve(Z)),2048&e.$$.dirty[0]&&i(11,ee=Ve(ee)),32768&e.$$.dirty[1]&&i(41,a=v.data),32768&e.$$.dirty[1]&&i(40,l=v.x),8&e.$$.dirty[0]|32768&e.$$.dirty[1]&&i(3,A=w?A:v.y),16&e.$$.dirty[0]|32768&e.$$.dirty[1]&&i(4,I=M?I:v.y2),32768&e.$$.dirty[1]&&i(34,r=v.swapXY),32768&e.$$.dirty[1]&&i(43,o=v.yFormat),32768&e.$$.dirty[1]&&i(42,c=v.y2Format),32768&e.$$.dirty[1]&&i(32,d=v.yCount),32768&e.$$.dirty[1]&&i(33,p=v.y2Count),32768&e.$$.dirty[1]&&i(35,h=v.xType),32768&e.$$.dirty[1]&&i(38,u=v.xMismatch),32768&e.$$.dirty[1]&&i(37,m=v.columnSummary),32&e.$$.dirty[0]|32768&e.$$.dirty[1]&&i(5,C=S?C:v.series),104&e.$$.dirty[0]|1600&e.$$.dirty[1])if(C||"object"==typeof A)try{i(41,a=Fl(a,l,A,C))}catch(e){console.warn("Failed to complete data",{e}),i(41,a=[])}else i(6,N=N??Wt(A,m[A].title));if(33554472&e.$$.dirty[0]|1536&e.$$.dirty[1]&&"zero"===K)try{i(41,a=Fl(a,l,A,C,!0))}catch(e){console.warn("Failed to complete data",{e}),i(41,a=[])}512&e.$$.dirty[0]|8&e.$$.dirty[1]&&i(9,V=(r?ae[V]:ne[V])??le),1712312192&e.$$.dirty[0]|30735&e.$$.dirty[1]&&i(39,y={type:"line",label:{show:J,formatter:e=>0===e.value[r?0:1]?"":ct(e.value[r?0:1],[W??D??o,j??D??c][Pi(e.componentIndex,d,p)]),fontSize:H,color:x,position:V,padding:3},labelLayout:{hideOverlap:!Z},connectNulls:"connect"===K,emphasis:{focus:"series",endLabel:{show:!1},lineStyle:{opacity:1,width:3}},lineStyle:{width:parseInt(F),type:P,opacity:R},itemStyle:{color:g,opacity:R},showSymbol:J||B,symbol:G,symbolSize:J&&!B?0:z,step:!!ee&&te}),402653304&e.$$.dirty[0]|1992&e.$$.dirty[1]&&i(36,f=zn(a,l,A,C,r,y,N,u,m,ie,void 0,void 0,I,se)),32&e.$$.dirty[1]&&L.update((e=>(e.series.push(...f),e.legend.data.push(...f.map((e=>e.name.toString()))),e))),4096&e.$$.dirty[0]&&k&&L.update((e=>({...e,...k}))),16&e.$$.dirty[1]&&($={yAxis:{boundaryGap:["0%","1%"]},xAxis:{boundaryGap:["time"===h?"2%":"0%","2%"]}})},[n,s,T,A,I,C,N,B,J,V,Z,ee,k,U,F,P,R,G,z,H,Q,_,q,Y,X,K,te,ie,se,D,W,j,d,p,r,h,f,m,u,y,l,a,c,o,g,x,v]}class Pa extends st{constructor(e){super(),rt(this,e,Fa,null,nt,{y:3,y2:4,series:5,options:12,name:6,lineColor:13,lineWidth:14,lineType:15,lineOpacity:16,markers:7,markerShape:17,markerSize:18,labels:8,labelSize:19,labelPosition:9,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,showAllLabels:10,handleMissing:25,step:11,stepPosition:26,seriesOrder:27,seriesLabelFmt:28},null,[-1,-1])}}function Ra(e){let t,i,s;t=new Pa({props:{lineColor:e[73],lineWidth:e[38],lineOpacity:e[37],lineType:e[36],markers:e[40],markerShape:e[41],markerSize:e[42],handleMissing:e[43],step:e[44],stepPosition:e[45],labels:e[47],labelSize:e[48],labelPosition:e[49],labelColor:e[71],labelFmt:e[50],yLabelFmt:e[51],y2LabelFmt:e[52],showAllLabels:e[53],y2SeriesType:e[8],seriesOrder:e[62],seriesLabelFmt:e[64]}});const n=e[80].default,a=el(n,e,e[81],null);return{c(){ue(t.$$.fragment),i=x(),a&&a.c()},l(e){oe(t.$$.fragment,e),i=J(e),a&&a.l(e)},m(e,n){fe(t,e,n),E(e,i,n),a&&a.m(e,n),s=!0},p(e,i){const l={};2048&i[2]&&(l.lineColor=e[73]),128&i[1]&&(l.lineWidth=e[38]),64&i[1]&&(l.lineOpacity=e[37]),32&i[1]&&(l.lineType=e[36]),512&i[1]&&(l.markers=e[40]),1024&i[1]&&(l.markerShape=e[41]),2048&i[1]&&(l.markerSize=e[42]),4096&i[1]&&(l.handleMissing=e[43]),8192&i[1]&&(l.step=e[44]),16384&i[1]&&(l.stepPosition=e[45]),65536&i[1]&&(l.labels=e[47]),131072&i[1]&&(l.labelSize=e[48]),262144&i[1]&&(l.labelPosition=e[49]),512&i[2]&&(l.labelColor=e[71]),524288&i[1]&&(l.labelFmt=e[50]),1048576&i[1]&&(l.yLabelFmt=e[51]),2097152&i[1]&&(l.y2LabelFmt=e[52]),4194304&i[1]&&(l.showAllLabels=e[53]),256&i[0]&&(l.y2SeriesType=e[8]),1&i[2]&&(l.seriesOrder=e[62]),4&i[2]&&(l.seriesLabelFmt=e[64]),t.$set(l),a&&a.p&&(!s||524288&i[2])&&tl(a,n,e,e[81],s?il(n,e[81],i,null):ll(e[81]),null)},i(e){s||(O(t.$$.fragment,e),O(a,e),s=!0)},o(e){U(t.$$.fragment,e),U(a,e),s=!1},d(e){e&&d(i),ae(t,e),a&&a.d(e)}}}function Ba(e){let t,i;return t=new Bn({props:{data:e[0],x:e[1],y:e[2],y2:e[3],xFmt:e[10],yFmt:e[9],y2Fmt:e[11],series:e[4],xType:e[5],yLog:e[6],yLogBase:e[7],legend:e[14],xAxisTitle:e[15],yAxisTitle:e[16],y2AxisTitle:e[17],xGridlines:e[18],yGridlines:e[19],y2Gridlines:e[20],xAxisLabels:e[21],yAxisLabels:e[22],y2AxisLabels:e[23],xBaseline:e[24],yBaseline:e[25],y2Baseline:e[26],xTickMarks:e[27],yTickMarks:e[28],y2TickMarks:e[29],yAxisColor:e[70],y2AxisColor:e[69],yMin:e[30],yMax:e[31],yScale:e[32],y2Min:e[33],y2Max:e[34],y2Scale:e[35],title:e[12],subtitle:e[13],chartType:"Line Chart",sort:e[46],chartAreaHeight:e[39],colorPalette:e[72],echartsOptions:e[54],seriesOptions:e[55],printEchartsConfig:e[56],emptySet:e[57],emptyMessage:e[58],renderer:e[59],downloadableData:e[60],downloadableImage:e[61],connectGroup:e[63],seriesColors:e[68],leftPadding:e[65],rightPadding:e[66],xLabelWrap:e[67],$$slots:{default:[Ra]},$$scope:{ctx:e}}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};1&i[0]&&(s.data=e[0]),2&i[0]&&(s.x=e[1]),4&i[0]&&(s.y=e[2]),8&i[0]&&(s.y2=e[3]),1024&i[0]&&(s.xFmt=e[10]),512&i[0]&&(s.yFmt=e[9]),2048&i[0]&&(s.y2Fmt=e[11]),16&i[0]&&(s.series=e[4]),32&i[0]&&(s.xType=e[5]),64&i[0]&&(s.yLog=e[6]),128&i[0]&&(s.yLogBase=e[7]),16384&i[0]&&(s.legend=e[14]),32768&i[0]&&(s.xAxisTitle=e[15]),65536&i[0]&&(s.yAxisTitle=e[16]),131072&i[0]&&(s.y2AxisTitle=e[17]),262144&i[0]&&(s.xGridlines=e[18]),524288&i[0]&&(s.yGridlines=e[19]),1048576&i[0]&&(s.y2Gridlines=e[20]),2097152&i[0]&&(s.xAxisLabels=e[21]),4194304&i[0]&&(s.yAxisLabels=e[22]),8388608&i[0]&&(s.y2AxisLabels=e[23]),16777216&i[0]&&(s.xBaseline=e[24]),33554432&i[0]&&(s.yBaseline=e[25]),67108864&i[0]&&(s.y2Baseline=e[26]),134217728&i[0]&&(s.xTickMarks=e[27]),268435456&i[0]&&(s.yTickMarks=e[28]),536870912&i[0]&&(s.y2TickMarks=e[29]),256&i[2]&&(s.yAxisColor=e[70]),128&i[2]&&(s.y2AxisColor=e[69]),1073741824&i[0]&&(s.yMin=e[30]),1&i[1]&&(s.yMax=e[31]),2&i[1]&&(s.yScale=e[32]),4&i[1]&&(s.y2Min=e[33]),8&i[1]&&(s.y2Max=e[34]),16&i[1]&&(s.y2Scale=e[35]),4096&i[0]&&(s.title=e[12]),8192&i[0]&&(s.subtitle=e[13]),32768&i[1]&&(s.sort=e[46]),256&i[1]&&(s.chartAreaHeight=e[39]),1024&i[2]&&(s.colorPalette=e[72]),8388608&i[1]&&(s.echartsOptions=e[54]),16777216&i[1]&&(s.seriesOptions=e[55]),33554432&i[1]&&(s.printEchartsConfig=e[56]),67108864&i[1]&&(s.emptySet=e[57]),134217728&i[1]&&(s.emptyMessage=e[58]),268435456&i[1]&&(s.renderer=e[59]),536870912&i[1]&&(s.downloadableData=e[60]),1073741824&i[1]&&(s.downloadableImage=e[61]),2&i[2]&&(s.connectGroup=e[63]),64&i[2]&&(s.seriesColors=e[68]),8&i[2]&&(s.leftPadding=e[65]),16&i[2]&&(s.rightPadding=e[66]),32&i[2]&&(s.xLabelWrap=e[67]),256&i[0]|8355552&i[1]|526853&i[2]&&(s.$$scope={dirty:i,ctx:e}),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function za(e,t,i){let s,n,a,l,r,o,{$$slots:c={},$$scope:d}=t;const{resolveColor:p,resolveColorsObject:h,resolveColorPalette:u}=Bt();let{data:m}=t,{x:y}=t,{y:f}=t,{y2:$}=t,{series:g}=t,{xType:x}=t,{yLog:v}=t,{yLogBase:b}=t,{y2SeriesType:E}=t,{yFmt:T}=t,{xFmt:L}=t,{y2Fmt:O}=t,{title:A}=t,{subtitle:w}=t,{legend:I}=t,{xAxisTitle:M}=t,{yAxisTitle:C=($?"true":void 0)}=t,{y2AxisTitle:S=($?"true":void 0)}=t,{xGridlines:D}=t,{yGridlines:k}=t,{y2Gridlines:N}=t,{xAxisLabels:U}=t,{yAxisLabels:F}=t,{y2AxisLabels:P}=t,{xBaseline:R}=t,{yBaseline:B}=t,{y2Baseline:G}=t,{xTickMarks:z}=t,{yTickMarks:J}=t,{y2TickMarks:H}=t,{yMin:V}=t,{yMax:Q}=t,{yScale:_}=t,{y2Min:W}=t,{y2Max:q}=t,{y2Scale:j}=t,{lineColor:Y}=t,{lineType:X}=t,{lineOpacity:Z}=t,{lineWidth:K}=t,{chartAreaHeight:ee}=t,{markers:te}=t,{markerShape:ie}=t,{markerSize:se}=t,{handleMissing:ne}=t,{step:ae}=t,{stepPosition:le}=t,{sort:re}=t,{colorPalette:oe="default"}=t,{labels:ce}=t,{labelSize:de}=t,{labelPosition:pe}=t,{labelColor:he}=t,{labelFmt:ue}=t,{yLabelFmt:me}=t,{y2LabelFmt:ye}=t,{showAllLabels:fe}=t,{yAxisColor:$e}=t,{y2AxisColor:ge}=t,{echartsOptions:xe}=t,{seriesOptions:ve}=t,{printEchartsConfig:be=!1}=t,{emptySet:Ee}=t,{emptyMessage:Te}=t,{renderer:Le}=t,{downloadableData:Oe}=t,{downloadableImage:Ae}=t,{seriesColors:we}=t,{seriesOrder:Ie}=t,{connectGroup:Me}=t,{seriesLabelFmt:Ce}=t,{leftPadding:Se}=t,{rightPadding:De}=t,{xLabelWrap:ke}=t;return e.$$set=e=>{"data"in e&&i(0,m=e.data),"x"in e&&i(1,y=e.x),"y"in e&&i(2,f=e.y),"y2"in e&&i(3,$=e.y2),"series"in e&&i(4,g=e.series),"xType"in e&&i(5,x=e.xType),"yLog"in e&&i(6,v=e.yLog),"yLogBase"in e&&i(7,b=e.yLogBase),"y2SeriesType"in e&&i(8,E=e.y2SeriesType),"yFmt"in e&&i(9,T=e.yFmt),"xFmt"in e&&i(10,L=e.xFmt),"y2Fmt"in e&&i(11,O=e.y2Fmt),"title"in e&&i(12,A=e.title),"subtitle"in e&&i(13,w=e.subtitle),"legend"in e&&i(14,I=e.legend),"xAxisTitle"in e&&i(15,M=e.xAxisTitle),"yAxisTitle"in e&&i(16,C=e.yAxisTitle),"y2AxisTitle"in e&&i(17,S=e.y2AxisTitle),"xGridlines"in e&&i(18,D=e.xGridlines),"yGridlines"in e&&i(19,k=e.yGridlines),"y2Gridlines"in e&&i(20,N=e.y2Gridlines),"xAxisLabels"in e&&i(21,U=e.xAxisLabels),"yAxisLabels"in e&&i(22,F=e.yAxisLabels),"y2AxisLabels"in e&&i(23,P=e.y2AxisLabels),"xBaseline"in e&&i(24,R=e.xBaseline),"yBaseline"in e&&i(25,B=e.yBaseline),"y2Baseline"in e&&i(26,G=e.y2Baseline),"xTickMarks"in e&&i(27,z=e.xTickMarks),"yTickMarks"in e&&i(28,J=e.yTickMarks),"y2TickMarks"in e&&i(29,H=e.y2TickMarks),"yMin"in e&&i(30,V=e.yMin),"yMax"in e&&i(31,Q=e.yMax),"yScale"in e&&i(32,_=e.yScale),"y2Min"in e&&i(33,W=e.y2Min),"y2Max"in e&&i(34,q=e.y2Max),"y2Scale"in e&&i(35,j=e.y2Scale),"lineColor"in e&&i(74,Y=e.lineColor),"lineType"in e&&i(36,X=e.lineType),"lineOpacity"in e&&i(37,Z=e.lineOpacity),"lineWidth"in e&&i(38,K=e.lineWidth),"chartAreaHeight"in e&&i(39,ee=e.chartAreaHeight),"markers"in e&&i(40,te=e.markers),"markerShape"in e&&i(41,ie=e.markerShape),"markerSize"in e&&i(42,se=e.markerSize),"handleMissing"in e&&i(43,ne=e.handleMissing),"step"in e&&i(44,ae=e.step),"stepPosition"in e&&i(45,le=e.stepPosition),"sort"in e&&i(46,re=e.sort),"colorPalette"in e&&i(75,oe=e.colorPalette),"labels"in e&&i(47,ce=e.labels),"labelSize"in e&&i(48,de=e.labelSize),"labelPosition"in e&&i(49,pe=e.labelPosition),"labelColor"in e&&i(76,he=e.labelColor),"labelFmt"in e&&i(50,ue=e.labelFmt),"yLabelFmt"in e&&i(51,me=e.yLabelFmt),"y2LabelFmt"in e&&i(52,ye=e.y2LabelFmt),"showAllLabels"in e&&i(53,fe=e.showAllLabels),"yAxisColor"in e&&i(77,$e=e.yAxisColor),"y2AxisColor"in e&&i(78,ge=e.y2AxisColor),"echartsOptions"in e&&i(54,xe=e.echartsOptions),"seriesOptions"in e&&i(55,ve=e.seriesOptions),"printEchartsConfig"in e&&i(56,be=e.printEchartsConfig),"emptySet"in e&&i(57,Ee=e.emptySet),"emptyMessage"in e&&i(58,Te=e.emptyMessage),"renderer"in e&&i(59,Le=e.renderer),"downloadableData"in e&&i(60,Oe=e.downloadableData),"downloadableImage"in e&&i(61,Ae=e.downloadableImage),"seriesColors"in e&&i(79,we=e.seriesColors),"seriesOrder"in e&&i(62,Ie=e.seriesOrder),"connectGroup"in e&&i(63,Me=e.connectGroup),"seriesLabelFmt"in e&&i(64,Ce=e.seriesLabelFmt),"leftPadding"in e&&i(65,Se=e.leftPadding),"rightPadding"in e&&i(66,De=e.rightPadding),"xLabelWrap"in e&&i(67,ke=e.xLabelWrap),"$$scope"in e&&i(81,d=e.$$scope)},e.$$.update=()=>{4096&e.$$.dirty[2]&&i(73,s=p(Y)),8192&e.$$.dirty[2]&&i(72,n=u(oe)),16384&e.$$.dirty[2]&&i(71,a=p(he)),32768&e.$$.dirty[2]&&i(70,l=p($e)),65536&e.$$.dirty[2]&&i(69,r=p(ge)),131072&e.$$.dirty[2]&&i(68,o=h(we))},[m,y,f,$,g,x,v,b,E,T,L,O,A,w,I,M,C,S,D,k,N,U,F,P,R,B,G,z,J,H,V,Q,_,W,q,j,X,Z,K,ee,te,ie,se,ne,ae,le,re,ce,de,pe,ue,me,ye,fe,xe,ve,be,Ee,Te,Le,Oe,Ae,Ie,Me,Ce,Se,De,ke,o,r,l,a,n,s,Y,oe,he,$e,ge,we,c,d]}class Ga extends st{constructor(e){super(),rt(this,e,za,Ba,nt,{data:0,x:1,y:2,y2:3,series:4,xType:5,yLog:6,yLogBase:7,y2SeriesType:8,yFmt:9,xFmt:10,y2Fmt:11,title:12,subtitle:13,legend:14,xAxisTitle:15,yAxisTitle:16,y2AxisTitle:17,xGridlines:18,yGridlines:19,y2Gridlines:20,xAxisLabels:21,yAxisLabels:22,y2AxisLabels:23,xBaseline:24,yBaseline:25,y2Baseline:26,xTickMarks:27,yTickMarks:28,y2TickMarks:29,yMin:30,yMax:31,yScale:32,y2Min:33,y2Max:34,y2Scale:35,lineColor:74,lineType:36,lineOpacity:37,lineWidth:38,chartAreaHeight:39,markers:40,markerShape:41,markerSize:42,handleMissing:43,step:44,stepPosition:45,sort:46,colorPalette:75,labels:47,labelSize:48,labelPosition:49,labelColor:76,labelFmt:50,yLabelFmt:51,y2LabelFmt:52,showAllLabels:53,yAxisColor:77,y2AxisColor:78,echartsOptions:54,seriesOptions:55,printEchartsConfig:56,emptySet:57,emptyMessage:58,renderer:59,downloadableData:60,downloadableImage:61,seriesColors:79,seriesOrder:62,connectGroup:63,seriesLabelFmt:64,leftPadding:65,rightPadding:66,xLabelWrap:67},null,[-1,-1,-1])}}function Ua(e){let t,i,s=Ae.title+"";return{c(){t=D("h1"),i=Le(s),this.h()},l(e){t=M(e,"H1",{class:!0});var n=Q(t);i=Te(n,s),n.forEach(d),this.h()},h(){h(t,"class","title")},m(e,s){E(e,t,s),I(t,i)},p:ce,d(e){e&&d(t)}}}function Ha(e){return{c(){this.h()},l(e){this.h()},h(){document.title="Evidence"},m:ce,p:ce,d:ce}}function Va(e){let t,i,s,n,a;return document.title=t=Ae.title,{c(){i=x(),s=D("meta"),n=x(),a=D("meta"),this.h()},l(e){i=J(e),s=M(e,"META",{property:!0,content:!0}),n=J(e),a=M(e,"META",{name:!0,content:!0}),this.h()},h(){var e,t;h(s,"property","og:title"),h(s,"content",(null==(e=Ae.og)?void 0:e.title)??Ae.title),h(a,"name","twitter:title"),h(a,"content",(null==(t=Ae.og)?void 0:t.title)??Ae.title)},m(e,t){E(e,i,t),E(e,s,t),E(e,n,t),E(e,a,t)},p(e,i){0&i&&t!==(t=Ae.title)&&(document.title=t)},d(e){e&&(d(i),d(s),d(n),d(a))}}}function Wa(e){var t,i;let s,n,a=(Ae.description||(null==(t=Ae.og)?void 0:t.description))&&qa(),l=(null==(i=Ae.og)?void 0:i.image)&&va();return{c(){a&&a.c(),s=x(),l&&l.c(),n=$e()},l(e){a&&a.l(e),s=J(e),l&&l.l(e),n=$e()},m(e,t){a&&a.m(e,t),E(e,s,t),l&&l.m(e,t),E(e,n,t)},p(e,t){var i,s;(Ae.description||null!=(i=Ae.og)&&i.description)&&a.p(e,t),null!=(s=Ae.og)&&s.image&&l.p(e,t)},d(e){e&&(d(s),d(n)),a&&a.d(e),l&&l.d(e)}}}function qa(e){let t,i,s,n,a;return{c(){t=D("meta"),i=x(),s=D("meta"),n=x(),a=D("meta"),this.h()},l(e){t=M(e,"META",{name:!0,content:!0}),i=J(e),s=M(e,"META",{property:!0,content:!0}),n=J(e),a=M(e,"META",{name:!0,content:!0}),this.h()},h(){var e,i,n;h(t,"name","description"),h(t,"content",Ae.description??(null==(e=Ae.og)?void 0:e.description)),h(s,"property","og:description"),h(s,"content",(null==(i=Ae.og)?void 0:i.description)??Ae.description),h(a,"name","twitter:description"),h(a,"content",(null==(n=Ae.og)?void 0:n.description)??Ae.description)},m(e,l){E(e,t,l),E(e,i,l),E(e,s,l),E(e,n,l),E(e,a,l)},p:ce,d(e){e&&(d(t),d(i),d(s),d(n),d(a))}}}function va(e){let t,i,s;return{c(){t=D("meta"),i=x(),s=D("meta"),this.h()},l(e){t=M(e,"META",{property:!0,content:!0}),i=J(e),s=M(e,"META",{name:!0,content:!0}),this.h()},h(){var e,i;h(t,"property","og:image"),h(t,"content",Ll(null==(e=Ae.og)?void 0:e.image)),h(s,"name","twitter:image"),h(s,"content",Ll(null==(i=Ae.og)?void 0:i.image))},m(e,n){E(e,t,n),E(e,i,n),E(e,s,n)},p:ce,d(e){e&&(d(t),d(i),d(s))}}}function On(e){let t,i;return t=new er({props:{queryID:"sample_sales",queryResult:e[0]}}),{c(){ue(t.$$.fragment)},l(e){oe(t.$$.fragment,e)},m(e,s){fe(t,e,s),i=!0},p(e,i){const s={};1&i&&(s.queryResult=e[0]),t.$set(s)},i(e){i||(O(t.$$.fragment,e),i=!0)},o(e){U(t.$$.fragment,e),i=!1},d(e){ae(t,e)}}}function ja(e){let t,i,s,n,a,l,r,o,c,p,u,m,y,f,$,g,b,T,L,A,w,C,S,k,N,F,P,R,B,G,z,H,V,_,W,q,j,Y,X,Z,K,ee,te,ie,se,ne,le,re,ce,de,pe,he,me,ye,ge,ve,be,Ee,Te,Le,we,Ie,Me,Ce,Se,De,ke,Ne,Ue,Fe='<a href="#domo-data-loader">Domo Data Loader</a>',Pe="Load datasets from Domo into DuckDB for analysis with Evidence.",Re='<h3 class="svelte-16omzez">🚀 Enhanced Domo DDX Integration</h3> <p>This app now supports real DuckDB integration, multiple dataset loading, iframe compatibility, and data visualizations!</p>',Be="Select Dataset:",Ge="Choose a dataset...",ze='<h4>📊 Dataset Information</h4> <div id="preview-content" class="preview-content"><div id="dataset-info" class="dataset-info-grid"></div> <div class="dataset-tabs"><button class="tab-button active" data-tab="schema">Schema</button> <button class="tab-button" data-tab="sample">Sample Data</button> <button class="tab-button" data-tab="metadata">Metadata</button></div> <div id="schema-tab" class="tab-content active"><div id="schema-table" class="schema-table"></div></div> <div id="sample-tab" class="tab-content"><div class="preview-actions svelte-16omzez"><button id="preview-btn" class="btn btn-secondary svelte-16omzez">🔍 Load Sample Data</button> <span class="preview-note">Shows first 10 rows</span></div> <div id="data-preview" class="data-preview svelte-16omzez" style="display: none;"></div></div> <div id="metadata-tab" class="tab-content"><div id="dataset-metadata" class="dataset-metadata"></div></div></div>',He="⚙️ Loading Configuration",Ve='<label for="table-name" class="svelte-16omzez">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-16omzez"/> <small class="field-help">Use lowercase letters, numbers, and underscores only</small>',Qe="Refresh Mode:",_e="Replace existing data",We="Append to existing data",qe="Choose how to handle existing data",je='<label for="row-limit" class="svelte-16omzez">Row Limit (optional):</label> <input id="row-limit" type="number" placeholder="Leave empty for all rows" min="1" max="1000000" class="svelte-16omzez"/> <small class="field-help">Limit rows for testing (max 1M)</small>',Ye='<label class="svelte-16omzez"><input type="checkbox" id="create-index" checked="" class="svelte-16omzez"/>\n            Create indexes for better performance</label>',Xe='<div class="action-buttons"><button id="validate-config-btn" class="btn btn-secondary svelte-16omzez">✅ Validate Configuration</button> <button id="load-dataset-btn" class="btn btn-primary svelte-16omzez">📊 Load Dataset into DuckDB</button></div> <div id="validation-results" class="validation-results" style="display: none;"></div>',Ze='<div class="loading-spinner svelte-16omzez"></div> <p id="loading-message" class="svelte-16omzez">Loading...</p> <div class="progress-bar"><div class="progress-fill" id="progress-fill"></div></div>',Ke='<h2 class="markdown">📚 Loaded Datasets</h2> <div class="loaded-datasets-header"><p>Datasets currently available in DuckDB for analysis:</p> <button id="refresh-loaded-btn" class="btn btn-secondary btn-small svelte-16omzez">🔄 Refresh</button></div> <div id="loaded-datasets-list" class="loaded-datasets-grid"></div>',et='<a href="#sample-evidence-visualizations">Sample Evidence Visualizations</a>',tt="Once you load data, you can create Evidence-style visualizations. Here are some examples:",it='<a href="#sales-performance-dashboard">Sales Performance Dashboard</a>',st='<a href="#key-metrics">Key Metrics</a>',nt='<strong class="markdown">Next Steps:</strong>',at='<li class="markdown">Select a dataset from the dropdown above</li> <li class="markdown">Configure loading options</li> <li class="markdown">Load the data into DuckDB</li> <li class="markdown">Run SQL queries to explore your data</li> <li class="markdown">Create Evidence visualizations with your results</li>',lt=typeof Ae<"u"&&Ae.title&&!0!==Ae.hide_title&&Ua(),rt=(typeof Ae<"u"&&Ae.title?Va:Ha)(e),ot="object"==typeof Ae&&Wa(),ct=e[0]&&On(e);return ye=new Yr({props:{data:e[0],x:"product",y:"revenue",title:"Revenue by Product"}}),ve=new Ga({props:{data:e[0],x:"month",y:"units_sold",series:"product",title:"Units Sold Trend"}}),Le=new wn({props:{data:e[0],value:"revenue",title:"Total Revenue",fmt:"$#,##0"}}),Ie=new wn({props:{data:e[0],value:"units_sold",title:"Total Units",fmt:"#,##0"}}),{c(){lt&&lt.c(),t=x(),rt.c(),i=D("meta"),s=D("meta"),ot&&ot.c(),n=$e(),a=x(),l=D("h1"),l.innerHTML=Fe,r=x(),o=D("p"),o.textContent=Pe,c=x(),p=D("div"),p.innerHTML=Re,u=x(),m=D("div"),y=D("div"),f=D("div"),$=D("label"),$.textContent=Be,g=x(),b=D("select"),T=D("option"),T.textContent=Ge,L=x(),A=D("div"),A.innerHTML=ze,w=x(),C=D("div"),S=D("h4"),S.textContent=He,k=x(),N=D("div"),F=D("div"),F.innerHTML=Ve,P=x(),R=D("div"),B=D("label"),B.textContent=Qe,G=x(),z=D("select"),H=D("option"),H.textContent=_e,V=D("option"),V.textContent=We,_=x(),W=D("small"),W.textContent=qe,q=x(),j=D("div"),j.innerHTML=je,Y=x(),X=D("div"),X.innerHTML=Ye,Z=x(),K=D("div"),K.innerHTML=Xe,ee=x(),te=D("div"),te.innerHTML=Ze,ie=x(),se=D("div"),se.innerHTML=Ke,ne=x(),le=D("h2"),le.innerHTML=et,re=x(),ce=D("p"),ce.textContent=tt,de=x(),pe=D("h3"),pe.innerHTML=it,he=x(),ct&&ct.c(),me=x(),ue(ye.$$.fragment),ge=x(),ue(ve.$$.fragment),be=x(),Ee=D("h3"),Ee.innerHTML=st,Te=x(),ue(Le.$$.fragment),we=x(),ue(Ie.$$.fragment),Me=x(),Ce=D("hr"),Se=x(),De=D("p"),De.innerHTML=nt,ke=x(),Ne=D("ol"),Ne.innerHTML=at,this.h()},l(e){lt&&lt.l(e),t=J(e);const h=Xn("svelte-2igo1p",document.head);rt.l(h),i=M(h,"META",{name:!0,content:!0}),s=M(h,"META",{name:!0,content:!0}),ot&&ot.l(h),n=$e(),h.forEach(d),a=J(e),l=M(e,"H1",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-94lco6"!==Oe(l)&&(l.innerHTML=Fe),r=J(e),o=M(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1s6ws79"!==Oe(o)&&(o.textContent=Pe),c=J(e),p=M(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1top7fi"!==Oe(p)&&(p.innerHTML=Re),u=J(e),m=M(e,"DIV",{class:!0});var x=Q(m);y=M(x,"DIV",{id:!0,class:!0});var v=Q(y);f=M(v,"DIV",{class:!0});var E=Q(f);$=M(E,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-1fci9ty"!==Oe($)&&($.textContent=Be),g=J(E),b=M(E,"SELECT",{id:!0,class:!0});var O=Q(b);T=M(O,"OPTION",{"data-svelte-h":!0}),"svelte-59d9xk"!==Oe(T)&&(T.textContent=Ge),O.forEach(d),E.forEach(d),L=J(v),A=M(v,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-19tcyh0"!==Oe(A)&&(A.innerHTML=ze),w=J(v),C=M(v,"DIV",{id:!0,class:!0,style:!0});var I=Q(C);S=M(I,"H4",{"data-svelte-h":!0}),"svelte-6xztzo"!==Oe(S)&&(S.textContent=He),k=J(I),N=M(I,"DIV",{class:!0});var D=Q(N);F=M(D,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-199xsoc"!==Oe(F)&&(F.innerHTML=Ve),P=J(D),R=M(D,"DIV",{class:!0});var U=Q(R);B=M(U,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-p1qydn"!==Oe(B)&&(B.textContent=Qe),G=J(U),z=M(U,"SELECT",{id:!0,class:!0});var ae=Q(z);H=M(ae,"OPTION",{"data-svelte-h":!0}),"svelte-qvzdub"!==Oe(H)&&(H.textContent=_e),V=M(ae,"OPTION",{"data-svelte-h":!0}),"svelte-idsvi6"!==Oe(V)&&(V.textContent=We),ae.forEach(d),_=J(U),W=M(U,"SMALL",{class:!0,"data-svelte-h":!0}),"svelte-fpi9b6"!==Oe(W)&&(W.textContent=qe),U.forEach(d),q=J(D),j=M(D,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-15v44ck"!==Oe(j)&&(j.innerHTML=je),Y=J(D),X=M(D,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-5ivc8q"!==Oe(X)&&(X.innerHTML=Ye),D.forEach(d),I.forEach(d),Z=J(v),K=M(v,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-68tw8y"!==Oe(K)&&(K.innerHTML=Xe),ee=J(v),te=M(v,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-1oq3jl0"!==Oe(te)&&(te.innerHTML=Ze),v.forEach(d),x.forEach(d),ie=J(e),se=M(e,"DIV",{id:!0,style:!0,"data-svelte-h":!0}),"svelte-n8w8em"!==Oe(se)&&(se.innerHTML=Ke),ne=J(e),le=M(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1t5nmyw"!==Oe(le)&&(le.innerHTML=et),re=J(e),ce=M(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-nbtkwk"!==Oe(ce)&&(ce.textContent=tt),de=J(e),pe=M(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-bguwp4"!==Oe(pe)&&(pe.innerHTML=it),he=J(e),ct&&ct.l(e),me=J(e),oe(ye.$$.fragment,e),ge=J(e),oe(ve.$$.fragment,e),be=J(e),Ee=M(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-aivela"!==Oe(Ee)&&(Ee.innerHTML=st),Te=J(e),oe(Le.$$.fragment,e),we=J(e),oe(Ie.$$.fragment,e),Me=J(e),Ce=M(e,"HR",{class:!0}),Se=J(e),De=M(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1mg5bp7"!==Oe(De)&&(De.innerHTML=nt),ke=J(e),Ne=M(e,"OL",{class:!0,"data-svelte-h":!0}),"svelte-1ei1c0u"!==Oe(Ne)&&(Ne.innerHTML=at),this.h()},h(){h(i,"name","twitter:card"),h(i,"content","summary_large_image"),h(s,"name","twitter:site"),h(s,"content","@evidence_dev"),h(l,"class","markdown"),h(l,"id","domo-data-loader"),h(o,"class","markdown"),h(p,"class","dev-banner svelte-16omzez"),h($,"for","dataset-selector"),h($,"class","svelte-16omzez"),T.__value="",Dl(T,T.__value),h(b,"id","dataset-selector"),h(b,"class","dataset-dropdown svelte-16omzez"),h(f,"class","workflow-step svelte-16omzez"),h(A,"id","dataset-preview"),h(A,"class","dataset-preview svelte-16omzez"),v(A,"display","none"),h(F,"class","config-item"),h(B,"for","refresh-mode"),h(B,"class","svelte-16omzez"),H.__value="replace",Dl(H,H.__value),V.__value="append",Dl(V,V.__value),h(z,"id","refresh-mode"),h(z,"class","svelte-16omzez"),h(W,"class","field-help"),h(R,"class","config-item"),h(j,"class","config-item"),h(X,"class","config-item"),h(N,"class","config-grid svelte-16omzez"),h(C,"id","loading-config"),h(C,"class","workflow-step svelte-16omzez"),v(C,"display","none"),h(K,"id","workflow-actions"),h(K,"class","workflow-actions svelte-16omzez"),v(K,"display","none"),h(te,"id","loading-status"),h(te,"class","loading-status svelte-16omzez"),v(te,"display","none"),h(y,"id","domo-workflow-picker"),h(y,"class","workflow-picker svelte-16omzez"),h(m,"class","workflow-picker-section svelte-16omzez"),h(se,"id","loaded-datasets-section"),v(se,"display","none"),h(le,"class","markdown"),h(le,"id","sample-evidence-visualizations"),h(ce,"class","markdown"),h(pe,"class","markdown"),h(pe,"id","sales-performance-dashboard"),h(Ee,"class","markdown"),h(Ee,"id","key-metrics"),h(Ce,"class","markdown"),h(De,"class","markdown"),h(Ne,"class","markdown")},m(e,d){lt&&lt.m(e,d),E(e,t,d),rt.m(document.head,null),I(document.head,i),I(document.head,s),ot&&ot.m(document.head,null),I(document.head,n),E(e,a,d),E(e,l,d),E(e,r,d),E(e,o,d),E(e,c,d),E(e,p,d),E(e,u,d),E(e,m,d),I(m,y),I(y,f),I(f,$),I(f,g),I(f,b),I(b,T),I(y,L),I(y,A),I(y,w),I(y,C),I(C,S),I(C,k),I(C,N),I(N,F),I(N,P),I(N,R),I(R,B),I(R,G),I(R,z),I(z,H),I(z,V),I(R,_),I(R,W),I(N,q),I(N,j),I(N,Y),I(N,X),I(y,Z),I(y,K),I(y,ee),I(y,te),E(e,ie,d),E(e,se,d),E(e,ne,d),E(e,le,d),E(e,re,d),E(e,ce,d),E(e,de,d),E(e,pe,d),E(e,he,d),ct&&ct.m(e,d),E(e,me,d),fe(ye,e,d),E(e,ge,d),fe(ve,e,d),E(e,be,d),E(e,Ee,d),E(e,Te,d),fe(Le,e,d),E(e,we,d),fe(Ie,e,d),E(e,Me,d),E(e,Ce,d),E(e,Se,d),E(e,De,d),E(e,ke,d),E(e,Ne,d),Ue=!0},p(e,[t]){typeof Ae<"u"&&Ae.title&&!0!==Ae.hide_title&&lt.p(e,t),rt.p(e,t),"object"==typeof Ae&&ot.p(e,t),e[0]?ct?(ct.p(e,t),1&t&&O(ct,1)):(ct=On(e),ct.c(),O(ct,1),ct.m(me.parentNode,me)):ct&&(Je(),U(ct,1,1,(()=>{ct=null})),xe());const i={};1&t&&(i.data=e[0]),ye.$set(i);const s={};1&t&&(s.data=e[0]),ve.$set(s);const n={};1&t&&(n.data=e[0]),Le.$set(n);const a={};1&t&&(a.data=e[0]),Ie.$set(a)},i(e){Ue||(O(ct),O(ye.$$.fragment,e),O(ve.$$.fragment,e),O(Le.$$.fragment,e),O(Ie.$$.fragment,e),Ue=!0)},o(e){U(ct),U(ye.$$.fragment,e),U(ve.$$.fragment,e),U(Le.$$.fragment,e),U(Ie.$$.fragment,e),Ue=!1},d(e){e&&(d(t),d(a),d(l),d(r),d(o),d(c),d(p),d(u),d(m),d(ie),d(se),d(ne),d(le),d(re),d(ce),d(de),d(pe),d(he),d(me),d(ge),d(be),d(Ee),d(Te),d(we),d(Me),d(Ce),d(Se),d(De),d(ke),d(Ne)),lt&&lt.d(e),rt.d(e),d(i),d(s),ot&&ot.d(e),d(n),ct&&ct.d(e),ae(ye,e),ae(ve,e),ae(Le,e),ae(Ie,e)}}}const Ae={title:"Domo Data Loader"};function Ya(e,t,i){let s,n;Dt(e,Fn,(e=>i(7,s=e))),Dt(e,Xi,(e=>i(13,n=e)));let{data:a}=t,{data:l={},customFormattingSettings:r,__db:o,inputs:c}=a;Ti(Xi,n="6666cd76f96956469e7be39d750cc7d9",n);let d=hs(Ei(c));Qn(d.subscribe((e=>c=e))),Li(bs,{getCustomFormats:()=>r.customFormats||[]});const p=(e,t)=>_s(o.query,e,{query_name:t});ys(p),s.params,Kn((()=>!0));let h={initialData:void 0,initialError:void 0},u=Qi`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`,m="SELECT\n  'Product A' as product,\n  150000 as revenue,\n  1250 as units_sold,\n  '2024-01' as month\nUNION ALL\nSELECT 'Product B', 125000, 980, '2024-01'\nUNION ALL\nSELECT 'Product C', 180000, 1450, '2024-01'\nUNION ALL\nSELECT 'Product A', 165000, 1380, '2024-02'\nUNION ALL\nSELECT 'Product B', 140000, 1120, '2024-02'\nUNION ALL\nSELECT 'Product C', 195000, 1580, '2024-02'";l.sample_sales_data&&(l.sample_sales_data instanceof Error?h.initialError=l.sample_sales_data:h.initialData=l.sample_sales_data,l.sample_sales_columns&&(h.knownColumns=l.sample_sales_columns));let y,f=!1;const $=Mt.createReactive({callback:e=>{i(0,y=e)},execFn:p},{id:"sample_sales",...h});return $(m,{noResolve:u,...h}),globalThis[Symbol.for("sample_sales")]={get value(){return y}},e.$$set=e=>{"data"in e&&i(1,a=e.data)},e.$$.update=()=>{2&e.$$.dirty&&i(2,({data:l={},customFormattingSettings:r,__db:o}=a),l),4&e.$$.dirty&&gs.set(Object.keys(l).length>0),128&e.$$.dirty&&s.params,120&e.$$.dirty&&(u||!f?u||($(m,{noResolve:u,...h}),i(6,f=!0)):$(m,{noResolve:u}))},i(4,u=Qi`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`),i(5,m="SELECT\n  'Product A' as product,\n  150000 as revenue,\n  1250 as units_sold,\n  '2024-01' as month\nUNION ALL\nSELECT 'Product B', 125000, 980, '2024-01'\nUNION ALL\nSELECT 'Product C', 180000, 1450, '2024-01'\nUNION ALL\nSELECT 'Product A', 165000, 1380, '2024-02'\nUNION ALL\nSELECT 'Product B', 140000, 1120, '2024-02'\nUNION ALL\nSELECT 'Product C', 195000, 1580, '2024-02'"),[y,a,l,h,u,m,f,s]}class $a extends st{constructor(e){super(),rt(this,e,Ya,ja,nt,{data:1})}}export{$a as component};
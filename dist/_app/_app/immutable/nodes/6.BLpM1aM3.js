import{F as le,G as ne,H as R,I as ae,s as K,d as b,i as k,r as L,v as O,e as E,b as C,x as N,h as v,j as T,y as q,m as $,J as S,l as Q,k as j,n as I,q as B}from"../chunks/scheduler.DQwIXrE4.js";import{g as H,t as w,c as P,a as g,S as X,i as Y,d as D,m as M,b as U,e as F}from"../chunks/index.BEt_7cXZ.js";import{h as A,u as Z,o as ee,I as te,j as se,T as re,k as oe}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.Cu-GZpzJ.js";import"../chunks/entry.Ca5sOAmC.js";function ce(e,t){const n=t.token={};function a(e,a,s,l){if(t.token!==n)return;t.resolved=l;let o=t.ctx;void 0!==s&&(o=o.slice(),o[s]=l);const r=e&&(t.current=e)(o);let c=!1;t.block&&(t.blocks?t.blocks.forEach(((e,n)=>{n!==a&&e&&(H(),w(e,1,1,(()=>{t.blocks[n]===e&&(t.blocks[n]=null)})),P())})):t.block.d(1),r.c(),g(r,1),r.m(t.mount(),t.anchor),c=!0),t.block=r,t.blocks&&(t.blocks[a]=r),c&&ae()}if(le(e)){const n=ne();if(e.then((e=>{R(n),a(t.then,1,t.value,e),R(null)}),(e=>{if(R(n),a(t.catch,2,t.error,e),R(null),!t.hasCatch)throw e})),t.current!==t.pending)return a(t.pending,0),!0}else{if(t.current!==t.then)return a(t.then,1,t.value,e),!0;t.resolved=e}}function ie(e,t,n){const a=t.slice(),{resolved:s}=e;e.current===e.then&&(a[e.value]=s),e.current===e.catch&&(a[e.error]=s),e.block.p(a,n)}function V(e,t,n){const a=e.slice();return a[8]=t[n][0],a[9]=t[n][1],a}function W(e,t,n){const a=e.slice();return a[12]=t[n][0],a[13]=t[n][1],a}function ue(e){let t,n,a,s=e[16].message+"";return{c(){t=q("An error was encountered while loading project schema.\n\n\t"),n=$("pre"),a=q(s),this.h()},l(e){t=N(e,"An error was encountered while loading project schema.\n\n\t"),n=v(e,"PRE",{class:!0});var l=T(n);a=N(l,s),l.forEach(b),this.h()},h(){C(n,"class","px-4 py-2 bg-negative")},m(e,s){k(e,t,s),k(e,n,s),E(n,a)},p:O,i:O,o:O,d(e){e&&(b(t),b(n))}}}function fe(e){let t,n,a,s,l=[],o=new Map,r=A(Object.entries(e[7]));const c=e=>e[8];for(let t=0;t<r.length;t+=1){let n=V(e,r,t),a=c(n);o.set(a,l[t]=z(a,n))}return{c(){t=$("section"),n=$("div"),a=$("ul");for(let e=0;e<l.length;e+=1)l[e].c();this.h()},l(e){t=v(e,"SECTION",{});var s=T(t);n=v(s,"DIV",{});var o=T(n);a=v(o,"UL",{class:!0});var r=T(a);for(let e=0;e<l.length;e+=1)l[e].l(r);r.forEach(b),o.forEach(b),s.forEach(b),this.h()},h(){C(a,"class","list-none m-0 p-0 flex flex-col gap-1 mb-1")},m(e,o){k(e,t,o),E(t,n),E(n,a);for(let e=0;e<l.length;e+=1)l[e]&&l[e].m(a,null);s=!0},p(e,t){7&t&&(r=A(Object.entries(e[7])),H(),l=Z(l,t,c,1,e,r,o,a,ee,z,null,V),P())},i(e){if(!s){for(let e=0;e<r.length;e+=1)g(l[e]);s=!0}},o(e){for(let e=0;e<l.length;e+=1)w(l[e]);s=!1},d(e){e&&b(t);for(let e=0;e<l.length;e+=1)l[e].d()}}}function x(e){let t,n,a,s=[],l=new Map,o=A(Object.entries(e[9]));const r=e=>e[12];for(let t=0;t<o.length;t+=1){let n=W(e,o,t),a=r(n);l.set(a,s[t]=J(a,n))}return{c(){t=$("ul");for(let e=0;e<s.length;e+=1)s[e].c();n=I(),this.h()},l(e){t=v(e,"UL",{class:!0});var a=T(t);for(let e=0;e<s.length;e+=1)s[e].l(a);n=j(a),a.forEach(b),this.h()},h(){C(t,"class","list-none m-0 flex flex-col gap-1")},m(e,l){k(e,t,l);for(let e=0;e<s.length;e+=1)s[e]&&s[e].m(t,null);E(t,n),a=!0},p(e,a){5&a&&(o=A(Object.entries(e[9])),H(),s=Z(s,a,r,1,e,o,l,t,ee,J,n,W),P())},i(e){if(!a){for(let e=0;e<o.length;e+=1)g(s[e]);a=!0}},o(e){for(let e=0;e<s.length;e+=1)w(s[e]);a=!1},d(e){e&&b(t);for(let e=0;e<s.length;e+=1)s[e].d()}}}function G(e){let t,n;return t=new oe({props:{columns:e[13].columns,rowClass:"ml-6 "}}),{c(){F(t.$$.fragment)},l(e){U(t.$$.fragment,e)},m(e,a){M(t,e,a),n=!0},p:O,i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){w(t.$$.fragment,e),n=!1},d(e){D(t,e)}}}function J(e,t){let n,a,s,l,o,r,c,i,h,f,u=t[12]+"";function m(){return t[5](t[13])}s=new te({props:{src:re,class:"w-5 h-5 mr-1"}});let d=t[0]===t[13]&&G(t);return{key:e,first:null,c(){n=$("li"),a=$("button"),F(s.$$.fragment),l=I(),o=q(u),r=I(),d&&d.c(),c=L(),this.h()},l(e){n=v(e,"LI",{class:!0});var t=T(n);a=v(t,"BUTTON",{class:!0});var i=T(a);U(s.$$.fragment,i),l=j(i),o=N(i,u),i.forEach(b),t.forEach(b),r=j(e),d&&d.l(e),c=L(),this.h()},h(){C(a,"class","bg-base-200 px-2 py-1 rounded-sm flex w-full hover:bg-base-300 hover:text-base-content"),S(a,"bg-info",t[0]===t[13]),S(a,"text-info-content",t[0]===t[13]),C(n,"class","font-mono m-0 text-sm font-bold ml-3"),this.first=n},m(e,t){k(e,n,t),E(n,a),M(s,a,null),E(a,l),E(a,o),k(e,r,t),d&&d.m(e,t),k(e,c,t),i=!0,h||(f=Q(a,"click",m),h=!0)},p(e,n){t=e,(!i||5&n)&&S(a,"bg-info",t[0]===t[13]),(!i||5&n)&&S(a,"text-info-content",t[0]===t[13]),t[0]===t[13]?d?(d.p(t,n),1&n&&g(d,1)):(d=G(t),d.c(),g(d,1),d.m(c.parentNode,c)):d&&(H(),w(d,1,1,(()=>{d=null})),P())},i(e){i||(g(s.$$.fragment,e),g(d),i=!0)},o(e){w(s.$$.fragment,e),w(d),i=!1},d(e){e&&(b(n),b(r),b(c)),D(s),d&&d.d(e),h=!1,f()}}}function z(e,t){let n,a,s,l,o,r,c,i,h,f,u=t[8]+"";function m(){return t[4](t[8])}s=new te({props:{src:se,class:"w-5 h-5 mr-1"}});let d=t[1]===t[8]&&x(t);return{key:e,first:null,c(){n=$("li"),a=$("button"),F(s.$$.fragment),l=I(),o=q(u),r=I(),d&&d.c(),c=L(),this.h()},l(e){n=v(e,"LI",{class:!0});var t=T(n);a=v(t,"BUTTON",{class:!0});var i=T(a);U(s.$$.fragment,i),l=j(i),o=N(i,u),i.forEach(b),t.forEach(b),r=j(e),d&&d.l(e),c=L(),this.h()},h(){C(a,"class","bg-base-200 px-2 py-1 rounded-sm font-bold flex w-full hover:bg-base-300 hover:text-base-content"),S(a,"bg-info",t[1]===t[8]),S(a,"text-info-content",t[1]===t[8]),C(n,"class","font-mono m-0 text-sm"),this.first=n},m(e,t){k(e,n,t),E(n,a),M(s,a,null),E(a,l),E(a,o),k(e,r,t),d&&d.m(e,t),k(e,c,t),i=!0,h||(f=Q(a,"click",m),h=!0)},p(e,n){t=e,(!i||6&n)&&S(a,"bg-info",t[1]===t[8]),(!i||6&n)&&S(a,"text-info-content",t[1]===t[8]),t[1]===t[8]?d?(d.p(t,n),2&n&&g(d,1)):(d=x(t),d.c(),g(d,1),d.m(c.parentNode,c)):d&&(H(),w(d,1,1,(()=>{d=null})),P())},i(e){i||(g(s.$$.fragment,e),g(d),i=!0)},o(e){w(s.$$.fragment,e),w(d),i=!1},d(e){e&&(b(n),b(r),b(c)),D(s),d&&d.d(e),h=!1,f()}}}function he(e){let t;return{c(){t=q("Loading Schema Information...")},l(e){t=N(e,"Loading Schema Information...")},m(e,n){k(e,t,n)},p:O,i:O,o:O,d(e){e&&b(t)}}}function _e(e){let t,n,a={ctx:e,current:null,token:null,hasCatch:!0,pending:he,then:fe,catch:ue,value:7,error:16,blocks:[,,,]};return ce(e[2](),a),{c(){t=L(),a.block.c()},l(e){t=L(),a.block.l(e)},m(e,s){k(e,t,s),a.block.m(e,a.anchor=s),a.mount=()=>t.parentNode,a.anchor=t,n=!0},p(t,[n]){ie(a,e=t,n)},i(e){n||(g(a.block),n=!0)},o(e){for(let e=0;e<3;e+=1){const t=a.blocks[e];w(t)}n=!1},d(e){e&&b(t),a.block.d(e),a.token=null,a=null}}}function me(e,t,n){let{data:a}=t,{__db:s}=a,l="",o="";return e.$$set=e=>{"data"in e&&n(3,a=e.data)},[l,o,async function(){const e=await s.query("SELECT * FROM information_schema.tables WHERE table_catalog = 'memory' AND table_name != 'stats'"),t={};return await Promise.all(e.map((async e=>{const n=await s.query(`SELECT * FROM information_schema.columns WHERE table_name = '${e.table_name}' AND table_schema = '${e.table_schema}'`);t[e.table_schema]||(t[e.table_schema]={}),t[e.table_schema][e.table_name]={table:e,columns:n}}))),t},a,e=>{n(1,o=o===e?"":e),n(0,l="")},e=>{n(0,l=l===e?"":e)}]}class be extends X{constructor(e){super(),Y(this,e,me,_e,K,{data:3})}}function de(e){let t,n,a,s,l,o,r,c,i="Project Schema",h="This page details the tables and columns that are currently loaded in your project.",f="Sources";return r=new be({props:{data:e[0]}}),{c(){t=$("h1"),t.textContent=i,n=I(),a=$("p"),a.textContent=h,s=I(),l=$("h2"),l.textContent=f,o=I(),F(r.$$.fragment),this.h()},l(e){t=v(e,"H1",{class:!0,"data-svelte-h":!0}),"svelte-15777oi"!==B(t)&&(t.textContent=i),n=j(e),a=v(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-ak948l"!==B(a)&&(a.textContent=h),s=j(e),l=v(e,"H2",{class:!0,"data-svelte-h":!0}),"svelte-9qt1ro"!==B(l)&&(l.textContent=f),o=j(e),U(r.$$.fragment,e),this.h()},h(){C(t,"class","markdown"),C(a,"class","markdown"),C(l,"class","markdown")},m(e,i){k(e,t,i),k(e,n,i),k(e,a,i),k(e,s,i),k(e,l,i),k(e,o,i),M(r,e,i),c=!0},p(e,[t]){const n={};1&t&&(n.data=e[0]),r.$set(n)},i(e){c||(g(r.$$.fragment,e),c=!0)},o(e){w(r.$$.fragment,e),c=!1},d(e){e&&(b(t),b(n),b(a),b(s),b(l),b(o)),D(r,e)}}}function pe(e,t,n){let{data:a}=t;return e.$$set=e=>{"data"in e&&n(0,a=e.data)},[a]}class we extends X{constructor(e){super(),Y(this,e,pe,de,K,{data:0})}}export{we as component};
import{L as le,M as ne,N as R,O as ae,s as K,d as b,i as k,r as N,v as L,e as E,b as C,x as I,h as v,j as T,y as P,m as $,P as S,l as Q,k as j,n as O,q as F}from"../chunks/scheduler.D0cbHTIG.js";import{g as q,t as w,c as M,a as g,S as X,i as Y,d as D,m as H,b as U,e as B}from"../chunks/index.YnsWT1Qn.js";import{e as A,u as Z,o as ee,I as te,h as se,T as re,j as oe}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.p4QJCV1Q.js";import"../chunks/entry.DRa3JAkG.js";import"../chunks/inferColumnTypes.DiSvbcQ7.js";function ce(e,t){const n=t.token={};function s(e,s,a,l){if(t.token!==n)return;t.resolved=l;let o=t.ctx;void 0!==a&&(o=o.slice(),o[a]=l);const r=e&&(t.current=e)(o);let c=!1;t.block&&(t.blocks?t.blocks.forEach(((e,n)=>{n!==s&&e&&(q(),w(e,1,1,(()=>{t.blocks[n]===e&&(t.blocks[n]=null)})),M())})):t.block.d(1),r.c(),g(r,1),r.m(t.mount(),t.anchor),c=!0),t.block=r,t.blocks&&(t.blocks[s]=r),c&&ae()}if(le(e)){const n=ne();if(e.then((e=>{R(n),s(t.then,1,t.value,e),R(null)}),(e=>{if(R(n),s(t.catch,2,t.error,e),R(null),!t.hasCatch)throw e})),t.current!==t.pending)return s(t.pending,0),!0}else{if(t.current!==t.then)return s(t.then,1,t.value,e),!0;t.resolved=e}}function ie(e,t,n){const s=t.slice(),{resolved:a}=e;e.current===e.then&&(s[e.value]=a),e.current===e.catch&&(s[e.error]=a),e.block.p(s,n)}function V(e,t,n){const s=e.slice();return s[8]=t[n][0],s[9]=t[n][1],s}function W(e,t,n){const s=e.slice();return s[12]=t[n][0],s[13]=t[n][1],s}function ue(e){let t,n,s,a=e[16].message+"";return{c(){t=P("An error was encountered while loading project schema.\n\n\t"),n=$("pre"),s=P(a),this.h()},l(e){t=I(e,"An error was encountered while loading project schema.\n\n\t"),n=v(e,"PRE",{class:!0});var l=T(n);s=I(l,a),l.forEach(b),this.h()},h(){C(n,"class","px-4 py-2 bg-negative")},m(e,a){k(e,t,a),k(e,n,a),E(n,s)},p:L,i:L,o:L,d(e){e&&(b(t),b(n))}}}function fe(e){let t,n,s,a,l=[],o=new Map,r=A(Object.entries(e[7]));const c=e=>e[8];for(let t=0;t<r.length;t+=1){let n=V(e,r,t),s=c(n);o.set(s,l[t]=J(s,n))}return{c(){t=$("section"),n=$("div"),s=$("ul");for(let e=0;e<l.length;e+=1)l[e].c();this.h()},l(e){t=v(e,"SECTION",{});var a=T(t);n=v(a,"DIV",{});var o=T(n);s=v(o,"UL",{class:!0});var r=T(s);for(let e=0;e<l.length;e+=1)l[e].l(r);r.forEach(b),o.forEach(b),a.forEach(b),this.h()},h(){C(s,"class","list-none m-0 p-0 flex flex-col gap-1 mb-1")},m(e,o){k(e,t,o),E(t,n),E(n,s);for(let e=0;e<l.length;e+=1)l[e]&&l[e].m(s,null);a=!0},p(e,t){7&t&&(r=A(Object.entries(e[7])),q(),l=Z(l,t,c,1,e,r,o,s,ee,J,null,V),M())},i(e){if(!a){for(let e=0;e<r.length;e+=1)g(l[e]);a=!0}},o(e){for(let e=0;e<l.length;e+=1)w(l[e]);a=!1},d(e){e&&b(t);for(let e=0;e<l.length;e+=1)l[e].d()}}}function x(e){let t,n,s,a=[],l=new Map,o=A(Object.entries(e[9]));const r=e=>e[12];for(let t=0;t<o.length;t+=1){let n=W(e,o,t),s=r(n);l.set(s,a[t]=G(s,n))}return{c(){t=$("ul");for(let e=0;e<a.length;e+=1)a[e].c();n=O(),this.h()},l(e){t=v(e,"UL",{class:!0});var s=T(t);for(let e=0;e<a.length;e+=1)a[e].l(s);n=j(s),s.forEach(b),this.h()},h(){C(t,"class","list-none m-0 flex flex-col gap-1")},m(e,l){k(e,t,l);for(let e=0;e<a.length;e+=1)a[e]&&a[e].m(t,null);E(t,n),s=!0},p(e,s){5&s&&(o=A(Object.entries(e[9])),q(),a=Z(a,s,r,1,e,o,l,t,ee,G,n,W),M())},i(e){if(!s){for(let e=0;e<o.length;e+=1)g(a[e]);s=!0}},o(e){for(let e=0;e<a.length;e+=1)w(a[e]);s=!1},d(e){e&&b(t);for(let e=0;e<a.length;e+=1)a[e].d()}}}function z(e){let t,n;return t=new oe({props:{columns:e[13].columns,rowClass:"ml-6 "}}),{c(){B(t.$$.fragment)},l(e){U(t.$$.fragment,e)},m(e,s){H(t,e,s),n=!0},p:L,i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){w(t.$$.fragment,e),n=!1},d(e){D(t,e)}}}function G(e,t){let n,s,a,l,o,r,c,i,h,f,u=t[12]+"";function m(){return t[5](t[13])}a=new te({props:{src:re,class:"w-5 h-5 mr-1"}});let d=t[0]===t[13]&&z(t);return{key:e,first:null,c(){n=$("li"),s=$("button"),B(a.$$.fragment),l=O(),o=P(u),r=O(),d&&d.c(),c=N(),this.h()},l(e){n=v(e,"LI",{class:!0});var t=T(n);s=v(t,"BUTTON",{class:!0});var i=T(s);U(a.$$.fragment,i),l=j(i),o=I(i,u),i.forEach(b),t.forEach(b),r=j(e),d&&d.l(e),c=N(),this.h()},h(){C(s,"class","bg-base-200 px-2 py-1 rounded-sm flex w-full hover:bg-base-300 hover:text-base-content"),S(s,"bg-info",t[0]===t[13]),S(s,"text-info-content",t[0]===t[13]),C(n,"class","font-mono m-0 text-sm font-bold ml-3"),this.first=n},m(e,t){k(e,n,t),E(n,s),H(a,s,null),E(s,l),E(s,o),k(e,r,t),d&&d.m(e,t),k(e,c,t),i=!0,h||(f=Q(s,"click",m),h=!0)},p(e,n){t=e,(!i||5&n)&&S(s,"bg-info",t[0]===t[13]),(!i||5&n)&&S(s,"text-info-content",t[0]===t[13]),t[0]===t[13]?d?(d.p(t,n),1&n&&g(d,1)):(d=z(t),d.c(),g(d,1),d.m(c.parentNode,c)):d&&(q(),w(d,1,1,(()=>{d=null})),M())},i(e){i||(g(a.$$.fragment,e),g(d),i=!0)},o(e){w(a.$$.fragment,e),w(d),i=!1},d(e){e&&(b(n),b(r),b(c)),D(a),d&&d.d(e),h=!1,f()}}}function J(e,t){let n,s,a,l,o,r,c,i,h,f,u=t[8]+"";function m(){return t[4](t[8])}a=new te({props:{src:se,class:"w-5 h-5 mr-1"}});let d=t[1]===t[8]&&x(t);return{key:e,first:null,c(){n=$("li"),s=$("button"),B(a.$$.fragment),l=O(),o=P(u),r=O(),d&&d.c(),c=N(),this.h()},l(e){n=v(e,"LI",{class:!0});var t=T(n);s=v(t,"BUTTON",{class:!0});var i=T(s);U(a.$$.fragment,i),l=j(i),o=I(i,u),i.forEach(b),t.forEach(b),r=j(e),d&&d.l(e),c=N(),this.h()},h(){C(s,"class","bg-base-200 px-2 py-1 rounded-sm font-bold flex w-full hover:bg-base-300 hover:text-base-content"),S(s,"bg-info",t[1]===t[8]),S(s,"text-info-content",t[1]===t[8]),C(n,"class","font-mono m-0 text-sm"),this.first=n},m(e,t){k(e,n,t),E(n,s),H(a,s,null),E(s,l),E(s,o),k(e,r,t),d&&d.m(e,t),k(e,c,t),i=!0,h||(f=Q(s,"click",m),h=!0)},p(e,n){t=e,(!i||6&n)&&S(s,"bg-info",t[1]===t[8]),(!i||6&n)&&S(s,"text-info-content",t[1]===t[8]),t[1]===t[8]?d?(d.p(t,n),2&n&&g(d,1)):(d=x(t),d.c(),g(d,1),d.m(c.parentNode,c)):d&&(q(),w(d,1,1,(()=>{d=null})),M())},i(e){i||(g(a.$$.fragment,e),g(d),i=!0)},o(e){w(a.$$.fragment,e),w(d),i=!1},d(e){e&&(b(n),b(r),b(c)),D(a),d&&d.d(e),h=!1,f()}}}function he(e){let t;return{c(){t=P("Loading Schema Information...")},l(e){t=I(e,"Loading Schema Information...")},m(e,n){k(e,t,n)},p:L,i:L,o:L,d(e){e&&b(t)}}}function _e(e){let t,n,s={ctx:e,current:null,token:null,hasCatch:!0,pending:he,then:fe,catch:ue,value:7,error:16,blocks:[,,,]};return ce(e[2](),s),{c(){t=N(),s.block.c()},l(e){t=N(),s.block.l(e)},m(e,a){k(e,t,a),s.block.m(e,s.anchor=a),s.mount=()=>t.parentNode,s.anchor=t,n=!0},p(t,[n]){ie(s,e=t,n)},i(e){n||(g(s.block),n=!0)},o(e){for(let e=0;e<3;e+=1){const t=s.blocks[e];w(t)}n=!1},d(e){e&&b(t),s.block.d(e),s.token=null,s=null}}}function me(e,t,n){let{data:s}=t,{__db:a}=s,l="",o="";return e.$$set=e=>{"data"in e&&n(3,s=e.data)},[l,o,async function(){const e=await a.query("SELECT * FROM information_schema.tables WHERE table_catalog = 'memory' AND table_name != 'stats'"),t={};return await Promise.all(e.map((async e=>{const n=await a.query(`SELECT * FROM information_schema.columns WHERE table_name = '${e.table_name}' AND table_schema = '${e.table_schema}'`);t[e.table_schema]||(t[e.table_schema]={}),t[e.table_schema][e.table_name]={table:e,columns:n}}))),t},s,e=>{n(1,o=o===e?"":e),n(0,l="")},e=>{n(0,l=l===e?"":e)}]}class be extends X{constructor(e){super(),Y(this,e,me,_e,K,{data:3})}}function de(e){let t,n,s,a,l,o,r,c,i="Project Schema",h="This page details the tables and columns that are currently loaded in your project.",f="Sources";return r=new be({props:{data:e[0]}}),{c(){t=$("h1"),t.textContent=i,n=O(),s=$("p"),s.textContent=h,a=O(),l=$("h2"),l.textContent=f,o=O(),B(r.$$.fragment),this.h()},l(e){t=v(e,"H1",{class:!0,"data-svelte-h":!0}),"svelte-15777oi"!==F(t)&&(t.textContent=i),n=j(e),s=v(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-ak948l"!==F(s)&&(s.textContent=h),a=j(e),l=v(e,"H2",{class:!0,"data-svelte-h":!0}),"svelte-9qt1ro"!==F(l)&&(l.textContent=f),o=j(e),U(r.$$.fragment,e),this.h()},h(){C(t,"class","markdown"),C(s,"class","markdown"),C(l,"class","markdown")},m(e,i){k(e,t,i),k(e,n,i),k(e,s,i),k(e,a,i),k(e,l,i),k(e,o,i),H(r,e,i),c=!0},p(e,[t]){const n={};1&t&&(n.data=e[0]),r.$set(n)},i(e){c||(g(r.$$.fragment,e),c=!0)},o(e){w(r.$$.fragment,e),c=!1},d(e){e&&(b(t),b(n),b(s),b(a),b(l),b(o)),D(r,e)}}}function pe(e,t,n){let{data:s}=t;return e.$$set=e=>{"data"in e&&n(0,s=e.data)},[s]}class Ee extends X{constructor(e){super(),Y(this,e,pe,de,K,{data:0})}}export{Ee as component};
import{s as l,c as i,u as r,g as u,a as f}from"../chunks/scheduler.DQwIXrE4.js";import{S as _,i as c,t as p,a as m}from"../chunks/index.BEt_7cXZ.js";function $(s){let t;const c=s[1].default,n=i(c,s,s[0],null);return{c(){n&&n.c()},l(s){n&&n.l(s)},m(s,c){n&&n.m(s,c),t=!0},p(s,[e]){n&&n.p&&(!t||1&e)&&r(n,c,s,s[0],t?f(c,s[0],e,null):u(s[0]),null)},i(s){t||(m(n,s),t=!0)},o(s){p(n,s),t=!1},d(s){n&&n.d(s)}}}function d(s,t,c){let{$$slots:n={},$$scope:e}=t;return s.$$set=s=>{"$$scope"in s&&c(0,e=s.$$scope)},[e,n]}class S extends _{constructor(s){super(),c(this,s,d,$,l,{})}}export{S as component};
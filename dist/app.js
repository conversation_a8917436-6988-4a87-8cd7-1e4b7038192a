(()=>{"use strict";console.log("Evidence app loaded for Domo DDX");if("undefined"!=typeof window&&(window.enhancedDomoDuckDB=new class{constructor(){this.duckdb=null,this.connection=null,this.availableDatasets=[],this.filteredDatasets=[],this.loadedDatasets=new Map,this.isInitialized=!1,this.isDomoEnvironment=this.detectDomoEnvironment(),this.domoApiAvailable=!1,this.currentDataset=null,this.activeTab="schema","loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>this.init())):this.init()}detectDomoEnvironment(){if("undefined"==typeof window)return!1;const e={hasDomoObject:void 0!==window.domo,isInIframe:window.self!==window.top,hasDomoHostname:window.location&&window.location.hostname.includes("domo"),hasDomoAppsDomain:window.location&&window.location.hostname.includes("domoapps")},t=e.hasDomoObject||e.isInIframe&&(e.hasDomoHostname||e.hasDomoAppsDomain);return console.log("🔍 Domo Environment Detection:",e),console.log("🏢 Environment detected as: "+(t?"Domo DDX":"Development")),t}async init(){try{console.log("🚀 Initializing Enhanced Domo-DuckDB Integration..."),console.log("Environment check:",{isDomoEnvironment:this.isDomoEnvironment,hasDomoAPI:void 0!==window.domo,isInIframe:window.self!==window.top}),await this.initializeDuckDB(),await this.loadAvailableDatasets(),this.setupEventListeners(),this.setupTabSwitching(),this.isInitialized=!0,console.log("✅ Enhanced Domo-DuckDB integration initialized successfully");const e=document.getElementById("dataset-selector");e?console.log(`📋 Dropdown populated with ${e.options.length-1} datasets`):console.warn("⚠️ Dataset selector element not found!")}catch(e){console.error("❌ Failed to initialize Enhanced Domo-DuckDB integration:",e),this.showError("Failed to initialize data integration. Please refresh the page.")}}async initializeDuckDB(){try{if(console.log("🦆 Initializing DuckDB-WASM..."),this.isDomoEnvironment){console.log("🔗 Running in Domo DDX environment");try{window.evidence&&window.evidence.duckdb?(console.log("📊 Using Evidence built-in DuckDB"),this.duckdb=this.createEvidenceDuckDBWrapper()):(console.log("🔧 Attempting DuckDB-WASM without blob URLs"),await this.loadDuckDBWithoutBlob())}catch(e){console.warn("⚠️ Advanced DuckDB methods failed, using enhanced fallback:",e),this.duckdb=this.createEnhancedFallbackDuckDB()}}else{console.log("🛠️ Running in development environment");try{await this.loadDuckDBFromCDN()}catch(e){console.warn("⚠️ DuckDB-WASM initialization failed, using enhanced fallback:",e),this.duckdb=this.createEnhancedFallbackDuckDB()}}console.log("✅ DuckDB integration ready")}catch(e){console.error("❌ DuckDB initialization failed:",e),this.duckdb=this.createEnhancedFallbackDuckDB(),console.log("✅ Using enhanced fallback DuckDB implementation")}}async loadDuckDBFromCDN(){return new Promise(((e,t)=>{const a=document.createElement("script");a.type="module",a.innerHTML="\n                // DuckDB import removed for DDX compatibility\n\n                window.initializeDuckDB = async function() {\n                    try {\n                        const JSDELIVR_BUNDLES = duckdb.getJsDelivrBundles();\n                        const bundle = await duckdb.selectBundle(JSDELIVR_BUNDLES);\n\n                        const worker_url = URL.createObjectURL(\n                            new Blob([`importScripts(\"${bundle.mainWorker}\");`], { type: 'text/javascript' })\n                        );\n\n                        const worker = new Worker(worker_url);\n                        const logger = new duckdb.ConsoleLogger();\n\n                        const db = new duckdb.AsyncDuckDB(logger, worker);\n                        await db.instantiate(bundle.mainModule, bundle.pthreadWorker);\n\n                        const connection = await db.connect();\n\n                        URL.revokeObjectURL(worker_url);\n\n                        // Test connection\n                        await connection.query('SELECT 1 as test');\n                        console.log('✅ Real DuckDB-WASM initialized successfully');\n\n                        window.duckDBConnection = connection;\n                        window.duckDBReady = true;\n\n                        return { success: true, connection };\n                    } catch (error) {\n                        console.error('DuckDB initialization failed:', error);\n                        return { success: false, error };\n                    }\n                };\n            ",a.onload=async()=>{try{const t=await window.initializeDuckDB();if(!t.success)throw t.error;this.connection=window.duckDBConnection,this.duckdb={query:async e=>(console.log("🔍 Executing SQL:",e),await this.connection.query(e)),insertData:async(e,t)=>await this.insertDataIntoTable(e,t)},e()}catch(e){t(e)}},a.onerror=()=>{t(new Error("Failed to load DuckDB-WASM from CDN"))},document.head.appendChild(a)}))}createEvidenceDuckDBWrapper(){return{query:async e=>{console.log("🔍 Evidence DuckDB execution:",e);try{if(window.evidence&&window.evidence.duckdb)return await window.evidence.duckdb.query(e);throw new Error("Evidence DuckDB not available")}catch(t){return console.warn("Evidence DuckDB failed, using simulation:",t),this.simulateQueryResult(e)}},insertData:async(e,t)=>{console.log(`📊 Evidence DuckDB: Inserting into ${e}`,t.length,"rows");try{if(window.evidence&&window.evidence.duckdb)return await this.insertDataUsingEvidence(e,t);throw new Error("Evidence DuckDB not available")}catch(e){return console.warn("Evidence DuckDB insert failed, using simulation:",e),{success:!0,rowsInserted:t.length}}}}}async loadDuckDBWithoutBlob(){throw console.log("🔧 Attempting DuckDB without blob URLs..."),new Error("Alternative DuckDB loading not yet implemented")}createEnhancedFallbackDuckDB(){const e=new Map;return{query:async t=>(console.log("🔍 Enhanced Fallback SQL execution:",t),this.simulateQueryResult(t,e)),insertData:async(t,a)=>(console.log(`📊 Enhanced Fallback: Inserting into ${t}`,a.length,"rows"),e.set(t,a),{success:!0,rowsInserted:a.length})}}simulateQueryResult(e,t=null){if(e.includes("CREATE TABLE"))return{success:!0,message:"Table created (simulated)"};if(e.includes("SELECT")){const a=e.match(/FROM\s+(\w+)/i),n=a?a[1]:null;if(t&&n&&t.has(n)){const e=t.get(n);return{rows:e.slice(0,100),columns:e.length>0?Object.keys(e[0]):[]}}return{rows:[{column1:"Sample Data 1",column2:123,column3:"2024-01-01"},{column1:"Sample Data 2",column2:456,column3:"2024-01-02"}],columns:["column1","column2","column3"]}}return{rows:[],columns:[]}}createFallbackDuckDB(){return this.createEnhancedFallbackDuckDB()}async insertDataIntoTable(e,t){if(!this.connection||!t||0===t.length)throw new Error("Invalid connection or data");try{const a=t[0],n=Object.keys(a),o=n.map((e=>{const t=a[e];let n="VARCHAR";return"number"==typeof t?n=Number.isInteger(t)?"INTEGER":"DOUBLE":t instanceof Date||/^\d{4}-\d{2}-\d{2}/.test(t)?n="DATE":"boolean"==typeof t&&(n="BOOLEAN"),`${e} ${n}`})).join(", ");await this.connection.query(`DROP TABLE IF EXISTS ${e}`),await this.connection.query(`CREATE TABLE ${e} (${o})`);const s=1e3;let i=0;for(let a=0;a<t.length;a+=s){const o=t.slice(a,a+s),r=o.map((e=>`(${n.map((t=>{const a=e[t];return null==a?"NULL":"string"==typeof a?`'${a.replace(/'/g,"''")}'`:a})).join(", ")})`)).join(", ");await this.connection.query(`INSERT INTO ${e} VALUES ${r}`),i+=o.length;const d=Math.round(i/t.length*100);this.updateProgress(d)}return{success:!0,rowsInserted:i}}catch(e){throw console.error("❌ Failed to insert data into DuckDB:",e),e}}async loadAvailableDatasets(){try{console.log("📊 Loading available datasets...");const e=this.getConfiguredDatasets();if(console.log(`📋 Found ${e.length} configured datasets`),this.availableDatasets=[...e],this.isDomoEnvironment)if(console.log("🏢 Domo DDX environment detected, checking for API..."),await this.waitForDomoAPI()){console.log("✅ Domo API is now available, loading additional datasets...");try{const e=(await this.fetchDatasetsFromDomo()).map((e=>this.normalizeDomoDataset(e)));e.forEach((e=>{this.availableDatasets.find((t=>t.id===e.id))||this.availableDatasets.push(e)})),console.log(`✅ Loaded ${e.length} additional datasets from Domo API`)}catch(e){console.warn("⚠️ Failed to load additional datasets from Domo API:",e),console.log("📋 Using configured datasets only")}}else console.log("⚠️ Domo API not available, using configured datasets only");else console.log("🧪 Development mode: Adding mock datasets"),this.availableDatasets.push(...this.getMockDatasets());console.log(`✅ Total available datasets: ${this.availableDatasets.length} (${e.length} configured)`),this.populateDatasetDropdown()}catch(e){console.error("❌ Failed to load available datasets:",e);const t=this.getConfiguredDatasets();this.availableDatasets=t,console.log(`🔄 Emergency fallback: Using ${t.length} configured datasets`),this.populateDatasetDropdown(),this.showWarning("Some datasets may not be available. Using configured datasets.")}}async waitForDomoAPI(e=1e4){return console.log("⏳ Waiting for Domo API to become available..."),new Promise((t=>{if(window.domo&&window.domo.get)return console.log("✅ Domo API found immediately"),t(!0);let a=0;const n=e/100,o=()=>{a++,console.log(`🔍 Checking for Domo API (attempt ${a}/${n})`),window.domo&&window.domo.get?(console.log("✅ Domo API found after waiting"),t(!0)):a>=n?(console.warn(`⚠️ Domo API not available after ${e}ms`),t(!1)):setTimeout(o,100)};o()}))}getConfiguredDatasets(){return[{id:"a8cf6a39-11f0-4b11-b577-7e8d4bf0efbe",name:"Excel Dataset",displayName:"Excel Dataset (Configured)",description:"Pre-configured Excel dataset from Domo manifest mapping",alias:"excel",rowCount:"Unknown",lastUpdated:"Unknown",schema:[{name:"Loading...",type:"STRING",description:"Schema will be loaded when selected"}],tags:["configured","excel","manifest"],owner:"Domo Configuration",isConfigured:!0},{id:"a92b693c-44e6-4d68-b7c9-f98753ca3edc",name:"Government Dataset",displayName:"Government Dataset (Configured)",description:"Pre-configured government dataset from Domo manifest mapping",alias:"gov",rowCount:"Unknown",lastUpdated:"Unknown",schema:[{name:"Loading...",type:"STRING",description:"Schema will be loaded when selected"}],tags:["configured","government","manifest"],owner:"Domo Configuration",isConfigured:!0}]}async fetchDatasetsFromDomo(){const e=["/data/v1/datasets","/data/v2/datasets","/domo/datasets","/api/data/v1/datasets"];for(const t of e)try{console.log(`🔍 Trying endpoint: ${t}`);const e=await window.domo.get(t);if(e&&e.length>0)return console.log(`✅ Successfully loaded from ${t}`),e}catch(e){console.log(`❌ Endpoint ${t} failed:`,e.message)}throw new Error("All dataset endpoints failed")}getMockDatasets(){return[{id:"sales-data-2024",name:"Sales Data 2024",displayName:"Sales Data 2024",description:"Comprehensive sales data for 2024 including revenue, products, and customer information",rowCount:125e3,lastUpdated:"2024-01-15T10:30:00Z",schema:[{name:"order_id",type:"STRING",description:"Unique order identifier"},{name:"customer_id",type:"STRING",description:"Customer identifier"},{name:"product_name",type:"STRING",description:"Product name"},{name:"quantity",type:"LONG",description:"Quantity ordered"},{name:"unit_price",type:"DOUBLE",description:"Price per unit"},{name:"total_amount",type:"DOUBLE",description:"Total order amount"},{name:"order_date",type:"DATE",description:"Date of order"},{name:"region",type:"STRING",description:"Sales region"}],tags:["sales","revenue","customers"],owner:"Sales Team"},{id:"customer-demographics",name:"Customer Demographics",displayName:"Customer Demographics",description:"Customer demographic and behavioral data for segmentation analysis",rowCount:45e3,lastUpdated:"2024-01-10T14:20:00Z",schema:[{name:"customer_id",type:"STRING",description:"Customer identifier"},{name:"age",type:"LONG",description:"Customer age"},{name:"gender",type:"STRING",description:"Customer gender"},{name:"income_bracket",type:"STRING",description:"Income range"},{name:"location",type:"STRING",description:"Customer location"},{name:"signup_date",type:"DATE",description:"Account creation date"},{name:"lifetime_value",type:"DOUBLE",description:"Customer lifetime value"}],tags:["customers","demographics","segmentation"],owner:"Marketing Team"},{id:"product-inventory",name:"Product Inventory",displayName:"Product Inventory",description:"Real-time product inventory levels and warehouse data",rowCount:8500,lastUpdated:"2024-01-16T09:15:00Z",schema:[{name:"product_id",type:"STRING",description:"Product identifier"},{name:"product_name",type:"STRING",description:"Product name"},{name:"category",type:"STRING",description:"Product category"},{name:"current_stock",type:"LONG",description:"Current inventory level"},{name:"reorder_point",type:"LONG",description:"Reorder threshold"},{name:"unit_cost",type:"DOUBLE",description:"Cost per unit"},{name:"warehouse_location",type:"STRING",description:"Storage location"}],tags:["inventory","products","warehouse"],owner:"Operations Team"}]}normalizeDomoDataset(e){return{id:e.id,name:e.name||e.displayName||"Unnamed Dataset",description:e.description||"No description available",schema:this.normalizeSchema(e.schema||e.columns||[]),rowCount:e.rowCount||e.rows||0,lastUpdated:e.lastUpdated||e.updatedAt||(new Date).toISOString(),tags:e.tags||[],owner:e.owner||"Unknown"}}normalizeSchema(e){return Array.isArray(e)?e.map((e=>({name:e.name||e.columnName||e.field,type:this.normalizeColumnType(e.type||e.columnType||e.dataType),description:e.description||""}))):[]}normalizeColumnType(e){return{STRING:"STRING",TEXT:"STRING",LONG:"LONG",INTEGER:"LONG",DOUBLE:"DOUBLE",FLOAT:"DOUBLE",DECIMAL:"DECIMAL",DATE:"DATE",DATETIME:"DATETIME",TIMESTAMP:"DATETIME",BOOLEAN:"BOOLEAN"}[e?.toString().toUpperCase()]||"STRING"}setupEventListeners(){const e=document.getElementById("dataset-selector");e&&e.addEventListener("change",(e=>this.onDatasetSelected(e.target.value)));const t=document.getElementById("preview-btn");t&&t.addEventListener("click",(()=>this.previewDataset()));const a=document.getElementById("validate-config-btn");a&&a.addEventListener("click",(()=>this.validateConfiguration()));const n=document.getElementById("load-dataset-btn");n&&n.addEventListener("click",(()=>this.loadDatasetIntoDuckDB()));const o=document.getElementById("table-name");o&&o.addEventListener("input",(e=>this.validateTableName(e.target.value)))}setupTabSwitching(){document.querySelectorAll(".tab-button").forEach((e=>{e.addEventListener("click",(e=>{const t=e.target.dataset.tab;this.switchTab(t)}))}))}switchTab(e){document.querySelectorAll(".tab-button").forEach((e=>e.classList.remove("active"))),document.querySelector(`[data-tab="${e}"]`).classList.add("active"),document.querySelectorAll(".tab-content").forEach((e=>e.classList.remove("active"))),document.getElementById(`${e}-tab`).classList.add("active"),this.activeTab=e,"metadata"===e&&this.currentDataset&&this.showDatasetMetadata(this.currentDataset)}populateDatasetDropdown(){console.log("📋 Populating dataset dropdown...");const e=()=>{const e=document.getElementById("dataset-selector");if(!e)return console.error("❌ Dataset selector element not found!"),void console.log("Available elements with id:",Array.from(document.querySelectorAll("[id]")).map((e=>e.id)));this.doPopulateDropdown(e)};"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e):e()}doPopulateDropdown(e){if(console.log(`📊 doPopulateDropdown called with ${this.availableDatasets.length} datasets`),0===this.availableDatasets.length)return void console.warn("⚠️ No datasets available to populate dropdown!");this.availableDatasets.forEach(((e,t)=>{console.log(`  ${t+1}. ${e.name} (${e.id}) - configured: ${e.isConfigured}`)})),e.innerHTML='<option value="">Choose a dataset...</option>',console.log("🔄 Cleared dropdown and added default option");const t=[...this.availableDatasets].sort(((e,t)=>e.isConfigured&&!t.isConfigured?-1:!e.isConfigured&&t.isConfigured?1:e.name.localeCompare(t.name)));console.log(`📋 Sorted ${t.length} datasets for dropdown`),t.forEach(((t,a)=>{const n=document.createElement("option");n.value=t.id;const o="number"==typeof t.rowCount?`${t.rowCount.toLocaleString()} rows`:t.rowCount,s=t.isConfigured?"📋 ":"",i=t.alias?` [${t.alias}]`:"";n.textContent=`${s}${t.name}${i} (${o})`,n.dataset.description=t.description,n.dataset.configured=t.isConfigured?"true":"false",t.isConfigured&&(n.style.fontWeight="600",n.style.background="#f0fdf4"),e.appendChild(n),console.log(`  ➕ Added option ${a+1}: ${n.textContent}`)})),console.log(`✅ Dropdown populated with ${t.length} datasets`),console.log(`📊 Final dropdown has ${e.options.length} total options (including default)`)}async onDatasetSelected(e){if(!e)return this.currentDataset=null,this.hideDatasetPreview(),void this.hideWorkflowSteps();this.currentDataset=this.availableDatasets.find((t=>t.id===e)),this.currentDataset&&(this.currentDataset.isConfigured&&await this.loadConfiguredDatasetMetadata(this.currentDataset),this.showDatasetPreview(this.currentDataset),this.showWorkflowSteps(),this.autoGenerateTableName(this.currentDataset))}async loadConfiguredDatasetMetadata(e){if(this.isDomoEnvironment&&window.domo)try{console.log(`🔍 Loading metadata for configured dataset: ${e.id}`);const t=await window.domo.get(`/data/v1/datasets/${e.id}`);t&&(e.name=t.name||e.name,e.description=t.description||e.description,e.rowCount=t.rowCount||t.rows||"Unknown",e.lastUpdated=t.lastUpdated||t.updatedAt||"Unknown",e.schema=this.normalizeSchema(t.schema||t.columns||[]),e.owner=t.owner||e.owner,console.log(`✅ Loaded metadata for ${e.name}: ${e.rowCount} rows, ${e.schema.length} columns`))}catch(t){console.warn(`⚠️ Could not load metadata for configured dataset ${e.id}:`,t)}else console.log("🧪 Mock mode: Using placeholder metadata for configured dataset")}autoGenerateTableName(e){const t=document.getElementById("table-name");if(!t||t.value.trim())return;const a=e.name.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"_").substring(0,50);t.value=a,this.validateTableName(a)}validateTableName(e){const t=document.getElementById("table-name");if(!t)return;const a=/^[a-z][a-z0-9_]*$/.test(e)&&e.length<=50;return a?(t.classList.remove("invalid"),t.classList.add("valid")):(t.classList.remove("valid"),t.classList.add("invalid")),a}showDatasetPreview(e){const t=document.getElementById("dataset-preview");t&&(this.showDatasetInfo(e),this.showDatasetSchema(e),t.style.display="block")}showDatasetInfo(e){const t=document.getElementById("dataset-info");if(!t)return;const a="Unknown"!==e.lastUpdated?new Date(e.lastUpdated).toLocaleDateString():e.lastUpdated,n="number"==typeof e.rowCount?e.rowCount.toLocaleString():e.rowCount,o=e.tags?e.tags.map((e=>`<span class="tag">${e}</span>`)).join(""):"",s=e.isConfigured?'<span class="configured-badge">📋 Configured in Manifest</span>':"";t.innerHTML=`\n            <div class="info-card ${e.isConfigured?"configured-dataset":""}">\n                <h5>${e.name} ${s}</h5>\n                <p class="description">${e.description}</p>\n                ${e.alias?`<p class="dataset-alias"><strong>Alias:</strong> <code>${e.alias}</code></p>`:""}\n                <div class="info-stats">\n                    <div class="stat">\n                        <span class="label">Dataset ID:</span>\n                        <span class="value"><code>${e.id}</code></span>\n                    </div>\n                    <div class="stat">\n                        <span class="label">Rows:</span>\n                        <span class="value">${n}</span>\n                    </div>\n                    <div class="stat">\n                        <span class="label">Columns:</span>\n                        <span class="value">${e.schema.length}</span>\n                    </div>\n                    <div class="stat">\n                        <span class="label">Last Updated:</span>\n                        <span class="value">${a}</span>\n                    </div>\n                    <div class="stat">\n                        <span class="label">Owner:</span>\n                        <span class="value">${e.owner}</span>\n                    </div>\n                </div>\n                ${o?`<div class="tags">${o}</div>`:""}\n            </div>\n        `}showDatasetSchema(e){const t=document.getElementById("schema-table");if(!t)return;const a=e.schema.map((e=>`\n            <tr>\n                <td class="column-name">${e.name}</td>\n                <td class="column-type">\n                    <span class="type-badge type-${e.type.toLowerCase()}">${e.type}</span>\n                </td>\n                <td class="column-description">${e.description||"-"}</td>\n            </tr>\n        `)).join("");t.innerHTML=`\n            <table class="schema-table">\n                <thead>\n                    <tr>\n                        <th>Column Name</th>\n                        <th>Data Type</th>\n                        <th>Description</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    ${a}\n                </tbody>\n            </table>\n        `}showDatasetMetadata(e){const t=document.getElementById("dataset-metadata");if(!t)return;const a=this.estimateDatasetSize(e);t.innerHTML=`\n            <div class="metadata-grid">\n                <div class="metadata-item">\n                    <h6>Dataset ID</h6>\n                    <p><code>${e.id}</code></p>\n                </div>\n                <div class="metadata-item">\n                    <h6>Estimated Size</h6>\n                    <p>${a}</p>\n                </div>\n                <div class="metadata-item">\n                    <h6>Data Quality</h6>\n                    <p>Schema: ${e.schema.length} columns defined</p>\n                </div>\n                <div class="metadata-item">\n                    <h6>Access Information</h6>\n                    <p>Owner: ${e.owner}<br>\n                    Last Updated: ${new Date(e.lastUpdated).toLocaleString()}</p>\n                </div>\n            </div>\n        `}estimateDatasetSize(e){const t=e.schema.reduce(((e,t)=>e+({STRING:50,LONG:8,DOUBLE:8,DECIMAL:16,DATE:8,DATETIME:8,BOOLEAN:1}[t.type]||50)),0),a=e.rowCount*t;return a<1024?`${a} bytes`:a<1048576?`${(a/1024).toFixed(1)} KB`:a<1073741824?`${(a/1048576).toFixed(1)} MB`:`${(a/1073741824).toFixed(1)} GB`}hideDatasetPreview(){const e=document.getElementById("dataset-preview");e&&(e.style.display="none")}showWorkflowSteps(){const e=document.getElementById("loading-config");e&&(e.style.display="block");const t=document.getElementById("workflow-actions");t&&(t.style.display="block")}hideWorkflowSteps(){const e=document.getElementById("loading-config");e&&(e.style.display="none");const t=document.getElementById("workflow-actions");t&&(t.style.display="none")}async validateConfiguration(){if(!document.getElementById("validation-results"))return;const e=document.getElementById("table-name")?.value?.trim(),t=this.currentDataset,a=[];if(t?a.push({type:"success",message:`Dataset "${t.name}" selected`}):a.push({type:"error",message:"No dataset selected"}),e?this.validateTableName(e)?a.push({type:"success",message:`Table name "${e}" is valid`}):a.push({type:"error",message:"Invalid table name format"}):a.push({type:"error",message:"Table name is required"}),e&&this.loadedDatasets.has(e)){const t=document.getElementById("refresh-mode")?.value;"replace"===t?a.push({type:"warning",message:`Table "${e}" will be replaced`}):a.push({type:"info",message:`Data will be appended to existing table "${e}"`})}this.showValidationResults(a)}showValidationResults(e){const t=document.getElementById("validation-results");if(!t)return;const a=e.map((e=>`\n            <div class="validation-item validation-${e.type}">\n                <span class="validation-icon">${this.getValidationIcon(e.type)}</span>\n                <span class="validation-message">${e.message}</span>\n            </div>\n        `)).join("");t.innerHTML=a,t.style.display="block"}getValidationIcon(e){return{success:"✅",error:"❌",warning:"⚠️",info:"ℹ️"}[e]||"ℹ️"}async previewDataset(){if(this.currentDataset)try{this.showLoading("Loading sample data...");const e=await this.fetchSampleData(this.currentDataset.id);this.showDataPreview(e)}catch(e){console.error("Preview failed:",e),this.showError("Failed to preview dataset")}finally{this.hideLoading()}}async fetchSampleData(e){const t={columns:this.currentDataset.schema.map((e=>e.name)),rows:this.generateMockRows(this.currentDataset.schema,10)};return await new Promise((e=>setTimeout(e,1e3))),t}generateMockRows(e,t){const a=[];for(let n=0;n<t;n++){const t=e.map((e=>{switch(e.type){case"STRING":return`Sample ${e.name} ${n+1}`;case"LONG":return Math.floor(1e3*Math.random())+1;case"DOUBLE":return(1e3*Math.random()).toFixed(2);case"DATE":return new Date(2024,0,n+1).toISOString().split("T")[0];case"DATETIME":return new Date(2024,0,n+1,10,0,0).toISOString();case"BOOLEAN":return Math.random()>.5;default:return`Value ${n+1}`}}));a.push(t)}return a}showDataPreview(e){const t=document.getElementById("data-preview");if(!t||!e)return;const{columns:a,rows:n}=e;if(!a||!n||0===n.length)return t.innerHTML="<p>No preview data available</p>",void(t.style.display="block");const o=a.map((e=>`<th>${e}</th>`)).join(""),s=n.slice(0,10).map((e=>`<tr>${e.map((e=>`<td>${e||""}</td>`)).join("")}</tr>`)).join("");t.innerHTML=`\n            <h6>Sample Data (first ${Math.min(n.length,10)} rows):</h6>\n            <div class="table-container">\n                <table class="data-preview-table">\n                    <thead>\n                        <tr>${o}</tr>\n                    </thead>\n                    <tbody>\n                        ${s}\n                    </tbody>\n                </table>\n            </div>\n        `,t.style.display="block"}async loadDatasetIntoDuckDB(){const e=document.getElementById("table-name")?.value?.trim(),t=document.getElementById("refresh-mode")?.value,a=document.getElementById("row-limit")?.value;if(document.getElementById("create-index"),this.currentDataset&&e)if(this.validateTableName(e))try{this.showLoading(`Loading ${this.currentDataset.name} into DuckDB...`),this.updateProgress(10),console.log(`📊 Fetching data for dataset: ${this.currentDataset.id}`);const n=await this.fetchDatasetData(this.currentDataset.id,a);if(this.updateProgress(50),!n||0===n.length)throw new Error("No data received from dataset");console.log(`📊 Received ${n.length} rows of data`),this.updateProgress(70);const o=await this.duckdb.insertData(e,n);if(this.updateProgress(90),!o.success)throw new Error("Failed to load data into DuckDB");this.loadedDatasets.set(e,{dataset:this.currentDataset,tableName:e,rowCount:o.rowsInserted||n.length,loadedAt:new Date,refreshMode:t,schema:this.currentDataset.schema,data:n.slice(0,100)}),this.updateProgress(100),this.showSuccess(`Successfully loaded ${o.rowsInserted||n.length} rows into table "${e}"`),this.updateLoadedDatasetsList(),this.createDataVisualization(e,n.slice(0,100))}catch(e){console.error("❌ Failed to load dataset:",e),this.showError(`Failed to load dataset: ${e.message}`)}finally{this.hideLoading()}else this.showError("Please provide a valid table name");else this.showError("Please select a dataset and provide a table name")}async fetchDatasetData(e,t=null){try{if(this.isDomoEnvironment&&window.domo&&window.domo.get){console.log(`🔍 Fetching data from Domo for dataset: ${e}`);let a=`/data/v1/datasets/${e}/data`;const n=new URLSearchParams;t&&parseInt(t)>0&&n.append("limit",t),n.append("includeHeader","true"),n.toString()&&(a+=`?${n.toString()}`);const o=await window.domo.get(a);return o&&o.length>0?(console.log(`✅ Successfully fetched ${o.length} rows from Domo`),this.normalizeDataResponse(o)):(console.warn("⚠️ No data returned from Domo API"),this.generateMockData(e,t))}return console.log("🧪 Generating mock data for development"),this.generateMockData(e,t)}catch(a){return console.error("❌ Failed to fetch dataset data:",a),console.log("🧪 Falling back to mock data"),this.generateMockData(e,t)}}normalizeDataResponse(e){return Array.isArray(e)?e:e.data&&Array.isArray(e.data)?e.data:e.rows&&Array.isArray(e.rows)?e.rows:(console.warn("⚠️ Unexpected response format:",e),[])}generateMockData(e,t=100){const a=t?Math.min(parseInt(t),1e3):100,n=this.currentDataset?.name||"Unknown Dataset";return console.log(`🧪 Generating realistic mock data for: ${n} (${e})`),"a8cf6a39-11f0-4b11-b577-7e8d4bf0efbe"===e||n.includes("Excel")?this.generateExcelDatasetMockData(a):"a92b693c-44e6-4d68-b7c9-f98753ca3edc"===e||n.includes("Government")?this.generateGovernmentDatasetMockData(a):this.generateGenericMockData(a)}generateExcelDatasetMockData(e){console.log(`📊 Generating Excel dataset mock data (${e} rows)`);const t=[],a=["Office Suite","Excel Pro","PowerPoint Premium","Word Advanced","Teams Enterprise"],n=["North America","Europe","Asia Pacific","Latin America","Middle East"],o=["Alice Johnson","Bob Smith","Carol Davis","David Wilson","Eva Brown"];for(let s=0;s<e;s++){const e=new Date("2024-01-01");e.setDate(e.getDate()+Math.floor(365*Math.random()));const i=Math.floor(50*Math.random())+1,r=Math.round(100*(200*Math.random()+50))/100,d=Math.round(i*r*100)/100;t.push({order_id:`ORD-${String(s+1).padStart(6,"0")}`,product_name:a[Math.floor(Math.random()*a.length)],quantity:i,unit_price:r,total_amount:d,order_date:e.toISOString().split("T")[0],region:n[Math.floor(Math.random()*n.length)],sales_rep:o[Math.floor(Math.random()*o.length)],customer_type:Math.random()>.3?"Enterprise":"SMB",discount_percent:Math.floor(20*Math.random())})}return console.log(`✅ Generated ${t.length} Excel dataset rows`),t}generateGovernmentDatasetMockData(e){console.log(`🏛️ Generating Government dataset mock data (${e} rows)`);const t=[],a=["Health","Education","Transportation","Defense","Environment"],n=["California","Texas","Florida","New York","Illinois","Pennsylvania"],o=["Infrastructure","Social Services","Research","Emergency Response","Public Safety"];for(let s=0;s<e;s++){const e=2020+Math.floor(5*Math.random()),i=Math.round(100*(1e7*Math.random()+1e5))/100,r=Math.round(i*(.6+.35*Math.random())*100)/100;t.push({program_id:`GOV-${String(s+1).padStart(5,"0")}`,department:a[Math.floor(Math.random()*a.length)],program_name:o[Math.floor(Math.random()*o.length)],fiscal_year:e,allocated_budget:i,amount_spent:r,remaining_budget:Math.round(100*(i-r))/100,state:n[Math.floor(Math.random()*n.length)],beneficiaries:Math.floor(1e5*Math.random())+1e3,completion_rate:Math.round(r/i*100*100)/100})}return console.log(`✅ Generated ${t.length} Government dataset rows`),t}generateGenericMockData(e){console.log(`📋 Generating generic mock data (${e} rows)`);const t=[],a=this.currentDataset?.schema||[{name:"id",type:"LONG"},{name:"name",type:"STRING"},{name:"value",type:"DOUBLE"},{name:"date",type:"DATE"},{name:"category",type:"STRING"}];for(let n=0;n<e;n++){const e={};a.forEach((t=>{switch(t.type){case"LONG":e[t.name]=Math.floor(1e4*Math.random())+1;break;case"DOUBLE":e[t.name]=Math.round(1e3*Math.random()*100)/100;break;case"DATE":const a=new Date;a.setDate(a.getDate()-Math.floor(365*Math.random())),e[t.name]=a.toISOString().split("T")[0];break;case"BOOLEAN":e[t.name]=Math.random()>.5;break;default:const o=["Category A","Category B","Category C","Category D"],s=["Product Alpha","Product Beta","Product Gamma","Product Delta"];e[t.name]=t.name.includes("category")?o[Math.floor(Math.random()*o.length)]:s[Math.floor(Math.random()*s.length)]+` ${n+1}`}})),t.push(e)}return console.log(`✅ Generated ${t.length} generic dataset rows`),t}createDataVisualization(e,t){try{console.log(`📊 Creating visualization for table: ${e}`);let a=document.getElementById("visualization-section");if(!a){a=document.createElement("div"),a.id="visualization-section",a.innerHTML='\n                    <h2 class="markdown">📊 Data Visualization</h2>\n                    <div id="visualization-container" class="visualization-container"></div>\n                ';const e=document.getElementById("query-section");e?e.parentNode.insertBefore(a,e.nextSibling):document.querySelector("article").appendChild(a)}const n=document.getElementById("visualization-container");if(!n||!t||0===t.length)return;const o=this.generateCharts(e,t);n.innerHTML=o,a.style.display="block"}catch(e){console.error("❌ Failed to create visualization:",e)}}generateCharts(e,t){if(!t||0===t.length)return"<p>No data available for visualization</p>";const a=t[0],n=Object.keys(a),o=n.filter((e=>{const t=a[e];return"number"==typeof t||!isNaN(parseFloat(t))&&isFinite(t)})),s=n.filter((e=>"string"==typeof a[e]&&!o.includes(e)));let i='<div class="charts-grid">';if(i+=`\n            <div class="chart-card">\n                <h4>📋 Data Summary</h4>\n                <div class="summary-stats">\n                    <div class="stat-item">\n                        <span class="stat-label">Total Rows:</span>\n                        <span class="stat-value">${t.length.toLocaleString()}</span>\n                    </div>\n                    <div class="stat-item">\n                        <span class="stat-label">Columns:</span>\n                        <span class="stat-value">${n.length}</span>\n                    </div>\n                    <div class="stat-item">\n                        <span class="stat-label">Numeric Columns:</span>\n                        <span class="stat-value">${o.length}</span>\n                    </div>\n                    <div class="stat-item">\n                        <span class="stat-label">Text Columns:</span>\n                        <span class="stat-value">${s.length}</span>\n                    </div>\n                </div>\n            </div>\n        `,o.length>0){const e=o[0],a=t.slice(0,10).map((t=>parseFloat(t[e])||0)),n=Math.max(...a);i+=`\n                <div class="chart-card">\n                    <h4>📊 ${e} (First 10 rows)</h4>\n                    <div class="simple-bar-chart">\n                        ${a.map(((e,t)=>`\n                            <div class="bar-item">\n                                <div class="bar-label">Row ${t+1}</div>\n                                <div class="bar-container">\n                                    <div class="bar-fill" style="width: ${e/n*100}%"></div>\n                                    <span class="bar-value">${e}</span>\n                                </div>\n                            </div>\n                        `)).join("")}\n                    </div>\n                </div>\n            `}if(s.length>0){const e=s[0],a={};t.forEach((t=>{const n=t[e];a[n]=(a[n]||0)+1}));const n=Object.entries(a).sort((([,e],[,t])=>t-e)).slice(0,8),o=Math.max(...n.map((([,e])=>e)));i+=`\n                <div class="chart-card">\n                    <h4>📈 ${e} Distribution</h4>\n                    <div class="simple-bar-chart">\n                        ${n.map((([e,t])=>`\n                            <div class="bar-item">\n                                <div class="bar-label">${e}</div>\n                                <div class="bar-container">\n                                    <div class="bar-fill" style="width: ${t/o*100}%"></div>\n                                    <span class="bar-value">${t}</span>\n                                </div>\n                            </div>\n                        `)).join("")}\n                    </div>\n                </div>\n            `}return i+="</div>",i}updateLoadedDatasetsList(){const e=document.getElementById("loaded-datasets-list"),t=document.getElementById("loaded-datasets-section");if(!e||!t)return;if(0===this.loadedDatasets.size)return void(t.style.display="none");const a=Array.from(this.loadedDatasets.entries()).map((([e,t])=>`\n            <div class="loaded-dataset-card">\n                <h4>${e}</h4>\n                <p class="dataset-source">Source: ${t.dataset.name}</p>\n                <div class="dataset-stats">\n                    <span>Rows: ${t.rowCount.toLocaleString()}</span>\n                    <span>Loaded: ${t.loadedAt.toLocaleString()}</span>\n                </div>\n                <div class="dataset-actions">\n                    <button class="btn btn-small btn-secondary" onclick="window.enhancedDomoDuckDB.dropTable('${e}')">\n                        🗑️ Remove\n                    </button>\n                </div>\n            </div>\n        `)).join("");e.innerHTML=a,t.style.display="block"}async dropTable(e){confirm(`Are you sure you want to remove table "${e}"?`)&&(this.loadedDatasets.delete(e),this.updateLoadedDatasetsList(),this.showSuccess(`Table "${e}" removed successfully`))}showLoading(e="Loading..."){const t=document.getElementById("loading-status");t&&(this.updateLoadingMessage(e),t.style.display="block")}hideLoading(){const e=document.getElementById("loading-status");e&&(e.style.display="none")}updateLoadingMessage(e){const t=document.getElementById("loading-message");t&&(t.textContent=e)}updateProgress(e){const t=document.getElementById("progress-fill");t&&(t.style.width=`${e}%`)}showError(e){alert(`Error: ${e}`)}showWarning(e){console.warn(`⚠️ Warning: ${e}`)}showSuccess(e){alert(`Success: ${e}`)}}),"undefined"!=typeof window){if(window.isInIframe=window.self!==window.top,window.isDomoEnvironment=void 0!==window.domo||window.isInIframe,window.isInIframe){console.log("🖼️ Running in iframe mode - applying compatibility fixes");const e=console.log;console.log=function(...t){try{e.apply(console,t)}catch(e){}},document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll('img[src^="_app/"]').forEach((e=>{e.src.includes("_app/")&&(e.src=e.src.replace("_app/_app/","_app/"))})),document.querySelectorAll('img[src*="wordmark"]').forEach((e=>{e.style.display="none",console.log("🖼️ Hidden Evidence branding image to prevent 404:",e.src)}))}))}document.addEventListener("DOMContentLoaded",(function(){console.log("🚀 Evidence DDX App initialized"),console.log("Environment:",{isIframe:window.isInIframe,isDomo:window.isDomoEnvironment,hasDomoAPI:void 0!==window.domo})}))}})();
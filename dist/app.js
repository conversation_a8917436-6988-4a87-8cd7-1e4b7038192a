(()=>{"use strict";if(window.domoDuckDBIntegration=new class{constructor(){this.duckdb=null,this.connection=null,this.availableDatasets=[],this.isInitialized=!1,this.isDomoEnvironment="undefined"!=typeof window&&void 0!==window.domo,"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>this.init())):this.init()}async init(){try{this.isDomoEnvironment&&await this.waitForDomo(),await this.initializeDuckDB(),await this.loadAvailableDatasets(),this.setupEventListeners(),this.isInitialized=!0,console.log("Domo-DuckDB integration initialized successfully")}catch(e){console.error("Failed to initialize Domo-DuckDB integration:",e),this.showError("Failed to initialize data integration. Please refresh the page.")}}async waitForDomo(){return new Promise(((e,t)=>{if(window.domo&&window.domo.get)return void e();let a=0;const n=()=>{a++,window.domo&&window.domo.get?e():a>=50?t(new Error("Domo DDX environment not available")):setTimeout(n,100)};n()}))}async initializeDuckDB(){try{this.isDomoEnvironment?console.log("Running in Domo DDX environment"):console.log("Running in development environment"),this.duckdb={query:async e=>(console.log("Executing SQL:",e),{rows:[],columns:[]})},console.log("DuckDB integration ready")}catch(e){throw new Error(`DuckDB initialization failed: ${e.message}`)}}async loadAvailableDatasets(){try{if(this.isDomoEnvironment&&window.domo&&window.domo.get){console.log("Loading datasets from Domo DDX...");try{const e=await window.domo.get("/data/v1/datasets");this.availableDatasets=e.map((e=>this.normalizeDomoDataset(e)))}catch(e){console.warn("Standard dataset API failed, trying alternative approach:",e),this.availableDatasets=await this.getDatasetsFallback()}}else this.showError("This application requires a Domo DDX environment to access datasets."),this.availableDatasets=[];this.populateDatasetDropdown(),console.log(`Loaded ${this.availableDatasets.length} datasets`)}catch(e){console.error("Failed to load available datasets:",e),this.showError("Failed to load available datasets. Please check your Domo connection."),this.availableDatasets=[],this.populateDatasetDropdown()}}normalizeDomoDataset(e){return{id:e.id,name:e.name||e.displayName||"Unnamed Dataset",description:e.description||"No description available",schema:this.normalizeSchema(e.schema||e.columns||[]),rowCount:e.rowCount||e.rows||0,lastUpdated:e.lastUpdated||e.updatedAt||(new Date).toISOString()}}normalizeSchema(e){return Array.isArray(e)?e.map((e=>({name:e.name||e.columnName||e.field,type:this.normalizeColumnType(e.type||e.columnType||e.dataType),description:e.description||""}))):[]}normalizeColumnType(e){return{STRING:"STRING",TEXT:"STRING",LONG:"LONG",INTEGER:"LONG",DOUBLE:"DOUBLE",FLOAT:"DOUBLE",DECIMAL:"DECIMAL",DATE:"DATE",DATETIME:"DATETIME",TIMESTAMP:"DATETIME",BOOLEAN:"BOOLEAN"}[e?.toString().toUpperCase()]||"STRING"}async getDatasetsFallback(){console.log("Attempting fallback dataset loading...");try{const e=["/data/v2/datasets","/domo/datasets","/api/data/v1/datasets"];for(const t of e)try{console.log(`Trying endpoint: ${t}`);const e=await window.domo.get(t);if(e&&e.length>0)return e.map((e=>this.normalizeDomoDataset(e)))}catch(e){console.log(`Endpoint ${t} failed:`,e.message)}throw new Error("All fallback endpoints failed")}catch(e){return console.error("All dataset loading methods failed:",e),[]}}populateDatasetDropdown(){const e=document.getElementById("dataset-selector");e&&(e.innerHTML='<option value="">Select a dataset...</option>',this.availableDatasets.forEach((t=>{const a=document.createElement("option");a.value=t.id,a.textContent=`${t.name} (${t.rowCount} rows)`,a.dataset.description=t.description,e.appendChild(a)})))}setupEventListeners(){const e=document.getElementById("dataset-selector"),t=document.getElementById("preview-btn"),a=document.getElementById("load-dataset-btn"),n=document.getElementById("table-name");e&&e.addEventListener("change",(e=>{const s=e.target.value;if(s){this.onDatasetSelected(s),t&&(t.disabled=!1),a&&(a.disabled=!1);const e=this.availableDatasets.find((e=>e.id===s));e&&n&&(n.value=e.name.toLowerCase().replace(/\s+/g,"_"))}else t&&(t.disabled=!0),a&&(a.disabled=!0),this.hideDatasetPreview(),this.hideWorkflowSteps()})),t&&t.addEventListener("click",(()=>this.previewDataset())),a&&a.addEventListener("click",(()=>this.loadDatasetIntoDuckDB()))}onDatasetSelected(e){const t=this.availableDatasets.find((t=>t.id===e));t&&(this.showDatasetPreview(t),this.showWorkflowSteps())}showWorkflowSteps(){const e=document.getElementById("loading-config");e&&(e.style.display="block");const t=document.getElementById("workflow-actions");t&&(t.style.display="block")}hideWorkflowSteps(){const e=document.getElementById("loading-config");e&&(e.style.display="none");const t=document.getElementById("workflow-actions");t&&(t.style.display="none")}showDatasetPreview(e){const t=document.getElementById("dataset-preview"),a=document.getElementById("preview-content");if(!t||!a)return;const n=e.schema.map((e=>`<tr><td>${e.name}</td><td>${e.type}</td></tr>`)).join("");a.innerHTML=`\n            <div class="dataset-info">\n                <h5>${e.name}</h5>\n                <p><strong>Description:</strong> ${e.description}</p>\n                <p><strong>Rows:</strong> ${e.rowCount.toLocaleString()}</p>\n                <p><strong>Last Updated:</strong> ${e.lastUpdated}</p>\n                \n                <h6>Schema:</h6>\n                <table class="schema-table">\n                    <thead>\n                        <tr><th>Column</th><th>Type</th></tr>\n                    </thead>\n                    <tbody>\n                        ${n}\n                    </tbody>\n                </table>\n            </div>\n        `,t.style.display="block"}hideDatasetPreview(){const e=document.getElementById("dataset-preview");e&&(e.style.display="none")}async previewDataset(){const e=document.getElementById("dataset-selector").value;if(e)try{this.showLoading("Loading preview...");const t=await this.fetchSampleData(e);this.showDataPreview(t)}catch(e){console.error("Preview failed:",e),this.showError("Failed to preview dataset")}finally{this.hideLoading()}}async loadDatasetIntoDuckDB(){const e=document.getElementById("dataset-selector").value,t=document.getElementById("table-name").value,a=document.getElementById("refresh-mode").value;if(e&&t)try{this.showLoading("Loading dataset into DuckDB...");const n=await this.fetchDatasetData(e);await this.createDuckDBTable(t,n,a),this.showSuccess(`Dataset loaded successfully into table: ${t}`),setTimeout((()=>{window.location.reload()}),2e3)}catch(e){console.error("Load failed:",e),this.showError(`Failed to load dataset: ${e.message}`)}finally{this.hideLoading()}else this.showError("Please select a dataset and provide a table name")}async fetchSampleData(e){if(!(this.isDomoEnvironment&&window.domo&&window.domo.get))throw new Error("Domo environment not available");try{const t=await window.domo.get(`/data/v1/datasets/${e}/data?limit=5`);return this.normalizeDataResponse(t)}catch(e){throw console.error("Failed to fetch sample data from Domo:",e),new Error(`Failed to fetch sample data: ${e.message}`)}}async fetchDatasetData(e){if(!(this.isDomoEnvironment&&window.domo&&window.domo.get))throw new Error("Domo environment not available");try{console.log(`Fetching full dataset: ${e}`);const t=await window.domo.get(`/data/v1/datasets/${e}/data`);return this.normalizeDataResponse(t)}catch(e){throw console.error("Failed to fetch dataset data from Domo:",e),new Error(`Failed to fetch dataset data: ${e.message}`)}}normalizeDataResponse(e){return Array.isArray(e)?{rows:e,columns:[],totalRows:e.length}:{rows:e.rows||e.data||[],columns:e.columns||e.headers||[],totalRows:e.totalRows||e.rows?.length||0}}async createDuckDBTable(e,t,a){return console.log(`Creating table ${e} with mode ${a}`),console.log("Dataset:",t),Promise.resolve()}showLoading(e){const t=document.getElementById("loading-status");t&&(t.querySelector("p").textContent=e,t.style.display="block")}hideLoading(){const e=document.getElementById("loading-status");e&&(e.style.display="none")}showError(e){alert(`Error: ${e}`)}showSuccess(e){alert(`Success: ${e}`)}showDataPreview(e){const t=document.getElementById("data-preview");if(!t||!e)return;const{columns:a,rows:n}=e;if(!a||!n||0===n.length)return t.innerHTML="<p>No preview data available</p>",void(t.style.display="block");const s=a.map((e=>`<th>${e}</th>`)).join(""),o=n.slice(0,5).map((e=>`<tr>${e.map((e=>`<td>${e||""}</td>`)).join("")}</tr>`)).join("");t.innerHTML=`\n            <h6>Sample Data (first 5 rows):</h6>\n            <table class="data-preview-table">\n                <thead>\n                    <tr>${s}</tr>\n                </thead>\n                <tbody>\n                    ${o}\n                </tbody>\n            </table>\n        `,t.style.display="block"}},"undefined"!=typeof window&&"serviceWorker"in navigator){const e=new Blob(["/** This ServiceWorker adds Cache-Control=no-cache to requests loading a .parquet file on a Windows machine to prevent TProtocolException within duckdb-wasm */\n\n// @ts-check\n/// <reference types=\"@sveltejs/kit\" />\n/// <reference no-default-lib=\"true\"/>\n/// <reference lib=\"webworker\" />\n\nconst sw = /** @type {ServiceWorkerGlobalScope} */ (/** @type {unknown} */ (self));\n\n// The following line is replaced when disabling the service worker using VITE_EVIDENCE_DISABLE_WINDOWS_CACHE_SERVICE_WORKER\nconst disabled = false;\n\nsw.addEventListener('activate', () => {\n\tif (disabled) {\n\t\tconsole.debug(\n\t\t\t'Detected VITE_EVIDENCE_DISABLE_WINDOWS_CACHE_SERVICE_WORKER. Service Worker disabled.'\n\t\t);\n\t}\n});\n\nsw.addEventListener('fetch', (event) => {\n\tif (disabled) return;\n\tif (!event.request.url.endsWith('.parquet')) return;\n\n\tconst userAgent = event.request.headers.get('User-Agent');\n\tconst isWindows = userAgent?.includes('Windows');\n\tif (!isWindows) return;\n\n\tconst headers = new Headers(event.request.headers);\n\theaders.set('Cache-Control', 'no-cache');\n\theaders.set('X-Evidence-Windows-Cache-Disable', 'true');\n\n\tconst newRequest = new Request(event.request.url, { headers });\n\n\tevent.respondWith(fetch(newRequest));\n});\n"],{type:"application/javascript"}),t=URL.createObjectURL(e);navigator.serviceWorker.register(t).then((e=>{console.log("Service Worker registered successfully:",e)})).catch((e=>{console.log("Service Worker registration failed:",e)}))}console.log("Evidence app loaded for Domo DDX");"undefined"!=typeof window&&(window.enhancedDomoDuckDB=new class{constructor(){this.duckdb=null,this.connection=null,this.availableDatasets=[],this.filteredDatasets=[],this.loadedDatasets=new Map,this.isInitialized=!1,this.isDomoEnvironment="undefined"!=typeof window&&void 0!==window.domo,this.currentDataset=null,this.activeTab="schema","loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>this.init())):this.init()}async init(){try{console.log("🚀 Initializing Enhanced Domo-DuckDB Integration..."),this.isDomoEnvironment&&await this.waitForDomo(),await this.initializeDuckDB(),await this.loadAvailableDatasets(),this.setupEventListeners(),this.setupTabSwitching(),this.isInitialized=!0,console.log("✅ Enhanced Domo-DuckDB integration initialized successfully")}catch(e){console.error("❌ Failed to initialize Enhanced Domo-DuckDB integration:",e),this.showError("Failed to initialize data integration. Please refresh the page.")}}async waitForDomo(){return new Promise(((e,t)=>{if(window.domo&&window.domo.get)return e();let a=0;const n=()=>{a++,window.domo&&window.domo.get?e():a>=50?t(new Error("Domo DDX environment not available")):setTimeout(n,100)};n()}))}async initializeDuckDB(){try{this.isDomoEnvironment?(console.log("🔗 Running in Domo DDX environment"),this.duckdb={query:async e=>(console.log("🔍 Executing SQL:",e),{rows:[],columns:[]})}):(console.log("🛠️ Running in development environment"),this.duckdb={query:async e=>(console.log("🔍 Mock SQL execution:",e),{rows:[],columns:[]})}),console.log("✅ DuckDB integration ready")}catch(e){throw new Error(`DuckDB initialization failed: ${e.message}`)}}async loadAvailableDatasets(){try{console.log("📊 Loading available datasets...");const e=this.getConfiguredDatasets();if(this.isDomoEnvironment&&window.domo&&window.domo.get)try{const t=(await this.fetchDatasetsFromDomo()).map((e=>this.normalizeDomoDataset(e))),a=[...e];t.forEach((e=>{a.find((t=>t.id===e.id))||a.push(e)})),this.availableDatasets=a}catch(t){console.warn("⚠️ Failed to load additional datasets from Domo API:",t),console.log("📋 Using configured datasets only"),this.availableDatasets=e}else console.log("🧪 Using configured and mock datasets for development"),this.availableDatasets=[...e,...this.getMockDatasets()];this.filteredDatasets=[...this.availableDatasets],this.populateDatasetDropdown(),this.updateDatasetStats(),console.log(`✅ Loaded ${this.availableDatasets.length} datasets (${e.length} configured)`)}catch(e){console.error("❌ Failed to load available datasets:",e),this.showError("Failed to load available datasets. Please check your Domo connection."),this.availableDatasets=[],this.filteredDatasets=[],this.populateDatasetDropdown()}}getConfiguredDatasets(){return[{id:"a8cf6a39-11f0-4b11-b577-7e8d4bf0efbe",name:"Excel Dataset",displayName:"Excel Dataset (Configured)",description:"Pre-configured Excel dataset from Domo manifest mapping",alias:"excel",rowCount:"Unknown",lastUpdated:"Unknown",schema:[{name:"Loading...",type:"STRING",description:"Schema will be loaded when selected"}],tags:["configured","excel","manifest"],owner:"Domo Configuration",isConfigured:!0},{id:"a92b693c-44e6-4d68-b7c9-f98753ca3edc",name:"Government Dataset",displayName:"Government Dataset (Configured)",description:"Pre-configured government dataset from Domo manifest mapping",alias:"gov",rowCount:"Unknown",lastUpdated:"Unknown",schema:[{name:"Loading...",type:"STRING",description:"Schema will be loaded when selected"}],tags:["configured","government","manifest"],owner:"Domo Configuration",isConfigured:!0}]}async fetchDatasetsFromDomo(){const e=["/data/v1/datasets","/data/v2/datasets","/domo/datasets","/api/data/v1/datasets"];for(const t of e)try{console.log(`🔍 Trying endpoint: ${t}`);const e=await window.domo.get(t);if(e&&e.length>0)return console.log(`✅ Successfully loaded from ${t}`),e}catch(e){console.log(`❌ Endpoint ${t} failed:`,e.message)}throw new Error("All dataset endpoints failed")}getMockDatasets(){return[{id:"sales-data-2024",name:"Sales Data 2024",displayName:"Sales Data 2024",description:"Comprehensive sales data for 2024 including revenue, products, and customer information",rowCount:125e3,lastUpdated:"2024-01-15T10:30:00Z",schema:[{name:"order_id",type:"STRING",description:"Unique order identifier"},{name:"customer_id",type:"STRING",description:"Customer identifier"},{name:"product_name",type:"STRING",description:"Product name"},{name:"quantity",type:"LONG",description:"Quantity ordered"},{name:"unit_price",type:"DOUBLE",description:"Price per unit"},{name:"total_amount",type:"DOUBLE",description:"Total order amount"},{name:"order_date",type:"DATE",description:"Date of order"},{name:"region",type:"STRING",description:"Sales region"}],tags:["sales","revenue","customers"],owner:"Sales Team"},{id:"customer-demographics",name:"Customer Demographics",displayName:"Customer Demographics",description:"Customer demographic and behavioral data for segmentation analysis",rowCount:45e3,lastUpdated:"2024-01-10T14:20:00Z",schema:[{name:"customer_id",type:"STRING",description:"Customer identifier"},{name:"age",type:"LONG",description:"Customer age"},{name:"gender",type:"STRING",description:"Customer gender"},{name:"income_bracket",type:"STRING",description:"Income range"},{name:"location",type:"STRING",description:"Customer location"},{name:"signup_date",type:"DATE",description:"Account creation date"},{name:"lifetime_value",type:"DOUBLE",description:"Customer lifetime value"}],tags:["customers","demographics","segmentation"],owner:"Marketing Team"},{id:"product-inventory",name:"Product Inventory",displayName:"Product Inventory",description:"Real-time product inventory levels and warehouse data",rowCount:8500,lastUpdated:"2024-01-16T09:15:00Z",schema:[{name:"product_id",type:"STRING",description:"Product identifier"},{name:"product_name",type:"STRING",description:"Product name"},{name:"category",type:"STRING",description:"Product category"},{name:"current_stock",type:"LONG",description:"Current inventory level"},{name:"reorder_point",type:"LONG",description:"Reorder threshold"},{name:"unit_cost",type:"DOUBLE",description:"Cost per unit"},{name:"warehouse_location",type:"STRING",description:"Storage location"}],tags:["inventory","products","warehouse"],owner:"Operations Team"}]}normalizeDomoDataset(e){return{id:e.id,name:e.name||e.displayName||"Unnamed Dataset",description:e.description||"No description available",schema:this.normalizeSchema(e.schema||e.columns||[]),rowCount:e.rowCount||e.rows||0,lastUpdated:e.lastUpdated||e.updatedAt||(new Date).toISOString(),tags:e.tags||[],owner:e.owner||"Unknown"}}normalizeSchema(e){return Array.isArray(e)?e.map((e=>({name:e.name||e.columnName||e.field,type:this.normalizeColumnType(e.type||e.columnType||e.dataType),description:e.description||""}))):[]}normalizeColumnType(e){return{STRING:"STRING",TEXT:"STRING",LONG:"LONG",INTEGER:"LONG",DOUBLE:"DOUBLE",FLOAT:"DOUBLE",DECIMAL:"DECIMAL",DATE:"DATE",DATETIME:"DATETIME",TIMESTAMP:"DATETIME",BOOLEAN:"BOOLEAN"}[e?.toString().toUpperCase()]||"STRING"}setupEventListeners(){const e=document.getElementById("dataset-search");e&&e.addEventListener("input",(e=>this.filterDatasets(e.target.value)));const t=document.getElementById("dataset-selector");t&&t.addEventListener("change",(e=>this.onDatasetSelected(e.target.value)));const a=document.getElementById("preview-btn");a&&a.addEventListener("click",(()=>this.previewDataset()));const n=document.getElementById("validate-config-btn");n&&n.addEventListener("click",(()=>this.validateConfiguration()));const s=document.getElementById("load-dataset-btn");s&&s.addEventListener("click",(()=>this.loadDatasetIntoDuckDB()));const o=document.getElementById("run-query-btn");o&&o.addEventListener("click",(()=>this.runQuery()));const i=document.getElementById("clear-query-btn");i&&i.addEventListener("click",(()=>this.clearQuery()));const d=document.getElementById("table-name");d&&d.addEventListener("input",(e=>this.validateTableName(e.target.value)))}setupTabSwitching(){document.querySelectorAll(".tab-button").forEach((e=>{e.addEventListener("click",(e=>{const t=e.target.dataset.tab;this.switchTab(t)}))}))}switchTab(e){document.querySelectorAll(".tab-button").forEach((e=>e.classList.remove("active"))),document.querySelector(`[data-tab="${e}"]`).classList.add("active"),document.querySelectorAll(".tab-content").forEach((e=>e.classList.remove("active"))),document.getElementById(`${e}-tab`).classList.add("active"),this.activeTab=e,"metadata"===e&&this.currentDataset&&this.showDatasetMetadata(this.currentDataset)}filterDatasets(e){const t=e.toLowerCase().trim();this.filteredDatasets=t?this.availableDatasets.filter((e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.tags&&e.tags.some((e=>e.toLowerCase().includes(t)))||e.owner&&e.owner.toLowerCase().includes(t))):[...this.availableDatasets],this.populateDatasetDropdown(),this.updateDatasetStats()}populateDatasetDropdown(){const e=document.getElementById("dataset-selector");e&&(e.innerHTML='<option value="">Choose a dataset...</option>',[...this.filteredDatasets].sort(((e,t)=>e.isConfigured&&!t.isConfigured?-1:!e.isConfigured&&t.isConfigured?1:e.name.localeCompare(t.name))).forEach((t=>{const a=document.createElement("option");a.value=t.id;const n="number"==typeof t.rowCount?`${t.rowCount.toLocaleString()} rows`:t.rowCount,s=t.isConfigured?"📋 ":"",o=t.alias?` [${t.alias}]`:"";a.textContent=`${s}${t.name}${o} (${n})`,a.dataset.description=t.description,a.dataset.configured=t.isConfigured?"true":"false",t.isConfigured&&(a.style.fontWeight="600",a.style.background="#f0fdf4"),e.appendChild(a)})))}updateDatasetStats(){const e=document.getElementById("dataset-stats");if(!e)return;const t=this.availableDatasets.length,a=this.filteredDatasets.length;e.innerHTML=t===a?`<span id="dataset-count">${t} datasets available</span>`:`<span id="dataset-count">${a} of ${t} datasets shown</span>`}async onDatasetSelected(e){if(!e)return this.currentDataset=null,this.hideDatasetPreview(),void this.hideWorkflowSteps();this.currentDataset=this.availableDatasets.find((t=>t.id===e)),this.currentDataset&&(this.currentDataset.isConfigured&&await this.loadConfiguredDatasetMetadata(this.currentDataset),this.showDatasetPreview(this.currentDataset),this.showWorkflowSteps(),this.autoGenerateTableName(this.currentDataset))}async loadConfiguredDatasetMetadata(e){if(this.isDomoEnvironment&&window.domo)try{console.log(`🔍 Loading metadata for configured dataset: ${e.id}`);const t=await window.domo.get(`/data/v1/datasets/${e.id}`);t&&(e.name=t.name||e.name,e.description=t.description||e.description,e.rowCount=t.rowCount||t.rows||"Unknown",e.lastUpdated=t.lastUpdated||t.updatedAt||"Unknown",e.schema=this.normalizeSchema(t.schema||t.columns||[]),e.owner=t.owner||e.owner,console.log(`✅ Loaded metadata for ${e.name}: ${e.rowCount} rows, ${e.schema.length} columns`))}catch(t){console.warn(`⚠️ Could not load metadata for configured dataset ${e.id}:`,t)}else console.log("🧪 Mock mode: Using placeholder metadata for configured dataset")}autoGenerateTableName(e){const t=document.getElementById("table-name");if(!t||t.value.trim())return;const a=e.name.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"_").substring(0,50);t.value=a,this.validateTableName(a)}validateTableName(e){const t=document.getElementById("table-name");if(!t)return;const a=/^[a-z][a-z0-9_]*$/.test(e)&&e.length<=50;return a?(t.classList.remove("invalid"),t.classList.add("valid")):(t.classList.remove("valid"),t.classList.add("invalid")),a}showDatasetPreview(e){const t=document.getElementById("dataset-preview");t&&(this.showDatasetInfo(e),this.showDatasetSchema(e),t.style.display="block")}showDatasetInfo(e){const t=document.getElementById("dataset-info");if(!t)return;const a="Unknown"!==e.lastUpdated?new Date(e.lastUpdated).toLocaleDateString():e.lastUpdated,n="number"==typeof e.rowCount?e.rowCount.toLocaleString():e.rowCount,s=e.tags?e.tags.map((e=>`<span class="tag">${e}</span>`)).join(""):"",o=e.isConfigured?'<span class="configured-badge">📋 Configured in Manifest</span>':"";t.innerHTML=`\n            <div class="info-card ${e.isConfigured?"configured-dataset":""}">\n                <h5>${e.name} ${o}</h5>\n                <p class="description">${e.description}</p>\n                ${e.alias?`<p class="dataset-alias"><strong>Alias:</strong> <code>${e.alias}</code></p>`:""}\n                <div class="info-stats">\n                    <div class="stat">\n                        <span class="label">Dataset ID:</span>\n                        <span class="value"><code>${e.id}</code></span>\n                    </div>\n                    <div class="stat">\n                        <span class="label">Rows:</span>\n                        <span class="value">${n}</span>\n                    </div>\n                    <div class="stat">\n                        <span class="label">Columns:</span>\n                        <span class="value">${e.schema.length}</span>\n                    </div>\n                    <div class="stat">\n                        <span class="label">Last Updated:</span>\n                        <span class="value">${a}</span>\n                    </div>\n                    <div class="stat">\n                        <span class="label">Owner:</span>\n                        <span class="value">${e.owner}</span>\n                    </div>\n                </div>\n                ${s?`<div class="tags">${s}</div>`:""}\n            </div>\n        `}showDatasetSchema(e){const t=document.getElementById("schema-table");if(!t)return;const a=e.schema.map((e=>`\n            <tr>\n                <td class="column-name">${e.name}</td>\n                <td class="column-type">\n                    <span class="type-badge type-${e.type.toLowerCase()}">${e.type}</span>\n                </td>\n                <td class="column-description">${e.description||"-"}</td>\n            </tr>\n        `)).join("");t.innerHTML=`\n            <table class="schema-table">\n                <thead>\n                    <tr>\n                        <th>Column Name</th>\n                        <th>Data Type</th>\n                        <th>Description</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    ${a}\n                </tbody>\n            </table>\n        `}showDatasetMetadata(e){const t=document.getElementById("dataset-metadata");if(!t)return;const a=this.estimateDatasetSize(e);t.innerHTML=`\n            <div class="metadata-grid">\n                <div class="metadata-item">\n                    <h6>Dataset ID</h6>\n                    <p><code>${e.id}</code></p>\n                </div>\n                <div class="metadata-item">\n                    <h6>Estimated Size</h6>\n                    <p>${a}</p>\n                </div>\n                <div class="metadata-item">\n                    <h6>Data Quality</h6>\n                    <p>Schema: ${e.schema.length} columns defined</p>\n                </div>\n                <div class="metadata-item">\n                    <h6>Access Information</h6>\n                    <p>Owner: ${e.owner}<br>\n                    Last Updated: ${new Date(e.lastUpdated).toLocaleString()}</p>\n                </div>\n            </div>\n        `}estimateDatasetSize(e){const t=e.schema.reduce(((e,t)=>e+({STRING:50,LONG:8,DOUBLE:8,DECIMAL:16,DATE:8,DATETIME:8,BOOLEAN:1}[t.type]||50)),0),a=e.rowCount*t;return a<1024?`${a} bytes`:a<1048576?`${(a/1024).toFixed(1)} KB`:a<1073741824?`${(a/1048576).toFixed(1)} MB`:`${(a/1073741824).toFixed(1)} GB`}hideDatasetPreview(){const e=document.getElementById("dataset-preview");e&&(e.style.display="none")}showWorkflowSteps(){const e=document.getElementById("loading-config");e&&(e.style.display="block");const t=document.getElementById("workflow-actions");t&&(t.style.display="block")}hideWorkflowSteps(){const e=document.getElementById("loading-config");e&&(e.style.display="none");const t=document.getElementById("workflow-actions");t&&(t.style.display="none")}async validateConfiguration(){if(!document.getElementById("validation-results"))return;const e=document.getElementById("table-name")?.value?.trim(),t=this.currentDataset,a=[];if(t?a.push({type:"success",message:`Dataset "${t.name}" selected`}):a.push({type:"error",message:"No dataset selected"}),e?this.validateTableName(e)?a.push({type:"success",message:`Table name "${e}" is valid`}):a.push({type:"error",message:"Invalid table name format"}):a.push({type:"error",message:"Table name is required"}),e&&this.loadedDatasets.has(e)){const t=document.getElementById("refresh-mode")?.value;"replace"===t?a.push({type:"warning",message:`Table "${e}" will be replaced`}):a.push({type:"info",message:`Data will be appended to existing table "${e}"`})}this.showValidationResults(a)}showValidationResults(e){const t=document.getElementById("validation-results");if(!t)return;const a=e.map((e=>`\n            <div class="validation-item validation-${e.type}">\n                <span class="validation-icon">${this.getValidationIcon(e.type)}</span>\n                <span class="validation-message">${e.message}</span>\n            </div>\n        `)).join("");t.innerHTML=a,t.style.display="block"}getValidationIcon(e){return{success:"✅",error:"❌",warning:"⚠️",info:"ℹ️"}[e]||"ℹ️"}async previewDataset(){if(this.currentDataset)try{this.showLoading("Loading sample data...");const e=await this.fetchSampleData(this.currentDataset.id);this.showDataPreview(e)}catch(e){console.error("Preview failed:",e),this.showError("Failed to preview dataset")}finally{this.hideLoading()}}async fetchSampleData(e){const t={columns:this.currentDataset.schema.map((e=>e.name)),rows:this.generateMockRows(this.currentDataset.schema,10)};return await new Promise((e=>setTimeout(e,1e3))),t}generateMockRows(e,t){const a=[];for(let n=0;n<t;n++){const t=e.map((e=>{switch(e.type){case"STRING":return`Sample ${e.name} ${n+1}`;case"LONG":return Math.floor(1e3*Math.random())+1;case"DOUBLE":return(1e3*Math.random()).toFixed(2);case"DATE":return new Date(2024,0,n+1).toISOString().split("T")[0];case"DATETIME":return new Date(2024,0,n+1,10,0,0).toISOString();case"BOOLEAN":return Math.random()>.5;default:return`Value ${n+1}`}}));a.push(t)}return a}showDataPreview(e){const t=document.getElementById("data-preview");if(!t||!e)return;const{columns:a,rows:n}=e;if(!a||!n||0===n.length)return t.innerHTML="<p>No preview data available</p>",void(t.style.display="block");const s=a.map((e=>`<th>${e}</th>`)).join(""),o=n.slice(0,10).map((e=>`<tr>${e.map((e=>`<td>${e||""}</td>`)).join("")}</tr>`)).join("");t.innerHTML=`\n            <h6>Sample Data (first ${Math.min(n.length,10)} rows):</h6>\n            <div class="table-container">\n                <table class="data-preview-table">\n                    <thead>\n                        <tr>${s}</tr>\n                    </thead>\n                    <tbody>\n                        ${o}\n                    </tbody>\n                </table>\n            </div>\n        `,t.style.display="block"}async loadDatasetIntoDuckDB(){const e=document.getElementById("table-name")?.value?.trim(),t=document.getElementById("refresh-mode")?.value,a=document.getElementById("row-limit")?.value,n=document.getElementById("create-index")?.checked;if(this.currentDataset&&e)if(this.validateTableName(e))try{this.showLoading("Loading dataset into DuckDB..."),this.updateProgress(0),await this.simulateDatasetLoading(e,t,a,n),this.loadedDatasets.set(e,{dataset:this.currentDataset,tableName:e,loadedAt:new Date,rowCount:a?Math.min(parseInt(a),this.currentDataset.rowCount):this.currentDataset.rowCount}),this.showSuccess(`Dataset loaded successfully into table: ${e}`),this.updateLoadedDatasetsList(),this.showQuerySection(),this.setDefaultQuery(e)}catch(e){console.error("Load failed:",e),this.showError(`Failed to load dataset: ${e.message}`)}finally{this.hideLoading()}else this.showError("Please provide a valid table name");else this.showError("Please select a dataset and provide a table name")}async simulateDatasetLoading(e,t,a,n){const s=["Connecting to Domo API...","Fetching dataset metadata...","Downloading data...","Creating DuckDB table...","Inserting data...",n?"Creating indexes...":null,"Finalizing..."].filter(Boolean);for(let e=0;e<s.length;e++)this.updateLoadingMessage(s[e]),this.updateProgress((e+1)/s.length*100),await new Promise((e=>setTimeout(e,800)))}setDefaultQuery(e){const t=document.getElementById("sql-query");t&&(t.value=`SELECT * FROM ${e} LIMIT 10;`)}showQuerySection(){const e=document.getElementById("query-section");e&&(e.style.display="block")}updateLoadedDatasetsList(){const e=document.getElementById("loaded-datasets-list"),t=document.getElementById("loaded-datasets-section");if(!e||!t)return;if(0===this.loadedDatasets.size)return void(t.style.display="none");const a=Array.from(this.loadedDatasets.entries()).map((([e,t])=>`\n            <div class="loaded-dataset-card">\n                <h4>${e}</h4>\n                <p class="dataset-source">Source: ${t.dataset.name}</p>\n                <div class="dataset-stats">\n                    <span>Rows: ${t.rowCount.toLocaleString()}</span>\n                    <span>Loaded: ${t.loadedAt.toLocaleString()}</span>\n                </div>\n                <div class="dataset-actions">\n                    <button class="btn btn-small btn-secondary" onclick="window.enhancedDomoDuckDB.queryTable('${e}')">\n                        🔍 Query\n                    </button>\n                    <button class="btn btn-small btn-secondary" onclick="window.enhancedDomoDuckDB.dropTable('${e}')">\n                        🗑️ Remove\n                    </button>\n                </div>\n            </div>\n        `)).join("");e.innerHTML=a,t.style.display="block"}queryTable(e){const t=document.getElementById("sql-query");t&&(t.value=`SELECT * FROM ${e} LIMIT 10;`,t.focus()),document.getElementById("query-section")?.scrollIntoView({behavior:"smooth"})}async dropTable(e){confirm(`Are you sure you want to remove table "${e}"?`)&&(this.loadedDatasets.delete(e),this.updateLoadedDatasetsList(),this.showSuccess(`Table "${e}" removed successfully`))}async runQuery(){const e=document.getElementById("sql-query"),t=document.getElementById("query-results");if(e&&t)if(e.value.trim())try{this.showLoading("Executing query..."),await new Promise((e=>setTimeout(e,1e3)));const e={columns:["id","name","value","date"],rows:[[1,"Sample A",123.45,"2024-01-15"],[2,"Sample B",678.9,"2024-01-16"],[3,"Sample C",234.56,"2024-01-17"]],executionTime:"0.045s",rowCount:3};this.showQueryResults(e)}catch(e){console.error("Query failed:",e),this.showError(`Query failed: ${e.message}`)}finally{this.hideLoading()}else this.showError("Please enter a SQL query")}showQueryResults(e){const t=document.getElementById("query-results");if(!t)return;const{columns:a,rows:n,executionTime:s,rowCount:o}=e,i=a.map((e=>`<th>${e}</th>`)).join(""),d=n.map((e=>`<tr>${e.map((e=>`<td>${e}</td>`)).join("")}</tr>`)).join("");t.innerHTML=`\n            <div class="query-results-header">\n                <h4>Query Results</h4>\n                <div class="query-stats">\n                    <span>${o} rows returned</span>\n                    <span>Execution time: ${s}</span>\n                </div>\n            </div>\n            <div class="table-container">\n                <table class="query-results-table">\n                    <thead>\n                        <tr>${i}</tr>\n                    </thead>\n                    <tbody>\n                        ${d}\n                    </tbody>\n                </table>\n            </div>\n        `,t.style.display="block"}clearQuery(){const e=document.getElementById("sql-query"),t=document.getElementById("query-results");e&&(e.value=""),t&&(t.style.display="none")}showLoading(e="Loading..."){const t=document.getElementById("loading-status");t&&(this.updateLoadingMessage(e),t.style.display="block")}hideLoading(){const e=document.getElementById("loading-status");e&&(e.style.display="none")}updateLoadingMessage(e){const t=document.getElementById("loading-message");t&&(t.textContent=e)}updateProgress(e){const t=document.getElementById("progress-fill");t&&(t.style.width=`${e}%`)}showError(e){alert(`Error: ${e}`)}showSuccess(e){alert(`Success: ${e}`)}})})();
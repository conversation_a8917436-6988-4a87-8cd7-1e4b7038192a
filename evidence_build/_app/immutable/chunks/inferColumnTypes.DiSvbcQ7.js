var xl=Object.defineProperty;var No=n=>{throw TypeError(n)};var Pl=(n,t,e)=>t in n?xl(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var It=(n,t,e)=>Pl(n,typeof t!="symbol"?t+"":t,e),Bo=(n,t,e)=>t.has(n)||No("Cannot "+e);var u=(n,t,e)=>(Bo(n,t,"read from private field"),e?e.call(n):t.get(n)),j=(n,t,e)=>t.has(n)?No("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(n):t.set(n,e),nt=(n,t,e,i)=>(Bo(n,t,"write to private field"),i?i.call(n,e):t.set(n,e),e);var Ro=(n,t,e,i)=>({set _(r){nt(n,t,r,e)},get _(){return u(n,t,i)}});import{d as kl}from"./index.rV6zwFgL.js";import{w as Jr,b as $l,r as jl}from"./entry.DRa3JAkG.js";import{T as ws,an as pa,E as ya,a5 as ma}from"./scheduler.D0cbHTIG.js";import{_ as Hi}from"./preload-helper.D7HrI6pR.js";function Y(n,t,e,i){function r(s){return s instanceof e?s:new e(function(o){o(s)})}return new(e||(e=Promise))(function(s,o){function a(b){try{l(i.next(b))}catch(S){o(S)}}function c(b){try{l(i.throw(b))}catch(S){o(S)}}function l(b){b.done?s(b.value):r(b.value).then(a,c)}l((i=i.apply(n,t||[])).next())})}function Mo(n){var t=typeof Symbol=="function"&&Symbol.iterator,e=t&&n[t],i=0;if(e)return e.call(n);if(n&&typeof n.length=="number")return{next:function(){return n&&i>=n.length&&(n=void 0),{value:n&&n[i++],done:!n}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function q(n){return this instanceof q?(this.v=n,this):new q(n)}function Ae(n,t,e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i=e.apply(n,t||[]),r,s=[];return r=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",o),r[Symbol.asyncIterator]=function(){return this},r;function o(O){return function(W){return Promise.resolve(W).then(O,S)}}function a(O,W){i[O]&&(r[O]=function(Bt){return new Promise(function(K,it){s.push([O,Bt,K,it])>1||c(O,Bt)})},W&&(r[O]=W(r[O])))}function c(O,W){try{l(i[O](W))}catch(Bt){tt(s[0][3],Bt)}}function l(O){O.value instanceof q?Promise.resolve(O.value.v).then(b,S):tt(s[0][2],O)}function b(O){c("next",O)}function S(O){c("throw",O)}function tt(O,W){O(W),s.shift(),s.length&&c(s[0][0],s[0][1])}}function Xi(n){var t,e;return t={},i("next"),i("throw",function(r){throw r}),i("return"),t[Symbol.iterator]=function(){return this},t;function i(r,s){t[r]=n[r]?function(o){return(e=!e)?{value:q(n[r](o)),done:!1}:s?s(o):o}:s}}function In(n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=n[Symbol.asyncIterator],e;return t?t.call(n):(n=typeof Mo=="function"?Mo(n):n[Symbol.iterator](),e={},i("next"),i("throw"),i("return"),e[Symbol.asyncIterator]=function(){return this},e);function i(s){e[s]=n[s]&&function(o){return new Promise(function(a,c){o=n[s](o),r(a,c,o.done,o.value)})}}function r(s,o,a,c){Promise.resolve(c).then(function(l){s({value:l,done:a})},o)}}var ye;(function(n){n[n.NONE=0]="NONE",n[n.Null=1]="Null",n[n.Int=2]="Int",n[n.Float=3]="Float",n[n.Binary=4]="Binary",n[n.Utf8=5]="Utf8",n[n.Bool=6]="Bool",n[n.Decimal=7]="Decimal",n[n.Date=8]="Date",n[n.Time=9]="Time",n[n.Timestamp=10]="Timestamp",n[n.Interval=11]="Interval",n[n.List=12]="List",n[n.Struct=13]="Struct",n[n.Union=14]="Union",n[n.FixedSizeBinary=15]="FixedSizeBinary",n[n.FixedSizeList=16]="FixedSizeList",n[n.Map=17]="Map",n[n.Duration=18]="Duration",n[n.LargeBinary=19]="LargeBinary",n[n.LargeUtf8=20]="LargeUtf8",n[n.Dictionary=-1]="Dictionary",n[n.Int8=-2]="Int8",n[n.Int16=-3]="Int16",n[n.Int32=-4]="Int32",n[n.Int64=-5]="Int64",n[n.Uint8=-6]="Uint8",n[n.Uint16=-7]="Uint16",n[n.Uint32=-8]="Uint32",n[n.Uint64=-9]="Uint64",n[n.Float16=-10]="Float16",n[n.Float32=-11]="Float32",n[n.Float64=-12]="Float64",n[n.DateDay=-13]="DateDay",n[n.DateMillisecond=-14]="DateMillisecond",n[n.TimestampSecond=-15]="TimestampSecond",n[n.TimestampMillisecond=-16]="TimestampMillisecond",n[n.TimestampMicrosecond=-17]="TimestampMicrosecond",n[n.TimestampNanosecond=-18]="TimestampNanosecond",n[n.TimeSecond=-19]="TimeSecond",n[n.TimeMillisecond=-20]="TimeMillisecond",n[n.TimeMicrosecond=-21]="TimeMicrosecond",n[n.TimeNanosecond=-22]="TimeNanosecond",n[n.DenseUnion=-23]="DenseUnion",n[n.SparseUnion=-24]="SparseUnion",n[n.IntervalDayTime=-25]="IntervalDayTime",n[n.IntervalYearMonth=-26]="IntervalYearMonth",n[n.DurationSecond=-27]="DurationSecond",n[n.DurationMillisecond=-28]="DurationMillisecond",n[n.DurationMicrosecond=-29]="DurationMicrosecond",n[n.DurationNanosecond=-30]="DurationNanosecond"})(ye||(ye={}));var Co;(function(n){n[n.OFFSET=0]="OFFSET",n[n.DATA=1]="DATA",n[n.VALIDITY=2]="VALIDITY",n[n.TYPE=3]="TYPE"})(Co||(Co={}));function Vl(n){switch(n.typeId){case ye.Date:return"date";case ye.Float:case ye.Int:return"number";case ye.Bool:return"boolean";case ye.Dictionary:default:return"string"}}function zl(n){if(n==null)return[];const t=n.toArray();Object.defineProperty(t,"_evidenceColumnTypes",{enumerable:!1,value:n.schema.fields.map(r=>({name:r.name,evidenceType:Vl(r.type),typeFidelity:"precise"}))});const e=n.schema.fields.filter(r=>r.type.typeId===ye.Date),i=n.schema.fields.filter(r=>r.type.typeId===ye.List);for(const r of t){for(const s of e)r[s.name]=new Date(r[s.name]);for(const s of i)r[s.name]=ga(r[s.name])}return t}function ga(n){var r,s,o,a;if(n==null)return[];const t=n.toArray(),e=((s=(r=n.type)==null?void 0:r.children)==null?void 0:s.filter(c=>c.type.typeId===ye.Date))??[],i=((a=(o=n.type)==null?void 0:o.children)==null?void 0:a.filter(c=>c.type.typeId===ye.List))??[];for(const c of t){for(const l of e)c[l.name]=new Date(c[l.name]);for(const l of i)c[l.name]=ga(c[l.name])}return t}function ba(){let n,t,e=new Promise((i,r)=>{n=i,t=r});return{resolve:n,reject:t,promise:e}}function _a(n){return Promise.race([n,new Promise((t,e)=>setTimeout(()=>e(new Error("Timeout while initializing database")),5e3))])}const Wl=new TextDecoder("utf-8"),Is=n=>Wl.decode(n),Yl=new TextEncoder,$s=n=>Yl.encode(n),Gl=n=>typeof n=="number",wa=n=>typeof n=="boolean",Rt=n=>typeof n=="function",Jt=n=>n!=null&&Object(n)===n,Sn=n=>Jt(n)&&Rt(n.then),$i=n=>Jt(n)&&Rt(n[Symbol.iterator]),ei=n=>Jt(n)&&Rt(n[Symbol.asyncIterator]),vs=n=>Jt(n)&&Jt(n.schema),Ia=n=>Jt(n)&&"done"in n&&"value"in n,va=n=>Jt(n)&&Rt(n.stat)&&Gl(n.fd),Sa=n=>Jt(n)&&js(n.body),Kr=n=>"_getDOMStream"in n&&"_getNodeStream"in n,ql=n=>Jt(n)&&Rt(n.abort)&&Rt(n.getWriter)&&!Kr(n),js=n=>Jt(n)&&Rt(n.cancel)&&Rt(n.getReader)&&!Kr(n),Hl=n=>Jt(n)&&Rt(n.end)&&Rt(n.write)&&wa(n.writable)&&!Kr(n),Ea=n=>Jt(n)&&Rt(n.read)&&Rt(n.pipe)&&wa(n.readable)&&!Kr(n),Ql=n=>Jt(n)&&Rt(n.clear)&&Rt(n.bytes)&&Rt(n.position)&&Rt(n.setPosition)&&Rt(n.capacity)&&Rt(n.getBufferIdentifier)&&Rt(n.createLong),Vs=typeof SharedArrayBuffer<"u"?SharedArrayBuffer:ArrayBuffer;function Jl(n){const t=n[0]?[n[0]]:[];let e,i,r,s;for(let o,a,c=0,l=0,b=n.length;++c<b;){if(o=t[l],a=n[c],!o||!a||o.buffer!==a.buffer||a.byteOffset<o.byteOffset){a&&(t[++l]=a);continue}if({byteOffset:e,byteLength:r}=o,{byteOffset:i,byteLength:s}=a,e+r<i||i+s<e){a&&(t[++l]=a);continue}t[l]=new Uint8Array(o.buffer,e,i-e+s)}return t}function Lo(n,t,e=0,i=t.byteLength){const r=n.byteLength,s=new Uint8Array(n.buffer,n.byteOffset,r),o=new Uint8Array(t.buffer,t.byteOffset,Math.min(i,r));return s.set(o,e),n}function Oe(n,t){const e=Jl(n),i=e.reduce((b,S)=>b+S.byteLength,0);let r,s,o,a=0,c=-1;const l=Math.min(t||Number.POSITIVE_INFINITY,i);for(const b=e.length;++c<b;){if(r=e[c],s=r.subarray(0,Math.min(r.length,l-a)),l<=a+s.length){s.length<r.length?e[c]=r.subarray(s.length):s.length===r.length&&c++,o?Lo(o,s,a):o=s;break}Lo(o||(o=new Uint8Array(l)),s,a),a+=s.length}return[o||new Uint8Array(0),e.slice(c),i-(o?o.byteLength:0)]}function ft(n,t){let e=Ia(t)?t.value:t;return e instanceof n?n===Uint8Array?new n(e.buffer,e.byteOffset,e.byteLength):e:e?(typeof e=="string"&&(e=$s(e)),e instanceof ArrayBuffer?new n(e):e instanceof Vs?new n(e):Ql(e)?ft(n,e.bytes()):ArrayBuffer.isView(e)?e.byteLength<=0?new n(0):new n(e.buffer,e.byteOffset,e.byteLength/n.BYTES_PER_ELEMENT):n.from(e)):new n(0)}const li=n=>ft(Int32Array,n),Uo=n=>ft(BigInt64Array,n),rt=n=>ft(Uint8Array,n),Ss=n=>(n.next(),n);function*Kl(n,t){const e=function*(r){yield r},i=typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof Vs?e(t):$i(t)?t:e(t);return yield*Ss(function*(r){let s=null;do s=r.next(yield ft(n,s));while(!s.done)}(i[Symbol.iterator]())),new n}const Zl=n=>Kl(Uint8Array,n);function Ta(n,t){return Ae(this,arguments,function*(){if(Sn(t))return yield q(yield q(yield*Xi(In(Ta(n,yield q(t))))));const i=function(o){return Ae(this,arguments,function*(){yield yield q(yield q(o))})},r=function(o){return Ae(this,arguments,function*(){yield q(yield*Xi(In(Ss(function*(a){let c=null;do c=a.next(yield c==null?void 0:c.value);while(!c.done)}(o[Symbol.iterator]())))))})},s=typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof Vs?i(t):$i(t)?r(t):ei(t)?t:i(t);return yield q(yield*Xi(In(Ss(function(o){return Ae(this,arguments,function*(){let a=null;do a=yield q(o.next(yield yield q(ft(n,a))));while(!a.done)})}(s[Symbol.asyncIterator]()))))),yield q(new n)})}const Xl=n=>Ta(Uint8Array,n);function Aa(n,t,e){if(n!==0){e=e.slice(0,t);for(let i=-1,r=e.length;++i<r;)e[i]+=n}return e.subarray(0,t)}function tu(n,t){let e=0;const i=n.length;if(i!==t.length)return!1;if(i>0)do if(n[e]!==t[e])return!1;while(++e<i);return!0}const se={fromIterable(n){return Qi(eu(n))},fromAsyncIterable(n){return Qi(nu(n))},fromDOMStream(n){return Qi(iu(n))},fromNodeStream(n){return Qi(su(n))},toDOMStream(n,t){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(n,t){throw new Error('"toNodeStream" not available in this environment')}},Qi=n=>(n.next(),n);function*eu(n){let t,e=!1,i=[],r,s,o,a=0;function c(){return s==="peek"?Oe(i,o)[0]:([r,i,a]=Oe(i,o),r)}({cmd:s,size:o}=(yield null)||{cmd:"read",size:0});const l=Zl(n)[Symbol.iterator]();try{do if({done:t,value:r}=Number.isNaN(o-a)?l.next():l.next(o-a),!t&&r.byteLength>0&&(i.push(r),a+=r.byteLength),t||o<=a)do({cmd:s,size:o}=yield c());while(o<a);while(!t)}catch(b){(e=!0)&&typeof l.throw=="function"&&l.throw(b)}finally{e===!1&&typeof l.return=="function"&&l.return(null)}return null}function nu(n){return Ae(this,arguments,function*(){let e,i=!1,r=[],s,o,a,c=0;function l(){return o==="peek"?Oe(r,a)[0]:([s,r,c]=Oe(r,a),s)}({cmd:o,size:a}=(yield yield q(null))||{cmd:"read",size:0});const b=Xl(n)[Symbol.asyncIterator]();try{do if({done:e,value:s}=Number.isNaN(a-c)?yield q(b.next()):yield q(b.next(a-c)),!e&&s.byteLength>0&&(r.push(s),c+=s.byteLength),e||a<=c)do({cmd:o,size:a}=yield yield q(l()));while(a<c);while(!e)}catch(S){(i=!0)&&typeof b.throw=="function"&&(yield q(b.throw(S)))}finally{i===!1&&typeof b.return=="function"&&(yield q(b.return(new Uint8Array(0))))}return yield q(null)})}function iu(n){return Ae(this,arguments,function*(){let e=!1,i=!1,r=[],s,o,a,c=0;function l(){return o==="peek"?Oe(r,a)[0]:([s,r,c]=Oe(r,a),s)}({cmd:o,size:a}=(yield yield q(null))||{cmd:"read",size:0});const b=new ru(n);try{do if({done:e,value:s}=Number.isNaN(a-c)?yield q(b.read()):yield q(b.read(a-c)),!e&&s.byteLength>0&&(r.push(rt(s)),c+=s.byteLength),e||a<=c)do({cmd:o,size:a}=yield yield q(l()));while(a<c);while(!e)}catch(S){(i=!0)&&(yield q(b.cancel(S)))}finally{i===!1?yield q(b.cancel()):n.locked&&b.releaseLock()}return yield q(null)})}class ru{constructor(t){this.source=t,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch(()=>{})}get closed(){return this.reader?this.reader.closed.catch(()=>{}):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(t){return Y(this,void 0,void 0,function*(){const{reader:e,source:i}=this;e&&(yield e.cancel(t).catch(()=>{})),i&&i.locked&&this.releaseLock()})}read(t){return Y(this,void 0,void 0,function*(){if(t===0)return{done:this.reader==null,value:new Uint8Array(0)};const e=yield this.reader.read();return!e.done&&(e.value=rt(e)),e})}}const ss=(n,t)=>{const e=r=>i([t,r]);let i;return[t,e,new Promise(r=>(i=r)&&n.once(t,e))]};function su(n){return Ae(this,arguments,function*(){const e=[];let i="error",r=!1,s=null,o,a,c=0,l=[],b;function S(){return o==="peek"?Oe(l,a)[0]:([b,l,c]=Oe(l,a),b)}if({cmd:o,size:a}=(yield yield q(null))||{cmd:"read",size:0},n.isTTY)return yield yield q(new Uint8Array(0)),yield q(null);try{e[0]=ss(n,"end"),e[1]=ss(n,"error");do{if(e[2]=ss(n,"readable"),[i,s]=yield q(Promise.race(e.map(O=>O[2]))),i==="error")break;if((r=i==="end")||(Number.isFinite(a-c)?(b=rt(n.read(a-c)),b.byteLength<a-c&&(b=rt(n.read()))):b=rt(n.read()),b.byteLength>0&&(l.push(b),c+=b.byteLength)),r||a<=c)do({cmd:o,size:a}=yield yield q(S()));while(a<c)}while(!r)}finally{yield q(tt(e,i==="error"?s:null))}return yield q(null);function tt(O,W){return b=l=null,new Promise((Bt,K)=>{for(const[it,gt]of O)n.off(it,gt);try{const it=n.destroy;it&&it.call(n,W),W=void 0}catch(it){W=it||W}finally{W!=null?K(W):Bt()}})}})}var Dt;(function(n){n[n.V1=0]="V1",n[n.V2=1]="V2",n[n.V3=2]="V3",n[n.V4=3]="V4",n[n.V5=4]="V5"})(Dt||(Dt={}));var Pt;(function(n){n[n.Sparse=0]="Sparse",n[n.Dense=1]="Dense"})(Pt||(Pt={}));var xt;(function(n){n[n.HALF=0]="HALF",n[n.SINGLE=1]="SINGLE",n[n.DOUBLE=2]="DOUBLE"})(xt||(xt={}));var le;(function(n){n[n.DAY=0]="DAY",n[n.MILLISECOND=1]="MILLISECOND"})(le||(le={}));var x;(function(n){n[n.SECOND=0]="SECOND",n[n.MILLISECOND=1]="MILLISECOND",n[n.MICROSECOND=2]="MICROSECOND",n[n.NANOSECOND=3]="NANOSECOND"})(x||(x={}));var De;(function(n){n[n.YEAR_MONTH=0]="YEAR_MONTH",n[n.DAY_TIME=1]="DAY_TIME",n[n.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"})(De||(De={}));const os=2,Se=4,$e=4,ut=4,Je=new Int32Array(2),xo=new Float32Array(Je.buffer),Po=new Float64Array(Je.buffer),Ji=new Uint16Array(new Uint8Array([1,0]).buffer)[0]===1;var Es;(function(n){n[n.UTF8_BYTES=1]="UTF8_BYTES",n[n.UTF16_STRING=2]="UTF16_STRING"})(Es||(Es={}));let Jn=class Fa{constructor(t){this.bytes_=t,this.position_=0,this.text_decoder_=new TextDecoder}static allocate(t){return new Fa(new Uint8Array(t))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return BigInt.asIntN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readUint64(t){return BigInt.asUintN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readFloat32(t){return Je[0]=this.readInt32(t),xo[0]}readFloat64(t){return Je[Ji?0:1]=this.readInt32(t),Je[Ji?1:0]=this.readInt32(t+4),Po[0]}writeInt8(t,e){this.bytes_[t]=e}writeUint8(t,e){this.bytes_[t]=e}writeInt16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeUint16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeInt32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeUint32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeInt64(t,e){this.writeInt32(t,Number(BigInt.asIntN(32,e))),this.writeInt32(t+4,Number(BigInt.asIntN(32,e>>BigInt(32))))}writeUint64(t,e){this.writeUint32(t,Number(BigInt.asUintN(32,e))),this.writeUint32(t+4,Number(BigInt.asUintN(32,e>>BigInt(32))))}writeFloat32(t,e){xo[0]=e,this.writeInt32(t,Je[0])}writeFloat64(t,e){Po[0]=e,this.writeInt32(t,Je[Ji?0:1]),this.writeInt32(t+4,Je[Ji?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+Se+$e)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let e=0;e<$e;e++)t+=String.fromCharCode(this.readInt8(this.position_+Se+e));return t}__offset(t,e){const i=t-this.readInt32(t);return e<this.readInt16(i)?this.readInt16(i+e):0}__union(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t}__string(t,e){t+=this.readInt32(t);const i=this.readInt32(t);t+=Se;const r=this.bytes_.subarray(t,t+i);return e===Es.UTF8_BYTES?r:this.text_decoder_.decode(r)}__union_with_string(t,e){return typeof t=="string"?this.__string(e):this.__union(t,e)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+Se}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(t.length!=$e)throw new Error("FlatBuffers: file identifier must be length "+$e);for(let e=0;e<$e;e++)if(t.charCodeAt(e)!=this.readInt8(this.position()+Se+e))return!1;return!0}createScalarList(t,e){const i=[];for(let r=0;r<e;++r){const s=t(r);s!==null&&i.push(s)}return i}createObjList(t,e){const i=[];for(let r=0;r<e;++r){const s=t(r);s!==null&&i.push(s.unpack())}return i}},Oa=class Da{constructor(t){this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,this.text_encoder=new TextEncoder;let e;t?e=t:e=1024,this.bb=Jn.allocate(e),this.space=e}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(t,e){t>this.minalign&&(this.minalign=t);const i=~(this.bb.capacity()-this.space+e)+1&t-1;for(;this.space<i+t+e;){const r=this.bb.capacity();this.bb=Da.growByteBuffer(this.bb),this.space+=this.bb.capacity()-r}this.pad(i)}pad(t){for(let e=0;e<t;e++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,e,i){(this.force_defaults||e!=i)&&(this.addInt8(e),this.slot(t))}addFieldInt16(t,e,i){(this.force_defaults||e!=i)&&(this.addInt16(e),this.slot(t))}addFieldInt32(t,e,i){(this.force_defaults||e!=i)&&(this.addInt32(e),this.slot(t))}addFieldInt64(t,e,i){(this.force_defaults||e!==i)&&(this.addInt64(e),this.slot(t))}addFieldFloat32(t,e,i){(this.force_defaults||e!=i)&&(this.addFloat32(e),this.slot(t))}addFieldFloat64(t,e,i){(this.force_defaults||e!=i)&&(this.addFloat64(e),this.slot(t))}addFieldOffset(t,e,i){(this.force_defaults||e!=i)&&(this.addOffset(e),this.slot(t))}addFieldStruct(t,e,i){e!=i&&(this.nested(e),this.slot(t))}nested(t){if(t!=this.offset())throw new TypeError("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new TypeError("FlatBuffers: object serialization must not be nested.")}slot(t){this.vtable!==null&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){const e=t.capacity();if(e&3221225472)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const i=e<<1,r=Jn.allocate(i);return r.setPosition(i-e),r.bytes().set(t.bytes(),i-e),r}addOffset(t){this.prep(Se,0),this.writeInt32(this.offset()-t+Se)}startObject(t){this.notNested(),this.vtable==null&&(this.vtable=[]),this.vtable_in_use=t;for(let e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(this.vtable==null||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const t=this.offset();let e=this.vtable_in_use-1;for(;e>=0&&this.vtable[e]==0;e--);const i=e+1;for(;e>=0;e--)this.addInt16(this.vtable[e]!=0?t-this.vtable[e]:0);const r=2;this.addInt16(t-this.object_start);const s=(i+r)*os;this.addInt16(s);let o=0;const a=this.space;t:for(e=0;e<this.vtables.length;e++){const c=this.bb.capacity()-this.vtables[e];if(s==this.bb.readInt16(c)){for(let l=os;l<s;l+=os)if(this.bb.readInt16(a+l)!=this.bb.readInt16(c+l))continue t;o=this.vtables[e];break}}return o?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,o-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,e,i){const r=i?ut:0;if(e){const s=e;if(this.prep(this.minalign,Se+$e+r),s.length!=$e)throw new TypeError("FlatBuffers: file identifier must be length "+$e);for(let o=$e-1;o>=0;o--)this.writeInt8(s.charCodeAt(o))}this.prep(this.minalign,Se+r),this.addOffset(t),r&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,e){this.finish(t,e,!0)}requiredField(t,e){const i=this.bb.capacity()-t,r=i-this.bb.readInt32(i);if(!(e<this.bb.readInt16(r)&&this.bb.readInt16(r+e)!=0))throw new TypeError("FlatBuffers: field "+e+" must be set")}startVector(t,e,i){this.notNested(),this.vector_num_elems=e,this.prep(Se,t*e),this.prep(i,t*e)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);const e=this.createString(t);return this.string_maps.set(t,e),e}createString(t){if(t==null)return 0;let e;return t instanceof Uint8Array?e=t:e=this.text_encoder.encode(t),this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length),this.bb.bytes().set(e,this.space),this.endVector()}createByteVector(t){return t==null?0:(this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length),this.bb.bytes().set(t,this.space),this.endVector())}createObjectOffset(t){return t===null?0:typeof t=="string"?this.createString(t):t.pack(this)}createObjectOffsetList(t){const e=[];for(let i=0;i<t.length;++i){const r=t[i];if(r!==null)e.push(this.createObjectOffset(r));else throw new TypeError("FlatBuffers: Argument for createObjectOffsetList cannot contain null.")}return e}createStructOffsetList(t,e){return e(this,t.length),this.createObjectOffsetList(t.slice().reverse()),this.endVector()}};var hr;(function(n){n[n.BUFFER=0]="BUFFER"})(hr||(hr={}));var dr;(function(n){n[n.LZ4_FRAME=0]="LZ4_FRAME",n[n.ZSTD=1]="ZSTD"})(dr||(dr={}));class Ke{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBodyCompression(t,e){return(e||new Ke).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBodyCompression(t,e){return t.setPosition(t.position()+ut),(e||new Ke).__init(t.readInt32(t.position())+t.position(),t)}codec(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):dr.LZ4_FRAME}method(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt8(this.bb_pos+t):hr.BUFFER}static startBodyCompression(t){t.startObject(2)}static addCodec(t,e){t.addFieldInt8(0,e,dr.LZ4_FRAME)}static addMethod(t,e){t.addFieldInt8(1,e,hr.BUFFER)}static endBodyCompression(t){return t.endObject()}static createBodyCompression(t,e,i){return Ke.startBodyCompression(t),Ke.addCodec(t,e),Ke.addMethod(t,i),Ke.endBodyCompression(t)}}class Na{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(t,e,i){return t.prep(8,16),t.writeInt64(BigInt(i??0)),t.writeInt64(BigInt(e??0)),t.offset()}}let Ba=class{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(t,e,i){return t.prep(8,16),t.writeInt64(BigInt(i??0)),t.writeInt64(BigInt(e??0)),t.offset()}},Ce=class Ts{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsRecordBatch(t,e){return(e||new Ts).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsRecordBatch(t,e){return t.setPosition(t.position()+ut),(e||new Ts).__init(t.readInt32(t.position())+t.position(),t)}length(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}nodes(t,e){const i=this.bb.__offset(this.bb_pos,6);return i?(e||new Ba).__init(this.bb.__vector(this.bb_pos+i)+t*16,this.bb):null}nodesLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,e){const i=this.bb.__offset(this.bb_pos,8);return i?(e||new Na).__init(this.bb.__vector(this.bb_pos+i)+t*16,this.bb):null}buffersLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}compression(t){const e=this.bb.__offset(this.bb_pos,10);return e?(t||new Ke).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}static startRecordBatch(t){t.startObject(4)}static addLength(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addNodes(t,e){t.addFieldOffset(1,e,0)}static startNodesVector(t,e){t.startVector(16,e,8)}static addBuffers(t,e){t.addFieldOffset(2,e,0)}static startBuffersVector(t,e){t.startVector(16,e,8)}static addCompression(t,e){t.addFieldOffset(3,e,0)}static endRecordBatch(t){return t.endObject()}},Fn=class As{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryBatch(t,e){return(e||new As).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryBatch(t,e){return t.setPosition(t.position()+ut),(e||new As).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}data(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Ce).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isDelta(){const t=this.bb.__offset(this.bb_pos,8);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startDictionaryBatch(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addData(t,e){t.addFieldOffset(1,e,0)}static addIsDelta(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryBatch(t){return t.endObject()}};var Kn;(function(n){n[n.Little=0]="Little",n[n.Big=1]="Big"})(Kn||(Kn={}));var fr;(function(n){n[n.DenseArray=0]="DenseArray"})(fr||(fr={}));class te{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInt(t,e){return(e||new te).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInt(t,e){return t.setPosition(t.position()+ut),(e||new te).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){const t=this.bb.__offset(this.bb_pos,6);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startInt(t){t.startObject(2)}static addBitWidth(t,e){t.addFieldInt32(0,e,0)}static addIsSigned(t,e){t.addFieldInt8(1,+e,0)}static endInt(t){return t.endObject()}static createInt(t,e,i){return te.startInt(t),te.addBitWidth(t,e),te.addIsSigned(t,i),te.endInt(t)}}class je{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryEncoding(t,e){return(e||new je).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryEncoding(t,e){return t.setPosition(t.position()+ut),(e||new je).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}indexType(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new te).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isOrdered(){const t=this.bb.__offset(this.bb_pos,8);return t?!!this.bb.readInt8(this.bb_pos+t):!1}dictionaryKind(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt16(this.bb_pos+t):fr.DenseArray}static startDictionaryEncoding(t){t.startObject(4)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addIndexType(t,e){t.addFieldOffset(1,e,0)}static addIsOrdered(t,e){t.addFieldInt8(2,+e,0)}static addDictionaryKind(t,e){t.addFieldInt16(3,e,fr.DenseArray)}static endDictionaryEncoding(t){return t.endObject()}}class Mt{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKeyValue(t,e){return(e||new Mt).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKeyValue(t,e){return t.setPosition(t.position()+ut),(e||new Mt).__init(t.readInt32(t.position())+t.position(),t)}key(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}value(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,e){t.addFieldOffset(0,e,0)}static addValue(t,e){t.addFieldOffset(1,e,0)}static endKeyValue(t){return t.endObject()}static createKeyValue(t,e,i){return Mt.startKeyValue(t),Mt.addKey(t,e),Mt.addValue(t,i),Mt.endKeyValue(t)}}let ko=class ui{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBinary(t,e){return(e||new ui).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBinary(t,e){return t.setPosition(t.position()+ut),(e||new ui).__init(t.readInt32(t.position())+t.position(),t)}static startBinary(t){t.startObject(0)}static endBinary(t){return t.endObject()}static createBinary(t){return ui.startBinary(t),ui.endBinary(t)}},$o=class hi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBool(t,e){return(e||new hi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBool(t,e){return t.setPosition(t.position()+ut),(e||new hi).__init(t.readInt32(t.position())+t.position(),t)}static startBool(t){t.startObject(0)}static endBool(t){return t.endObject()}static createBool(t){return hi.startBool(t),hi.endBool(t)}},tr=class On{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDate(t,e){return(e||new On).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDate(t,e){return t.setPosition(t.position()+ut),(e||new On).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):le.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,le.MILLISECOND)}static endDate(t){return t.endObject()}static createDate(t,e){return On.startDate(t),On.addUnit(t,e),On.endDate(t)}},Dn=class qe{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDecimal(t,e){return(e||new qe).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDecimal(t,e){return t.setPosition(t.position()+ut),(e||new qe).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}bitWidth(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):128}static startDecimal(t){t.startObject(3)}static addPrecision(t,e){t.addFieldInt32(0,e,0)}static addScale(t,e){t.addFieldInt32(1,e,0)}static addBitWidth(t,e){t.addFieldInt32(2,e,128)}static endDecimal(t){return t.endObject()}static createDecimal(t,e,i,r){return qe.startDecimal(t),qe.addPrecision(t,e),qe.addScale(t,i),qe.addBitWidth(t,r),qe.endDecimal(t)}},er=class Nn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDuration(t,e){return(e||new Nn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDuration(t,e){return t.setPosition(t.position()+ut),(e||new Nn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):x.MILLISECOND}static startDuration(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,x.MILLISECOND)}static endDuration(t){return t.endObject()}static createDuration(t,e){return Nn.startDuration(t),Nn.addUnit(t,e),Nn.endDuration(t)}},nr=class Bn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeBinary(t,e){return(e||new Bn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeBinary(t,e){return t.setPosition(t.position()+ut),(e||new Bn).__init(t.readInt32(t.position())+t.position(),t)}byteWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeBinary(t){return t.endObject()}static createFixedSizeBinary(t,e){return Bn.startFixedSizeBinary(t),Bn.addByteWidth(t,e),Bn.endFixedSizeBinary(t)}},ir=class Rn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeList(t,e){return(e||new Rn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeList(t,e){return t.setPosition(t.position()+ut),(e||new Rn).__init(t.readInt32(t.position())+t.position(),t)}listSize(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeList(t){return t.endObject()}static createFixedSizeList(t,e){return Rn.startFixedSizeList(t),Rn.addListSize(t,e),Rn.endFixedSizeList(t)}};class Ee{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFloatingPoint(t,e){return(e||new Ee).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFloatingPoint(t,e){return t.setPosition(t.position()+ut),(e||new Ee).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):xt.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(t,e){t.addFieldInt16(0,e,xt.HALF)}static endFloatingPoint(t){return t.endObject()}static createFloatingPoint(t,e){return Ee.startFloatingPoint(t),Ee.addPrecision(t,e),Ee.endFloatingPoint(t)}}class Te{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInterval(t,e){return(e||new Te).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInterval(t,e){return t.setPosition(t.position()+ut),(e||new Te).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):De.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,De.YEAR_MONTH)}static endInterval(t){return t.endObject()}static createInterval(t,e){return Te.startInterval(t),Te.addUnit(t,e),Te.endInterval(t)}}let jo=class di{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeBinary(t,e){return(e||new di).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsLargeBinary(t,e){return t.setPosition(t.position()+ut),(e||new di).__init(t.readInt32(t.position())+t.position(),t)}static startLargeBinary(t){t.startObject(0)}static endLargeBinary(t){return t.endObject()}static createLargeBinary(t){return di.startLargeBinary(t),di.endLargeBinary(t)}},Vo=class fi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeUtf8(t,e){return(e||new fi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsLargeUtf8(t,e){return t.setPosition(t.position()+ut),(e||new fi).__init(t.readInt32(t.position())+t.position(),t)}static startLargeUtf8(t){t.startObject(0)}static endLargeUtf8(t){return t.endObject()}static createLargeUtf8(t){return fi.startLargeUtf8(t),fi.endLargeUtf8(t)}},zo=class pi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsList(t,e){return(e||new pi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsList(t,e){return t.setPosition(t.position()+ut),(e||new pi).__init(t.readInt32(t.position())+t.position(),t)}static startList(t){t.startObject(0)}static endList(t){return t.endObject()}static createList(t){return pi.startList(t),pi.endList(t)}},rr=class Mn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMap(t,e){return(e||new Mn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMap(t,e){return t.setPosition(t.position()+ut),(e||new Mn).__init(t.readInt32(t.position())+t.position(),t)}keysSorted(){const t=this.bb.__offset(this.bb_pos,4);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startMap(t){t.startObject(1)}static addKeysSorted(t,e){t.addFieldInt8(0,+e,0)}static endMap(t){return t.endObject()}static createMap(t,e){return Mn.startMap(t),Mn.addKeysSorted(t,e),Mn.endMap(t)}},Wo=class yi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNull(t,e){return(e||new yi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNull(t,e){return t.setPosition(t.position()+ut),(e||new yi).__init(t.readInt32(t.position())+t.position(),t)}static startNull(t){t.startObject(0)}static endNull(t){return t.endObject()}static createNull(t){return yi.startNull(t),yi.endNull(t)}};class pn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsStruct_(t,e){return(e||new pn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStruct_(t,e){return t.setPosition(t.position()+ut),(e||new pn).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){return t.endObject()}static createStruct_(t){return pn.startStruct_(t),pn.endStruct_(t)}}class oe{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTime(t,e){return(e||new oe).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTime(t,e){return t.setPosition(t.position()+ut),(e||new oe).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):x.MILLISECOND}bitWidth(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,x.MILLISECOND)}static addBitWidth(t,e){t.addFieldInt32(1,e,32)}static endTime(t){return t.endObject()}static createTime(t,e,i){return oe.startTime(t),oe.addUnit(t,e),oe.addBitWidth(t,i),oe.endTime(t)}}class ae{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTimestamp(t,e){return(e||new ae).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTimestamp(t,e){return t.setPosition(t.position()+ut),(e||new ae).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):x.SECOND}timezone(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,x.SECOND)}static addTimezone(t,e){t.addFieldOffset(1,e,0)}static endTimestamp(t){return t.endObject()}static createTimestamp(t,e,i){return ae.startTimestamp(t),ae.addUnit(t,e),ae.addTimezone(t,i),ae.endTimestamp(t)}}class Ht{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUnion(t,e){return(e||new Ht).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUnion(t,e){return t.setPosition(t.position()+ut),(e||new Ht).__init(t.readInt32(t.position())+t.position(),t)}mode(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Pt.Sparse}typeIds(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+t*4):0}typeIdsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){const t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(t,e){t.addFieldInt16(0,e,Pt.Sparse)}static addTypeIds(t,e){t.addFieldOffset(1,e,0)}static createTypeIdsVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addInt32(e[i]);return t.endVector()}static startTypeIdsVector(t,e){t.startVector(4,e,4)}static endUnion(t){return t.endObject()}static createUnion(t,e,i){return Ht.startUnion(t),Ht.addMode(t,e),Ht.addTypeIds(t,i),Ht.endUnion(t)}}let Yo=class mi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUtf8(t,e){return(e||new mi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUtf8(t,e){return t.setPosition(t.position()+ut),(e||new mi).__init(t.readInt32(t.position())+t.position(),t)}static startUtf8(t){t.startObject(0)}static endUtf8(t){return t.endObject()}static createUtf8(t){return mi.startUtf8(t),mi.endUtf8(t)}};var mt;(function(n){n[n.NONE=0]="NONE",n[n.Null=1]="Null",n[n.Int=2]="Int",n[n.FloatingPoint=3]="FloatingPoint",n[n.Binary=4]="Binary",n[n.Utf8=5]="Utf8",n[n.Bool=6]="Bool",n[n.Decimal=7]="Decimal",n[n.Date=8]="Date",n[n.Time=9]="Time",n[n.Timestamp=10]="Timestamp",n[n.Interval=11]="Interval",n[n.List=12]="List",n[n.Struct_=13]="Struct_",n[n.Union=14]="Union",n[n.FixedSizeBinary=15]="FixedSizeBinary",n[n.FixedSizeList=16]="FixedSizeList",n[n.Map=17]="Map",n[n.Duration=18]="Duration",n[n.LargeBinary=19]="LargeBinary",n[n.LargeUtf8=20]="LargeUtf8",n[n.LargeList=21]="LargeList",n[n.RunEndEncoded=22]="RunEndEncoded"})(mt||(mt={}));let ne=class sr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsField(t,e){return(e||new sr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsField(t,e){return t.setPosition(t.position()+ut),(e||new sr).__init(t.readInt32(t.position())+t.position(),t)}name(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}nullable(){const t=this.bb.__offset(this.bb_pos,6);return t?!!this.bb.readInt8(this.bb_pos+t):!1}typeType(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):mt.NONE}type(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__union(t,this.bb_pos+e):null}dictionary(t){const e=this.bb.__offset(this.bb_pos,12);return e?(t||new je).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}children(t,e){const i=this.bb.__offset(this.bb_pos,14);return i?(e||new sr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}childrenLength(){const t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const i=this.bb.__offset(this.bb_pos,16);return i?(e||new Mt).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,e){t.addFieldOffset(0,e,0)}static addNullable(t,e){t.addFieldInt8(1,+e,0)}static addTypeType(t,e){t.addFieldInt8(2,e,mt.NONE)}static addType(t,e){t.addFieldOffset(3,e,0)}static addDictionary(t,e){t.addFieldOffset(4,e,0)}static addChildren(t,e){t.addFieldOffset(5,e,0)}static createChildrenVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startChildrenVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(6,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endField(t){return t.endObject()}},Ie=class Me{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSchema(t,e){return(e||new Me).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSchema(t,e){return t.setPosition(t.position()+ut),(e||new Me).__init(t.readInt32(t.position())+t.position(),t)}endianness(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Kn.Little}fields(t,e){const i=this.bb.__offset(this.bb_pos,6);return i?(e||new ne).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}fieldsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const i=this.bb.__offset(this.bb_pos,8);return i?(e||new Mt).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}features(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+t*8):BigInt(0)}featuresLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(4)}static addEndianness(t,e){t.addFieldInt16(0,e,Kn.Little)}static addFields(t,e){t.addFieldOffset(1,e,0)}static createFieldsVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startFieldsVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(2,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static addFeatures(t,e){t.addFieldOffset(3,e,0)}static createFeaturesVector(t,e){t.startVector(8,e.length,8);for(let i=e.length-1;i>=0;i--)t.addInt64(e[i]);return t.endVector()}static startFeaturesVector(t,e){t.startVector(8,e,8)}static endSchema(t){return t.endObject()}static finishSchemaBuffer(t,e){t.finish(e)}static finishSizePrefixedSchemaBuffer(t,e){t.finish(e,void 0,!0)}static createSchema(t,e,i,r,s){return Me.startSchema(t),Me.addEndianness(t,e),Me.addFields(t,i),Me.addCustomMetadata(t,r),Me.addFeatures(t,s),Me.endSchema(t)}};var at;(function(n){n[n.NONE=0]="NONE",n[n.Schema=1]="Schema",n[n.DictionaryBatch=2]="DictionaryBatch",n[n.RecordBatch=3]="RecordBatch",n[n.Tensor=4]="Tensor",n[n.SparseTensor=5]="SparseTensor"})(at||(at={}));var h;(function(n){n[n.NONE=0]="NONE",n[n.Null=1]="Null",n[n.Int=2]="Int",n[n.Float=3]="Float",n[n.Binary=4]="Binary",n[n.Utf8=5]="Utf8",n[n.Bool=6]="Bool",n[n.Decimal=7]="Decimal",n[n.Date=8]="Date",n[n.Time=9]="Time",n[n.Timestamp=10]="Timestamp",n[n.Interval=11]="Interval",n[n.List=12]="List",n[n.Struct=13]="Struct",n[n.Union=14]="Union",n[n.FixedSizeBinary=15]="FixedSizeBinary",n[n.FixedSizeList=16]="FixedSizeList",n[n.Map=17]="Map",n[n.Duration=18]="Duration",n[n.LargeBinary=19]="LargeBinary",n[n.LargeUtf8=20]="LargeUtf8",n[n.Dictionary=-1]="Dictionary",n[n.Int8=-2]="Int8",n[n.Int16=-3]="Int16",n[n.Int32=-4]="Int32",n[n.Int64=-5]="Int64",n[n.Uint8=-6]="Uint8",n[n.Uint16=-7]="Uint16",n[n.Uint32=-8]="Uint32",n[n.Uint64=-9]="Uint64",n[n.Float16=-10]="Float16",n[n.Float32=-11]="Float32",n[n.Float64=-12]="Float64",n[n.DateDay=-13]="DateDay",n[n.DateMillisecond=-14]="DateMillisecond",n[n.TimestampSecond=-15]="TimestampSecond",n[n.TimestampMillisecond=-16]="TimestampMillisecond",n[n.TimestampMicrosecond=-17]="TimestampMicrosecond",n[n.TimestampNanosecond=-18]="TimestampNanosecond",n[n.TimeSecond=-19]="TimeSecond",n[n.TimeMillisecond=-20]="TimeMillisecond",n[n.TimeMicrosecond=-21]="TimeMicrosecond",n[n.TimeNanosecond=-22]="TimeNanosecond",n[n.DenseUnion=-23]="DenseUnion",n[n.SparseUnion=-24]="SparseUnion",n[n.IntervalDayTime=-25]="IntervalDayTime",n[n.IntervalYearMonth=-26]="IntervalYearMonth",n[n.DurationSecond=-27]="DurationSecond",n[n.DurationMillisecond=-28]="DurationMillisecond",n[n.DurationMicrosecond=-29]="DurationMicrosecond",n[n.DurationNanosecond=-30]="DurationNanosecond"})(h||(h={}));var Le;(function(n){n[n.OFFSET=0]="OFFSET",n[n.DATA=1]="DATA",n[n.VALIDITY=2]="VALIDITY",n[n.TYPE=3]="TYPE"})(Le||(Le={}));const ou=void 0;function vi(n){if(n===null)return"null";if(n===ou)return"undefined";switch(typeof n){case"number":return`${n}`;case"bigint":return`${n}`;case"string":return`"${n}"`}return typeof n[Symbol.toPrimitive]=="function"?n[Symbol.toPrimitive]("string"):ArrayBuffer.isView(n)?n instanceof BigInt64Array||n instanceof BigUint64Array?`[${[...n].map(t=>vi(t))}]`:`[${n}]`:ArrayBuffer.isView(n)?`[${n}]`:JSON.stringify(n,(t,e)=>typeof e=="bigint"?`${e}`:e)}function pt(n){if(typeof n=="bigint"&&(n<Number.MIN_SAFE_INTEGER||n>Number.MAX_SAFE_INTEGER))throw new TypeError(`${n} is not safe to convert to a number.`);return Number(n)}function Ra(n,t){return pt(n/t)+pt(n%t)/pt(t)}const au=Symbol.for("isArrowBigNum");function be(n,...t){return t.length===0?Object.setPrototypeOf(ft(this.TypedArray,n),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(n,...t),this.constructor.prototype)}be.prototype[au]=!0;be.prototype.toJSON=function(){return`"${Ei(this)}"`};be.prototype.valueOf=function(n){return Ma(this,n)};be.prototype.toString=function(){return Ei(this)};be.prototype[Symbol.toPrimitive]=function(n="default"){switch(n){case"number":return Ma(this);case"string":return Ei(this);case"default":return uu(this)}return Ei(this)};function kn(...n){return be.apply(this,n)}function $n(...n){return be.apply(this,n)}function Si(...n){return be.apply(this,n)}Object.setPrototypeOf(kn.prototype,Object.create(Int32Array.prototype));Object.setPrototypeOf($n.prototype,Object.create(Uint32Array.prototype));Object.setPrototypeOf(Si.prototype,Object.create(Uint32Array.prototype));Object.assign(kn.prototype,be.prototype,{constructor:kn,signed:!0,TypedArray:Int32Array,BigIntArray:BigInt64Array});Object.assign($n.prototype,be.prototype,{constructor:$n,signed:!1,TypedArray:Uint32Array,BigIntArray:BigUint64Array});Object.assign(Si.prototype,be.prototype,{constructor:Si,signed:!0,TypedArray:Uint32Array,BigIntArray:BigUint64Array});const cu=BigInt(4294967296)*BigInt(4294967296),lu=cu-BigInt(1);function Ma(n,t){const{buffer:e,byteOffset:i,byteLength:r,signed:s}=n,o=new BigUint64Array(e,i,r/8),a=s&&o.at(-1)&BigInt(1)<<BigInt(63);let c=BigInt(0),l=0;if(a){for(const b of o)c|=(b^lu)*(BigInt(1)<<BigInt(64*l++));c*=BigInt(-1),c-=BigInt(1)}else for(const b of o)c|=b*(BigInt(1)<<BigInt(64*l++));if(typeof t=="number"){const b=BigInt(Math.pow(10,t)),S=c/b,tt=c%b;return pt(S)+pt(tt)/pt(b)}return pt(c)}function Ei(n){if(n.byteLength===8)return`${new n.BigIntArray(n.buffer,n.byteOffset,1)[0]}`;if(!n.signed)return as(n);let t=new Uint16Array(n.buffer,n.byteOffset,n.byteLength/2);if(new Int16Array([t.at(-1)])[0]>=0)return as(n);t=t.slice();let i=1;for(let s=0;s<t.length;s++){const o=t[s],a=~o+i;t[s]=a,i&=o===0?1:0}return`-${as(t)}`}function uu(n){return n.byteLength===8?new n.BigIntArray(n.buffer,n.byteOffset,1)[0]:Ei(n)}function as(n){let t="";const e=new Uint32Array(2);let i=new Uint16Array(n.buffer,n.byteOffset,n.byteLength/2);const r=new Uint32Array((i=new Uint16Array(i).reverse()).buffer);let s=-1;const o=i.length-1;do{for(e[0]=i[s=0];s<o;)i[s++]=e[1]=e[0]/10,e[0]=(e[0]-e[1]*10<<16)+i[s];i[s]=e[1]=e[0]/10,e[0]=e[0]-e[1]*10,t=`${e[0]}${t}`}while(r[0]||r[1]||r[2]||r[3]);return t??"0"}class zs{static new(t,e){switch(e){case!0:return new kn(t);case!1:return new $n(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case BigInt64Array:return new kn(t)}return t.byteLength===16?new Si(t):new $n(t)}static signed(t){return new kn(t)}static unsigned(t){return new $n(t)}static decimal(t){return new Si(t)}constructor(t,e){return zs.new(t,e)}}var Ca,La,Ua,xa,Pa,ka,$a,ja,Va,za,Wa,Ya,Ga,qa,Ha,Qa,Ja,Ka,Za,Xa,tc,ec;class D{static isNull(t){return(t==null?void 0:t.typeId)===h.Null}static isInt(t){return(t==null?void 0:t.typeId)===h.Int}static isFloat(t){return(t==null?void 0:t.typeId)===h.Float}static isBinary(t){return(t==null?void 0:t.typeId)===h.Binary}static isLargeBinary(t){return(t==null?void 0:t.typeId)===h.LargeBinary}static isUtf8(t){return(t==null?void 0:t.typeId)===h.Utf8}static isLargeUtf8(t){return(t==null?void 0:t.typeId)===h.LargeUtf8}static isBool(t){return(t==null?void 0:t.typeId)===h.Bool}static isDecimal(t){return(t==null?void 0:t.typeId)===h.Decimal}static isDate(t){return(t==null?void 0:t.typeId)===h.Date}static isTime(t){return(t==null?void 0:t.typeId)===h.Time}static isTimestamp(t){return(t==null?void 0:t.typeId)===h.Timestamp}static isInterval(t){return(t==null?void 0:t.typeId)===h.Interval}static isDuration(t){return(t==null?void 0:t.typeId)===h.Duration}static isList(t){return(t==null?void 0:t.typeId)===h.List}static isStruct(t){return(t==null?void 0:t.typeId)===h.Struct}static isUnion(t){return(t==null?void 0:t.typeId)===h.Union}static isFixedSizeBinary(t){return(t==null?void 0:t.typeId)===h.FixedSizeBinary}static isFixedSizeList(t){return(t==null?void 0:t.typeId)===h.FixedSizeList}static isMap(t){return(t==null?void 0:t.typeId)===h.Map}static isDictionary(t){return(t==null?void 0:t.typeId)===h.Dictionary}static isDenseUnion(t){return D.isUnion(t)&&t.mode===Pt.Dense}static isSparseUnion(t){return D.isUnion(t)&&t.mode===Pt.Sparse}constructor(t){this.typeId=t}}Ca=Symbol.toStringTag;D[Ca]=(n=>(n.children=null,n.ArrayType=Array,n.OffsetArrayType=Int32Array,n[Symbol.toStringTag]="DataType"))(D.prototype);class rn extends D{constructor(){super(h.Null)}toString(){return"Null"}}La=Symbol.toStringTag;rn[La]=(n=>n[Symbol.toStringTag]="Null")(rn.prototype);class En extends D{constructor(t,e){super(h.Int),this.isSigned=t,this.bitWidth=e}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?BigInt64Array:BigUint64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}Ua=Symbol.toStringTag;En[Ua]=(n=>(n.isSigned=null,n.bitWidth=null,n[Symbol.toStringTag]="Int"))(En.prototype);class Ti extends En{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(Ti.prototype,"ArrayType",{value:Int32Array});class pr extends D{constructor(t){super(h.Float),this.precision=t}get ArrayType(){switch(this.precision){case xt.HALF:return Uint16Array;case xt.SINGLE:return Float32Array;case xt.DOUBLE:return Float64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`Float${this.precision<<5||16}`}}xa=Symbol.toStringTag;pr[xa]=(n=>(n.precision=null,n[Symbol.toStringTag]="Float"))(pr.prototype);class yr extends D{constructor(){super(h.Binary)}toString(){return"Binary"}}Pa=Symbol.toStringTag;yr[Pa]=(n=>(n.ArrayType=Uint8Array,n[Symbol.toStringTag]="Binary"))(yr.prototype);class mr extends D{constructor(){super(h.LargeBinary)}toString(){return"LargeBinary"}}ka=Symbol.toStringTag;mr[ka]=(n=>(n.ArrayType=Uint8Array,n.OffsetArrayType=BigInt64Array,n[Symbol.toStringTag]="LargeBinary"))(mr.prototype);class gr extends D{constructor(){super(h.Utf8)}toString(){return"Utf8"}}$a=Symbol.toStringTag;gr[$a]=(n=>(n.ArrayType=Uint8Array,n[Symbol.toStringTag]="Utf8"))(gr.prototype);class br extends D{constructor(){super(h.LargeUtf8)}toString(){return"LargeUtf8"}}ja=Symbol.toStringTag;br[ja]=(n=>(n.ArrayType=Uint8Array,n.OffsetArrayType=BigInt64Array,n[Symbol.toStringTag]="LargeUtf8"))(br.prototype);class _r extends D{constructor(){super(h.Bool)}toString(){return"Bool"}}Va=Symbol.toStringTag;_r[Va]=(n=>(n.ArrayType=Uint8Array,n[Symbol.toStringTag]="Bool"))(_r.prototype);class wr extends D{constructor(t,e,i=128){super(h.Decimal),this.scale=t,this.precision=e,this.bitWidth=i}toString(){return`Decimal[${this.precision}e${this.scale>0?"+":""}${this.scale}]`}}za=Symbol.toStringTag;wr[za]=(n=>(n.scale=null,n.precision=null,n.ArrayType=Uint32Array,n[Symbol.toStringTag]="Decimal"))(wr.prototype);class Ir extends D{constructor(t){super(h.Date),this.unit=t}toString(){return`Date${(this.unit+1)*32}<${le[this.unit]}>`}get ArrayType(){return this.unit===le.DAY?Int32Array:BigInt64Array}}Wa=Symbol.toStringTag;Ir[Wa]=(n=>(n.unit=null,n[Symbol.toStringTag]="Date"))(Ir.prototype);class vr extends D{constructor(t,e){super(h.Time),this.unit=t,this.bitWidth=e}toString(){return`Time${this.bitWidth}<${x[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return BigInt64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}}Ya=Symbol.toStringTag;vr[Ya]=(n=>(n.unit=null,n.bitWidth=null,n[Symbol.toStringTag]="Time"))(vr.prototype);class Sr extends D{constructor(t,e){super(h.Timestamp),this.unit=t,this.timezone=e}toString(){return`Timestamp<${x[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}Ga=Symbol.toStringTag;Sr[Ga]=(n=>(n.unit=null,n.timezone=null,n.ArrayType=BigInt64Array,n[Symbol.toStringTag]="Timestamp"))(Sr.prototype);class Er extends D{constructor(t){super(h.Interval),this.unit=t}toString(){return`Interval<${De[this.unit]}>`}}qa=Symbol.toStringTag;Er[qa]=(n=>(n.unit=null,n.ArrayType=Int32Array,n[Symbol.toStringTag]="Interval"))(Er.prototype);class Tr extends D{constructor(t){super(h.Duration),this.unit=t}toString(){return`Duration<${x[this.unit]}>`}}Ha=Symbol.toStringTag;Tr[Ha]=(n=>(n.unit=null,n.ArrayType=BigInt64Array,n[Symbol.toStringTag]="Duration"))(Tr.prototype);class Ar extends D{constructor(t){super(h.List),this.children=[t]}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}Qa=Symbol.toStringTag;Ar[Qa]=(n=>(n.children=null,n[Symbol.toStringTag]="List"))(Ar.prototype);class zt extends D{constructor(t){super(h.Struct),this.children=t}toString(){return`Struct<{${this.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}Ja=Symbol.toStringTag;zt[Ja]=(n=>(n.children=null,n[Symbol.toStringTag]="Struct"))(zt.prototype);class Fr extends D{constructor(t,e,i){super(h.Union),this.mode=t,this.children=i,this.typeIds=e=Int32Array.from(e),this.typeIdToChildIndex=e.reduce((r,s,o)=>(r[s]=o)&&r||r,Object.create(null))}toString(){return`${this[Symbol.toStringTag]}<${this.children.map(t=>`${t.type}`).join(" | ")}>`}}Ka=Symbol.toStringTag;Fr[Ka]=(n=>(n.mode=null,n.typeIds=null,n.children=null,n.typeIdToChildIndex=null,n.ArrayType=Int8Array,n[Symbol.toStringTag]="Union"))(Fr.prototype);class Or extends D{constructor(t){super(h.FixedSizeBinary),this.byteWidth=t}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}Za=Symbol.toStringTag;Or[Za]=(n=>(n.byteWidth=null,n.ArrayType=Uint8Array,n[Symbol.toStringTag]="FixedSizeBinary"))(Or.prototype);class Dr extends D{constructor(t,e){super(h.FixedSizeList),this.listSize=t,this.children=[e]}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}Xa=Symbol.toStringTag;Dr[Xa]=(n=>(n.children=null,n.listSize=null,n[Symbol.toStringTag]="FixedSizeList"))(Dr.prototype);class Nr extends D{constructor(t,e=!1){var i,r,s;if(super(h.Map),this.children=[t],this.keysSorted=e,t&&(t.name="entries",!((i=t==null?void 0:t.type)===null||i===void 0)&&i.children)){const o=(r=t==null?void 0:t.type)===null||r===void 0?void 0:r.children[0];o&&(o.name="key");const a=(s=t==null?void 0:t.type)===null||s===void 0?void 0:s.children[1];a&&(a.name="value")}}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}tc=Symbol.toStringTag;Nr[tc]=(n=>(n.children=null,n.keysSorted=null,n[Symbol.toStringTag]="Map_"))(Nr.prototype);const hu=(n=>()=>++n)(-1);class Zn extends D{constructor(t,e,i,r){super(h.Dictionary),this.indices=e,this.dictionary=t,this.isOrdered=r||!1,this.id=i==null?hu():pt(i)}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}ec=Symbol.toStringTag;Zn[ec]=(n=>(n.id=null,n.indices=null,n.isOrdered=null,n.dictionary=null,n[Symbol.toStringTag]="Dictionary"))(Zn.prototype);function Ue(n){const t=n;switch(n.typeId){case h.Decimal:return n.bitWidth/32;case h.Interval:return 1+t.unit;case h.FixedSizeList:return t.listSize;case h.FixedSizeBinary:return t.byteWidth;default:return 1}}class J{visitMany(t,...e){return t.map((i,r)=>this.visit(i,...e.map(s=>s[r])))}visit(...t){return this.getVisitFn(t[0],!1).apply(this,t)}getVisitFn(t,e=!0){return du(this,t,e)}getVisitFnByTypeId(t,e=!0){return Cn(this,t,e)}visitNull(t,...e){return null}visitBool(t,...e){return null}visitInt(t,...e){return null}visitFloat(t,...e){return null}visitUtf8(t,...e){return null}visitLargeUtf8(t,...e){return null}visitBinary(t,...e){return null}visitLargeBinary(t,...e){return null}visitFixedSizeBinary(t,...e){return null}visitDate(t,...e){return null}visitTimestamp(t,...e){return null}visitTime(t,...e){return null}visitDecimal(t,...e){return null}visitList(t,...e){return null}visitStruct(t,...e){return null}visitUnion(t,...e){return null}visitDictionary(t,...e){return null}visitInterval(t,...e){return null}visitDuration(t,...e){return null}visitFixedSizeList(t,...e){return null}visitMap(t,...e){return null}}function du(n,t,e=!0){return typeof t=="number"?Cn(n,t,e):typeof t=="string"&&t in h?Cn(n,h[t],e):t&&t instanceof D?Cn(n,Go(t),e):t!=null&&t.type&&t.type instanceof D?Cn(n,Go(t.type),e):Cn(n,h.NONE,e)}function Cn(n,t,e=!0){let i=null;switch(t){case h.Null:i=n.visitNull;break;case h.Bool:i=n.visitBool;break;case h.Int:i=n.visitInt;break;case h.Int8:i=n.visitInt8||n.visitInt;break;case h.Int16:i=n.visitInt16||n.visitInt;break;case h.Int32:i=n.visitInt32||n.visitInt;break;case h.Int64:i=n.visitInt64||n.visitInt;break;case h.Uint8:i=n.visitUint8||n.visitInt;break;case h.Uint16:i=n.visitUint16||n.visitInt;break;case h.Uint32:i=n.visitUint32||n.visitInt;break;case h.Uint64:i=n.visitUint64||n.visitInt;break;case h.Float:i=n.visitFloat;break;case h.Float16:i=n.visitFloat16||n.visitFloat;break;case h.Float32:i=n.visitFloat32||n.visitFloat;break;case h.Float64:i=n.visitFloat64||n.visitFloat;break;case h.Utf8:i=n.visitUtf8;break;case h.LargeUtf8:i=n.visitLargeUtf8;break;case h.Binary:i=n.visitBinary;break;case h.LargeBinary:i=n.visitLargeBinary;break;case h.FixedSizeBinary:i=n.visitFixedSizeBinary;break;case h.Date:i=n.visitDate;break;case h.DateDay:i=n.visitDateDay||n.visitDate;break;case h.DateMillisecond:i=n.visitDateMillisecond||n.visitDate;break;case h.Timestamp:i=n.visitTimestamp;break;case h.TimestampSecond:i=n.visitTimestampSecond||n.visitTimestamp;break;case h.TimestampMillisecond:i=n.visitTimestampMillisecond||n.visitTimestamp;break;case h.TimestampMicrosecond:i=n.visitTimestampMicrosecond||n.visitTimestamp;break;case h.TimestampNanosecond:i=n.visitTimestampNanosecond||n.visitTimestamp;break;case h.Time:i=n.visitTime;break;case h.TimeSecond:i=n.visitTimeSecond||n.visitTime;break;case h.TimeMillisecond:i=n.visitTimeMillisecond||n.visitTime;break;case h.TimeMicrosecond:i=n.visitTimeMicrosecond||n.visitTime;break;case h.TimeNanosecond:i=n.visitTimeNanosecond||n.visitTime;break;case h.Decimal:i=n.visitDecimal;break;case h.List:i=n.visitList;break;case h.Struct:i=n.visitStruct;break;case h.Union:i=n.visitUnion;break;case h.DenseUnion:i=n.visitDenseUnion||n.visitUnion;break;case h.SparseUnion:i=n.visitSparseUnion||n.visitUnion;break;case h.Dictionary:i=n.visitDictionary;break;case h.Interval:i=n.visitInterval;break;case h.IntervalDayTime:i=n.visitIntervalDayTime||n.visitInterval;break;case h.IntervalYearMonth:i=n.visitIntervalYearMonth||n.visitInterval;break;case h.Duration:i=n.visitDuration;break;case h.DurationSecond:i=n.visitDurationSecond||n.visitDuration;break;case h.DurationMillisecond:i=n.visitDurationMillisecond||n.visitDuration;break;case h.DurationMicrosecond:i=n.visitDurationMicrosecond||n.visitDuration;break;case h.DurationNanosecond:i=n.visitDurationNanosecond||n.visitDuration;break;case h.FixedSizeList:i=n.visitFixedSizeList;break;case h.Map:i=n.visitMap;break}if(typeof i=="function")return i;if(!e)return()=>null;throw new Error(`Unrecognized type '${h[t]}'`)}function Go(n){switch(n.typeId){case h.Null:return h.Null;case h.Int:{const{bitWidth:t,isSigned:e}=n;switch(t){case 8:return e?h.Int8:h.Uint8;case 16:return e?h.Int16:h.Uint16;case 32:return e?h.Int32:h.Uint32;case 64:return e?h.Int64:h.Uint64}return h.Int}case h.Float:switch(n.precision){case xt.HALF:return h.Float16;case xt.SINGLE:return h.Float32;case xt.DOUBLE:return h.Float64}return h.Float;case h.Binary:return h.Binary;case h.LargeBinary:return h.LargeBinary;case h.Utf8:return h.Utf8;case h.LargeUtf8:return h.LargeUtf8;case h.Bool:return h.Bool;case h.Decimal:return h.Decimal;case h.Time:switch(n.unit){case x.SECOND:return h.TimeSecond;case x.MILLISECOND:return h.TimeMillisecond;case x.MICROSECOND:return h.TimeMicrosecond;case x.NANOSECOND:return h.TimeNanosecond}return h.Time;case h.Timestamp:switch(n.unit){case x.SECOND:return h.TimestampSecond;case x.MILLISECOND:return h.TimestampMillisecond;case x.MICROSECOND:return h.TimestampMicrosecond;case x.NANOSECOND:return h.TimestampNanosecond}return h.Timestamp;case h.Date:switch(n.unit){case le.DAY:return h.DateDay;case le.MILLISECOND:return h.DateMillisecond}return h.Date;case h.Interval:switch(n.unit){case De.DAY_TIME:return h.IntervalDayTime;case De.YEAR_MONTH:return h.IntervalYearMonth}return h.Interval;case h.Duration:switch(n.unit){case x.SECOND:return h.DurationSecond;case x.MILLISECOND:return h.DurationMillisecond;case x.MICROSECOND:return h.DurationMicrosecond;case x.NANOSECOND:return h.DurationNanosecond}return h.Duration;case h.Map:return h.Map;case h.List:return h.List;case h.Struct:return h.Struct;case h.Union:switch(n.mode){case Pt.Dense:return h.DenseUnion;case Pt.Sparse:return h.SparseUnion}return h.Union;case h.FixedSizeBinary:return h.FixedSizeBinary;case h.FixedSizeList:return h.FixedSizeList;case h.Dictionary:return h.Dictionary}throw new Error(`Unrecognized type '${h[n.typeId]}'`)}J.prototype.visitInt8=null;J.prototype.visitInt16=null;J.prototype.visitInt32=null;J.prototype.visitInt64=null;J.prototype.visitUint8=null;J.prototype.visitUint16=null;J.prototype.visitUint32=null;J.prototype.visitUint64=null;J.prototype.visitFloat16=null;J.prototype.visitFloat32=null;J.prototype.visitFloat64=null;J.prototype.visitDateDay=null;J.prototype.visitDateMillisecond=null;J.prototype.visitTimestampSecond=null;J.prototype.visitTimestampMillisecond=null;J.prototype.visitTimestampMicrosecond=null;J.prototype.visitTimestampNanosecond=null;J.prototype.visitTimeSecond=null;J.prototype.visitTimeMillisecond=null;J.prototype.visitTimeMicrosecond=null;J.prototype.visitTimeNanosecond=null;J.prototype.visitDenseUnion=null;J.prototype.visitSparseUnion=null;J.prototype.visitIntervalDayTime=null;J.prototype.visitIntervalYearMonth=null;J.prototype.visitDuration=null;J.prototype.visitDurationSecond=null;J.prototype.visitDurationMillisecond=null;J.prototype.visitDurationMicrosecond=null;J.prototype.visitDurationNanosecond=null;const nc=new Float64Array(1),An=new Uint32Array(nc.buffer);function ic(n){const t=(n&31744)>>10,e=(n&1023)/1024,i=Math.pow(-1,(n&32768)>>15);switch(t){case 31:return i*(e?Number.NaN:1/0);case 0:return i*(e?6103515625e-14*e:0)}return i*Math.pow(2,t-15)*(1+e)}function fu(n){if(n!==n)return 32256;nc[0]=n;const t=(An[1]&2147483648)>>16&65535;let e=An[1]&2146435072,i=0;return e>=1089470464?An[0]>0?e=31744:(e=(e&2080374784)>>16,i=(An[1]&1048575)>>10):e<=1056964608?(i=1048576+(An[1]&1048575),i=1048576+(i<<(e>>20)-998)>>21,e=0):(e=e-1056964608>>10,i=(An[1]&1048575)+512>>10),t|e|i&65535}class P extends J{}function V(n){return(t,e,i)=>{if(t.setValid(e,i!=null))return n(t,e,i)}}const pu=(n,t,e)=>{n[t]=Math.floor(e/864e5)},rc=(n,t,e,i)=>{if(e+1<t.length){const r=pt(t[e]),s=pt(t[e+1]);n.set(i.subarray(0,s-r),r)}},yu=({offset:n,values:t},e,i)=>{const r=n+e;i?t[r>>3]|=1<<r%8:t[r>>3]&=~(1<<r%8)},Ve=({values:n},t,e)=>{n[t]=e},Ws=({values:n},t,e)=>{n[t]=e},sc=({values:n},t,e)=>{n[t]=fu(e)},mu=(n,t,e)=>{switch(n.type.precision){case xt.HALF:return sc(n,t,e);case xt.SINGLE:case xt.DOUBLE:return Ws(n,t,e)}},oc=({values:n},t,e)=>{pu(n,t,e.valueOf())},ac=({values:n},t,e)=>{n[t]=BigInt(e)},gu=({stride:n,values:t},e,i)=>{t.set(i.subarray(0,n),n*e)},cc=({values:n,valueOffsets:t},e,i)=>rc(n,t,e,i),lc=({values:n,valueOffsets:t},e,i)=>rc(n,t,e,$s(i)),bu=(n,t,e)=>{n.type.unit===le.DAY?oc(n,t,e):ac(n,t,e)},uc=({values:n},t,e)=>{n[t]=BigInt(e/1e3)},hc=({values:n},t,e)=>{n[t]=BigInt(e)},dc=({values:n},t,e)=>{n[t]=BigInt(e*1e3)},fc=({values:n},t,e)=>{n[t]=BigInt(e*1e6)},_u=(n,t,e)=>{switch(n.type.unit){case x.SECOND:return uc(n,t,e);case x.MILLISECOND:return hc(n,t,e);case x.MICROSECOND:return dc(n,t,e);case x.NANOSECOND:return fc(n,t,e)}},pc=({values:n},t,e)=>{n[t]=e},yc=({values:n},t,e)=>{n[t]=e},mc=({values:n},t,e)=>{n[t]=e},gc=({values:n},t,e)=>{n[t]=e},wu=(n,t,e)=>{switch(n.type.unit){case x.SECOND:return pc(n,t,e);case x.MILLISECOND:return yc(n,t,e);case x.MICROSECOND:return mc(n,t,e);case x.NANOSECOND:return gc(n,t,e)}},Iu=({values:n,stride:t},e,i)=>{n.set(i.subarray(0,t),t*e)},vu=(n,t,e)=>{const i=n.children[0],r=n.valueOffsets,s=ue.getVisitFn(i);if(Array.isArray(e))for(let o=-1,a=r[t],c=r[t+1];a<c;)s(i,a++,e[++o]);else for(let o=-1,a=r[t],c=r[t+1];a<c;)s(i,a++,e.get(++o))},Su=(n,t,e)=>{const i=n.children[0],{valueOffsets:r}=n,s=ue.getVisitFn(i);let{[t]:o,[t+1]:a}=r;const c=e instanceof Map?e.entries():Object.entries(e);for(const l of c)if(s(i,o,l),++o>=a)break},Eu=(n,t)=>(e,i,r,s)=>i&&e(i,n,t[s]),Tu=(n,t)=>(e,i,r,s)=>i&&e(i,n,t.get(s)),Au=(n,t)=>(e,i,r,s)=>i&&e(i,n,t.get(r.name)),Fu=(n,t)=>(e,i,r,s)=>i&&e(i,n,t[r.name]),Ou=(n,t,e)=>{const i=n.type.children.map(s=>ue.getVisitFn(s.type)),r=e instanceof Map?Au(t,e):e instanceof ct?Tu(t,e):Array.isArray(e)?Eu(t,e):Fu(t,e);n.type.children.forEach((s,o)=>r(i[o],n.children[o],s,o))},Du=(n,t,e)=>{n.type.mode===Pt.Dense?bc(n,t,e):_c(n,t,e)},bc=(n,t,e)=>{const i=n.type.typeIdToChildIndex[n.typeIds[t]],r=n.children[i];ue.visit(r,n.valueOffsets[t],e)},_c=(n,t,e)=>{const i=n.type.typeIdToChildIndex[n.typeIds[t]],r=n.children[i];ue.visit(r,t,e)},Nu=(n,t,e)=>{var i;(i=n.dictionary)===null||i===void 0||i.set(n.values[t],e)},Bu=(n,t,e)=>{n.type.unit===De.DAY_TIME?wc(n,t,e):Ic(n,t,e)},wc=({values:n},t,e)=>{n.set(e.subarray(0,2),2*t)},Ic=({values:n},t,e)=>{n[t]=e[0]*12+e[1]%12},vc=({values:n},t,e)=>{n[t]=e},Sc=({values:n},t,e)=>{n[t]=e},Ec=({values:n},t,e)=>{n[t]=e},Tc=({values:n},t,e)=>{n[t]=e},Ru=(n,t,e)=>{switch(n.type.unit){case x.SECOND:return vc(n,t,e);case x.MILLISECOND:return Sc(n,t,e);case x.MICROSECOND:return Ec(n,t,e);case x.NANOSECOND:return Tc(n,t,e)}},Mu=(n,t,e)=>{const{stride:i}=n,r=n.children[0],s=ue.getVisitFn(r);if(Array.isArray(e))for(let o=-1,a=t*i;++o<i;)s(r,a+o,e[o]);else for(let o=-1,a=t*i;++o<i;)s(r,a+o,e.get(o))};P.prototype.visitBool=V(yu);P.prototype.visitInt=V(Ve);P.prototype.visitInt8=V(Ve);P.prototype.visitInt16=V(Ve);P.prototype.visitInt32=V(Ve);P.prototype.visitInt64=V(Ve);P.prototype.visitUint8=V(Ve);P.prototype.visitUint16=V(Ve);P.prototype.visitUint32=V(Ve);P.prototype.visitUint64=V(Ve);P.prototype.visitFloat=V(mu);P.prototype.visitFloat16=V(sc);P.prototype.visitFloat32=V(Ws);P.prototype.visitFloat64=V(Ws);P.prototype.visitUtf8=V(lc);P.prototype.visitLargeUtf8=V(lc);P.prototype.visitBinary=V(cc);P.prototype.visitLargeBinary=V(cc);P.prototype.visitFixedSizeBinary=V(gu);P.prototype.visitDate=V(bu);P.prototype.visitDateDay=V(oc);P.prototype.visitDateMillisecond=V(ac);P.prototype.visitTimestamp=V(_u);P.prototype.visitTimestampSecond=V(uc);P.prototype.visitTimestampMillisecond=V(hc);P.prototype.visitTimestampMicrosecond=V(dc);P.prototype.visitTimestampNanosecond=V(fc);P.prototype.visitTime=V(wu);P.prototype.visitTimeSecond=V(pc);P.prototype.visitTimeMillisecond=V(yc);P.prototype.visitTimeMicrosecond=V(mc);P.prototype.visitTimeNanosecond=V(gc);P.prototype.visitDecimal=V(Iu);P.prototype.visitList=V(vu);P.prototype.visitStruct=V(Ou);P.prototype.visitUnion=V(Du);P.prototype.visitDenseUnion=V(bc);P.prototype.visitSparseUnion=V(_c);P.prototype.visitDictionary=V(Nu);P.prototype.visitInterval=V(Bu);P.prototype.visitIntervalDayTime=V(wc);P.prototype.visitIntervalYearMonth=V(Ic);P.prototype.visitDuration=V(Ru);P.prototype.visitDurationSecond=V(vc);P.prototype.visitDurationMillisecond=V(Sc);P.prototype.visitDurationMicrosecond=V(Ec);P.prototype.visitDurationNanosecond=V(Tc);P.prototype.visitFixedSizeList=V(Mu);P.prototype.visitMap=V(Su);const ue=new P,pe=Symbol.for("parent"),jn=Symbol.for("rowIndex");class Ys{constructor(t,e){return this[pe]=t,this[jn]=e,new Proxy(this,new Lu)}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[jn],e=this[pe],i=e.type.children,r={};for(let s=-1,o=i.length;++s<o;)r[i[s].name]=Kt.visit(e.children[s],t);return r}toString(){return`{${[...this].map(([t,e])=>`${vi(t)}: ${vi(e)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new Cu(this[pe],this[jn])}}class Cu{constructor(t,e){this.childIndex=0,this.children=t.children,this.rowIndex=e,this.childFields=t.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const t=this.childIndex;return t<this.numChildren?(this.childIndex=t+1,{done:!1,value:[this.childFields[t].name,Kt.visit(this.children[t],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(Ys.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[pe]:{writable:!0,enumerable:!1,configurable:!1,value:null},[jn]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class Lu{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[pe].type.children.map(e=>e.name)}has(t,e){return t[pe].type.children.findIndex(i=>i.name===e)!==-1}getOwnPropertyDescriptor(t,e){if(t[pe].type.children.findIndex(i=>i.name===e)!==-1)return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const i=t[pe].type.children.findIndex(r=>r.name===e);if(i!==-1){const r=Kt.visit(t[pe].children[i],t[jn]);return Reflect.set(t,e,r),r}}set(t,e,i){const r=t[pe].type.children.findIndex(s=>s.name===e);return r!==-1?(ue.visit(t[pe].children[r],t[jn],i),Reflect.set(t,e,i)):Reflect.has(t,e)||typeof e=="symbol"?Reflect.set(t,e,i):!1}}class M extends J{}function k(n){return(t,e)=>t.getValid(e)?n(t,e):null}const Uu=(n,t)=>864e5*n[t],xu=(n,t)=>null,Ac=(n,t,e)=>{if(e+1>=t.length)return null;const i=pt(t[e]),r=pt(t[e+1]);return n.subarray(i,r)},Pu=({offset:n,values:t},e)=>{const i=n+e;return(t[i>>3]&1<<i%8)!==0},Fc=({values:n},t)=>Uu(n,t),Oc=({values:n},t)=>pt(n[t]),cn=({stride:n,values:t},e)=>t[n*e],ku=({stride:n,values:t},e)=>ic(t[n*e]),Dc=({values:n},t)=>n[t],$u=({stride:n,values:t},e)=>t.subarray(n*e,n*(e+1)),Nc=({values:n,valueOffsets:t},e)=>Ac(n,t,e),Bc=({values:n,valueOffsets:t},e)=>{const i=Ac(n,t,e);return i!==null?Is(i):null},ju=({values:n},t)=>n[t],Vu=({type:n,values:t},e)=>n.precision!==xt.HALF?t[e]:ic(t[e]),zu=(n,t)=>n.type.unit===le.DAY?Fc(n,t):Oc(n,t),Rc=({values:n},t)=>1e3*pt(n[t]),Mc=({values:n},t)=>pt(n[t]),Cc=({values:n},t)=>Ra(n[t],BigInt(1e3)),Lc=({values:n},t)=>Ra(n[t],BigInt(1e6)),Wu=(n,t)=>{switch(n.type.unit){case x.SECOND:return Rc(n,t);case x.MILLISECOND:return Mc(n,t);case x.MICROSECOND:return Cc(n,t);case x.NANOSECOND:return Lc(n,t)}},Uc=({values:n},t)=>n[t],xc=({values:n},t)=>n[t],Pc=({values:n},t)=>n[t],kc=({values:n},t)=>n[t],Yu=(n,t)=>{switch(n.type.unit){case x.SECOND:return Uc(n,t);case x.MILLISECOND:return xc(n,t);case x.MICROSECOND:return Pc(n,t);case x.NANOSECOND:return kc(n,t)}},Gu=({values:n,stride:t},e)=>zs.decimal(n.subarray(t*e,t*(e+1))),qu=(n,t)=>{const{valueOffsets:e,stride:i,children:r}=n,{[t*i]:s,[t*i+1]:o}=e,c=r[0].slice(s,o-s);return new ct([c])},Hu=(n,t)=>{const{valueOffsets:e,children:i}=n,{[t]:r,[t+1]:s}=e,o=i[0];return new Gs(o.slice(r,s-r))},Qu=(n,t)=>new Ys(n,t),Ju=(n,t)=>n.type.mode===Pt.Dense?$c(n,t):jc(n,t),$c=(n,t)=>{const e=n.type.typeIdToChildIndex[n.typeIds[t]],i=n.children[e];return Kt.visit(i,n.valueOffsets[t])},jc=(n,t)=>{const e=n.type.typeIdToChildIndex[n.typeIds[t]],i=n.children[e];return Kt.visit(i,t)},Ku=(n,t)=>{var e;return(e=n.dictionary)===null||e===void 0?void 0:e.get(n.values[t])},Zu=(n,t)=>n.type.unit===De.DAY_TIME?Vc(n,t):zc(n,t),Vc=({values:n},t)=>n.subarray(2*t,2*(t+1)),zc=({values:n},t)=>{const e=n[t],i=new Int32Array(2);return i[0]=Math.trunc(e/12),i[1]=Math.trunc(e%12),i},Wc=({values:n},t)=>n[t],Yc=({values:n},t)=>n[t],Gc=({values:n},t)=>n[t],qc=({values:n},t)=>n[t],Xu=(n,t)=>{switch(n.type.unit){case x.SECOND:return Wc(n,t);case x.MILLISECOND:return Yc(n,t);case x.MICROSECOND:return Gc(n,t);case x.NANOSECOND:return qc(n,t)}},th=(n,t)=>{const{stride:e,children:i}=n,s=i[0].slice(t*e,e);return new ct([s])};M.prototype.visitNull=k(xu);M.prototype.visitBool=k(Pu);M.prototype.visitInt=k(ju);M.prototype.visitInt8=k(cn);M.prototype.visitInt16=k(cn);M.prototype.visitInt32=k(cn);M.prototype.visitInt64=k(Dc);M.prototype.visitUint8=k(cn);M.prototype.visitUint16=k(cn);M.prototype.visitUint32=k(cn);M.prototype.visitUint64=k(Dc);M.prototype.visitFloat=k(Vu);M.prototype.visitFloat16=k(ku);M.prototype.visitFloat32=k(cn);M.prototype.visitFloat64=k(cn);M.prototype.visitUtf8=k(Bc);M.prototype.visitLargeUtf8=k(Bc);M.prototype.visitBinary=k(Nc);M.prototype.visitLargeBinary=k(Nc);M.prototype.visitFixedSizeBinary=k($u);M.prototype.visitDate=k(zu);M.prototype.visitDateDay=k(Fc);M.prototype.visitDateMillisecond=k(Oc);M.prototype.visitTimestamp=k(Wu);M.prototype.visitTimestampSecond=k(Rc);M.prototype.visitTimestampMillisecond=k(Mc);M.prototype.visitTimestampMicrosecond=k(Cc);M.prototype.visitTimestampNanosecond=k(Lc);M.prototype.visitTime=k(Yu);M.prototype.visitTimeSecond=k(Uc);M.prototype.visitTimeMillisecond=k(xc);M.prototype.visitTimeMicrosecond=k(Pc);M.prototype.visitTimeNanosecond=k(kc);M.prototype.visitDecimal=k(Gu);M.prototype.visitList=k(qu);M.prototype.visitStruct=k(Qu);M.prototype.visitUnion=k(Ju);M.prototype.visitDenseUnion=k($c);M.prototype.visitSparseUnion=k(jc);M.prototype.visitDictionary=k(Ku);M.prototype.visitInterval=k(Zu);M.prototype.visitIntervalDayTime=k(Vc);M.prototype.visitIntervalYearMonth=k(zc);M.prototype.visitDuration=k(Xu);M.prototype.visitDurationSecond=k(Wc);M.prototype.visitDurationMillisecond=k(Yc);M.prototype.visitDurationMicrosecond=k(Gc);M.prototype.visitDurationNanosecond=k(qc);M.prototype.visitFixedSizeList=k(th);M.prototype.visitMap=k(Hu);const Kt=new M,Ln=Symbol.for("keys"),Vn=Symbol.for("vals"),Un=Symbol.for("kKeysAsStrings"),Fs=Symbol.for("_kKeysAsStrings");class Gs{constructor(t){return this[Ln]=new ct([t.children[0]]).memoize(),this[Vn]=t.children[1],new Proxy(this,new nh)}get[Un](){return this[Fs]||(this[Fs]=Array.from(this[Ln].toArray(),String))}[Symbol.iterator](){return new eh(this[Ln],this[Vn])}get size(){return this[Ln].length}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Ln],e=this[Vn],i={};for(let r=-1,s=t.length;++r<s;)i[t.get(r)]=Kt.visit(e,r);return i}toString(){return`{${[...this].map(([t,e])=>`${vi(t)}: ${vi(e)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class eh{constructor(t,e){this.keys=t,this.vals=e,this.keyIndex=0,this.numKeys=t.length}[Symbol.iterator](){return this}next(){const t=this.keyIndex;return t===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(t),Kt.visit(this.vals,t)]})}}class nh{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Un]}has(t,e){return t[Un].includes(e)}getOwnPropertyDescriptor(t,e){if(t[Un].indexOf(e)!==-1)return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const i=t[Un].indexOf(e);if(i!==-1){const r=Kt.visit(Reflect.get(t,Vn),i);return Reflect.set(t,e,r),r}}set(t,e,i){const r=t[Un].indexOf(e);return r!==-1?(ue.visit(Reflect.get(t,Vn),r,i),Reflect.set(t,e,i)):Reflect.has(t,e)?Reflect.set(t,e,i):!1}}Object.defineProperties(Gs.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Ln]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Vn]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Fs]:{writable:!0,enumerable:!1,configurable:!1,value:null}});let qo;function Hc(n,t,e,i){const{length:r=0}=n;let s=typeof t!="number"?0:t,o=typeof e!="number"?r:e;return s<0&&(s=(s%r+r)%r),o<0&&(o=(o%r+r)%r),o<s&&(qo=s,s=o,o=qo),o>r&&(o=r),i?i(n,s,o):[s,o]}const qs=(n,t)=>n<0?t+n:n,Ho=n=>n!==n;function ni(n){if(typeof n!=="object"||n===null)return Ho(n)?Ho:e=>e===n;if(n instanceof Date){const e=n.valueOf();return i=>i instanceof Date?i.valueOf()===e:!1}return ArrayBuffer.isView(n)?e=>e?tu(n,e):!1:n instanceof Map?rh(n):Array.isArray(n)?ih(n):n instanceof ct?sh(n):oh(n,!0)}function ih(n){const t=[];for(let e=-1,i=n.length;++e<i;)t[e]=ni(n[e]);return Zr(t)}function rh(n){let t=-1;const e=[];for(const i of n.values())e[++t]=ni(i);return Zr(e)}function sh(n){const t=[];for(let e=-1,i=n.length;++e<i;)t[e]=ni(n.get(e));return Zr(t)}function oh(n,t=!1){const e=Object.keys(n);if(!t&&e.length===0)return()=>!1;const i=[];for(let r=-1,s=e.length;++r<s;)i[r]=ni(n[e[r]]);return Zr(i,e)}function Zr(n,t){return e=>{if(!e||typeof e!="object")return!1;switch(e.constructor){case Array:return ah(n,e);case Map:return Qo(n,e,e.keys());case Gs:case Ys:case Object:case void 0:return Qo(n,e,t||Object.keys(e))}return e instanceof ct?ch(n,e):!1}}function ah(n,t){const e=n.length;if(t.length!==e)return!1;for(let i=-1;++i<e;)if(!n[i](t[i]))return!1;return!0}function ch(n,t){const e=n.length;if(t.length!==e)return!1;for(let i=-1;++i<e;)if(!n[i](t.get(i)))return!1;return!0}function Qo(n,t,e){const i=e[Symbol.iterator](),r=t instanceof Map?t.keys():Object.keys(t)[Symbol.iterator](),s=t instanceof Map?t.values():Object.values(t)[Symbol.iterator]();let o=0;const a=n.length;let c=s.next(),l=i.next(),b=r.next();for(;o<a&&!l.done&&!b.done&&!c.done&&!(l.value!==b.value||!n[o](c.value));++o,l=i.next(),b=r.next(),c=s.next());return o===a&&l.done&&b.done&&c.done?!0:(i.return&&i.return(),r.return&&r.return(),s.return&&s.return(),!1)}function Qc(n,t,e,i){return(e&1<<i)!==0}function lh(n,t,e,i){return(e&1<<i)>>i}function Br(n,t,e){const i=e.byteLength+7&-8;if(n>0||e.byteLength<i){const r=new Uint8Array(i);return r.set(n%8===0?e.subarray(n>>3):Rr(new Hs(e,n,t,null,Qc)).subarray(0,i)),r}return e}function Rr(n){const t=[];let e=0,i=0,r=0;for(const o of n)o&&(r|=1<<i),++i===8&&(t[e++]=r,r=i=0);(e===0||i>0)&&(t[e++]=r);const s=new Uint8Array(t.length+7&-8);return s.set(t),s}class Hs{constructor(t,e,i,r,s){this.bytes=t,this.length=i,this.context=r,this.get=s,this.bit=e%8,this.byteIndex=e>>3,this.byte=t[this.byteIndex++],this.index=0}next(){return this.index<this.length?(this.bit===8&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function Os(n,t,e){if(e-t<=0)return 0;if(e-t<8){let s=0;for(const o of new Hs(n,t,e-t,n,lh))s+=o;return s}const i=e>>3<<3,r=t+(t%8===0?0:8-t%8);return Os(n,t,r)+Os(n,i,e)+uh(n,r>>3,i-r>>3)}function uh(n,t,e){let i=0,r=Math.trunc(t);const s=new DataView(n.buffer,n.byteOffset,n.byteLength),o=e===void 0?n.byteLength:r+e;for(;o-r>=4;)i+=cs(s.getUint32(r)),r+=4;for(;o-r>=2;)i+=cs(s.getUint16(r)),r+=2;for(;o-r>=1;)i+=cs(s.getUint8(r)),r+=1;return i}function cs(n){let t=Math.trunc(n);return t=t-(t>>>1&1431655765),t=(t&858993459)+(t>>>2&858993459),(t+(t>>>4)&252645135)*16843009>>>24}const hh=-1;class ht{get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get nullable(){if(this._nullCount!==0){const{type:t}=this;return D.isSparseUnion(t)?this.children.some(e=>e.nullable):D.isDenseUnion(t)?this.children.some(e=>e.nullable):this.nullBitmap&&this.nullBitmap.byteLength>0}return!0}get byteLength(){let t=0;const{valueOffsets:e,values:i,nullBitmap:r,typeIds:s}=this;return e&&(t+=e.byteLength),i&&(t+=i.byteLength),r&&(t+=r.byteLength),s&&(t+=s.byteLength),this.children.reduce((o,a)=>o+a.byteLength,t)}get nullCount(){if(D.isUnion(this.type))return this.children.reduce((i,r)=>i+r.nullCount,0);let t=this._nullCount,e;return t<=hh&&(e=this.nullBitmap)&&(this._nullCount=t=e.length===0?0:this.length-Os(e,this.offset,this.offset+this.length)),t}constructor(t,e,i,r,s,o=[],a){this.type=t,this.children=o,this.dictionary=a,this.offset=Math.floor(Math.max(e||0,0)),this.length=Math.floor(Math.max(i||0,0)),this._nullCount=Math.floor(Math.max(r||0,-1));let c;s instanceof ht?(this.stride=s.stride,this.values=s.values,this.typeIds=s.typeIds,this.nullBitmap=s.nullBitmap,this.valueOffsets=s.valueOffsets):(this.stride=Ue(t),s&&((c=s[0])&&(this.valueOffsets=c),(c=s[1])&&(this.values=c),(c=s[2])&&(this.nullBitmap=c),(c=s[3])&&(this.typeIds=c)))}getValid(t){const{type:e}=this;if(D.isUnion(e)){const i=e,r=this.children[i.typeIdToChildIndex[this.typeIds[t]]],s=i.mode===Pt.Dense?this.valueOffsets[t]:t;return r.getValid(s)}if(this.nullable&&this.nullCount>0){const i=this.offset+t;return(this.nullBitmap[i>>3]&1<<i%8)!==0}return!0}setValid(t,e){let i;const{type:r}=this;if(D.isUnion(r)){const s=r,o=this.children[s.typeIdToChildIndex[this.typeIds[t]]],a=s.mode===Pt.Dense?this.valueOffsets[t]:t;i=o.getValid(a),o.setValid(a,e)}else{let{nullBitmap:s}=this;const{offset:o,length:a}=this,c=o+t,l=1<<c%8,b=c>>3;(!s||s.byteLength<=b)&&(s=new Uint8Array((o+a+63&-64)>>3).fill(255),this.nullCount>0?(s.set(Br(o,a,this.nullBitmap),0),Object.assign(this,{nullBitmap:s})):Object.assign(this,{nullBitmap:s,_nullCount:0}));const S=s[b];i=(S&l)!==0,s[b]=e?S|l:S&~l}return i!==!!e&&(this._nullCount=this.nullCount+(e?-1:1)),e}clone(t=this.type,e=this.offset,i=this.length,r=this._nullCount,s=this,o=this.children){return new ht(t,e,i,r,s,o,this.dictionary)}slice(t,e){const{stride:i,typeId:r,children:s}=this,o=+(this._nullCount===0)-1,a=r===16?i:1,c=this._sliceBuffers(t,e,i,r);return this.clone(this.type,this.offset+t,e,o,c,s.length===0||this.valueOffsets?s:this._sliceChildren(s,a*t,a*e))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===h.Null)return this.clone(this.type,0,t,0);const{length:e,nullCount:i}=this,r=new Uint8Array((t+63&-64)>>3).fill(255,0,e>>3);r[e>>3]=(1<<e-(e&-8))-1,i>0&&r.set(Br(this.offset,e,this.nullBitmap),0);const s=this.buffers;return s[Le.VALIDITY]=r,this.clone(this.type,0,t,i+(t-e),s)}_sliceBuffers(t,e,i,r){let s;const{buffers:o}=this;return(s=o[Le.TYPE])&&(o[Le.TYPE]=s.subarray(t,t+e)),(s=o[Le.OFFSET])&&(o[Le.OFFSET]=s.subarray(t,t+e+1))||(s=o[Le.DATA])&&(o[Le.DATA]=r===6?s:s.subarray(i*t,i*(t+e))),o}_sliceChildren(t,e,i){return t.map(r=>r.slice(e,i))}}ht.prototype.children=Object.freeze([]);class _i extends J{visit(t){return this.getVisitFn(t.type).call(this,t)}visitNull(t){const{["type"]:e,["offset"]:i=0,["length"]:r=0}=t;return new ht(e,i,r,r)}visitBool(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length>>3,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitInt(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitFloat(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitUtf8(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.data),s=rt(t.nullBitmap),o=li(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ht(e,i,a,c,[o,r,s])}visitLargeUtf8(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.data),s=rt(t.nullBitmap),o=Uo(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ht(e,i,a,c,[o,r,s])}visitBinary(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.data),s=rt(t.nullBitmap),o=li(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ht(e,i,a,c,[o,r,s])}visitLargeBinary(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.data),s=rt(t.nullBitmap),o=Uo(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ht(e,i,a,c,[o,r,s])}visitFixedSizeBinary(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length/Ue(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitDate(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length/Ue(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitTimestamp(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length/Ue(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitTime(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length/Ue(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitDecimal(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length/Ue(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitList(t){const{["type"]:e,["offset"]:i=0,["child"]:r}=t,s=rt(t.nullBitmap),o=li(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ht(e,i,a,c,[o,void 0,s],[r])}visitStruct(t){const{["type"]:e,["offset"]:i=0,["children"]:r=[]}=t,s=rt(t.nullBitmap),{length:o=r.reduce((c,{length:l})=>Math.max(c,l),0),nullCount:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,void 0,s],r)}visitUnion(t){const{["type"]:e,["offset"]:i=0,["children"]:r=[]}=t,s=ft(e.ArrayType,t.typeIds),{["length"]:o=s.length,["nullCount"]:a=-1}=t;if(D.isSparseUnion(e))return new ht(e,i,o,a,[void 0,void 0,void 0,s],r);const c=li(t.valueOffsets);return new ht(e,i,o,a,[c,void 0,void 0,s],r)}visitDictionary(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.indices.ArrayType,t.data),{["dictionary"]:o=new ct([new _i().visit({type:e.dictionary})])}=t,{["length"]:a=s.length,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ht(e,i,a,c,[void 0,s,r],[],o)}visitInterval(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length/Ue(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitDuration(t){const{["type"]:e,["offset"]:i=0}=t,r=rt(t.nullBitmap),s=ft(e.ArrayType,t.data),{["length"]:o=s.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,s,r])}visitFixedSizeList(t){const{["type"]:e,["offset"]:i=0,["child"]:r=new _i().visit({type:e.valueType})}=t,s=rt(t.nullBitmap),{["length"]:o=r.length/Ue(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ht(e,i,o,a,[void 0,void 0,s],[r])}visitMap(t){const{["type"]:e,["offset"]:i=0,["child"]:r=new _i().visit({type:e.childType})}=t,s=rt(t.nullBitmap),o=li(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ht(e,i,a,c,[o,void 0,s],[r])}}const dh=new _i;function X(n){return dh.visit(n)}class Jo{constructor(t=0,e){this.numChunks=t,this.getChunkIterator=e,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const t=this.chunkIterator.next();if(!t.done)return t;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function fh(n){return n.some(t=>t.nullable)}function Jc(n){return n.reduce((t,e)=>t+e.nullCount,0)}function Kc(n){return n.reduce((t,e,i)=>(t[i+1]=t[i]+e.length,t),new Uint32Array(n.length+1))}function Zc(n,t,e,i){const r=[];for(let s=-1,o=n.length;++s<o;){const a=n[s],c=t[s],{length:l}=a;if(c>=i)break;if(e>=c+l)continue;if(c>=e&&c+l<=i){r.push(a);continue}const b=Math.max(0,e-c),S=Math.min(i-c,l);r.push(a.slice(b,S-b))}return r.length===0&&r.push(n[0].slice(0,0)),r}function Qs(n,t,e,i){let r=0,s=0,o=t.length-1;do{if(r>=o-1)return e<t[o]?i(n,r,e-t[r]):null;s=r+Math.trunc((o-r)*.5),e<t[s]?o=s:r=s}while(r<o)}function Js(n,t){return n.getValid(t)}function Mr(n){function t(e,i,r){return n(e[i],r)}return function(e){const i=this.data;return Qs(i,this._offsets,e,t)}}function Xc(n){let t;function e(i,r,s){return n(i[r],s,t)}return function(i,r){const s=this.data;t=r;const o=Qs(s,this._offsets,i,e);return t=void 0,o}}function tl(n){let t;function e(i,r,s){let o=s,a=0,c=0;for(let l=r-1,b=i.length;++l<b;){const S=i[l];if(~(a=n(S,t,o)))return c+a;o=0,c+=S.length}return-1}return function(i,r){t=i;const s=this.data,o=typeof r!="number"?e(s,0,0):Qs(s,this._offsets,r,e);return t=void 0,o}}class C extends J{}function ph(n,t){return t===null&&n.length>0?0:-1}function yh(n,t){const{nullBitmap:e}=n;if(!e||n.nullCount<=0)return-1;let i=0;for(const r of new Hs(e,n.offset+(t||0),n.length,e,Qc)){if(!r)return i;++i}return-1}function z(n,t,e){if(t===void 0)return-1;if(t===null)switch(n.typeId){case h.Union:break;case h.Dictionary:break;default:return yh(n,e)}const i=Kt.getVisitFn(n),r=ni(t);for(let s=(e||0)-1,o=n.length;++s<o;)if(r(i(n,s)))return s;return-1}function el(n,t,e){const i=Kt.getVisitFn(n),r=ni(t);for(let s=(e||0)-1,o=n.length;++s<o;)if(r(i(n,s)))return s;return-1}C.prototype.visitNull=ph;C.prototype.visitBool=z;C.prototype.visitInt=z;C.prototype.visitInt8=z;C.prototype.visitInt16=z;C.prototype.visitInt32=z;C.prototype.visitInt64=z;C.prototype.visitUint8=z;C.prototype.visitUint16=z;C.prototype.visitUint32=z;C.prototype.visitUint64=z;C.prototype.visitFloat=z;C.prototype.visitFloat16=z;C.prototype.visitFloat32=z;C.prototype.visitFloat64=z;C.prototype.visitUtf8=z;C.prototype.visitLargeUtf8=z;C.prototype.visitBinary=z;C.prototype.visitLargeBinary=z;C.prototype.visitFixedSizeBinary=z;C.prototype.visitDate=z;C.prototype.visitDateDay=z;C.prototype.visitDateMillisecond=z;C.prototype.visitTimestamp=z;C.prototype.visitTimestampSecond=z;C.prototype.visitTimestampMillisecond=z;C.prototype.visitTimestampMicrosecond=z;C.prototype.visitTimestampNanosecond=z;C.prototype.visitTime=z;C.prototype.visitTimeSecond=z;C.prototype.visitTimeMillisecond=z;C.prototype.visitTimeMicrosecond=z;C.prototype.visitTimeNanosecond=z;C.prototype.visitDecimal=z;C.prototype.visitList=z;C.prototype.visitStruct=z;C.prototype.visitUnion=z;C.prototype.visitDenseUnion=el;C.prototype.visitSparseUnion=el;C.prototype.visitDictionary=z;C.prototype.visitInterval=z;C.prototype.visitIntervalDayTime=z;C.prototype.visitIntervalYearMonth=z;C.prototype.visitDuration=z;C.prototype.visitDurationSecond=z;C.prototype.visitDurationMillisecond=z;C.prototype.visitDurationMicrosecond=z;C.prototype.visitDurationNanosecond=z;C.prototype.visitFixedSizeList=z;C.prototype.visitMap=z;const Cr=new C;class L extends J{}function $(n){const{type:t}=n;if(n.nullCount===0&&n.stride===1&&(D.isInt(t)&&t.bitWidth!==64||D.isTime(t)&&t.bitWidth!==64||D.isFloat(t)&&t.precision!==xt.HALF))return new Jo(n.data.length,i=>{const r=n.data[i];return r.values.subarray(0,r.length)[Symbol.iterator]()});let e=0;return new Jo(n.data.length,i=>{const s=n.data[i].length,o=n.slice(e,e+s);return e+=s,new mh(o)})}class mh{constructor(t){this.vector=t,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}L.prototype.visitNull=$;L.prototype.visitBool=$;L.prototype.visitInt=$;L.prototype.visitInt8=$;L.prototype.visitInt16=$;L.prototype.visitInt32=$;L.prototype.visitInt64=$;L.prototype.visitUint8=$;L.prototype.visitUint16=$;L.prototype.visitUint32=$;L.prototype.visitUint64=$;L.prototype.visitFloat=$;L.prototype.visitFloat16=$;L.prototype.visitFloat32=$;L.prototype.visitFloat64=$;L.prototype.visitUtf8=$;L.prototype.visitLargeUtf8=$;L.prototype.visitBinary=$;L.prototype.visitLargeBinary=$;L.prototype.visitFixedSizeBinary=$;L.prototype.visitDate=$;L.prototype.visitDateDay=$;L.prototype.visitDateMillisecond=$;L.prototype.visitTimestamp=$;L.prototype.visitTimestampSecond=$;L.prototype.visitTimestampMillisecond=$;L.prototype.visitTimestampMicrosecond=$;L.prototype.visitTimestampNanosecond=$;L.prototype.visitTime=$;L.prototype.visitTimeSecond=$;L.prototype.visitTimeMillisecond=$;L.prototype.visitTimeMicrosecond=$;L.prototype.visitTimeNanosecond=$;L.prototype.visitDecimal=$;L.prototype.visitList=$;L.prototype.visitStruct=$;L.prototype.visitUnion=$;L.prototype.visitDenseUnion=$;L.prototype.visitSparseUnion=$;L.prototype.visitDictionary=$;L.prototype.visitInterval=$;L.prototype.visitIntervalDayTime=$;L.prototype.visitIntervalYearMonth=$;L.prototype.visitDuration=$;L.prototype.visitDurationSecond=$;L.prototype.visitDurationMillisecond=$;L.prototype.visitDurationMicrosecond=$;L.prototype.visitDurationNanosecond=$;L.prototype.visitFixedSizeList=$;L.prototype.visitMap=$;const Ks=new L;var nl;const il={},rl={};class ct{constructor(t){var e,i,r;const s=t[0]instanceof ct?t.flatMap(a=>a.data):t;if(s.length===0||s.some(a=>!(a instanceof ht)))throw new TypeError("Vector constructor expects an Array of Data instances.");const o=(e=s[0])===null||e===void 0?void 0:e.type;switch(s.length){case 0:this._offsets=[0];break;case 1:{const{get:a,set:c,indexOf:l}=il[o.typeId],b=s[0];this.isValid=S=>Js(b,S),this.get=S=>a(b,S),this.set=(S,tt)=>c(b,S,tt),this.indexOf=S=>l(b,S),this._offsets=[0,b.length];break}default:Object.setPrototypeOf(this,rl[o.typeId]),this._offsets=Kc(s);break}this.data=s,this.type=o,this.stride=Ue(o),this.numChildren=(r=(i=o.children)===null||i===void 0?void 0:i.length)!==null&&r!==void 0?r:0,this.length=this._offsets.at(-1)}get byteLength(){return this.data.reduce((t,e)=>t+e.byteLength,0)}get nullable(){return fh(this.data)}get nullCount(){return Jc(this.data)}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${h[this.type.typeId]}Vector`}isValid(t){return!1}get(t){return null}at(t){return this.get(qs(t,this.length))}set(t,e){}indexOf(t,e){return-1}includes(t,e){return this.indexOf(t,e)>-1}[Symbol.iterator](){return Ks.visit(this)}concat(...t){return new ct(this.data.concat(t.flatMap(e=>e.data).flat(Number.POSITIVE_INFINITY)))}slice(t,e){return new ct(Hc(this,t,e,({data:i,_offsets:r},s,o)=>Zc(i,r,s,o)))}toJSON(){return[...this]}toArray(){const{type:t,data:e,length:i,stride:r,ArrayType:s}=this;switch(t.typeId){case h.Int:case h.Float:case h.Decimal:case h.Time:case h.Timestamp:switch(e.length){case 0:return new s;case 1:return e[0].values.subarray(0,i*r);default:return e.reduce((o,{values:a,length:c})=>(o.array.set(a.subarray(0,c*r),o.offset),o.offset+=c*r,o),{array:new s(i*r),offset:0}).array}}return[...this]}toString(){return`[${[...this].join(",")}]`}getChild(t){var e;return this.getChildAt((e=this.type.children)===null||e===void 0?void 0:e.findIndex(i=>i.name===t))}getChildAt(t){return t>-1&&t<this.numChildren?new ct(this.data.map(({children:e})=>e[t])):null}get isMemoized(){return D.isDictionary(this.type)?this.data[0].dictionary.isMemoized:!1}memoize(){if(D.isDictionary(this.type)){const t=new Lr(this.data[0].dictionary),e=this.data.map(i=>{const r=i.clone();return r.dictionary=t,r});return new ct(e)}return new Lr(this)}unmemoize(){if(D.isDictionary(this.type)&&this.isMemoized){const t=this.data[0].dictionary.unmemoize(),e=this.data.map(i=>{const r=i.clone();return r.dictionary=t,r});return new ct(e)}return this}}nl=Symbol.toStringTag;ct[nl]=(n=>{n.type=D.prototype,n.data=[],n.length=0,n.stride=1,n.numChildren=0,n._offsets=new Uint32Array([0]),n[Symbol.isConcatSpreadable]=!0;const t=Object.keys(h).map(e=>h[e]).filter(e=>typeof e=="number"&&e!==h.NONE);for(const e of t){const i=Kt.getVisitFnByTypeId(e),r=ue.getVisitFnByTypeId(e),s=Cr.getVisitFnByTypeId(e);il[e]={get:i,set:r,indexOf:s},rl[e]=Object.create(n,{isValid:{value:Mr(Js)},get:{value:Mr(Kt.getVisitFnByTypeId(e))},set:{value:Xc(ue.getVisitFnByTypeId(e))},indexOf:{value:tl(Cr.getVisitFnByTypeId(e))}})}return"Vector"})(ct.prototype);class Lr extends ct{constructor(t){super(t.data);const e=this.get,i=this.set,r=this.slice,s=new Array(this.length);Object.defineProperty(this,"get",{value(o){const a=s[o];if(a!==void 0)return a;const c=e.call(this,o);return s[o]=c,c}}),Object.defineProperty(this,"set",{value(o,a){i.call(this,o,a),s[o]=a}}),Object.defineProperty(this,"slice",{value:(o,a)=>new Lr(r.call(this,o,a))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new ct(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Ds{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(t,e,i,r){return t.prep(8,24),t.writeInt64(BigInt(r??0)),t.pad(4),t.writeInt32(i),t.writeInt64(BigInt(e??0)),t.offset()}}class Xt{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFooter(t,e){return(e||new Xt).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFooter(t,e){return t.setPosition(t.position()+ut),(e||new Xt).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Dt.V1}schema(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Ie).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}dictionaries(t,e){const i=this.bb.__offset(this.bb_pos,8);return i?(e||new Ds).__init(this.bb.__vector(this.bb_pos+i)+t*24,this.bb):null}dictionariesLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(t,e){const i=this.bb.__offset(this.bb_pos,10);return i?(e||new Ds).__init(this.bb.__vector(this.bb_pos+i)+t*24,this.bb):null}recordBatchesLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const i=this.bb.__offset(this.bb_pos,12);return i?(e||new Mt).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Dt.V1)}static addSchema(t,e){t.addFieldOffset(1,e,0)}static addDictionaries(t,e){t.addFieldOffset(2,e,0)}static startDictionariesVector(t,e){t.startVector(24,e,8)}static addRecordBatches(t,e){t.addFieldOffset(3,e,0)}static startRecordBatchesVector(t,e){t.startVector(24,e,8)}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endFooter(t){return t.endObject()}static finishFooterBuffer(t,e){t.finish(e)}static finishSizePrefixedFooterBuffer(t,e){t.finish(e,void 0,!0)}}class lt{constructor(t=[],e,i,r=Dt.V5){this.fields=t||[],this.metadata=e||new Map,i||(i=Ns(this.fields)),this.dictionaries=i,this.metadataVersion=r}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map(t=>t.name)}toString(){return`Schema<{ ${this.fields.map((t,e)=>`${e}: ${t}`).join(", ")} }>`}select(t){const e=new Set(t),i=this.fields.filter(r=>e.has(r.name));return new lt(i,this.metadata)}selectAt(t){const e=t.map(i=>this.fields[i]).filter(Boolean);return new lt(e,this.metadata)}assign(...t){const e=t[0]instanceof lt?t[0]:Array.isArray(t[0])?new lt(t[0]):new lt(t),i=[...this.fields],r=Ki(Ki(new Map,this.metadata),e.metadata),s=e.fields.filter(a=>{const c=i.findIndex(l=>l.name===a.name);return~c?(i[c]=a.clone({metadata:Ki(Ki(new Map,i[c].metadata),a.metadata)}))&&!1:!0}),o=Ns(s,new Map);return new lt([...i,...s],r,new Map([...this.dictionaries,...o]))}}lt.prototype.fields=null;lt.prototype.metadata=null;lt.prototype.dictionaries=null;class _t{static new(...t){let[e,i,r,s]=t;return t[0]&&typeof t[0]=="object"&&({name:e}=t[0],i===void 0&&(i=t[0].type),r===void 0&&(r=t[0].nullable),s===void 0&&(s=t[0].metadata)),new _t(`${e}`,i,r,s)}constructor(t,e,i=!1,r){this.name=t,this.type=e,this.nullable=i,this.metadata=r||new Map}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(...t){let[e,i,r,s]=t;return!t[0]||typeof t[0]!="object"?[e=this.name,i=this.type,r=this.nullable,s=this.metadata]=t:{name:e=this.name,type:i=this.type,nullable:r=this.nullable,metadata:s=this.metadata}=t[0],_t.new(e,i,r,s)}}_t.prototype.type=null;_t.prototype.name=null;_t.prototype.nullable=null;_t.prototype.metadata=null;function Ki(n,t){return new Map([...n||new Map,...t||new Map])}function Ns(n,t=new Map){for(let e=-1,i=n.length;++e<i;){const s=n[e].type;if(D.isDictionary(s)){if(!t.has(s.id))t.set(s.id,s.dictionary);else if(t.get(s.id)!==s.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}s.children&&s.children.length>0&&Ns(s.children,t)}return t}var gh=Oa,bh=Jn;class Ai{static decode(t){t=new bh(rt(t));const e=Xt.getRootAsFooter(t),i=lt.decode(e.schema(),new Map,e.version());return new _h(i,e)}static encode(t){const e=new gh,i=lt.encode(e,t.schema);Xt.startRecordBatchesVector(e,t.numRecordBatches);for(const o of[...t.recordBatches()].slice().reverse())sn.encode(e,o);const r=e.endVector();Xt.startDictionariesVector(e,t.numDictionaries);for(const o of[...t.dictionaryBatches()].slice().reverse())sn.encode(e,o);const s=e.endVector();return Xt.startFooter(e),Xt.addSchema(e,i),Xt.addVersion(e,Dt.V5),Xt.addRecordBatches(e,r),Xt.addDictionaries(e,s),Xt.finishFooterBuffer(e,Xt.endFooter(e)),e.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}constructor(t,e=Dt.V5,i,r){this.schema=t,this.version=e,i&&(this._recordBatches=i),r&&(this._dictionaryBatches=r)}*recordBatches(){for(let t,e=-1,i=this.numRecordBatches;++e<i;)(t=this.getRecordBatch(e))&&(yield t)}*dictionaryBatches(){for(let t,e=-1,i=this.numDictionaries;++e<i;)(t=this.getDictionaryBatch(e))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class _h extends Ai{get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}constructor(t,e){super(t,e.version()),this._footer=e}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const e=this._footer.recordBatches(t);if(e)return sn.decode(e)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const e=this._footer.dictionaries(t);if(e)return sn.decode(e)}return null}}class sn{static decode(t){return new sn(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,e){const{metaDataLength:i}=e,r=BigInt(e.offset),s=BigInt(e.bodyLength);return Ds.createBlock(t,r,i,s)}constructor(t,e,i){this.metaDataLength=t,this.offset=pt(i),this.bodyLength=pt(e)}}const Et=Object.freeze({done:!0,value:void 0});class Ko{constructor(t){this._json=t}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class Zs{tee(){return this._getDOMStream().tee()}pipe(t,e){return this._getNodeStream().pipe(t,e)}pipeTo(t,e){return this._getDOMStream().pipeTo(t,e)}pipeThrough(t,e){return this._getDOMStream().pipeThrough(t,e)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class wh extends Zs{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise(t=>this._closedPromiseResolve=t)}get closed(){return this._closedPromise}cancel(t){return Y(this,void 0,void 0,function*(){yield this.return(t)})}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;for(;t.length>0;)t.shift().resolve(Et);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return se.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return se.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}throw(t){return Y(this,void 0,void 0,function*(){return yield this.abort(t),Et})}return(t){return Y(this,void 0,void 0,function*(){return yield this.close(),Et})}read(t){return Y(this,void 0,void 0,function*(){return(yield this.next(t,"read")).value})}peek(t){return Y(this,void 0,void 0,function*(){return(yield this.next(t,"peek")).value})}next(...t){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise((e,i)=>{this.resolvers.push({resolve:e,reject:i})}):Promise.resolve(Et)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class or extends wh{write(t){if((t=rt(t)).byteLength>0)return super.write(t)}toString(t=!1){return t?Is(this.toUint8Array(!0)):this.toUint8Array(!1).then(Is)}toUint8Array(t=!1){return t?Oe(this._values)[0]:Y(this,void 0,void 0,function*(){var e,i,r,s;const o=[];let a=0;try{for(var c=!0,l=In(this),b;b=yield l.next(),e=b.done,!e;c=!0){s=b.value,c=!1;const S=s;o.push(S),a+=S.byteLength}}catch(S){i={error:S}}finally{try{!c&&!e&&(r=l.return)&&(yield r.call(l))}finally{if(i)throw i.error}}return Oe(o,a)[0]})}}class Ur{constructor(t){t&&(this.source=new Ih(se.fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class Xn{constructor(t){t instanceof Xn?this.source=t.source:t instanceof or?this.source=new un(se.fromAsyncIterable(t)):Ea(t)?this.source=new un(se.fromNodeStream(t)):js(t)?this.source=new un(se.fromDOMStream(t)):Sa(t)?this.source=new un(se.fromDOMStream(t.body)):$i(t)?this.source=new un(se.fromIterable(t)):Sn(t)?this.source=new un(se.fromAsyncIterable(t)):ei(t)&&(this.source=new un(se.fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class Ih{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t,e="read"){return this.source.next({cmd:e,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||Et)}return(t){return Object.create(this.source.return&&this.source.return(t)||Et)}}class un{constructor(t){this.source=t,this._closedPromise=new Promise(e=>this._closedPromiseResolve=e)}cancel(t){return Y(this,void 0,void 0,function*(){yield this.return(t)})}get closed(){return this._closedPromise}read(t){return Y(this,void 0,void 0,function*(){return(yield this.next(t,"read")).value})}peek(t){return Y(this,void 0,void 0,function*(){return(yield this.next(t,"peek")).value})}next(t){return Y(this,arguments,void 0,function*(e,i="read"){return yield this.source.next({cmd:i,size:e})})}throw(t){return Y(this,void 0,void 0,function*(){const e=this.source.throw&&(yield this.source.throw(t))||Et;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)})}return(t){return Y(this,void 0,void 0,function*(){const e=this.source.return&&(yield this.source.return(t))||Et;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)})}}class Zo extends Ur{constructor(t,e){super(),this.position=0,this.buffer=rt(t),this.size=e===void 0?this.buffer.byteLength:e}readInt32(t){const{buffer:e,byteOffset:i}=this.readAt(t,4);return new DataView(e,i).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:e,size:i,position:r}=this;return e&&r<i?(typeof t!="number"&&(t=Number.POSITIVE_INFINITY),this.position=Math.min(i,r+Math.min(i-r,t)),e.subarray(r,this.position)):null}readAt(t,e){const i=this.buffer,r=Math.min(this.size,t+e);return i?i.subarray(t,r):new Uint8Array(e)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class xr extends Xn{constructor(t,e){super(),this.position=0,this._handle=t,typeof e=="number"?this.size=e:this._pending=Y(this,void 0,void 0,function*(){this.size=(yield t.stat()).size,delete this._pending})}readInt32(t){return Y(this,void 0,void 0,function*(){const{buffer:e,byteOffset:i}=yield this.readAt(t,4);return new DataView(e,i).getInt32(0,!0)})}seek(t){return Y(this,void 0,void 0,function*(){return this._pending&&(yield this._pending),this.position=Math.min(t,this.size),t<this.size})}read(t){return Y(this,void 0,void 0,function*(){this._pending&&(yield this._pending);const{_handle:e,size:i,position:r}=this;if(e&&r<i){typeof t!="number"&&(t=Number.POSITIVE_INFINITY);let s=r,o=0,a=0;const c=Math.min(i,s+Math.min(i-s,t)),l=new Uint8Array(Math.max(0,(this.position=c)-s));for(;(s+=a)<c&&(o+=a)<l.byteLength;)({bytesRead:a}=yield e.read(l,o,l.byteLength-o,s));return l}return null})}readAt(t,e){return Y(this,void 0,void 0,function*(){this._pending&&(yield this._pending);const{_handle:i,size:r}=this;if(i&&t+e<r){const s=Math.min(r,t+e),o=new Uint8Array(s-t);return(yield i.read(o,0,e,t)).buffer}return new Uint8Array(e)})}close(){return Y(this,void 0,void 0,function*(){const t=this._handle;this._handle=null,t&&(yield t.close())})}throw(t){return Y(this,void 0,void 0,function*(){return yield this.close(),{done:!0,value:t}})}return(t){return Y(this,void 0,void 0,function*(){return yield this.close(),{done:!0,value:t}})}}const vh=65536;function Pn(n){return n<0&&(n=4294967295+n+1),`0x${n.toString(16)}`}const ti=8,Xs=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class sl{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const e=new Uint32Array([this.buffer[1]>>>16,this.buffer[1]&65535,this.buffer[0]>>>16,this.buffer[0]&65535]),i=new Uint32Array([t.buffer[1]>>>16,t.buffer[1]&65535,t.buffer[0]>>>16,t.buffer[0]&65535]);let r=e[3]*i[3];this.buffer[0]=r&65535;let s=r>>>16;return r=e[2]*i[3],s+=r,r=e[3]*i[2]>>>0,s+=r,this.buffer[0]+=s<<16,this.buffer[1]=s>>>0<r?vh:0,this.buffer[1]+=s>>>16,this.buffer[1]+=e[1]*i[3]+e[2]*i[2]+e[3]*i[1],this.buffer[1]+=e[0]*i[3]+e[1]*i[2]+e[2]*i[1]+e[3]*i[0]<<16,this}_plus(t){const e=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],e<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=e}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return`${Pn(this.buffer[1])} ${Pn(this.buffer[0])}`}}class dt extends sl{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t,e=new Uint32Array(2)){return dt.fromString(typeof t=="string"?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return dt.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const i=t.length,r=new dt(e);for(let s=0;s<i;){const o=ti<i-s?ti:i-s,a=new dt(new Uint32Array([Number.parseInt(t.slice(s,s+o),10),0])),c=new dt(new Uint32Array([Xs[o],0]));r.times(c),r.plus(a),s+=o}return r}static convertArray(t){const e=new Uint32Array(t.length*2);for(let i=-1,r=t.length;++i<r;)dt.from(t[i],new Uint32Array(e.buffer,e.byteOffset+2*i*4,2));return e}static multiply(t,e){return new dt(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new dt(new Uint32Array(t.buffer)).plus(e)}}class qt extends sl{negate(){return this.buffer[0]=~this.buffer[0]+1,this.buffer[1]=~this.buffer[1],this.buffer[0]==0&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const e=this.buffer[1]<<0,i=t.buffer[1]<<0;return e<i||e===i&&this.buffer[0]<t.buffer[0]}static from(t,e=new Uint32Array(2)){return qt.fromString(typeof t=="string"?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return qt.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const i=t.startsWith("-"),r=t.length,s=new qt(e);for(let o=i?1:0;o<r;){const a=ti<r-o?ti:r-o,c=new qt(new Uint32Array([Number.parseInt(t.slice(o,o+a),10),0])),l=new qt(new Uint32Array([Xs[a],0]));s.times(l),s.plus(c),o+=a}return i?s.negate():s}static convertArray(t){const e=new Uint32Array(t.length*2);for(let i=-1,r=t.length;++i<r;)qt.from(t[i],new Uint32Array(e.buffer,e.byteOffset+2*i*4,2));return e}static multiply(t,e){return new qt(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new qt(new Uint32Array(t.buffer)).plus(e)}}class ve{constructor(t){this.buffer=t}high(){return new qt(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new qt(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=~this.buffer[0]+1,this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],this.buffer[0]==0&&++this.buffer[1],this.buffer[1]==0&&++this.buffer[2],this.buffer[2]==0&&++this.buffer[3],this}times(t){const e=new dt(new Uint32Array([this.buffer[3],0])),i=new dt(new Uint32Array([this.buffer[2],0])),r=new dt(new Uint32Array([this.buffer[1],0])),s=new dt(new Uint32Array([this.buffer[0],0])),o=new dt(new Uint32Array([t.buffer[3],0])),a=new dt(new Uint32Array([t.buffer[2],0])),c=new dt(new Uint32Array([t.buffer[1],0])),l=new dt(new Uint32Array([t.buffer[0],0]));let b=dt.multiply(s,l);this.buffer[0]=b.low();const S=new dt(new Uint32Array([b.high(),0]));return b=dt.multiply(r,l),S.plus(b),b=dt.multiply(s,c),S.plus(b),this.buffer[1]=S.low(),this.buffer[3]=S.lessThan(b)?1:0,this.buffer[2]=S.high(),new dt(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(dt.multiply(i,l)).plus(dt.multiply(r,c)).plus(dt.multiply(s,a)),this.buffer[3]+=dt.multiply(e,l).plus(dt.multiply(i,c)).plus(dt.multiply(r,a)).plus(dt.multiply(s,o)).low(),this}plus(t){const e=new Uint32Array(4);return e[3]=this.buffer[3]+t.buffer[3]>>>0,e[2]=this.buffer[2]+t.buffer[2]>>>0,e[1]=this.buffer[1]+t.buffer[1]>>>0,e[0]=this.buffer[0]+t.buffer[0]>>>0,e[0]<this.buffer[0]>>>0&&++e[1],e[1]<this.buffer[1]>>>0&&++e[2],e[2]<this.buffer[2]>>>0&&++e[3],this.buffer[3]=e[3],this.buffer[2]=e[2],this.buffer[1]=e[1],this.buffer[0]=e[0],this}hex(){return`${Pn(this.buffer[3])} ${Pn(this.buffer[2])} ${Pn(this.buffer[1])} ${Pn(this.buffer[0])}`}static multiply(t,e){return new ve(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new ve(new Uint32Array(t.buffer)).plus(e)}static from(t,e=new Uint32Array(4)){return ve.fromString(typeof t=="string"?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(4)){return ve.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(4)){const i=t.startsWith("-"),r=t.length,s=new ve(e);for(let o=i?1:0;o<r;){const a=ti<r-o?ti:r-o,c=new ve(new Uint32Array([Number.parseInt(t.slice(o,o+a),10),0,0,0])),l=new ve(new Uint32Array([Xs[a],0,0,0]));s.times(l),s.plus(c),o+=a}return i?s.negate():s}static convertArray(t){const e=new Uint32Array(t.length*4);for(let i=-1,r=t.length;++i<r;)ve.from(t[i],new Uint32Array(e.buffer,e.byteOffset+4*4*i,4));return e}}class ol extends J{constructor(t,e,i,r,s=Dt.V5){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=e,this.buffers=i,this.dictionaries=r,this.metadataVersion=s}visit(t){return super.visit(t instanceof _t?t.type:t)}visitNull(t,{length:e}=this.nextFieldNode()){return X({type:t,length:e})}visitBool(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitInt(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitFloat(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitUtf8(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeUtf8(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitBinary(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeBinary(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitFixedSizeBinary(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitDate(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitTimestamp(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitTime(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitDecimal(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitList(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}visitStruct(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),children:this.visitMany(t.children)})}visitUnion(t,{length:e,nullCount:i}=this.nextFieldNode()){return this.metadataVersion<Dt.V5&&this.readNullBitmap(t,i),t.mode===Pt.Sparse?this.visitSparseUnion(t,{length:e,nullCount:i}):this.visitDenseUnion(t,{length:e,nullCount:i})}visitDenseUnion(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,typeIds:this.readTypeIds(t),valueOffsets:this.readOffsets(t),children:this.visitMany(t.children)})}visitSparseUnion(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,typeIds:this.readTypeIds(t),children:this.visitMany(t.children)})}visitDictionary(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t.indices),dictionary:this.readDictionary(t)})}visitInterval(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitDuration(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitFixedSizeList(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),child:this.visit(t.children[0])})}visitMap(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,e,i=this.nextBufferRange()){return e>0&&this.readData(t,i)||new Uint8Array(0)}readOffsets(t,e){return this.readData(t,e)}readTypeIds(t,e){return this.readData(t,e)}readData(t,{length:e,offset:i}=this.nextBufferRange()){return this.bytes.subarray(i,i+e)}readDictionary(t){return this.dictionaries.get(t.id)}}class Sh extends ol{constructor(t,e,i,r,s){super(new Uint8Array(0),e,i,r,s),this.sources=t}readNullBitmap(t,e,{offset:i}=this.nextBufferRange()){return e<=0?new Uint8Array(0):Rr(this.sources[i])}readOffsets(t,{offset:e}=this.nextBufferRange()){return ft(Uint8Array,ft(t.OffsetArrayType,this.sources[e]))}readTypeIds(t,{offset:e}=this.nextBufferRange()){return ft(Uint8Array,ft(t.ArrayType,this.sources[e]))}readData(t,{offset:e}=this.nextBufferRange()){const{sources:i}=this;return D.isTimestamp(t)||(D.isInt(t)||D.isTime(t))&&t.bitWidth===64||D.isDuration(t)||D.isDate(t)&&t.unit===le.MILLISECOND?ft(Uint8Array,qt.convertArray(i[e])):D.isDecimal(t)?ft(Uint8Array,ve.convertArray(i[e])):D.isBinary(t)||D.isLargeBinary(t)||D.isFixedSizeBinary(t)?Eh(i[e]):D.isBool(t)?Rr(i[e]):D.isUtf8(t)||D.isLargeUtf8(t)?$s(i[e].join("")):ft(Uint8Array,ft(t.ArrayType,i[e].map(r=>+r)))}}function Eh(n){const t=n.join(""),e=new Uint8Array(t.length/2);for(let i=0;i<t.length;i+=2)e[i>>1]=Number.parseInt(t.slice(i,i+2),16);return e}class U extends J{compareSchemas(t,e){return t===e||e instanceof t.constructor&&this.compareManyFields(t.fields,e.fields)}compareManyFields(t,e){return t===e||Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every((i,r)=>this.compareFields(i,e[r]))}compareFields(t,e){return t===e||e instanceof t.constructor&&t.name===e.name&&t.nullable===e.nullable&&this.visit(t.type,e.type)}}function Yt(n,t){return t instanceof n.constructor}function Tn(n,t){return n===t||Yt(n,t)}function ze(n,t){return n===t||Yt(n,t)&&n.bitWidth===t.bitWidth&&n.isSigned===t.isSigned}function Xr(n,t){return n===t||Yt(n,t)&&n.precision===t.precision}function Th(n,t){return n===t||Yt(n,t)&&n.byteWidth===t.byteWidth}function to(n,t){return n===t||Yt(n,t)&&n.unit===t.unit}function ji(n,t){return n===t||Yt(n,t)&&n.unit===t.unit&&n.timezone===t.timezone}function Vi(n,t){return n===t||Yt(n,t)&&n.unit===t.unit&&n.bitWidth===t.bitWidth}function Ah(n,t){return n===t||Yt(n,t)&&n.children.length===t.children.length&&on.compareManyFields(n.children,t.children)}function Fh(n,t){return n===t||Yt(n,t)&&n.children.length===t.children.length&&on.compareManyFields(n.children,t.children)}function eo(n,t){return n===t||Yt(n,t)&&n.mode===t.mode&&n.typeIds.every((e,i)=>e===t.typeIds[i])&&on.compareManyFields(n.children,t.children)}function Oh(n,t){return n===t||Yt(n,t)&&n.id===t.id&&n.isOrdered===t.isOrdered&&on.visit(n.indices,t.indices)&&on.visit(n.dictionary,t.dictionary)}function no(n,t){return n===t||Yt(n,t)&&n.unit===t.unit}function zi(n,t){return n===t||Yt(n,t)&&n.unit===t.unit}function Dh(n,t){return n===t||Yt(n,t)&&n.listSize===t.listSize&&n.children.length===t.children.length&&on.compareManyFields(n.children,t.children)}function Nh(n,t){return n===t||Yt(n,t)&&n.keysSorted===t.keysSorted&&n.children.length===t.children.length&&on.compareManyFields(n.children,t.children)}U.prototype.visitNull=Tn;U.prototype.visitBool=Tn;U.prototype.visitInt=ze;U.prototype.visitInt8=ze;U.prototype.visitInt16=ze;U.prototype.visitInt32=ze;U.prototype.visitInt64=ze;U.prototype.visitUint8=ze;U.prototype.visitUint16=ze;U.prototype.visitUint32=ze;U.prototype.visitUint64=ze;U.prototype.visitFloat=Xr;U.prototype.visitFloat16=Xr;U.prototype.visitFloat32=Xr;U.prototype.visitFloat64=Xr;U.prototype.visitUtf8=Tn;U.prototype.visitLargeUtf8=Tn;U.prototype.visitBinary=Tn;U.prototype.visitLargeBinary=Tn;U.prototype.visitFixedSizeBinary=Th;U.prototype.visitDate=to;U.prototype.visitDateDay=to;U.prototype.visitDateMillisecond=to;U.prototype.visitTimestamp=ji;U.prototype.visitTimestampSecond=ji;U.prototype.visitTimestampMillisecond=ji;U.prototype.visitTimestampMicrosecond=ji;U.prototype.visitTimestampNanosecond=ji;U.prototype.visitTime=Vi;U.prototype.visitTimeSecond=Vi;U.prototype.visitTimeMillisecond=Vi;U.prototype.visitTimeMicrosecond=Vi;U.prototype.visitTimeNanosecond=Vi;U.prototype.visitDecimal=Tn;U.prototype.visitList=Ah;U.prototype.visitStruct=Fh;U.prototype.visitUnion=eo;U.prototype.visitDenseUnion=eo;U.prototype.visitSparseUnion=eo;U.prototype.visitDictionary=Oh;U.prototype.visitInterval=no;U.prototype.visitIntervalDayTime=no;U.prototype.visitIntervalYearMonth=no;U.prototype.visitDuration=zi;U.prototype.visitDurationSecond=zi;U.prototype.visitDurationMillisecond=zi;U.prototype.visitDurationMicrosecond=zi;U.prototype.visitDurationNanosecond=zi;U.prototype.visitFixedSizeList=Dh;U.prototype.visitMap=Nh;const on=new U;function Bs(n,t){return on.compareSchemas(n,t)}function ls(n,t){return Bh(n,t.map(e=>e.data.concat()))}function Bh(n,t){const e=[...n.fields],i=[],r={numBatches:t.reduce((S,tt)=>Math.max(S,tt.length),0)};let s=0,o=0,a=-1;const c=t.length;let l,b=[];for(;r.numBatches-- >0;){for(o=Number.POSITIVE_INFINITY,a=-1;++a<c;)b[a]=l=t[a].shift(),o=Math.min(o,l?l.length:o);Number.isFinite(o)&&(b=Rh(e,o,b,t,r),o>0&&(i[s++]=X({type:new zt(e),length:o,nullCount:0,children:b.slice()})))}return[n=n.assign(e),i.map(S=>new Qt(n,S))]}function Rh(n,t,e,i,r){var s;const o=(t+63&-64)>>3;for(let a=-1,c=i.length;++a<c;){const l=e[a],b=l==null?void 0:l.length;if(b>=t)b===t?e[a]=l:(e[a]=l.slice(0,t),r.numBatches=Math.max(r.numBatches,i[a].unshift(l.slice(t,b-t))));else{const S=n[a];n[a]=S.clone({nullable:!0}),e[a]=(s=l==null?void 0:l._changeLengthAndBackfillNullBitmap(t))!==null&&s!==void 0?s:X({type:S.type,length:t,nullCount:t,nullBitmap:new Uint8Array(o)})}}return e}var al;class Vt{constructor(...t){var e,i;if(t.length===0)return this.batches=[],this.schema=new lt([]),this._offsets=[0],this;let r,s;t[0]instanceof lt&&(r=t.shift()),t.at(-1)instanceof Uint32Array&&(s=t.pop());const o=c=>{if(c){if(c instanceof Qt)return[c];if(c instanceof Vt)return c.batches;if(c instanceof ht){if(c.type instanceof zt)return[new Qt(new lt(c.type.children),c)]}else{if(Array.isArray(c))return c.flatMap(l=>o(l));if(typeof c[Symbol.iterator]=="function")return[...c].flatMap(l=>o(l));if(typeof c=="object"){const l=Object.keys(c),b=l.map(O=>new ct([c[O]])),S=r??new lt(l.map((O,W)=>new _t(String(O),b[W].type,b[W].nullable))),[,tt]=ls(S,b);return tt.length===0?[new Qt(c)]:tt}}}return[]},a=t.flatMap(c=>o(c));if(r=(i=r??((e=a[0])===null||e===void 0?void 0:e.schema))!==null&&i!==void 0?i:new lt([]),!(r instanceof lt))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const c of a){if(!(c instanceof Qt))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!Bs(r,c.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=r,this.batches=a,this._offsets=s??Kc(this.data)}get data(){return this.batches.map(({data:t})=>t)}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce((t,e)=>t+e.length,0)}get nullCount(){return this._nullCount===-1&&(this._nullCount=Jc(this.data)),this._nullCount}isValid(t){return!1}get(t){return null}at(t){return this.get(qs(t,this.numRows))}set(t,e){}indexOf(t,e){return-1}[Symbol.iterator](){return this.batches.length>0?Ks.visit(new ct(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return`[
  ${this.toArray().join(`,
  `)}
]`}concat(...t){const e=this.schema,i=this.data.concat(t.flatMap(({data:r})=>r));return new Vt(e,i.map(r=>new Qt(e,r)))}slice(t,e){const i=this.schema;[t,e]=Hc({length:this.numRows},t,e);const r=Zc(this.data,this._offsets,t,e);return new Vt(i,r.map(s=>new Qt(i,s)))}getChild(t){return this.getChildAt(this.schema.fields.findIndex(e=>e.name===t))}getChildAt(t){if(t>-1&&t<this.schema.fields.length){const e=this.data.map(i=>i.children[t]);if(e.length===0){const{type:i}=this.schema.fields[t],r=X({type:i,length:0,nullCount:0});e.push(r._changeLengthAndBackfillNullBitmap(this.numRows))}return new ct(e)}return null}setChild(t,e){var i;return this.setChildAt((i=this.schema.fields)===null||i===void 0?void 0:i.findIndex(r=>r.name===t),e)}setChildAt(t,e){let i=this.schema,r=[...this.batches];if(t>-1&&t<this.numCols){e||(e=new ct([X({type:new rn,length:this.numRows})]));const s=i.fields.slice(),o=s[t].clone({type:e.type}),a=this.schema.fields.map((c,l)=>this.getChildAt(l));[s[t],a[t]]=[o,e],[i,r]=ls(i,a)}return new Vt(i,r)}select(t){const e=this.schema.fields.reduce((i,r,s)=>i.set(r.name,s),new Map);return this.selectAt(t.map(i=>e.get(i)).filter(i=>i>-1))}selectAt(t){const e=this.schema.selectAt(t),i=this.batches.map(r=>r.selectAt(t));return new Vt(e,i)}assign(t){const e=this.schema.fields,[i,r]=t.schema.fields.reduce((a,c,l)=>{const[b,S]=a,tt=e.findIndex(O=>O.name===c.name);return~tt?S[tt]=l:b.push(l),a},[[],[]]),s=this.schema.assign(t.schema),o=[...e.map((a,c)=>[c,r[c]]).map(([a,c])=>c===void 0?this.getChildAt(a):t.getChildAt(c)),...i.map(a=>t.getChildAt(a))].filter(Boolean);return new Vt(...ls(s,o))}}al=Symbol.toStringTag;Vt[al]=(n=>(n.schema=null,n.batches=[],n._offsets=new Uint32Array([0]),n._nullCount=-1,n[Symbol.isConcatSpreadable]=!0,n.isValid=Mr(Js),n.get=Mr(Kt.getVisitFn(h.Struct)),n.set=Xc(ue.getVisitFn(h.Struct)),n.indexOf=tl(Cr.getVisitFn(h.Struct)),"Table"))(Vt.prototype);var cl;let Qt=class gi{constructor(...t){switch(t.length){case 2:{if([this.schema]=t,!(this.schema instanceof lt))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=X({nullCount:0,type:new zt(this.schema.fields),children:this.schema.fields.map(e=>X({type:e.type,nullCount:0}))})]=t,!(this.data instanceof ht))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=Xo(this.schema,this.data.children);break}case 1:{const[e]=t,{fields:i,children:r,length:s}=Object.keys(e).reduce((c,l,b)=>(c.children[b]=e[l],c.length=Math.max(c.length,e[l].length),c.fields[b]=_t.new({name:l,type:e[l].type,nullable:!0}),c),{length:0,fields:new Array,children:new Array}),o=new lt(i),a=X({type:new zt(i),length:s,children:r,nullCount:0});[this.schema,this.data]=Xo(o,a.children,s);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=ll(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(t){return this.data.getValid(t)}get(t){return Kt.visit(this.data,t)}at(t){return this.get(qs(t,this.numRows))}set(t,e){return ue.visit(this.data,t,e)}indexOf(t,e){return Cr.visit(this.data,t,e)}[Symbol.iterator](){return Ks.visit(new ct([this.data]))}toArray(){return[...this]}concat(...t){return new Vt(this.schema,[this,...t])}slice(t,e){const[i]=new ct([this.data]).slice(t,e).data;return new gi(this.schema,i)}getChild(t){var e;return this.getChildAt((e=this.schema.fields)===null||e===void 0?void 0:e.findIndex(i=>i.name===t))}getChildAt(t){return t>-1&&t<this.schema.fields.length?new ct([this.data.children[t]]):null}setChild(t,e){var i;return this.setChildAt((i=this.schema.fields)===null||i===void 0?void 0:i.findIndex(r=>r.name===t),e)}setChildAt(t,e){let i=this.schema,r=this.data;if(t>-1&&t<this.numCols){e||(e=new ct([X({type:new rn,length:this.numRows})]));const s=i.fields.slice(),o=r.children.slice(),a=s[t].clone({type:e.type});[s[t],o[t]]=[a,e.data[0]],i=new lt(s,new Map(this.schema.metadata)),r=X({type:new zt(s),children:o})}return new gi(i,r)}select(t){const e=this.schema.select(t),i=new zt(e.fields),r=[];for(const s of t){const o=this.schema.fields.findIndex(a=>a.name===s);~o&&(r[o]=this.data.children[o])}return new gi(e,X({type:i,length:this.numRows,children:r}))}selectAt(t){const e=this.schema.selectAt(t),i=t.map(s=>this.data.children[s]).filter(Boolean),r=X({type:new zt(e.fields),length:this.numRows,children:i});return new gi(e,r)}};cl=Symbol.toStringTag;Qt[cl]=(n=>(n._nullCount=-1,n[Symbol.isConcatSpreadable]=!0,"RecordBatch"))(Qt.prototype);function Xo(n,t,e=t.reduce((i,r)=>Math.max(i,r.length),0)){var i;const r=[...n.fields],s=[...t],o=(e+63&-64)>>3;for(const[a,c]of n.fields.entries()){const l=t[a];(!l||l.length!==e)&&(r[a]=c.clone({nullable:!0}),s[a]=(i=l==null?void 0:l._changeLengthAndBackfillNullBitmap(e))!==null&&i!==void 0?i:X({type:c.type,length:e,nullCount:e,nullBitmap:new Uint8Array(o)}))}return[n.assign(r),X({type:new zt(r),length:e,children:s})]}function ll(n,t,e=new Map){var i,r;if(((i=n==null?void 0:n.length)!==null&&i!==void 0?i:0)>0&&(n==null?void 0:n.length)===(t==null?void 0:t.length))for(let s=-1,o=n.length;++s<o;){const{type:a}=n[s],c=t[s];for(const l of[c,...((r=c==null?void 0:c.dictionary)===null||r===void 0?void 0:r.data)||[]])ll(a.children,l==null?void 0:l.children,e);if(D.isDictionary(a)){const{id:l}=a;if(!e.has(l))c!=null&&c.dictionary&&e.set(l,c.dictionary);else if(e.get(l)!==c.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}}return e}class io extends Qt{constructor(t){const e=t.fields.map(r=>X({type:r.type})),i=X({type:new zt(t.fields),nullCount:0,children:e});super(t,i)}}let Ge=class we{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMessage(t,e){return(e||new we).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMessage(t,e){return t.setPosition(t.position()+ut),(e||new we).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Dt.V1}headerType(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):at.NONE}header(t){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}bodyLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}customMetadata(t,e){const i=this.bb.__offset(this.bb_pos,12);return i?(e||new Mt).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Dt.V1)}static addHeaderType(t,e){t.addFieldInt8(1,e,at.NONE)}static addHeader(t,e){t.addFieldOffset(2,e,0)}static addBodyLength(t,e){t.addFieldInt64(3,e,BigInt("0"))}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endMessage(t){return t.endObject()}static finishMessageBuffer(t,e){t.finish(e)}static finishSizePrefixedMessageBuffer(t,e){t.finish(e,void 0,!0)}static createMessage(t,e,i,r,s,o){return we.startMessage(t),we.addVersion(t,e),we.addHeaderType(t,i),we.addHeader(t,r),we.addBodyLength(t,s),we.addCustomMetadata(t,o),we.endMessage(t)}};class Mh extends J{visit(t,e){return t==null||e==null?void 0:super.visit(t,e)}visitNull(t,e){return Wo.startNull(e),Wo.endNull(e)}visitInt(t,e){return te.startInt(e),te.addBitWidth(e,t.bitWidth),te.addIsSigned(e,t.isSigned),te.endInt(e)}visitFloat(t,e){return Ee.startFloatingPoint(e),Ee.addPrecision(e,t.precision),Ee.endFloatingPoint(e)}visitBinary(t,e){return ko.startBinary(e),ko.endBinary(e)}visitLargeBinary(t,e){return jo.startLargeBinary(e),jo.endLargeBinary(e)}visitBool(t,e){return $o.startBool(e),$o.endBool(e)}visitUtf8(t,e){return Yo.startUtf8(e),Yo.endUtf8(e)}visitLargeUtf8(t,e){return Vo.startLargeUtf8(e),Vo.endLargeUtf8(e)}visitDecimal(t,e){return Dn.startDecimal(e),Dn.addScale(e,t.scale),Dn.addPrecision(e,t.precision),Dn.addBitWidth(e,t.bitWidth),Dn.endDecimal(e)}visitDate(t,e){return tr.startDate(e),tr.addUnit(e,t.unit),tr.endDate(e)}visitTime(t,e){return oe.startTime(e),oe.addUnit(e,t.unit),oe.addBitWidth(e,t.bitWidth),oe.endTime(e)}visitTimestamp(t,e){const i=t.timezone&&e.createString(t.timezone)||void 0;return ae.startTimestamp(e),ae.addUnit(e,t.unit),i!==void 0&&ae.addTimezone(e,i),ae.endTimestamp(e)}visitInterval(t,e){return Te.startInterval(e),Te.addUnit(e,t.unit),Te.endInterval(e)}visitDuration(t,e){return er.startDuration(e),er.addUnit(e,t.unit),er.endDuration(e)}visitList(t,e){return zo.startList(e),zo.endList(e)}visitStruct(t,e){return pn.startStruct_(e),pn.endStruct_(e)}visitUnion(t,e){Ht.startTypeIdsVector(e,t.typeIds.length);const i=Ht.createTypeIdsVector(e,t.typeIds);return Ht.startUnion(e),Ht.addMode(e,t.mode),Ht.addTypeIds(e,i),Ht.endUnion(e)}visitDictionary(t,e){const i=this.visit(t.indices,e);return je.startDictionaryEncoding(e),je.addId(e,BigInt(t.id)),je.addIsOrdered(e,t.isOrdered),i!==void 0&&je.addIndexType(e,i),je.endDictionaryEncoding(e)}visitFixedSizeBinary(t,e){return nr.startFixedSizeBinary(e),nr.addByteWidth(e,t.byteWidth),nr.endFixedSizeBinary(e)}visitFixedSizeList(t,e){return ir.startFixedSizeList(e),ir.addListSize(e,t.listSize),ir.endFixedSizeList(e)}visitMap(t,e){return rr.startMap(e),rr.addKeysSorted(e,t.keysSorted),rr.endMap(e)}}const us=new Mh;function Ch(n,t=new Map){return new lt(Uh(n,t),ar(n.metadata),t)}function ul(n){return new ee(n.count,hl(n.columns),dl(n.columns))}function Lh(n){return new Ne(ul(n.data),n.id,n.isDelta)}function Uh(n,t){return(n.fields||[]).filter(Boolean).map(e=>_t.fromJSON(e,t))}function ta(n,t){return(n.children||[]).filter(Boolean).map(e=>_t.fromJSON(e,t))}function hl(n){return(n||[]).reduce((t,e)=>[...t,new an(e.count,xh(e.VALIDITY)),...hl(e.children)],[])}function dl(n,t=[]){for(let e=-1,i=(n||[]).length;++e<i;){const r=n[e];r.VALIDITY&&t.push(new Fe(t.length,r.VALIDITY.length)),r.TYPE_ID&&t.push(new Fe(t.length,r.TYPE_ID.length)),r.OFFSET&&t.push(new Fe(t.length,r.OFFSET.length)),r.DATA&&t.push(new Fe(t.length,r.DATA.length)),t=dl(r.children,t)}return t}function xh(n){return(n||[]).reduce((t,e)=>t+ +(e===0),0)}function Ph(n,t){let e,i,r,s,o,a;return!t||!(s=n.dictionary)?(o=na(n,ta(n,t)),r=new _t(n.name,o,n.nullable,ar(n.metadata))):t.has(e=s.id)?(i=(i=s.indexType)?ea(i):new Ti,a=new Zn(t.get(e),i,e,s.isOrdered),r=new _t(n.name,a,n.nullable,ar(n.metadata))):(i=(i=s.indexType)?ea(i):new Ti,t.set(e,o=na(n,ta(n,t))),a=new Zn(o,i,e,s.isOrdered),r=new _t(n.name,a,n.nullable,ar(n.metadata))),r||null}function ar(n=[]){return new Map(n.map(({key:t,value:e})=>[t,e]))}function ea(n){return new En(n.isSigned,n.bitWidth)}function na(n,t){const e=n.type.name;switch(e){case"NONE":return new rn;case"null":return new rn;case"binary":return new yr;case"largebinary":return new mr;case"utf8":return new gr;case"largeutf8":return new br;case"bool":return new _r;case"list":return new Ar((t||[])[0]);case"struct":return new zt(t||[]);case"struct_":return new zt(t||[])}switch(e){case"int":{const i=n.type;return new En(i.isSigned,i.bitWidth)}case"floatingpoint":{const i=n.type;return new pr(xt[i.precision])}case"decimal":{const i=n.type;return new wr(i.scale,i.precision,i.bitWidth)}case"date":{const i=n.type;return new Ir(le[i.unit])}case"time":{const i=n.type;return new vr(x[i.unit],i.bitWidth)}case"timestamp":{const i=n.type;return new Sr(x[i.unit],i.timezone)}case"interval":{const i=n.type;return new Er(De[i.unit])}case"duration":{const i=n.type;return new Tr(x[i.unit])}case"union":{const i=n.type,[r,...s]=(i.mode+"").toLowerCase(),o=r.toUpperCase()+s.join("");return new Fr(Pt[o],i.typeIds||[],t||[])}case"fixedsizebinary":{const i=n.type;return new Or(i.byteWidth)}case"fixedsizelist":{const i=n.type;return new Dr(i.listSize,(t||[])[0])}case"map":{const i=n.type;return new Nr((t||[])[0],i.keysSorted)}}throw new Error(`Unrecognized type: "${e}"`)}var kh=Oa,$h=Jn;class Wt{static fromJSON(t,e){const i=new Wt(0,Dt.V5,e);return i._createHeader=jh(t,e),i}static decode(t){t=new $h(rt(t));const e=Ge.getRootAsMessage(t),i=e.bodyLength(),r=e.version(),s=e.headerType(),o=new Wt(i,r,s);return o._createHeader=Vh(e,s),o}static encode(t){const e=new kh;let i=-1;return t.isSchema()?i=lt.encode(e,t.header()):t.isRecordBatch()?i=ee.encode(e,t.header()):t.isDictionaryBatch()&&(i=Ne.encode(e,t.header())),Ge.startMessage(e),Ge.addVersion(e,Dt.V5),Ge.addHeader(e,i),Ge.addHeaderType(e,t.headerType),Ge.addBodyLength(e,BigInt(t.bodyLength)),Ge.finishMessageBuffer(e,Ge.endMessage(e)),e.asUint8Array()}static from(t,e=0){if(t instanceof lt)return new Wt(0,Dt.V5,at.Schema,t);if(t instanceof ee)return new Wt(e,Dt.V5,at.RecordBatch,t);if(t instanceof Ne)return new Wt(e,Dt.V5,at.DictionaryBatch,t);throw new Error(`Unrecognized Message header: ${t}`)}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===at.Schema}isRecordBatch(){return this.headerType===at.RecordBatch}isDictionaryBatch(){return this.headerType===at.DictionaryBatch}constructor(t,e,i,r){this._version=e,this._headerType=i,this.body=new Uint8Array(0),r&&(this._createHeader=()=>r),this._bodyLength=pt(t)}}class ee{get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}constructor(t,e,i){this._nodes=e,this._buffers=i,this._length=pt(t)}}class Ne{get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}constructor(t,e,i=!1){this._data=t,this._isDelta=i,this._id=pt(e)}}class Fe{constructor(t,e){this.offset=pt(t),this.length=pt(e)}}class an{constructor(t,e){this.length=pt(t),this.nullCount=pt(e)}}function jh(n,t){return()=>{switch(t){case at.Schema:return lt.fromJSON(n);case at.RecordBatch:return ee.fromJSON(n);case at.DictionaryBatch:return Ne.fromJSON(n)}throw new Error(`Unrecognized Message type: { name: ${at[t]}, type: ${t} }`)}}function Vh(n,t){return()=>{switch(t){case at.Schema:return lt.decode(n.header(new Ie),new Map,n.version());case at.RecordBatch:return ee.decode(n.header(new Ce),n.version());case at.DictionaryBatch:return Ne.decode(n.header(new Fn),n.version())}throw new Error(`Unrecognized Message type: { name: ${at[t]}, type: ${t} }`)}}_t.encode=Xh;_t.decode=Kh;_t.fromJSON=Ph;lt.encode=Zh;lt.decode=zh;lt.fromJSON=Ch;ee.encode=td;ee.decode=Wh;ee.fromJSON=ul;Ne.encode=ed;Ne.decode=Yh;Ne.fromJSON=Lh;an.encode=nd;an.decode=qh;Fe.encode=id;Fe.decode=Gh;function zh(n,t=new Map,e=Dt.V5){const i=Jh(n,t);return new lt(i,cr(n),t,e)}function Wh(n,t=Dt.V5){if(n.compression()!==null)throw new Error("Record batch compression not implemented");return new ee(n.length(),Hh(n),Qh(n,t))}function Yh(n,t=Dt.V5){return new Ne(ee.decode(n.data(),t),n.id(),n.isDelta())}function Gh(n){return new Fe(n.offset(),n.length())}function qh(n){return new an(n.length(),n.nullCount())}function Hh(n){const t=[];for(let e,i=-1,r=-1,s=n.nodesLength();++i<s;)(e=n.nodes(i))&&(t[++r]=an.decode(e));return t}function Qh(n,t){const e=[];for(let i,r=-1,s=-1,o=n.buffersLength();++r<o;)(i=n.buffers(r))&&(t<Dt.V4&&(i.bb_pos+=8*(r+1)),e[++s]=Fe.decode(i));return e}function Jh(n,t){const e=[];for(let i,r=-1,s=-1,o=n.fieldsLength();++r<o;)(i=n.fields(r))&&(e[++s]=_t.decode(i,t));return e}function ia(n,t){const e=[];for(let i,r=-1,s=-1,o=n.childrenLength();++r<o;)(i=n.children(r))&&(e[++s]=_t.decode(i,t));return e}function Kh(n,t){let e,i,r,s,o,a;return!t||!(a=n.dictionary())?(r=sa(n,ia(n,t)),i=new _t(n.name(),r,n.nullable(),cr(n))):t.has(e=pt(a.id()))?(s=(s=a.indexType())?ra(s):new Ti,o=new Zn(t.get(e),s,e,a.isOrdered()),i=new _t(n.name(),o,n.nullable(),cr(n))):(s=(s=a.indexType())?ra(s):new Ti,t.set(e,r=sa(n,ia(n,t))),o=new Zn(r,s,e,a.isOrdered()),i=new _t(n.name(),o,n.nullable(),cr(n))),i||null}function cr(n){const t=new Map;if(n)for(let e,i,r=-1,s=Math.trunc(n.customMetadataLength());++r<s;)(e=n.customMetadata(r))&&(i=e.key())!=null&&t.set(i,e.value());return t}function ra(n){return new En(n.isSigned(),n.bitWidth())}function sa(n,t){const e=n.typeType();switch(e){case mt.NONE:return new rn;case mt.Null:return new rn;case mt.Binary:return new yr;case mt.LargeBinary:return new mr;case mt.Utf8:return new gr;case mt.LargeUtf8:return new br;case mt.Bool:return new _r;case mt.List:return new Ar((t||[])[0]);case mt.Struct_:return new zt(t||[])}switch(e){case mt.Int:{const i=n.type(new te);return new En(i.isSigned(),i.bitWidth())}case mt.FloatingPoint:{const i=n.type(new Ee);return new pr(i.precision())}case mt.Decimal:{const i=n.type(new Dn);return new wr(i.scale(),i.precision(),i.bitWidth())}case mt.Date:{const i=n.type(new tr);return new Ir(i.unit())}case mt.Time:{const i=n.type(new oe);return new vr(i.unit(),i.bitWidth())}case mt.Timestamp:{const i=n.type(new ae);return new Sr(i.unit(),i.timezone())}case mt.Interval:{const i=n.type(new Te);return new Er(i.unit())}case mt.Duration:{const i=n.type(new er);return new Tr(i.unit())}case mt.Union:{const i=n.type(new Ht);return new Fr(i.mode(),i.typeIdsArray()||[],t||[])}case mt.FixedSizeBinary:{const i=n.type(new nr);return new Or(i.byteWidth())}case mt.FixedSizeList:{const i=n.type(new ir);return new Dr(i.listSize(),(t||[])[0])}case mt.Map:{const i=n.type(new rr);return new Nr((t||[])[0],i.keysSorted())}}throw new Error(`Unrecognized type: "${mt[e]}" (${e})`)}function Zh(n,t){const e=t.fields.map(s=>_t.encode(n,s));Ie.startFieldsVector(n,e.length);const i=Ie.createFieldsVector(n,e),r=t.metadata&&t.metadata.size>0?Ie.createCustomMetadataVector(n,[...t.metadata].map(([s,o])=>{const a=n.createString(`${s}`),c=n.createString(`${o}`);return Mt.startKeyValue(n),Mt.addKey(n,a),Mt.addValue(n,c),Mt.endKeyValue(n)})):-1;return Ie.startSchema(n),Ie.addFields(n,i),Ie.addEndianness(n,rd?Kn.Little:Kn.Big),r!==-1&&Ie.addCustomMetadata(n,r),Ie.endSchema(n)}function Xh(n,t){let e=-1,i=-1,r=-1;const s=t.type;let o=t.typeId;D.isDictionary(s)?(o=s.dictionary.typeId,r=us.visit(s,n),i=us.visit(s.dictionary,n)):i=us.visit(s,n);const a=(s.children||[]).map(b=>_t.encode(n,b)),c=ne.createChildrenVector(n,a),l=t.metadata&&t.metadata.size>0?ne.createCustomMetadataVector(n,[...t.metadata].map(([b,S])=>{const tt=n.createString(`${b}`),O=n.createString(`${S}`);return Mt.startKeyValue(n),Mt.addKey(n,tt),Mt.addValue(n,O),Mt.endKeyValue(n)})):-1;return t.name&&(e=n.createString(t.name)),ne.startField(n),ne.addType(n,i),ne.addTypeType(n,o),ne.addChildren(n,c),ne.addNullable(n,!!t.nullable),e!==-1&&ne.addName(n,e),r!==-1&&ne.addDictionary(n,r),l!==-1&&ne.addCustomMetadata(n,l),ne.endField(n)}function td(n,t){const e=t.nodes||[],i=t.buffers||[];Ce.startNodesVector(n,e.length);for(const o of e.slice().reverse())an.encode(n,o);const r=n.endVector();Ce.startBuffersVector(n,i.length);for(const o of i.slice().reverse())Fe.encode(n,o);const s=n.endVector();return Ce.startRecordBatch(n),Ce.addLength(n,BigInt(t.length)),Ce.addNodes(n,r),Ce.addBuffers(n,s),Ce.endRecordBatch(n)}function ed(n,t){const e=ee.encode(n,t.data);return Fn.startDictionaryBatch(n),Fn.addId(n,BigInt(t.id)),Fn.addIsDelta(n,t.isDelta),Fn.addData(n,e),Fn.endDictionaryBatch(n)}function nd(n,t){return Ba.createFieldNode(n,BigInt(t.length),BigInt(t.nullCount))}function id(n,t){return Na.createBuffer(n,BigInt(t.offset),BigInt(t.length))}const rd=(()=>{const n=new ArrayBuffer(2);return new DataView(n).setInt16(0,256,!0),new Int16Array(n)[0]===256})(),ro=n=>`Expected ${at[n]} Message in stream, but was null or length 0.`,so=n=>`Header pointer of flatbuffer-encoded ${at[n]} Message is null or length 0.`,fl=(n,t)=>`Expected to read ${n} metadata bytes, but only read ${t}.`,pl=(n,t)=>`Expected to read ${n} bytes for message body, but only read ${t}.`;class yl{constructor(t){this.source=t instanceof Ur?t:new Ur(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||t.value===-1&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?Et:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let e;if((e=this.next()).done)return null;if(t!=null&&e.value.headerType!==t)throw new Error(ro(t));return e.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=rt(this.source.read(t));if(e.byteLength<t)throw new Error(pl(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}readSchema(t=!1){const e=at.Schema,i=this.readMessage(e),r=i==null?void 0:i.header();if(t&&!r)throw new Error(so(e));return r}readMetadataLength(){const t=this.source.read(ts),e=t&&new Jn(t),i=(e==null?void 0:e.readInt32(0))||0;return{done:i===0,value:i}}readMetadata(t){const e=this.source.read(t);if(!e)return Et;if(e.byteLength<t)throw new Error(fl(t,e.byteLength));return{done:!1,value:Wt.decode(e)}}}class sd{constructor(t,e){this.source=t instanceof Xn?t:va(t)?new xr(t,e):new Xn(t)}[Symbol.asyncIterator](){return this}next(){return Y(this,void 0,void 0,function*(){let t;return(t=yield this.readMetadataLength()).done||t.value===-1&&(t=yield this.readMetadataLength()).done||(t=yield this.readMetadata(t.value)).done?Et:t})}throw(t){return Y(this,void 0,void 0,function*(){return yield this.source.throw(t)})}return(t){return Y(this,void 0,void 0,function*(){return yield this.source.return(t)})}readMessage(t){return Y(this,void 0,void 0,function*(){let e;if((e=yield this.next()).done)return null;if(t!=null&&e.value.headerType!==t)throw new Error(ro(t));return e.value})}readMessageBody(t){return Y(this,void 0,void 0,function*(){if(t<=0)return new Uint8Array(0);const e=rt(yield this.source.read(t));if(e.byteLength<t)throw new Error(pl(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()})}readSchema(){return Y(this,arguments,void 0,function*(t=!1){const e=at.Schema,i=yield this.readMessage(e),r=i==null?void 0:i.header();if(t&&!r)throw new Error(so(e));return r})}readMetadataLength(){return Y(this,void 0,void 0,function*(){const t=yield this.source.read(ts),e=t&&new Jn(t),i=(e==null?void 0:e.readInt32(0))||0;return{done:i===0,value:i}})}readMetadata(t){return Y(this,void 0,void 0,function*(){const e=yield this.source.read(t);if(!e)return Et;if(e.byteLength<t)throw new Error(fl(t,e.byteLength));return{done:!1,value:Wt.decode(e)}})}}class od extends yl{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof Ko?t:new Ko(t)}next(){const{_json:t}=this;if(!this._schema)return this._schema=!0,{done:!1,value:Wt.fromJSON(t.schema,at.Schema)};if(this._dictionaryIndex<t.dictionaries.length){const e=t.dictionaries[this._dictionaryIndex++];return this._body=e.data.columns,{done:!1,value:Wt.fromJSON(e,at.DictionaryBatch)}}if(this._batchIndex<t.batches.length){const e=t.batches[this._batchIndex++];return this._body=e.columns,{done:!1,value:Wt.fromJSON(e,at.RecordBatch)}}return this._body=[],Et}readMessageBody(t){return e(this._body);function e(i){return(i||[]).reduce((r,s)=>[...r,...s.VALIDITY&&[s.VALIDITY]||[],...s.TYPE_ID&&[s.TYPE_ID]||[],...s.OFFSET&&[s.OFFSET]||[],...s.DATA&&[s.DATA]||[],...e(s.children)],[])}}readMessage(t){let e;if((e=this.next()).done)return null;if(t!=null&&e.value.headerType!==t)throw new Error(ro(t));return e.value}readSchema(){const t=at.Schema,e=this.readMessage(t),i=e==null?void 0:e.header();if(!e||!i)throw new Error(so(t));return i}}const ts=4,Rs="ARROW1",Fi=new Uint8Array(Rs.length);for(let n=0;n<Rs.length;n+=1)Fi[n]=Rs.codePointAt(n);function oo(n,t=0){for(let e=-1,i=Fi.length;++e<i;)if(Fi[e]!==n[t+e])return!1;return!0}const Wi=Fi.length,ml=Wi+ts,ad=Wi*2+ts;class ce extends Zs{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const e=this._impl.open(t);return Sn(e)?e.then(()=>this):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return se.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return se.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof ce?t:vs(t)?hd(t):va(t)?pd(t):Sn(t)?Y(this,void 0,void 0,function*(){return yield ce.from(yield t)}):Sa(t)||js(t)||Ea(t)||ei(t)?fd(new Xn(t)):dd(new Ur(t))}static readAll(t){return t instanceof ce?t.isSync()?oa(t):aa(t):vs(t)||ArrayBuffer.isView(t)||$i(t)||Ia(t)?oa(t):aa(t)}}class Pr extends ce{constructor(t){super(t),this._impl=t}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return Ae(this,arguments,function*(){yield q(yield*Xi(In(this[Symbol.iterator]())))})}}class kr extends ce{constructor(t){super(t),this._impl=t}readAll(){return Y(this,void 0,void 0,function*(){var t,e,i,r;const s=new Array;try{for(var o=!0,a=In(this),c;c=yield a.next(),t=c.done,!t;o=!0){r=c.value,o=!1;const l=r;s.push(l)}}catch(l){e={error:l}}finally{try{!o&&!t&&(i=a.return)&&(yield i.call(a))}finally{if(e)throw e.error}}return s})}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class gl extends Pr{constructor(t){super(t),this._impl=t}}class cd extends kr{constructor(t){super(t),this._impl=t}}class bl{get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}constructor(t=new Map){this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,e){const i=this._loadVectors(t,e,this.schema.fields),r=X({type:new zt(this.schema.fields),length:t.length,children:i});return new Qt(this.schema,r)}_loadDictionaryBatch(t,e){const{id:i,isDelta:r}=t,{dictionaries:s,schema:o}=this,a=s.get(i),c=o.dictionaries.get(i),l=this._loadVectors(t.data,e,[c]);return(a&&r?a.concat(new ct(l)):new ct(l)).memoize()}_loadVectors(t,e,i){return new ol(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(i)}}class $r extends bl{constructor(t,e){super(e),this._reader=vs(t)?new od(this._handle=t):new yl(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=wl(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):Et}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):Et}next(){if(this.closed)return Et;let t;const{_reader:e}=this;for(;t=this._readNextMessageAndValidate();)if(t.isSchema())this.reset(t.header());else if(t.isRecordBatch()){this._recordBatchIndex++;const i=t.header(),r=e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(i,r)}}else if(t.isDictionaryBatch()){this._dictionaryIndex++;const i=t.header(),r=e.readMessageBody(t.bodyLength),s=this._loadDictionaryBatch(i,r);this.dictionaries.set(i.id,s)}return this.schema&&this._recordBatchIndex===0?(this._recordBatchIndex++,{done:!1,value:new io(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class jr extends bl{constructor(t,e){super(e),this._reader=new sd(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return Y(this,void 0,void 0,function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)})}open(t){return Y(this,void 0,void 0,function*(){return this.closed||(this.autoDestroy=wl(this,t),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this})}throw(t){return Y(this,void 0,void 0,function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(t):Et})}return(t){return Y(this,void 0,void 0,function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(t):Et})}next(){return Y(this,void 0,void 0,function*(){if(this.closed)return Et;let t;const{_reader:e}=this;for(;t=yield this._readNextMessageAndValidate();)if(t.isSchema())yield this.reset(t.header());else if(t.isRecordBatch()){this._recordBatchIndex++;const i=t.header(),r=yield e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(i,r)}}else if(t.isDictionaryBatch()){this._dictionaryIndex++;const i=t.header(),r=yield e.readMessageBody(t.bodyLength),s=this._loadDictionaryBatch(i,r);this.dictionaries.set(i.id,s)}return this.schema&&this._recordBatchIndex===0?(this._recordBatchIndex++,{done:!1,value:new io(this.schema)}):yield this.return()})}_readNextMessageAndValidate(t){return Y(this,void 0,void 0,function*(){return yield this._reader.readMessage(t)})}}class _l extends $r{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,e){super(t instanceof Zo?t:new Zo(t),e)}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const e of this._footer.dictionaryBatches())e&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){var e;if(this.closed)return null;this._footer||this.open();const i=(e=this._footer)===null||e===void 0?void 0:e.getRecordBatch(t);if(i&&this._handle.seek(i.offset)){const r=this._reader.readMessage(at.RecordBatch);if(r!=null&&r.isRecordBatch()){const s=r.header(),o=this._reader.readMessageBody(r.bodyLength);return this._loadRecordBatch(s,o)}}return null}_readDictionaryBatch(t){var e;const i=(e=this._footer)===null||e===void 0?void 0:e.getDictionaryBatch(t);if(i&&this._handle.seek(i.offset)){const r=this._reader.readMessage(at.DictionaryBatch);if(r!=null&&r.isDictionaryBatch()){const s=r.header(),o=this._reader.readMessageBody(r.bodyLength),a=this._loadDictionaryBatch(s,o);this.dictionaries.set(s.id,a)}}}_readFooter(){const{_handle:t}=this,e=t.size-ml,i=t.readInt32(e),r=t.readAt(e-i,i);return Ai.decode(r)}_readNextMessageAndValidate(t){var e;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const i=(e=this._footer)===null||e===void 0?void 0:e.getRecordBatch(this._recordBatchIndex);if(i&&this._handle.seek(i.offset))return this._reader.readMessage(t)}return null}}class ld extends jr{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,...e){const i=typeof e[0]!="number"?e.shift():void 0,r=e[0]instanceof Map?e.shift():void 0;super(t instanceof xr?t:new xr(t,i),r)}isFile(){return!0}isAsync(){return!0}open(t){const e=Object.create(null,{open:{get:()=>super.open}});return Y(this,void 0,void 0,function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const i of this._footer.dictionaryBatches())i&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield e.open.call(this,t)})}readRecordBatch(t){return Y(this,void 0,void 0,function*(){var e;if(this.closed)return null;this._footer||(yield this.open());const i=(e=this._footer)===null||e===void 0?void 0:e.getRecordBatch(t);if(i&&(yield this._handle.seek(i.offset))){const r=yield this._reader.readMessage(at.RecordBatch);if(r!=null&&r.isRecordBatch()){const s=r.header(),o=yield this._reader.readMessageBody(r.bodyLength);return this._loadRecordBatch(s,o)}}return null})}_readDictionaryBatch(t){return Y(this,void 0,void 0,function*(){var e;const i=(e=this._footer)===null||e===void 0?void 0:e.getDictionaryBatch(t);if(i&&(yield this._handle.seek(i.offset))){const r=yield this._reader.readMessage(at.DictionaryBatch);if(r!=null&&r.isDictionaryBatch()){const s=r.header(),o=yield this._reader.readMessageBody(r.bodyLength),a=this._loadDictionaryBatch(s,o);this.dictionaries.set(s.id,a)}}})}_readFooter(){return Y(this,void 0,void 0,function*(){const{_handle:t}=this;t._pending&&(yield t._pending);const e=t.size-ml,i=yield t.readInt32(e),r=yield t.readAt(e-i,i);return Ai.decode(r)})}_readNextMessageAndValidate(t){return Y(this,void 0,void 0,function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer.getRecordBatch(this._recordBatchIndex);if(e&&(yield this._handle.seek(e.offset)))return yield this._reader.readMessage(t)}return null})}}class ud extends $r{constructor(t,e){super(t,e)}_loadVectors(t,e,i){return new Sh(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(i)}}function wl(n,t){return t&&typeof t.autoDestroy=="boolean"?t.autoDestroy:n.autoDestroy}function*oa(n){const t=ce.from(n);try{if(!t.open({autoDestroy:!1}).closed)do yield t;while(!t.reset().open().closed)}finally{t.cancel()}}function aa(n){return Ae(this,arguments,function*(){const e=yield q(ce.from(n));try{if(!(yield q(e.open({autoDestroy:!1}))).closed)do yield yield q(e);while(!(yield q(e.reset().open())).closed)}finally{yield q(e.cancel())}})}function hd(n){return new Pr(new ud(n))}function dd(n){const t=n.peek(Wi+7&-8);return t&&t.byteLength>=4?oo(t)?new gl(new _l(n.read())):new Pr(new $r(n)):new Pr(new $r(function*(){}()))}function fd(n){return Y(this,void 0,void 0,function*(){const t=yield n.peek(Wi+7&-8);return t&&t.byteLength>=4?oo(t)?new gl(new _l(yield n.read())):new kr(new jr(n)):new kr(new jr(function(){return Ae(this,arguments,function*(){})}()))})}function pd(n){return Y(this,void 0,void 0,function*(){const{size:t}=yield n.stat(),e=new xr(n,t);return t>=ad&&oo(yield e.readAt(0,Wi+7&-8))?new cd(new ld(e)):new kr(new jr(e))})}class Tt extends J{static assemble(...t){const e=r=>r.flatMap(s=>Array.isArray(s)?e(s):s instanceof Qt?s.data.children:s.data),i=new Tt;return i.visitMany(e(t)),i}constructor(){super(),this._byteLength=0,this._nodes=[],this._buffers=[],this._bufferRegions=[]}visit(t){if(t instanceof ct)return this.visitMany(t.data),this;const{type:e}=t;if(!D.isDictionary(e)){const{length:i}=t;if(i>2147483647)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");if(D.isUnion(e))this.nodes.push(new an(i,0));else{const{nullCount:r}=t;D.isNull(e)||ge.call(this,r<=0?new Uint8Array(0):Br(t.offset,i,t.nullBitmap)),this.nodes.push(new an(i,r))}}return super.visit(t)}visitNull(t){return this}visitDictionary(t){return this.visit(t.clone(t.type.indices))}get nodes(){return this._nodes}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get bufferRegions(){return this._bufferRegions}}function ge(n){const t=n.byteLength+7&-8;return this.buffers.push(n),this.bufferRegions.push(new Fe(this._byteLength,t)),this._byteLength+=t,this}function yd(n){var t;const{type:e,length:i,typeIds:r,valueOffsets:s}=n;if(ge.call(this,r),e.mode===Pt.Sparse)return Ms.call(this,n);if(e.mode===Pt.Dense){if(n.offset<=0)return ge.call(this,s),Ms.call(this,n);{const o=new Int32Array(i),a=Object.create(null),c=Object.create(null);for(let l,b,S=-1;++S<i;)(l=r[S])!==void 0&&((b=a[l])===void 0&&(b=a[l]=s[S]),o[S]=s[S]-b,c[l]=((t=c[l])!==null&&t!==void 0?t:0)+1);ge.call(this,o),this.visitMany(n.children.map((l,b)=>{const S=e.typeIds[b],tt=a[S],O=c[S];return l.slice(tt,Math.min(i,O))}))}}return this}function md(n){let t;return n.nullCount>=n.length?ge.call(this,new Uint8Array(0)):(t=n.values)instanceof Uint8Array?ge.call(this,Br(n.offset,n.length,t)):ge.call(this,Rr(n.values))}function We(n){return ge.call(this,n.values.subarray(0,n.length*n.stride))}function es(n){const{length:t,values:e,valueOffsets:i}=n,r=pt(i[0]),s=pt(i[t]),o=Math.min(s-r,e.byteLength-r);return ge.call(this,Aa(-r,t+1,i)),ge.call(this,e.subarray(r,r+o)),this}function ao(n){const{length:t,valueOffsets:e}=n;if(e){const{[0]:i,[t]:r}=e;return ge.call(this,Aa(-i,t+1,e)),this.visit(n.children[0].slice(i,r-i))}return this.visit(n.children[0])}function Ms(n){return this.visitMany(n.type.children.map((t,e)=>n.children[e]).filter(Boolean))[0]}Tt.prototype.visitBool=md;Tt.prototype.visitInt=We;Tt.prototype.visitFloat=We;Tt.prototype.visitUtf8=es;Tt.prototype.visitLargeUtf8=es;Tt.prototype.visitBinary=es;Tt.prototype.visitLargeBinary=es;Tt.prototype.visitFixedSizeBinary=We;Tt.prototype.visitDate=We;Tt.prototype.visitTimestamp=We;Tt.prototype.visitTime=We;Tt.prototype.visitDecimal=We;Tt.prototype.visitList=ao;Tt.prototype.visitStruct=Ms;Tt.prototype.visitUnion=yd;Tt.prototype.visitInterval=We;Tt.prototype.visitDuration=We;Tt.prototype.visitFixedSizeList=ao;Tt.prototype.visitMap=ao;class Il extends Zs{static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}constructor(t){super(),this._position=0,this._started=!1,this._sink=new or,this._schema=null,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._seenDictionaries=new Map,this._dictionaryDeltaOffsets=new Map,Jt(t)||(t={autoDestroy:!0,writeLegacyIpcFormat:!1}),this._autoDestroy=typeof t.autoDestroy=="boolean"?t.autoDestroy:!0,this._writeLegacyIpcFormat=typeof t.writeLegacyIpcFormat=="boolean"?t.writeLegacyIpcFormat:!1}toString(t=!1){return this._sink.toString(t)}toUint8Array(t=!1){return this._sink.toUint8Array(t)}writeAll(t){return Sn(t)?t.then(e=>this.writeAll(e)):ei(t)?ho(this,t):uo(this,t)}get closed(){return this._sink.closed}[Symbol.asyncIterator](){return this._sink[Symbol.asyncIterator]()}toDOMStream(t){return this._sink.toDOMStream(t)}toNodeStream(t){return this._sink.toNodeStream(t)}close(){return this.reset()._sink.close()}abort(t){return this.reset()._sink.abort(t)}finish(){return this._autoDestroy?this.close():this.reset(this._sink,this._schema),this}reset(t=this._sink,e=null){return t===this._sink||t instanceof or?this._sink=t:(this._sink=new or,t&&ql(t)?this.toDOMStream({type:"bytes"}).pipeTo(t):t&&Hl(t)&&this.toNodeStream({objectMode:!1}).pipe(t)),this._started&&this._schema&&this._writeFooter(this._schema),this._started=!1,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._seenDictionaries=new Map,this._dictionaryDeltaOffsets=new Map,(!e||!Bs(e,this._schema))&&(e==null?(this._position=0,this._schema=null):(this._started=!0,this._schema=e,this._writeSchema(e))),this}write(t){let e=null;if(this._sink){if(t==null)return this.finish()&&void 0;if(t instanceof Vt&&!(e=t.schema))return this.finish()&&void 0;if(t instanceof Qt&&!(e=t.schema))return this.finish()&&void 0}else throw new Error("RecordBatchWriter is closed");if(e&&!Bs(e,this._schema)){if(this._started&&this._autoDestroy)return this.close();this.reset(this._sink,e)}t instanceof Qt?t instanceof io||this._writeRecordBatch(t):t instanceof Vt?this.writeAll(t.batches):$i(t)&&this.writeAll(t)}_writeMessage(t,e=8){const i=e-1,r=Wt.encode(t),s=r.byteLength,o=this._writeLegacyIpcFormat?4:8,a=s+o+i&~i,c=a-s-o;return t.headerType===at.RecordBatch?this._recordBatchBlocks.push(new sn(a,t.bodyLength,this._position)):t.headerType===at.DictionaryBatch&&this._dictionaryBlocks.push(new sn(a,t.bodyLength,this._position)),this._writeLegacyIpcFormat||this._write(Int32Array.of(-1)),this._write(Int32Array.of(a-o)),s>0&&this._write(r),this._writePadding(c)}_write(t){if(this._started){const e=rt(t);e&&e.byteLength>0&&(this._sink.write(e),this._position+=e.byteLength)}return this}_writeSchema(t){return this._writeMessage(Wt.from(t))}_writeFooter(t){return this._writeLegacyIpcFormat?this._write(Int32Array.of(0)):this._write(Int32Array.of(-1,0))}_writeMagic(){return this._write(Fi)}_writePadding(t){return t>0?this._write(new Uint8Array(t)):this}_writeRecordBatch(t){const{byteLength:e,nodes:i,bufferRegions:r,buffers:s}=Tt.assemble(t),o=new ee(t.numRows,i,r),a=Wt.from(o,e);return this._writeDictionaries(t)._writeMessage(a)._writeBodyBuffers(s)}_writeDictionaryBatch(t,e,i=!1){const{byteLength:r,nodes:s,bufferRegions:o,buffers:a}=Tt.assemble(new ct([t])),c=new ee(t.length,s,o),l=new Ne(c,e,i),b=Wt.from(l,r);return this._writeMessage(b)._writeBodyBuffers(a)}_writeBodyBuffers(t){let e,i,r;for(let s=-1,o=t.length;++s<o;)(e=t[s])&&(i=e.byteLength)>0&&(this._write(e),(r=(i+7&-8)-i)>0&&this._writePadding(r));return this}_writeDictionaries(t){var e,i;for(const[r,s]of t.dictionaries){const o=(e=s==null?void 0:s.data)!==null&&e!==void 0?e:[],a=this._seenDictionaries.get(r),c=(i=this._dictionaryDeltaOffsets.get(r))!==null&&i!==void 0?i:0;if(!a||a.data[0]!==o[0])for(const[l,b]of o.entries())this._writeDictionaryBatch(b,r,l>0);else if(c<o.length)for(const l of o.slice(c))this._writeDictionaryBatch(l,r,!0);this._seenDictionaries.set(r,s),this._dictionaryDeltaOffsets.set(r,o.length)}return this}}class co extends Il{static writeAll(t,e){const i=new co(e);return Sn(t)?t.then(r=>i.writeAll(r)):ei(t)?ho(i,t):uo(i,t)}}class lo extends Il{static writeAll(t){const e=new lo;return Sn(t)?t.then(i=>e.writeAll(i)):ei(t)?ho(e,t):uo(e,t)}constructor(){super(),this._autoDestroy=!0}_writeSchema(t){return this._writeMagic()._writePadding(2)}_writeDictionaryBatch(t,e,i=!1){if(!i&&this._seenDictionaries.has(e))throw new Error("The Arrow File format does not support replacement dictionaries. ");return super._writeDictionaryBatch(t,e,i)}_writeFooter(t){const e=Ai.encode(new Ai(t,Dt.V5,this._recordBatchBlocks,this._dictionaryBlocks));return super._writeFooter(t)._write(e)._write(Int32Array.of(e.byteLength))._writeMagic()}}function uo(n,t){let e=t;t instanceof Vt&&(e=t.batches,n.reset(void 0,t.schema));for(const i of e)n.write(i);return n.finish()}function ho(n,t){return Y(this,void 0,void 0,function*(){var e,i,r,s,o,a,c;try{for(e=!0,i=In(t);r=yield i.next(),s=r.done,!s;e=!0){c=r.value,e=!1;const l=c;n.write(l)}}catch(l){o={error:l}}finally{try{!e&&!s&&(a=i.return)&&(yield a.call(i))}finally{if(o)throw o.error}}return n.finish()})}function gd(n,t="stream"){return(t==="stream"?co:lo).writeAll(n).toUint8Array(!0)}var bd=Object.create,vl=Object.defineProperty,_d=Object.getOwnPropertyDescriptor,wd=Object.getOwnPropertyNames,Id=Object.getPrototypeOf,vd=Object.prototype.hasOwnProperty,Sd=(n,t)=>()=>(t||n((t={exports:{}}).exports,t),t.exports),Ed=(n,t,e,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of wd(t))!vd.call(n,r)&&r!==e&&vl(n,r,{get:()=>t[r],enumerable:!(i=_d(t,r))||i.enumerable});return n},Td=(n,t,e)=>(e=n!=null?bd(Id(n)):{},Ed(!n||!n.__esModule?vl(e,"default",{value:n,enumerable:!0}):e,n)),Ad=Sd((n,t)=>{t.exports=Worker}),Fd=(n=>(n[n.UNDEFINED=0]="UNDEFINED",n[n.AUTOMATIC=1]="AUTOMATIC",n[n.READ_ONLY=2]="READ_ONLY",n[n.READ_WRITE=3]="READ_WRITE",n))(Fd||{}),Od=(n=>(n[n.IDENTIFIER=0]="IDENTIFIER",n[n.NUMERIC_CONSTANT=1]="NUMERIC_CONSTANT",n[n.STRING_CONSTANT=2]="STRING_CONSTANT",n[n.OPERATOR=3]="OPERATOR",n[n.KEYWORD=4]="KEYWORD",n[n.COMMENT=5]="COMMENT",n))(Od||{}),Dd=(n=>(n[n.NONE=0]="NONE",n[n.DEBUG=1]="DEBUG",n[n.INFO=2]="INFO",n[n.WARNING=3]="WARNING",n[n.ERROR=4]="ERROR",n))(Dd||{}),Nd=(n=>(n[n.NONE=0]="NONE",n[n.CONNECT=1]="CONNECT",n[n.DISCONNECT=2]="DISCONNECT",n[n.OPEN=3]="OPEN",n[n.QUERY=4]="QUERY",n[n.INSTANTIATE=5]="INSTANTIATE",n))(Nd||{}),Bd=(n=>(n[n.NONE=0]="NONE",n[n.OK=1]="OK",n[n.ERROR=2]="ERROR",n[n.START=3]="START",n[n.RUN=4]="RUN",n[n.CAPTURE=5]="CAPTURE",n))(Bd||{}),Rd=(n=>(n[n.NONE=0]="NONE",n[n.WEB_WORKER=1]="WEB_WORKER",n[n.NODE_WORKER=2]="NODE_WORKER",n[n.BINDINGS=3]="BINDINGS",n[n.ASYNC_DUCKDB=4]="ASYNC_DUCKDB",n))(Rd||{}),Md=class{log(n){}};var Cd=(n=>(n[n.SUCCESS=0]="SUCCESS",n))(Cd||{}),Ld=class{constructor(n,t){this._bindings=n,this._conn=t}get bindings(){return this._bindings}async close(){return this._bindings.disconnect(this._conn)}useUnsafe(n){return n(this._bindings,this._conn)}async query(n){this._bindings.logger.log({timestamp:new Date,level:2,origin:4,topic:4,event:4,value:n});let t=await this._bindings.runQuery(this._conn,n),e=ce.from(t);return console.assert(e.isSync(),"Reader is not sync"),console.assert(e.isFile(),"Reader is not file"),new Vt(e)}async send(n){this._bindings.logger.log({timestamp:new Date,level:2,origin:4,topic:4,event:4,value:n});let t=await this._bindings.startPendingQuery(this._conn,n);for(;t==null;)t=await this._bindings.pollPendingQuery(this._conn);let e=new Sl(this._bindings,this._conn,t),i=await ce.from(e);return console.assert(i.isAsync()),console.assert(i.isStream()),i}async cancelSent(){return await this._bindings.cancelPendingQuery(this._conn)}async getTableNames(n){return await this._bindings.getTableNames(this._conn,n)}async prepare(n){let t=await this._bindings.createPrepared(this._conn,n);return new Ud(this._bindings,this._conn,t)}async insertArrowTable(n,t){let e=gd(n,"stream");await this.insertArrowFromIPCStream(e,t)}async insertArrowFromIPCStream(n,t){await this._bindings.insertArrowFromIPCStream(this._conn,n,t)}async insertCSVFromPath(n,t){await this._bindings.insertCSVFromPath(this._conn,n,t)}async insertJSONFromPath(n,t){await this._bindings.insertJSONFromPath(this._conn,n,t)}},Sl=class{constructor(n,t,e){this.db=n,this.conn=t,this.header=e,this._first=!0,this._depleted=!1,this._inFlight=null}async next(){if(this._first)return this._first=!1,{done:!1,value:this.header};if(this._depleted)return{done:!0,value:null};let n;return this._inFlight!=null?(n=await this._inFlight,this._inFlight=null):n=await this.db.fetchQueryResults(this.conn),this._depleted=n.length==0,this._depleted||(this._inFlight=this.db.fetchQueryResults(this.conn)),{done:this._depleted,value:n}}[Symbol.asyncIterator](){return this}},Ud=class{constructor(n,t,e){this.bindings=n,this.connectionId=t,this.statementId=e}async close(){await this.bindings.closePrepared(this.connectionId,this.statementId)}async query(...n){let t=await this.bindings.runPrepared(this.connectionId,this.statementId,n),e=ce.from(t);return console.assert(e.isSync()),console.assert(e.isFile()),new Vt(e)}async send(...n){let t=await this.bindings.sendPrepared(this.connectionId,this.statementId,n),e=new Sl(this.bindings,this.connectionId,t),i=await ce.from(e);return console.assert(i.isAsync()),console.assert(i.isStream()),i}},xd=(n=>(n.CANCEL_PENDING_QUERY="CANCEL_PENDING_QUERY",n.CLOSE_PREPARED="CLOSE_PREPARED",n.COLLECT_FILE_STATISTICS="COLLECT_FILE_STATISTICS",n.CONNECT="CONNECT",n.COPY_FILE_TO_BUFFER="COPY_FILE_TO_BUFFER",n.COPY_FILE_TO_PATH="COPY_FILE_TO_PATH",n.CREATE_PREPARED="CREATE_PREPARED",n.DISCONNECT="DISCONNECT",n.DROP_FILE="DROP_FILE",n.DROP_FILES="DROP_FILES",n.EXPORT_FILE_STATISTICS="EXPORT_FILE_STATISTICS",n.FETCH_QUERY_RESULTS="FETCH_QUERY_RESULTS",n.FLUSH_FILES="FLUSH_FILES",n.GET_FEATURE_FLAGS="GET_FEATURE_FLAGS",n.GET_TABLE_NAMES="GET_TABLE_NAMES",n.GET_VERSION="GET_VERSION",n.GLOB_FILE_INFOS="GLOB_FILE_INFOS",n.INSERT_ARROW_FROM_IPC_STREAM="INSERT_ARROW_FROM_IPC_STREAM",n.INSERT_CSV_FROM_PATH="IMPORT_CSV_FROM_PATH",n.INSERT_JSON_FROM_PATH="IMPORT_JSON_FROM_PATH",n.INSTANTIATE="INSTANTIATE",n.OPEN="OPEN",n.PING="PING",n.POLL_PENDING_QUERY="POLL_PENDING_QUERY",n.REGISTER_FILE_BUFFER="REGISTER_FILE_BUFFER",n.REGISTER_FILE_HANDLE="REGISTER_FILE_HANDLE",n.REGISTER_FILE_URL="REGISTER_FILE_URL",n.RESET="RESET",n.RUN_PREPARED="RUN_PREPARED",n.RUN_QUERY="RUN_QUERY",n.SEND_PREPARED="SEND_PREPARED",n.START_PENDING_QUERY="START_PENDING_QUERY",n.TOKENIZE="TOKENIZE",n))(xd||{}),Pd=(n=>(n.CONNECTION_INFO="CONNECTION_INFO",n.ERROR="ERROR",n.FEATURE_FLAGS="FEATURE_FLAGS",n.FILE_BUFFER="FILE_BUFFER",n.FILE_INFOS="FILE_INFOS",n.FILE_SIZE="FILE_SIZE",n.FILE_STATISTICS="FILE_STATISTICS",n.INSTANTIATE_PROGRESS="INSTANTIATE_PROGRESS",n.LOG="LOG",n.OK="OK",n.PREPARED_STATEMENT_ID="PREPARED_STATEMENT_ID",n.QUERY_PLAN="QUERY_PLAN",n.QUERY_RESULT="QUERY_RESULT",n.QUERY_RESULT_CHUNK="QUERY_RESULT_CHUNK",n.QUERY_RESULT_HEADER="QUERY_RESULT_HEADER",n.QUERY_RESULT_HEADER_OR_NULL="QUERY_RESULT_HEADER_OR_NULL",n.REGISTERED_FILE="REGISTERED_FILE",n.SCRIPT_TOKENS="SCRIPT_TOKENS",n.SUCCESS="SUCCESS",n.TABLE_NAMES="TABLE_NAMES",n.VERSION_STRING="VERSION_STRING",n))(Pd||{}),ot=class{constructor(n,t){this.promiseResolver=()=>{},this.promiseRejecter=()=>{},this.type=n,this.data=t,this.promise=new Promise((e,i)=>{this.promiseResolver=e,this.promiseRejecter=i})}};function lr(n){switch(n.typeId){case h.Binary:return{sqlType:"binary"};case h.Bool:return{sqlType:"bool"};case h.Date:return{sqlType:"date"};case h.DateDay:return{sqlType:"date32[d]"};case h.DateMillisecond:return{sqlType:"date64[ms]"};case h.Decimal:{let t=n;return{sqlType:"decimal",precision:t.precision,scale:t.scale}}case h.Float:return{sqlType:"float"};case h.Float16:return{sqlType:"float16"};case h.Float32:return{sqlType:"float32"};case h.Float64:return{sqlType:"float64"};case h.Int:return{sqlType:"int32"};case h.Int16:return{sqlType:"int16"};case h.Int32:return{sqlType:"int32"};case h.Int64:return{sqlType:"int64"};case h.Uint16:return{sqlType:"uint16"};case h.Uint32:return{sqlType:"uint32"};case h.Uint64:return{sqlType:"uint64"};case h.Uint8:return{sqlType:"uint8"};case h.IntervalDayTime:return{sqlType:"interval[dt]"};case h.IntervalYearMonth:return{sqlType:"interval[m]"};case h.List:return{sqlType:"list",valueType:lr(n.valueType)};case h.FixedSizeBinary:return{sqlType:"fixedsizebinary",byteWidth:n.byteWidth};case h.Null:return{sqlType:"null"};case h.Utf8:return{sqlType:"utf8"};case h.Struct:return{sqlType:"struct",fields:n.children.map(t=>Cs(t.name,t.type))};case h.Map:{let t=n;return{sqlType:"map",keyType:lr(t.keyType),valueType:lr(t.valueType)}}case h.Time:return{sqlType:"time[s]"};case h.TimeMicrosecond:return{sqlType:"time[us]"};case h.TimeMillisecond:return{sqlType:"time[ms]"};case h.TimeNanosecond:return{sqlType:"time[ns]"};case h.TimeSecond:return{sqlType:"time[s]"};case h.Timestamp:return{sqlType:"timestamp",timezone:n.timezone||void 0};case h.TimestampSecond:return{sqlType:"timestamp[s]",timezone:n.timezone||void 0};case h.TimestampMicrosecond:return{sqlType:"timestamp[us]",timezone:n.timezone||void 0};case h.TimestampNanosecond:return{sqlType:"timestamp[ns]",timezone:n.timezone||void 0};case h.TimestampMillisecond:return{sqlType:"timestamp[ms]",timezone:n.timezone||void 0}}throw new Error("unsupported arrow type: ".concat(n.toString()))}function Cs(n,t){let e=lr(t);return e.name=n,e}var kd=new TextEncoder,$d=class{constructor(n,t=null){this._onInstantiationProgress=[],this._worker=null,this._workerShutdownPromise=null,this._workerShutdownResolver=()=>{},this._nextMessageId=0,this._pendingRequests=new Map,this._logger=n,this._onMessageHandler=this.onMessage.bind(this),this._onErrorHandler=this.onError.bind(this),this._onCloseHandler=this.onClose.bind(this),t!=null&&this.attach(t)}get logger(){return this._logger}attach(n){this._worker=n,this._worker.addEventListener("message",this._onMessageHandler),this._worker.addEventListener("error",this._onErrorHandler),this._worker.addEventListener("close",this._onCloseHandler),this._workerShutdownPromise=new Promise((t,e)=>{this._workerShutdownResolver=t})}detach(){this._worker&&(this._worker.removeEventListener("message",this._onMessageHandler),this._worker.removeEventListener("error",this._onErrorHandler),this._worker.removeEventListener("close",this._onCloseHandler),this._worker=null,this._workerShutdownResolver(null),this._workerShutdownPromise=null,this._workerShutdownResolver=()=>{})}async terminate(){this._worker&&(this._worker.terminate(),this._worker=null,this._workerShutdownPromise=null,this._workerShutdownResolver=()=>{})}async postTask(n,t=[]){if(!this._worker){console.error("cannot send a message since the worker is not set!");return}let e=this._nextMessageId++;return this._pendingRequests.set(e,n),this._worker.postMessage({messageId:e,type:n.type,data:n.data},t),await n.promise}onMessage(n){var t;let e=n.data;switch(e.type){case"LOG":{this._logger.log(e.data);return}case"INSTANTIATE_PROGRESS":{for(let r of this._onInstantiationProgress)r(e.data);return}}let i=this._pendingRequests.get(e.requestId);if(!i){console.warn("unassociated response: [".concat(e.requestId,", ").concat(e.type.toString(),"]"));return}if(this._pendingRequests.delete(e.requestId),e.type=="ERROR"){let r=new Error(e.data.message);r.name=e.data.name,(t=Object.getOwnPropertyDescriptor(r,"stack"))!=null&&t.writable&&(r.stack=e.data.stack),i.promiseRejecter(r);return}switch(i.type){case"CLOSE_PREPARED":case"COLLECT_FILE_STATISTICS":case"COPY_FILE_TO_PATH":case"DISCONNECT":case"DROP_FILE":case"DROP_FILES":case"FLUSH_FILES":case"INSERT_ARROW_FROM_IPC_STREAM":case"IMPORT_CSV_FROM_PATH":case"IMPORT_JSON_FROM_PATH":case"OPEN":case"PING":case"REGISTER_FILE_BUFFER":case"REGISTER_FILE_HANDLE":case"REGISTER_FILE_URL":case"RESET":if(e.type=="OK"){i.promiseResolver(e.data);return}break;case"INSTANTIATE":if(this._onInstantiationProgress=[],e.type=="OK"){i.promiseResolver(e.data);return}break;case"GLOB_FILE_INFOS":if(e.type=="FILE_INFOS"){i.promiseResolver(e.data);return}break;case"GET_VERSION":if(e.type=="VERSION_STRING"){i.promiseResolver(e.data);return}break;case"GET_FEATURE_FLAGS":if(e.type=="FEATURE_FLAGS"){i.promiseResolver(e.data);return}break;case"GET_TABLE_NAMES":if(e.type=="TABLE_NAMES"){i.promiseResolver(e.data);return}break;case"TOKENIZE":if(e.type=="SCRIPT_TOKENS"){i.promiseResolver(e.data);return}break;case"COPY_FILE_TO_BUFFER":if(e.type=="FILE_BUFFER"){i.promiseResolver(e.data);return}break;case"EXPORT_FILE_STATISTICS":if(e.type=="FILE_STATISTICS"){i.promiseResolver(e.data);return}break;case"CONNECT":if(e.type=="CONNECTION_INFO"){i.promiseResolver(e.data);return}break;case"RUN_PREPARED":case"RUN_QUERY":if(e.type=="QUERY_RESULT"){i.promiseResolver(e.data);return}break;case"SEND_PREPARED":if(e.type=="QUERY_RESULT_HEADER"){i.promiseResolver(e.data);return}break;case"START_PENDING_QUERY":if(e.type=="QUERY_RESULT_HEADER_OR_NULL"){i.promiseResolver(e.data);return}break;case"POLL_PENDING_QUERY":if(e.type=="QUERY_RESULT_HEADER_OR_NULL"){i.promiseResolver(e.data);return}break;case"CANCEL_PENDING_QUERY":if(this._onInstantiationProgress=[],e.type=="SUCCESS"){i.promiseResolver(e.data);return}break;case"FETCH_QUERY_RESULTS":if(e.type=="QUERY_RESULT_CHUNK"){i.promiseResolver(e.data);return}break;case"CREATE_PREPARED":if(e.type=="PREPARED_STATEMENT_ID"){i.promiseResolver(e.data);return}break}i.promiseRejecter(new Error("unexpected response type: ".concat(e.type.toString())))}onError(n){console.error(n),console.error("error in duckdb worker: ".concat(n.message)),this._pendingRequests.clear()}onClose(){if(this._workerShutdownResolver(null),this._pendingRequests.size!=0){console.warn("worker terminated with ".concat(this._pendingRequests.size," pending requests"));return}this._pendingRequests.clear()}async reset(){let n=new ot("RESET",null);return await this.postTask(n)}async ping(){let n=new ot("PING",null);await this.postTask(n)}async dropFile(n){let t=new ot("DROP_FILE",n);return await this.postTask(t)}async dropFiles(){let n=new ot("DROP_FILES",null);return await this.postTask(n)}async flushFiles(){let n=new ot("FLUSH_FILES",null);return await this.postTask(n)}async instantiate(n,t=null,e=i=>{}){this._onInstantiationProgress.push(e);let i=new ot("INSTANTIATE",[n,t]);return await this.postTask(i)}async getVersion(){let n=new ot("GET_VERSION",null);return await this.postTask(n)}async getFeatureFlags(){let n=new ot("GET_FEATURE_FLAGS",null);return await this.postTask(n)}async open(n){let t=new ot("OPEN",n);await this.postTask(t)}async tokenize(n){let t=new ot("TOKENIZE",n);return await this.postTask(t)}async connectInternal(){let n=new ot("CONNECT",null);return await this.postTask(n)}async connect(){let n=await this.connectInternal();return new Ld(this,n)}async disconnect(n){let t=new ot("DISCONNECT",n);await this.postTask(t)}async runQuery(n,t){let e=new ot("RUN_QUERY",[n,t]);return await this.postTask(e)}async startPendingQuery(n,t){let e=new ot("START_PENDING_QUERY",[n,t]);return await this.postTask(e)}async pollPendingQuery(n){let t=new ot("POLL_PENDING_QUERY",n);return await this.postTask(t)}async cancelPendingQuery(n){let t=new ot("CANCEL_PENDING_QUERY",n);return await this.postTask(t)}async fetchQueryResults(n){let t=new ot("FETCH_QUERY_RESULTS",n);return await this.postTask(t)}async getTableNames(n,t){let e=new ot("GET_TABLE_NAMES",[n,t]);return await this.postTask(e)}async createPrepared(n,t){let e=new ot("CREATE_PREPARED",[n,t]);return await this.postTask(e)}async closePrepared(n,t){let e=new ot("CLOSE_PREPARED",[n,t]);await this.postTask(e)}async runPrepared(n,t,e){let i=new ot("RUN_PREPARED",[n,t,e]);return await this.postTask(i)}async sendPrepared(n,t,e){let i=new ot("SEND_PREPARED",[n,t,e]);return await this.postTask(i)}async globFiles(n){let t=new ot("GLOB_FILE_INFOS",n);return await this.postTask(t)}async registerFileText(n,t){let e=kd.encode(t);await this.registerFileBuffer(n,e)}async registerFileURL(n,t,e,i){t===void 0&&(t=n);let r=new ot("REGISTER_FILE_URL",[n,t,e,i]);await this.postTask(r)}async registerEmptyFileBuffer(n){}async registerFileBuffer(n,t){let e=new ot("REGISTER_FILE_BUFFER",[n,t]);await this.postTask(e,[t.buffer])}async registerFileHandle(n,t,e,i){let r=new ot("REGISTER_FILE_HANDLE",[n,t,e,i]);await this.postTask(r,[])}async collectFileStatistics(n,t){let e=new ot("COLLECT_FILE_STATISTICS",[n,t]);await this.postTask(e,[])}async exportFileStatistics(n){let t=new ot("EXPORT_FILE_STATISTICS",n);return await this.postTask(t,[])}async copyFileToBuffer(n){let t=new ot("COPY_FILE_TO_BUFFER",n);return await this.postTask(t)}async copyFileToPath(n,t){let e=new ot("COPY_FILE_TO_PATH",[n,t]);await this.postTask(e)}async insertArrowFromIPCStream(n,t,e){if(t.length==0)return;let i=new ot("INSERT_ARROW_FROM_IPC_STREAM",[n,t,e]);await this.postTask(i,[t.buffer])}async insertCSVFromPath(n,t,e){if(e.columns!==void 0){let r=[];for(let s in e.columns){let o=e.columns[s];r.push(Cs(s,o))}e.columnsFlat=r,delete e.columns}let i=new ot("IMPORT_CSV_FROM_PATH",[n,t,e]);await this.postTask(i)}async insertJSONFromPath(n,t,e){if(e.columns!==void 0){let r=[];for(let s in e.columns){let o=e.columns[s];r.push(Cs(s,o))}e.columnsFlat=r,delete e.columns}let i=new ot("IMPORT_JSON_FROM_PATH",[n,t,e]);await this.postTask(i)}},jd=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,3,1,0,1,10,14,1,12,0,65,0,65,0,65,0,252,10,0,0,11])),Vd=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,8,1,6,0,6,64,25,11,11])),zd=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11])),Wd=()=>(async n=>{try{return typeof MessageChannel<"u"&&new MessageChannel().port1.postMessage(new SharedArrayBuffer(1)),WebAssembly.validate(n)}catch{return!1}})(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,4,1,3,1,1,10,11,1,9,0,65,0,254,16,2,0,26,11])),Yd={version:"1.29.0"},fo=Yd.version.split(".");fo[0];fo[1];fo[2];var Gd=()=>typeof navigator>"u",hs=null,ds=null,fs=null,ps=null,ys=null;async function qd(){return hs==null&&(hs=typeof BigInt64Array<"u"),ds==null&&(ds=await Vd()),fs==null&&(fs=await Wd()),ps==null&&(ps=await zd()),ys==null&&(ys=await jd()),{bigInt64Array:hs,crossOriginIsolated:Gd()||globalThis.crossOriginIsolated||!1,wasmExceptions:ds,wasmSIMD:ps,wasmThreads:fs,wasmBulkMemory:ys}}Td(Ad());function Hd(){let n=new TextDecoder;return t=>(typeof SharedArrayBuffer<"u"&&t.buffer instanceof SharedArrayBuffer&&(t=new Uint8Array(t)),n.decode(t))}Hd();var El=(n=>(n[n.BUFFER=0]="BUFFER",n[n.NODE_FS=1]="NODE_FS",n[n.BROWSER_FILEREADER=2]="BROWSER_FILEREADER",n[n.BROWSER_FSACCESS=3]="BROWSER_FSACCESS",n[n.HTTP=4]="HTTP",n[n.S3=5]="S3",n))(El||{});let me,vn;const{resolve:Qd,reject:Jd,promise:Kd}=ba(),{resolve:Zd,reject:Xd,promise:tf}=ba();let ca=!1;async function po(){if(!me){if(ca)return _a(Kd);ca=!0;try{const t=await qd().then(s=>s.wasmExceptions)?{mainModule:(await Hi(async()=>{const{default:s}=await import("./duckdb-eh.CjaN1hVf.js");return{default:s}},[])).default,mainWorker:(await Hi(async()=>{const{default:s}=await import("./duckdb-browser-eh.worker.xVZH6Sl3.js");return{default:s}},[])).default}:{mainModule:(await Hi(async()=>{const{default:s}=await import("./duckdb-mvp.Bsk8BUlZ.js");return{default:s}},[])).default,mainWorker:(await Hi(async()=>{const{default:s}=await import("./duckdb-browser-mvp.worker.BgKlvv5e.js");return{default:s}},[])).default},e=new Md,i=new t.mainWorker,r=new $d(e,i);window[Symbol.for("EVIDENCE_QUERY_ENGINE")]=r,await r.instantiate(t.mainModule),me=r,await me.open({query:{castBigIntToDouble:!0,castTimestampToDate:!0,castDecimalToDouble:!0,castDurationToTime64:!0}}),vn=await me.connect(),await vn.query("SET ieee_floating_point_ops = false;"),await vn.query("SET old_implicit_casting = true;"),Qd()}catch(n){throw Jd(n),n}}}async function qf(n){me||await po(),await vn.query(`PRAGMA search_path='${n.join(",")}'`)}async function ms(n){await me.flushFiles();for(const t of await me.globFiles(n))await me.dropFile(t.fileName)}async function Hf(n,{append:t,addBasePath:e=i=>i}={}){me||await po(),t||await ms("*");try{for(const i in n){await vn.query(`CREATE SCHEMA IF NOT EXISTS "${i}";`);for(const r of n[i]){const s=r.split(/[\\/]/).at(-1).slice(0,-8),o=`${i}_${s}.parquet`;let a=r;!r.startsWith("http")&&!r.startsWith("/")&&(a=`/${r}`),a.startsWith("/static")&&(a=a.substring(7)),t&&(await ms(o),await ms(r)),await me.registerFileURL(o,e(a),El.HTTP,!1),await vn.query(`CREATE OR REPLACE VIEW "${i}"."${s}" AS (SELECT * FROM read_parquet('${o}'));`)}}Zd()}catch(i){throw Xd(i),console.error("Error encountered while updating Parquet URLs",i),i}}async function ef(n){return me||await po(),await _a(tf),await vn.query(n).then(zl)}const Qf=Jr(!0),Jf=Jr("");function nf(){const{subscribe:n,update:t}=Jr([]),e=new Map,i=r=>{t(s=>s.filter(o=>o.id!==r))};return{subscribe:n,add:(r,s=2e3)=>{if(r.id=r.id??Math.random().toString(),t(o=>{const a=o.find(c=>c.id===r.id);return a?(Object.assign(a,r),e.has(r.id)&&(clearTimeout(e.get(r.id)),e.delete(r.id))):o.push(r),o}),s){const o=setTimeout(()=>{i(r.id),e.delete(r.id)},s);e.set(r.id,o)}},dismiss:r=>{i(r),e.has(r)&&(clearTimeout(e.get(r)),e.delete[r])}}}const Kf=nf(),rf=(n,t,e)=>{const i=(e==null?void 0:e.serialize)??JSON.stringify,r=(e==null?void 0:e.deserialize)??JSON.parse,s=Jr(r(localStorage.getItem(n))??t),{subscribe:o,set:a}=s,c=l=>{typeof l>"u"||l===null?localStorage.removeItem(n):localStorage.setItem(n,i(l))};return c(ws(s)),{subscribe:o,set:l=>{a(l),c(l)},update:l=>{const b=l(ws(s));a(b),c(b)}}},Zf=rf("showQueries",kl);let sf=(n=21)=>crypto.getRandomValues(new Uint8Array(n)).reduce((t,e)=>(e&=63,e<36?t+=e.toString(36):e<62?t+=(e-26).toString(36).toUpperCase():e>62?t+="-":t+="_",t),"");const of={};var la={};const af=new Set,Xf=n=>{bi()?n():af.add(n)},bi=()=>{if(typeof process<"u")return!!(la.EVIDENCE_DEBUG||la.VITE_PUBLIC_EVIDENCE_DEBUG);if(typeof of<"u")return!1};class Oi{constructor(t,e){t&&(this.table=String(t)),e&&(this.column=e)}get columns(){return this.column?[this.column]:[]}toString(){const{table:t,column:e}=this;if(e){const i=e.startsWith("*")?e:`"${e}"`;return`${t?`${ua(t)}.`:""}${i}`}else return t?ua(t):"NULL"}}function ua(n){return n.split(".").map(e=>`"${e}"`).join(".")}function cf(n,t){return n instanceof Oi&&n.column===t}function en(n){return typeof n=="string"?uf(n):n}function gs(n){return typeof n=="string"?lf(n):n}function lf(n){return new Oi(n)}function uf(n,t=null){return arguments.length===1&&(t=n,n=null),new Oi(n,t)}function yo(n){switch(typeof n){case"boolean":return n?"TRUE":"FALSE";case"string":return`'${n.replace("'","''")}'`;case"number":return Number.isFinite(n)?String(n):"NULL";default:if(n==null)return"NULL";if(n instanceof Date){const t=+n;if(Number.isNaN(t))return"NULL";const e=n.getUTCFullYear(),i=n.getUTCMonth(),r=n.getUTCDate();return t===Date.UTC(e,i,r)?`MAKE_DATE(${e}, ${i+1}, ${r})`:`EPOCH_MS(${t})`}else return n instanceof RegExp?`'${n.source}'`:String(n)}}const Di=n=>typeof(n==null?void 0:n.addEventListener)=="function";function Tl(n){return n instanceof ns}class ns{constructor(t,e,i){this._expr=Array.isArray(t)?t:[t],this._deps=e||[],this.annotate(i);const r=this._expr.filter(s=>Di(s));r.length>0?(this._params=Array.from(new Set(r)),this._params.forEach(s=>{s.addEventListener("value",()=>{var o;return hf(this,(o=this.map)==null?void 0:o.get("value"))})})):this.addEventListener=void 0}get value(){return this}get columns(){const{_params:t,_deps:e}=this;if(t){const i=new Set(t.flatMap(r=>{var o;const s=(o=r.value)==null?void 0:o.columns;return Array.isArray(s)?s:[]}));if(i.size){const r=new Set(e);return i.forEach(s=>r.add(s)),Array.from(r)}}return e}get column(){return this._deps.length?this._deps[0]:this.columns[0]}annotate(...t){return Object.assign(this,...t)}toString(){return this._expr.map(t=>Di(t)&&!Tl(t)?yo(t.value):t).join("")}addEventListener(t,e){const i=this.map||(this.map=new Map);(i.get(t)||(i.set(t,new Set),i.get(t))).add(e)}}function hf(n,t){if(t!=null&&t.size)return Promise.allSettled(Array.from(t,e=>e(n)))}function Al(n,t){const e=[n[0]],i=new Set,r=t.length;for(let s=0,o=0;s<r;){const a=t[s];Di(a)?e[++o]=a:(Array.isArray(a==null?void 0:a.columns)&&a.columns.forEach(l=>i.add(l)),e[o]+=typeof a=="string"?a:yo(a));const c=n[++s];Di(e[o])?e[++o]=c:e[o]+=c}return{spans:e,cols:Array.from(i)}}function Ct(n,...t){const{spans:e,cols:i}=Al(n,t);return new ns(e,i)}function Ls(n,t){return Array.from({length:n},()=>t)}class fn extends ns{constructor(t,e,i,r,s="",o="",a=""){let c;if(r&&!(s||o||a))c=r?Ct`${e} OVER "${r}"`:Ct`${e} OVER ()`;else{const tt=s&&o?" ":"",O=(s||o)&&a?" ":"";c=Ct`${e} OVER (${r?`"${r}" `:""}${s}${tt}${o}${O}${a})`}i&&(c=Ct`(${c})::${i}`);const{_expr:b,_deps:S}=c;super(b,S),this.window=t,this.func=e,this.type=i,this.name=r,this.group=s,this.order=o,this.frame=a}get basis(){return this.column}get label(){const{func:t}=this;return t.label??t.toString()}over(t){const{window:e,func:i,type:r,group:s,order:o,frame:a}=this;return new fn(e,i,r,t,s,o,a)}partitionby(...t){const e=t.flat().filter(b=>b).map(en),i=Ct(["PARTITION BY ",Ls(e.length-1,", "),""],...e),{window:r,func:s,type:o,name:a,order:c,frame:l}=this;return new fn(r,s,o,a,i,c,l)}orderby(...t){const e=t.flat().filter(b=>b).map(en),i=Ct(["ORDER BY ",Ls(e.length-1,", "),""],...e),{window:r,func:s,type:o,name:a,group:c,frame:l}=this;return new fn(r,s,o,a,c,i,l)}rows(t){const e=ha("ROWS",t),{window:i,func:r,type:s,name:o,group:a,order:c}=this;return new fn(i,r,s,o,a,c,e)}range(t){const e=ha("RANGE",t),{window:i,func:r,type:s,name:o,group:a,order:c}=this;return new fn(i,r,s,o,a,c,e)}}function ha(n,t){if(Di(t)){const e=Ct`${t}`;return e.toString=()=>`${n} ${da(t.value)}`,e}return`${n} ${da(t)}`}function da(n){const[t,e]=n,i=t===0?"CURRENT ROW":Number.isFinite(t)?`${Math.abs(t)} PRECEDING`:"UNBOUNDED PRECEDING",r=e===0?"CURRENT ROW":Number.isFinite(e)?`${Math.abs(e)} FOLLOWING`:"UNBOUNDED FOLLOWING";return`BETWEEN ${i} AND ${r}`}class wi extends ns{constructor(t,e,i,r,s){e=(e||[]).map(en);const{strings:o,exprs:a}=df(t,e,i,r,s),{spans:c,cols:l}=Al(o,a);super(c,l),this.aggregate=t,this.args=e,this.type=i,this.isDistinct=r,this.filter=s}get basis(){return this.column}get label(){const{aggregate:t,args:e,isDistinct:i}=this,r=i?"DISTINCT"+(e.length?" ":""):"",s=e.length?`(${r}${e.map(ff).join(", ")})`:"";return`${t.toLowerCase()}${s}`}distinct(){const{aggregate:t,args:e,type:i,filter:r}=this;return new wi(t,e,i,!0,r)}where(t){const{aggregate:e,args:i,type:r,isDistinct:s}=this;return new wi(e,i,r,s,t)}window(){const{aggregate:t,args:e,type:i,isDistinct:r}=this,s=new wi(t,e,null,r);return new fn(t,s,i)}partitionby(...t){return this.window().partitionby(...t)}orderby(...t){return this.window().orderby(...t)}rows(t){return this.window().rows(t)}range(t){return this.window().range(t)}}function df(n,t,e,i,r){const s=`)${e?`::${e}`:""}`;let o=[`${n}(${i?"DISTINCT ":""}`],a=[];return t.length?(o=o.concat([...Ls(t.length-1,", "),`${s}${r?" FILTER (WHERE ":""}`,...r?[")"]:[]]),a=[...t,...r?[r]:[]]):o[0]+="*"+s,{exprs:a,strings:o}}function ff(n){const t=yo(n);return t&&t.startsWith('"')&&t.endsWith('"')?t.slice(1,-1):t}function ii(n,t){return(...e)=>new wi(n,e,t)}const pf=ii("COUNT","INTEGER"),yf=ii("AVG"),mf=ii("MAX"),gf=ii("MIN"),bf=ii("SUM","DOUBLE"),_f=ii("MEDIAN");let ur=class xn{static select(...t){return new xn().select(...t)}static from(...t){return new xn().from(...t)}static with(...t){return new xn().with(...t)}static union(...t){return new yn("UNION",t.flat())}static unionAll(...t){return new yn("UNION ALL",t.flat())}static intersect(...t){return new yn("INTERSECT",t.flat())}static except(...t){return new yn("EXCEPT",t.flat())}static describe(t){const e=t.clone(),{clone:i,toString:r}=e;return Object.assign(e,{describe:!0,clone:()=>xn.describe(i.call(e)),toString:()=>`DESCRIBE ${r.call(e)}`})}constructor(){this.query={with:[],select:[],from:[],where:[],groupby:[],having:[],window:[],qualify:[],orderby:[]},this.cteFor=null}clone(){const t=new xn;return t.query={...this.query},t}with(...t){const{query:e}=this;if(t.length===0)return e.with;{const i=[],r=(s,o)=>{const a=o.clone();a.cteFor=this,i.push({as:s,query:a})};return t.flat().forEach(s=>{if(s!=null)if(s.as&&s.query)r(s.as,s.query);else for(const o in s)r(o,s[o])}),e.with=e.with.concat(i),this}}select(...t){const{query:e}=this;if(t.length===0)return e.select;{const i=[];for(const s of t.flat())if(s!=null)if(typeof s=="string")i.push({as:s,expr:en(s)});else if(s instanceof Oi)i.push({as:s.column,expr:s});else if(Array.isArray(s))i.push({as:s[0],expr:s[1]});else for(const o in s)i.push({as:Zi(o),expr:en(s[o])});const r=new Set(i.map(s=>s.as));return e.select=e.select.filter(s=>!r.has(s.as)).concat(i.filter(s=>s.expr)),this}}$select(...t){return this.query.select=[],this.select(...t)}distinct(t=!0){return this.query.distinct=!!t,this}from(...t){const{query:e}=this;if(t.length===0)return e.from;{const i=[];return t.flat().forEach(r=>{if(r!=null)if(typeof r=="string")i.push({as:r,from:gs(r)});else if(r instanceof Oi)i.push({as:r.table,from:r});else if(bs(r)||Tl(r))i.push({from:r});else if(Array.isArray(r))i.push({as:Zi(r[0]),from:gs(r[1])});else for(const s in r)i.push({as:Zi(s),from:gs(r[s])})}),e.from=e.from.concat(i),this}}$from(...t){return this.query.from=[],this.from(...t)}sample(t,e){const{query:i}=this;if(arguments.length===0)return i.sample;{let r=t;return typeof t=="number"&&(r=t>0&&t<1?{perc:100*t,method:e}:{rows:Math.round(t),method:e}),i.sample=r,this}}where(...t){const{query:e}=this;return t.length===0?e.where:(e.where=e.where.concat(t.flat().filter(i=>i)),this)}$where(...t){return this.query.where=[],this.where(...t)}groupby(...t){const{query:e}=this;return t.length===0?e.groupby:(e.groupby=e.groupby.concat(t.flat().filter(i=>i).map(en)),this)}$groupby(...t){return this.query.groupby=[],this.groupby(...t)}having(...t){const{query:e}=this;return t.length===0?e.having:(e.having=e.having.concat(t.flat().filter(i=>i)),this)}window(...t){const{query:e}=this;if(t.length===0)return e.window;{const i=[];return t.flat().forEach(r=>{if(r!=null)for(const s in r)i.push({as:Zi(s),expr:r[s]})}),e.window=e.window.concat(i),this}}qualify(...t){const{query:e}=this;return t.length===0?e.qualify:(e.qualify=e.qualify.concat(t.flat().filter(i=>i)),this)}orderby(...t){const{query:e}=this;return t.length===0?e.orderby:(e.orderby=e.orderby.concat(t.flat().filter(i=>i).map(en)),this)}limit(t){const{query:e}=this;return arguments.length===0?e.limit:(e.limit=Number.isFinite(t)?t:void 0,this)}offset(t){const{query:e}=this;return arguments.length===0?e.offset:(e.offset=Number.isFinite(t)?t:void 0,this)}get subqueries(){const{query:t,cteFor:e}=this,i=((e==null?void 0:e.query)||t).with,r=i==null?void 0:i.reduce((o,{as:a,query:c})=>(o[a]=c,o),{}),s=[];return t.from.forEach(({from:o})=>{if(bs(o))s.push(o);else if(r[o.table]){const a=r[o.table];s.push(a)}}),s}toString(){const{with:t,select:e,distinct:i,from:r,sample:s,where:o,groupby:a,having:c,window:l,qualify:b,orderby:S,limit:tt,offset:O}=this.query,W=[];if(t.length){const K=t.map(({as:it,query:gt})=>`"${it}" AS (${gt})`);W.push(`WITH ${K.join(", ")}`)}const Bt=e.map(({as:K,expr:it})=>cf(it,K)&&!it.table?`${it}`:`${it} AS "${K}"`);if(W.push(`SELECT${i?" DISTINCT":""} ${Bt.join(", ")}`),r.length){const K=r.map(({as:it,from:gt})=>{const At=bs(gt)?`(${gt})`:`${gt}`;return!it||it===gt.table?At:`${At} AS "${it}"`});W.push(`FROM ${K.join(", ")}`)}if(o.length){const K=o.map(String).filter(it=>it).join(" AND ");K&&W.push(`WHERE ${K}`)}if(s){const{rows:K,perc:it,method:gt,seed:At}=s,N=K?`${K} ROWS`:`${it} PERCENT`,kt=gt?` (${gt}${At!=null?`, ${At}`:""})`:"";W.push(`USING SAMPLE ${N}${kt}`)}if(a.length&&W.push(`GROUP BY ${a.join(", ")}`),c.length){const K=c.map(String).filter(it=>it).join(" AND ");K&&W.push(`HAVING ${K}`)}if(l.length){const K=l.map(({as:it,expr:gt})=>`"${it}" AS (${gt})`);W.push(`WINDOW ${K.join(", ")}`)}if(b.length){const K=b.map(String).filter(it=>it).join(" AND ");K&&W.push(`QUALIFY ${K}`)}return S.length&&W.push(`ORDER BY ${S.join(", ")}`),Number.isFinite(tt)&&W.push(`LIMIT ${tt}`),Number.isFinite(O)&&W.push(`OFFSET ${O}`),W.join(" ")}};class yn{constructor(t,e){this.op=t,this.queries=e.map(i=>i.clone()),this.query={orderby:[]},this.cteFor=null}clone(){const t=new yn(this.op,this.queries);return t.query={...this.query},t}orderby(...t){const{query:e}=this;return t.length===0?e.orderby:(e.orderby=e.orderby.concat(t.flat().filter(i=>i).map(en)),this)}limit(t){const{query:e}=this;return arguments.length===0?e.limit:(e.limit=Number.isFinite(t)?t:void 0,this)}offset(t){const{query:e}=this;return arguments.length===0?e.offset:(e.offset=Number.isFinite(t)?t:void 0,this)}get subqueries(){const{queries:t,cteFor:e}=this;return e&&t.forEach(i=>i.cteFor=e),t}toString(){const{op:t,queries:e,query:{orderby:i,limit:r,offset:s}}=this,o=[e.join(` ${t} `)];return i.length&&o.push(`ORDER BY ${i.join(", ")}`),Number.isFinite(r)&&o.push(`LIMIT ${r}`),Number.isFinite(s)&&o.push(`OFFSET ${s}`),o.join(" ")}}function bs(n){return n instanceof ur||n instanceof yn}function Zi(n){return wf(n)?n.slice(1,-1):n}function wf(n){return n[0]==='"'&&n[n.length-1]==='"'}const _s=n=>{let t=null,e=null;const i=new Promise((o,a)=>{t=o,e=a});let r="init",s=null;if(!t||!e)throw new Error;return{promise:i,resolve:o=>{if(t)(r==="loading"||r==="init")&&(r="resolved",s=o,t(o),n==null||n());else throw new Error("SharedPromise encountered an error: res not defined")},reject:o=>{if(e)(r==="loading"||r==="init")&&(r="rejected",i.catch(()=>{}),e(o),n==null||n());else throw new Error("SharedPromise encountered an error: rej not defined")},get state(){return r},get value(){return s},start(){r="loading",n==null||n()}}},_e=(n,t,e)=>{try{const i=typeof t=="function"?t():t;return i instanceof Promise?i.then(r=>n(r,!0)).catch(r=>{const s=r instanceof Error?r:new Error("Unknown Error",{cause:r});if(e)return e(s,!0);throw s}):n(i,!1)}catch(i){const r=i instanceof Error?i:new Error("Unknown Error",{cause:i});if(e)return e(r,!1);throw r}},fa=(n,t)=>{const e=t.reduce((i,r)=>i+Sf(r.column_type),t.length*4);return Math.abs(e*n)};function If(n){return n.startsWith("STRUCT")||n.endsWith("[]")}function vf(n){return n.startsWith("DECIMAL")}function Sf(n){const t={string:30,number:12,boolean:4,date:48};if(If(n))return console.warn(`[!] Evidence does not support DuckDB Struct or Array
If you need to use one, convert it to JSON in your query, and then manually parse it in your project`),t.string;if(vf(n))return t.number;switch(n){case"BOOLEAN":return t.boolean;case"BIGINT":case"DOUBLE":case"FLOAT":case"INTEGER":case"SMALLINT":case"TINYINT":case"UBIGINT":case"UINTEGER":case"USMALLINT":case"UTINYINT":case"HUGEINT":return t.number;case"UUID":case"VARCHAR":return t.string;case"DATE":case"TIMESTAMP":case"TIMESTAMP_S":case"TIMESTAMP_MS":case"TIMESTAMP_NS":case"TIMESTAMP WITH TIME ZONE":return t.date;case"INTERVAL":case"TIME":case"TIME WITH TIME ZONE":case"BLOB":case"BIT":return t.string;default:return console.error(`Column type ${n} is not supported`),t.string}}const Ef=/--([^']|'.*')+$/,Tf=/(\/\*.*\*\/)/g,Af=n=>{const t=n.split(`
`);let e=!1;for(let i=t.length;i>0;i--){let r=t[i-1],s="";const o=Array.from(r.matchAll(Tf));for(const c of o){const l=r.slice(0,c.index),b=r.slice(c.index+c[0].length);r=`${l}${b}`}if(e&&r.includes("/*")){e=!1;const c=r.split("/*");r=c.slice(0,-1).join("/*"),s+="/*"+c.slice(-1)}if(r.trim().endsWith("*/")){e=!0;continue}const a=Ef.exec(r);if(a){const c=r.slice(0,a.index),l=c.trimEnd();if(l.endsWith(";")){const b=r.slice(a.index),S=c.slice(l.length,c.length);r=`${c.slice(0,-1+-1*(c.length-l.length))}${S}${b}`}}else if(r.trimEnd().endsWith(";")){const l=r.lastIndexOf(";");r=r.slice(0,l)+r.slice(l+1)}for(const c of o){const l=r.slice(0,c.index),b=r.slice(c.index);r=`${l}${c[0]}${b}`}if(r!==t[i-1]){t[i-1]=r+s;break}}return t.push(""),t.join(`
`)};var gn,Lt,Bi,Ze,Ri,$t,Mi,Ci,Li,bt,He,dn,jt,zn,xe,zr,Wn,bn,Ui,ie,Wr,vt,_n,St,wn,Nt,Yn,Yr,re,Gr,qr,xi,xs,Ps,Pe,ks,Gt,Gn,qn,fe,ke,Xe,Qe,Ut,Pi,Hn,Hr,Qn,tn,ki,Qr;const A=class A{constructor(t,e,i={}){j(this,bt);j(this,gn);j(this,Lt,[]);j(this,Bi,-1);j(this,Ze,0);j(this,Ri,-1);j(this,$t,[]);j(this,Mi);j(this,Ci,-1);j(this,Li);j(this,jt);j(this,zn);j(this,ie,-1);j(this,Wr,()=>{this.lengthLoaded&&this.columnsLoaded?(nt(this,ie,fa(this.length,this.columns)),u(this,ie)>u(A,Ui)&&u(this,tn).call(this,"highScore",u(this,ie))):Promise.allSettled([u(this,St).promise,u(this,Nt).promise]).then(([t,e])=>{if(t.status==="rejected"||e.status==="rejected"){nt(this,ie,-1);return}if(!u(this,Ze)||!u(this,$t)){nt(this,ie,-1);return}nt(this,ie,fa(this.length,this.columns)),u(this,ie)>u(A,Ui)&&u(this,tn).call(this,"highScore",u(this,ie))}).catch(t=>{console.error(`${this.id} | Failed to calculate Query score ${t}`)})});j(this,vt,_s(()=>this.publish(`data promise (${u(this,vt).state})`)));j(this,_n,()=>{var s;if(u(this,vt).state!=="init")return u(this,vt).promise;if(u(this,bt,He))return u(this,Gt).call(this,"data error","Refusing to execute data query, store has an error state"),u(this,vt).promise;if(u(this,vt).state!=="init"||this.opts.noResolve)return u(this,vt).promise;u(this,vt).start();const t=`
---- Data ${u(this,fe)} ${u(this,ke)}
${this.text.trim()}
        `.trim()+`
`;u(this,Gn).call(this,"data query text",`
`+t,"font-family: monospace;");const e=u(this,Ut);u(s=A,zr).call(s,this);const i=performance.now();return _e((o,a)=>{nt(this,Lt,o);const c=performance.now();return i-c>5e3&&(u(this,tn).call(this,"longRun",i-c),u(this,Gt).call(this,"long-running",`Query took ${i-c}ms to execute`)),nt(this,Bi,c-i),u(this,wn).call(this),u(this,vt).resolve(this),u(this,tn).call(this,"dataReady",void 0),a?u(this,vt).promise:this},()=>e(t,`${u(this,fe)}_data`),(o,a)=>(nt(this,bt,o,dn),u(this,vt).reject(o),a?u(this,vt).promise:this))});It(this,"fetch",()=>u(this,Yn).call(this)instanceof Promise&&!this.opts.noResolve?Promise.allSettled([u(this,Yn).call(this),u(this,_n).call(this)]).then(()=>this.value):(u(this,_n).call(this),this.value));It(this,"backgroundFetch",()=>{if(typeof window>"u"){u(this,Gt).call(this,"background fetch skip","Did not execute backgroundFetch in SSR");return}u(this,Gt).call(this,"background fetch","Executed backgroundFetch"),_e(()=>{},async()=>(await new Promise(t=>setTimeout(t,0)),u(this,Ut).call(this,`--data
${this.text.trim()}`,this.id)),()=>{})});j(this,St,_s(()=>this.publish(`length promise (${u(this,St).state})`)));j(this,wn,()=>{if(u(this,Lt)&&u(this,vt).state==="resolved"&&u(this,St).state==="init")return u(this,Gt).call(this,"length inferred","Inferred length from already-resolved data promise",u(this,Lt)),nt(this,Ze,u(this,Lt).length),u(this,St).resolve(this),u(this,St).promise;if(u(this,bt,He))return u(this,Gt).call(this,"length error","Refusing to execute length query, store has an error state",u(this,bt,He)),u(this,St).reject(u(this,bt,He)),u(this,St).value??u(this,St).promise;if(u(this,St).state!=="init"||this.opts.noResolve)return u(this,St).promise;u(this,St).start();const t=`
---- Length ${u(this,fe)} (${u(this,ke)})
SELECT COUNT(*) as rowCount FROM (${this.text.trim()})
        `.trim()+`
`,e=u(this,Ut);u(this,Gn).call(this,"length query text",`
`+t,"font-family: monospace;");const i=performance.now();return _e((s,o)=>{const a=performance.now();return nt(this,Ri,a-i),nt(this,Ze,s[0].rowCount),u(this,St).resolve(this),o?u(this,St).promise:this},()=>e(t,`${u(this,fe)}_length`),(s,o)=>(nt(this,bt,s,dn),u(this,St).reject(s),o?u(this,St).promise:this))});j(this,Nt,_s(()=>this.publish(`columns promise (${u(this,Nt).state})`)));j(this,Yn,()=>{if(u(this,bt,He))return u(this,Gt).call(this,"cols query error","Refusing to execute columns query, store has an error state",u(this,bt,He)),u(this,Nt).value??u(this,Nt).promise;if(u(this,Nt).state!=="init"||this.opts.noResolve)return u(this,Nt).promise;u(this,Nt).start();const t=`
---- Columns ${u(this,fe)} (${u(this,ke)})
DESCRIBE ${this.text.trim()}
        `.trim()+`
`;u(this,Gn).call(this,"columns query text",`
`+t,"font-family: monospace;");const e=u(this,Ut),i=performance.now();return _e((s,o)=>{const a=performance.now();return nt(this,Ci,a-i),nt(this,$t,s),u(this,Nt).resolve(this),nt(this,Mi,Object.fromEntries(s.map(c=>[c.column_name,void 0]))),o?u(this,Nt).promise:this},()=>e(t,`${u(this,fe)}_columns`),(s,o)=>(nt(this,bt,s,dn),u(this,Nt).reject(s),o?u(this,Nt).promise:this))});j(this,Yr,()=>new Proxy([],{getPrototypeOf:()=>Object.getPrototypeOf(u(this,Lt)),has:(e,i)=>i in u(this,Lt)||i in this,get:(e,i)=>{let r=i;if(typeof r=="string"&&/^[\d.]+$/.exec(r)&&(r=parseInt(r)),(typeof r=="number"||A.ProxyFetchTriggers.includes(r.toString()))&&u(this,vt).state==="init"&&(u(this,Gt).call(this,"implicit fetch",`Implicit query fetch triggered by ${r.toString()}`),u(this,_n).call(this)),r==="length"&&u(this,wn).call(this),r==="constructor")return u(this,Lt).constructor;if(r==="toString")return u(this,Lt).toString.bind(u(this,Lt));const s=r in this?this:u(this,Lt)&&r in u(this,Lt)?u(this,Lt):null;if(s===null)return typeof r!="number"||this.lengthLoaded&&r>u(this,Ze)?void 0:u(this,Mi)??{};const o=s[r];return typeof o=="function"?o.bind(s):o}}));j(this,Gt,bi()?(t,...e)=>{const i=`${(performance.now()/1e3).toFixed(3)} | ${this.id} (${this.hash}) | ${t}`;console.groupCollapsed(i);for(const r of e)console.debug(typeof r=="function"?r():r);console.groupEnd()}:()=>{});j(this,Gn,bi()?(t,e,i)=>{const r=`${(performance.now()/1e3).toFixed(3)} | ${this.id} (${this.hash}) | ${t}`;console.groupCollapsed(r),console.debug(`%c${e}`,i),console.groupEnd()}:()=>{});j(this,fe);j(this,ke);j(this,Xe);j(this,Ut);It(this,"opts");j(this,Pi);j(this,Hn,new Set);It(this,"subscribe",t=>(u(this,Hn).add(t),t(u(this,gn)),()=>u(this,Hn).delete(t)));j(this,Hr,0);It(this,"publish",t=>{if(Ro(this,Hr)._++>1e5)throw new Error("Query published too many times.");u(this,Gt).call(this,"publish",`Publishing triggered by ${t}`),u(this,Hn).forEach(e=>e(u(this,gn)))});j(this,Qn,{dataReady:new Set,error:new Set,highScore:new Set,longRun:new Set});j(this,tn,(t,e)=>{u(this,Qn)[t].forEach(i=>i(e,t))});It(this,"on",(t,e)=>{u(this,Qn)[t].add(e)});It(this,"off",(t,e)=>{u(this,Qn)[t].delete(e)});It(this,"addEventListener",this.on);It(this,"removeEventListener",this.off);It(this,"where",t=>A.create(u(this,jt).clone().where(Ct`${t}`),u(this,Ut),{knownColumns:u(this,$t),noResolve:u(this,Xe).noResolve}));It(this,"withOrdinal",t=>{const e=u(this,jt).clone();return e.select({ordinal:Ct`row_number() over (${t})`}),A.create(e,u(this,Ut),{...u(this,bt,Qe),knownColumns:u(this,$t)})});It(this,"search",(t,e,i)=>{(typeof i>"u"||i<0||i>1)&&(i=1-1/t.length);const r=[...u(this,$t),{column_name:"similarity",column_type:"INTEGER",nullable:"NO"}],s=A.create,o=t.replaceAll("'","''"),c=(Array.isArray(e)?e:[e]).map(b=>{const S=Ct`CASE WHEN lower("${b.trim()}") = lower('${o}') THEN 2 ELSE 0 END`,tt=Ct`jaccard(lower('${o}'), lower("${b}"))`,O=o.length>=1?Ct`CASE WHEN lower("${b.trim()}") LIKE lower('%${o.split(" ").join("%")}%') THEN 1 ELSE 0 END`:Ct`0`;return Ct`GREATEST((${S}), (${tt}), (${O}))`}).join(",");return s(u(this,jt).clone().$select({similarity:Ct`GREATEST(${c})`},"*").where(Ct`"similarity" > ${i} `).orderby(Ct`"similarity" DESC`),u(this,Ut),{knownColumns:r,...u(this,bt,Qe)})});It(this,"limit",t=>A.create(u(this,jt).clone().limit(t),u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)}));It(this,"offset",t=>A.create(u(this,jt).clone().offset(t),u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)}));It(this,"paginate",(t,e)=>A.create(u(this,jt).clone().offset(t).limit(e),u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)}));It(this,"groupBy",(t,e)=>{const i=u(this,jt).clone();return i.$select(t),e&&i.select({rows:pf("*")}),i.$groupby(t),A.create(i,u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)})});It(this,"agg",t=>{var i;const e=u(this,jt).clone();for(const[r,s]of Object.entries(t)){if(!u(i=A,Qr).call(i,r))throw new Error(`Unknown agg function: ${r}`);const o=u(A,ki)[r],a=Array.isArray(s)?s:[s];for(const c of a){const l=typeof c=="object"?c.as:`${r}_${c}`,b=typeof c=="object"?c.col:c;e.select({[l]:o(b)})}}return A.create(e,u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)})});var c;nt(this,Pi,(c=new Error().stack)==null?void 0:c.split(`
`).slice(2).map(l=>l.slice(7)).join(`
`));const{id:r,initialData:s=void 0,knownColumns:o=void 0,initialError:a=void 0}=i;if(this.opts=i,nt(this,Ut,e),typeof t!="string"&&!(t instanceof ur)&&(console.warn(`Query ${r} has no query text`),i.noResolve=!0),u(A,qn)||console.warn("Directly using new Query() is not a recommended use-case. Please use Query.create()"),nt(A,qn,!1),nt(this,gn,u(this,Yr).call(this)),nt(this,zn,(t==null?void 0:t.toString())??"SELECT 'Empty Query' WHERE 0"),nt(this,ke,hn(u(this,zn))),nt(this,fe,r??u(this,ke)),nt(this,Xe,i),t&&typeof t!="string")nt(this,jt,t);else if(t){const l=new ur().from({[`inputQuery-${sf(2)}`]:Ct`(${Af(t)})`}).select("*");nt(this,jt,l)}else{nt(this,jt,new ur),nt(this,bt,new Error("Refusing to create Query: No Query Text provided"),dn);return}if(a){nt(this,bt,a,dn);return}if(s)u(this,Gt).call(this,"initial data","Created with initial data",s),_e(l=>{nt(this,Lt,l),i.initialDataDirty?(this.publish("dataDirty"),u(this,_n).call(this)):(u(this,vt).resolve(this),u(this,wn).call(this))},s,l=>{nt(this,bt,l,dn)});else if(i.noResolve)return u(this,vt).start(),u(this,St).start(),u(this,Nt).start(),this;if(o){if(!Array.isArray(o))throw new Error("Expected knownColumns to be an array",{cause:o});u(this,Gt).call(this,"known columns","Created with known columns",o),nt(this,$t,o),u(this,Nt).resolve(this)}else _e(()=>{},u(this,Yn).call(this),(l,b)=>{if(!b)throw l});_e(()=>{},u(this,wn).call(this),(l,b)=>{if(!b)throw l}),i.autoScore&&u(this,Wr).call(this)}get value(){return u(this,gn)}get dataLoaded(){return["resolved","rejected"].includes(u(this,vt).state)}get dataLoading(){return u(this,vt).state==="loading"}get dataQueryTime(){return u(this,Bi)}get length(){return u(this,Ze)}get lengthLoaded(){return["resolved","rejected"].includes(u(this,St).state)}get lengthLoading(){return u(this,St).state==="loading"}get lengthQueryTime(){return u(this,Ri)}get columns(){return u(this,$t)}get columnsLoaded(){return["resolved","rejected"].includes(u(this,Nt).state)}get columnsLoading(){return u(this,Nt).state==="loading"}get columnsQueryTime(){return u(this,Ci)}get ready(){return u(this,St).state==="resolved"&&u(this,Nt).state==="resolved"&&u(this,vt).state==="resolved"}get loading(){return u(this,St).state==="loading"||u(this,Nt).state==="loading"||u(this,vt).state==="loading"}get error(){return u(this,bt,He)}get originalText(){return u(this,zn)}get text(){var t;return((t=u(this,jt))==null?void 0:t.toString())??"SELECT 'Empty Query' WHERE 0"}static get queriesInFlight(){return u(A,xe).size>0}static resetInFlightQueries(){nt(A,xe,new Set)}static addEventListener(t,e){u(this,Wn)[t].add(e)}static removeEventListener(t,e){u(this,Wn)[t].delete(e)}get score(){return u(this,ie)}get isQuery(){return!0}static[Symbol.hasInstance](t){return A.isQuery(t)}static get ProxyFetchTriggers(){return["at"]}static get cacheSize(){return u(this,re).size}get id(){return u(this,fe)}get hash(){return u(this,ke)}get createdStack(){return u(this,Pi)}};gn=new WeakMap,Lt=new WeakMap,Bi=new WeakMap,Ze=new WeakMap,Ri=new WeakMap,$t=new WeakMap,Mi=new WeakMap,Ci=new WeakMap,Li=new WeakMap,bt=new WeakSet,He=function(){return u(this,Li)},dn=function(t){t&&(console.error(`${this.id} | Error in Query!`,t==null?void 0:t.message),u(this,tn).call(this,"error",t),nt(this,Li,t))},jt=new WeakMap,zn=new WeakMap,xe=new WeakMap,zr=new WeakMap,Wn=new WeakMap,bn=new WeakMap,Ui=new WeakMap,ie=new WeakMap,Wr=new WeakMap,vt=new WeakMap,_n=new WeakMap,St=new WeakMap,wn=new WeakMap,Nt=new WeakMap,Yn=new WeakMap,Yr=new WeakMap,re=new WeakMap,Gr=new WeakMap,qr=new WeakMap,xi=new WeakMap,xs=new WeakMap,Ps=new WeakMap,Pe=new WeakMap,ks=new WeakMap,Gt=new WeakMap,Gn=new WeakMap,qn=new WeakMap,fe=new WeakMap,ke=new WeakMap,Xe=new WeakMap,Qe=function(){return{autoScore:u(this,Xe).autoScore,noResolve:u(this,Xe).noResolve,disableCache:u(this,Xe).disableCache}},Ut=new WeakMap,Pi=new WeakMap,Hn=new WeakMap,Hr=new WeakMap,Qn=new WeakMap,tn=new WeakMap,ki=new WeakMap,Qr=new WeakMap,j(A,xe,new Set),j(A,zr,t=>{var e;u(A,xe).size===0&&u(e=A,bn).call(e,"inFlightQueryStart",void 0),u(A,xe).add(t),u(t,vt).promise.finally(()=>{var i;u(A,xe).delete(t),u(A,xe).size===0&&u(i=A,bn).call(i,"inFlightQueryEnd",void 0)})}),j(A,Wn,{inFlightQueryStart:new Set,inFlightQueryEnd:new Set,queryCreated:new Set,cacheCleared:new Set}),j(A,bn,(t,e)=>{u(A,Wn)[t].forEach(i=>i(e,t))}),j(A,Ui,10*1024*1024),It(A,"isQuery",t=>typeof t!="object"||!t?!1:"isQuery"in t&&t.isQuery===!0),It(A,"CacheMaxScore",5*10*1024),j(A,re,new Map),It(A,"emptyCache",()=>{var t;u(A,re).clear(),u(t=A,bn).call(t,"cacheCleared",void 0)}),j(A,Gr,t=>{var e;u(A,re).set(t.hash,{query:t,added:Date.now()}),u(e=A,Pe).call(e,"cache",`Added to cache: ${t.hash}`,{cacheSize:u(A,re).size,cacheScore:Array.from(u(A,re).values()).reduce((i,r)=>i+r.query.score,0)})}),j(A,qr,t=>{const e=u(A,re).get(t);return e?e.query:null}),j(A,xi,()=>{let t=Array.from(u(A,re).values()).reduce((i,r)=>i+r.query.score,0);const e=Array.from(u(A,re).values()).sort((i,r)=>i.added-r.added);for(;t>A.CacheMaxScore;){const i=e.shift();if(!i)break;u(A,re).delete(i.query.hash),t-=i.query.score}}),It(A,"createReactive",(t,e,i)=>{const{loadGracePeriod:r=250,callback:s=()=>{},execFn:o}=t,a=A.create;let c=i,l=0,b;const S=(O,W)=>{var At;if(!c)throw new Error;l+=1;const Bt=l;u(At=A,Pe).call(At,`${c.id} (${hn(O)}) | Reactive Updating`,O,{changeIdx:l,targetChangeIdx:Bt,hash:hn(O)},{initialOpts:e,newOpts:W});const K=A.isQuery(O)?O:a(O,o,Object.assign({},e,{initialData:void 0,initialError:void 0},W)),it=K.fetch();let gt=it;it instanceof Promise&&(gt=Promise.race([new Promise(N=>setTimeout(N,r)),K.fetch()])),_e(()=>{var N;if(l!==Bt){u(N=A,Pe).call(N,"changeIdx does not match, results are discarded");return}b==null||b(),c=K.value,b=c.subscribe(s)},gt,N=>{throw console.warn(`Error while attempting to update reactive query: ${N.message}`),N})};function tt(){e={...e,initialData:void 0,initialError:void 0}}return(O,W)=>{if(c){_e(()=>{},S(O,W),K=>{console.warn(`Error while attempting to update reactive query: ${K.message}`)});return}c=a(O,o,Object.assign({},e,W));const Bt=c.fetch();_e(tt,Bt),b=c.subscribe(s),s(c)}}),j(A,xs,!1),j(A,Ps,()=>{}),It(A,"create",(t,e,i,r)=>{var c,l,b,S,tt,O,W,Bt;const s=hn(t);let o;if(typeof i=="string"?o={...r,id:i}:i?(o=i,o.id||(o.id=s+"-"+Math.random().toString(36).substring(0,4))):o={id:s+"-"+Math.random().toString(36).substring(0,4)},"autoScore"in o||(o.autoScore=!0),o.disableCache)u(tt=A,Pe).call(tt,`${o.id??"[query id missing]"} (${s}) | cache disabled`,`Cache is disabled for ${o.id??"[query id missing]"}`,{opts:o,query:t,hash:hn(t)});else{const K=u(c=A,qr).call(c,s);if(u(l=A,xi).call(l),K)return u(b=A,Pe).call(b,`${o.id??"[query id missing]"} (${s}) | Using cached query`,{opts:o,hash:hn(t)},t,K),K.value;u(S=A,Pe).call(S,`${o.id??"[query id missing]"} (${s}) | Cached query not found`,{opts:o,hash:hn(t)},t)}nt(A,qn,!0);const a=new A(t,e,o);return u(O=A,bn).call(O,"queryCreated",{raw:a,proxied:a.value}),o.disableCache||(u(W=A,Gr).call(W,a),u(Bt=A,xi).call(Bt)),a.value}),j(A,Pe,bi()?(t,...e)=>{const i=`${(performance.now()/1e3).toFixed(3)} | Query | ${t}`;console.groupCollapsed(i);for(const r of e)console.debug(typeof r=="function"?r():r);console.groupEnd()}:()=>{}),j(A,ks,bi()?(t,e,i)=>{const r=`${(performance.now()/1e3).toFixed(3)} | Query | ${t}`;console.groupCollapsed(r),console.debug(`%c${e}`,i),console.groupEnd()}:()=>{}),j(A,qn,!1),j(A,ki,{sum:bf,avg:yf,min:gf,max:mf,median:_f}),j(A,Qr,t=>t in u(A,ki));let Us=A;const hn=(...n)=>(e=>{let i=0;for(let r=0;r<e.length;r++){const s=e.charCodeAt(r);i=(i<<5)-i+s,i&=i}return new Uint32Array([i])[0].toString(36)})(JSON.stringify(n)),Ii=Symbol("InputStore"),Fl=n=>typeof n!="object"||n===null?!1:"subscribe"in n,Ff=n=>Fl(n)?"set"in n&&"update"in n:!1,tp=n=>{if(!Ff(n))throw console.error({InputStoreValue:n}),new Error("InputStore must be a writable store");if(pa().has(Ii)){const t=ma(Ii);return t.set(ws(n)),t}else return ya(Ii,n),n},ep=()=>{if(!pa().has(Ii))return console.warn("InputStoreKey not found in context. Did you forget to call ensureInputContext?"),$l({});const n=ma(Ii);if(Fl(n))return jl(n);throw new Error(`InputStoreKey is not a readable store: ${n}`)},Of=(n,t)=>{let e;if(n instanceof String)e=n.toString();else{if(typeof n!="string")return n;e=n}if(e.startsWith("http")||e.startsWith("#")||/^[^/]*:/.test(e))return e;let i=t.deployment.basePath;return i?(i!=null&&i.startsWith("/")||(i=`/${i}`),i.endsWith("/")&&(i=i.slice(0,-1)),e.startsWith(i)?e:(e.startsWith("/")||(e=`/${e}`),`${i}${e}`)):e},Df={deployment:{basePath:""}},np=n=>Of(n,Df);var ip=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Nf(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}const Bf="___usql_query";let Ol=ef;const rp=n=>{ya(Bf,n),Ol=n},sp=(n,t,e,i)=>Us.create(n,Ol,t,{...i,initialData:e});var Dl={exports:{}};(function(n){var t={},e=function(r){r.version="0.11.2";function s(f){for(var d="",p=f.length-1;p>=0;)d+=f.charAt(p--);return d}function o(f,d){for(var p="";p.length<d;)p+=f;return p}function a(f,d){var p=""+f;return p.length>=d?p:o("0",d-p.length)+p}function c(f,d){var p=""+f;return p.length>=d?p:o(" ",d-p.length)+p}function l(f,d){var p=""+f;return p.length>=d?p:p+o(" ",d-p.length)}function b(f,d){var p=""+Math.round(f);return p.length>=d?p:o("0",d-p.length)+p}function S(f,d){var p=""+f;return p.length>=d?p:o("0",d-p.length)+p}var tt=Math.pow(2,32);function O(f,d){if(f>tt||f<-tt)return b(f,d);var p=Math.round(f);return S(p,d)}function W(f,d){return d=d||0,f.length>=7+d&&(f.charCodeAt(d)|32)===103&&(f.charCodeAt(d+1)|32)===101&&(f.charCodeAt(d+2)|32)===110&&(f.charCodeAt(d+3)|32)===101&&(f.charCodeAt(d+4)|32)===114&&(f.charCodeAt(d+5)|32)===97&&(f.charCodeAt(d+6)|32)===108}var Bt=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],K=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function it(f){f[0]="General",f[1]="0",f[2]="0.00",f[3]="#,##0",f[4]="#,##0.00",f[9]="0%",f[10]="0.00%",f[11]="0.00E+00",f[12]="# ?/?",f[13]="# ??/??",f[14]="m/d/yy",f[15]="d-mmm-yy",f[16]="d-mmm",f[17]="mmm-yy",f[18]="h:mm AM/PM",f[19]="h:mm:ss AM/PM",f[20]="h:mm",f[21]="h:mm:ss",f[22]="m/d/yy h:mm",f[37]="#,##0 ;(#,##0)",f[38]="#,##0 ;[Red](#,##0)",f[39]="#,##0.00;(#,##0.00)",f[40]="#,##0.00;[Red](#,##0.00)",f[45]="mm:ss",f[46]="[h]:mm:ss",f[47]="mmss.0",f[48]="##0.0E+0",f[49]="@",f[56]='"上午/下午 "hh"時"mm"分"ss"秒 "'}var gt={};it(gt);var At=[],N=0;for(N=5;N<=8;++N)At[N]=32+N;for(N=23;N<=26;++N)At[N]=0;for(N=27;N<=31;++N)At[N]=14;for(N=50;N<=58;++N)At[N]=14;for(N=59;N<=62;++N)At[N]=N-58;for(N=67;N<=68;++N)At[N]=N-58;for(N=72;N<=75;++N)At[N]=N-58;for(N=67;N<=68;++N)At[N]=N-57;for(N=76;N<=78;++N)At[N]=N-56;for(N=79;N<=81;++N)At[N]=N-34;var kt=[];kt[5]=kt[63]='"$"#,##0_);\\("$"#,##0\\)',kt[6]=kt[64]='"$"#,##0_);[Red]\\("$"#,##0\\)',kt[7]=kt[65]='"$"#,##0.00_);\\("$"#,##0.00\\)',kt[8]=kt[66]='"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',kt[41]='_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',kt[42]='_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',kt[43]='_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',kt[44]='_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)';function Yi(f,d,p){for(var F=f<0?-1:1,m=f*F,B=0,g=1,E=0,H=1,T=0,Q=0,et=Math.floor(m);T<d&&(et=Math.floor(m),E=et*g+B,Q=et*T+H,!(m-et<5e-8));)m=1/(m-et),B=g,g=E,H=T,T=Q;if(Q>d&&(T>d?(Q=H,E=B):(Q=T,E=g)),!p)return[0,F*E,Q];var he=Math.floor(F*E/Q);return[he,F*E-he*Q,Q]}function ri(f,d,p){if(f>2958465||f<0)return null;var F=f|0,m=Math.floor(86400*(f-F)),B=0,g=[],E={D:F,T:m,u:86400*(f-F)-m,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(E.u)<1e-6&&(E.u=0),d&&d.date1904&&(F+=1462),E.u>.9999&&(E.u=0,++m==86400&&(E.T=m=0,++F,++E.D)),F===60)g=p?[1317,10,29]:[1900,2,29],B=3;else if(F===0)g=p?[1317,8,29]:[1900,1,0],B=6;else{F>60&&--F;var H=new Date(1900,0,1);H.setDate(H.getDate()+F-1),g=[H.getFullYear(),H.getMonth()+1,H.getDate()],B=H.getDay(),F<60&&(B=(B+6)%7),p&&(B=Cl(H,g))}return E.y=g[0],E.m=g[1],E.d=g[2],E.S=m%60,m=Math.floor(m/60),E.M=m%60,m=Math.floor(m/60),E.H=m,E.q=B,E}r.parse_date_code=ri;var bo=new Date(1899,11,31,0,0,0),Bl=bo.getTime(),Rl=new Date(1900,2,1,0,0,0);function _o(f,d){var p=f.getTime();return d?p-=1262304e5:f>=Rl&&(p+=864e5),(p-(Bl+(f.getTimezoneOffset()-bo.getTimezoneOffset())*6e4))/864e5}function Ml(f){return f.toString(10)}r._general_int=Ml;var wo=function(){var d=/(?:\.0*|(\.\d*[1-9])0+)$/;function p(T){return T.indexOf(".")==-1?T:T.replace(d,"$1")}var F=/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,m=/(E[+-])(\d)$/;function B(T){return T.indexOf("E")==-1?T:T.replace(F,"$1E").replace(m,"$10$2")}function g(T){var Q=T<0?12:11,et=p(T.toFixed(12));return et.length<=Q||(et=T.toPrecision(10),et.length<=Q)?et:T.toExponential(5)}function E(T){var Q=p(T.toFixed(11));return Q.length>(T<0?12:11)||Q==="0"||Q==="-0"?T.toPrecision(6):Q}function H(T){var Q=Math.floor(Math.log(Math.abs(T))*Math.LOG10E),et;return Q>=-4&&Q<=-1?et=T.toPrecision(10+Q):Math.abs(Q)<=9?et=g(T):Q===10?et=T.toFixed(10).substr(0,12):et=E(T),p(B(et.toUpperCase()))}return H}();r._general_num=wo;function Gi(f,d){switch(typeof f){case"string":return f;case"boolean":return f?"TRUE":"FALSE";case"number":return(f|0)===f?f.toString(10):wo(f);case"undefined":return"";case"object":if(f==null)return"";if(f instanceof Date)return Oo(14,_o(f,d&&d.date1904),d)}throw new Error("unsupported value in General format: "+f)}r._general=Gi;function Cl(f,d){d[0]-=581;var p=f.getDay();return f<60&&(p=(p+6)%7),p}function Ll(f,d,p,F){var m="",B=0,g=0,E=p.y,H,T=0;switch(f){case 98:E=p.y+543;case 121:switch(d.length){case 1:case 2:H=E%100,T=2;break;default:H=E%1e4,T=4;break}break;case 109:switch(d.length){case 1:case 2:H=p.m,T=d.length;break;case 3:return K[p.m-1][1];case 5:return K[p.m-1][0];default:return K[p.m-1][2]}break;case 100:switch(d.length){case 1:case 2:H=p.d,T=d.length;break;case 3:return Bt[p.q][0];default:return Bt[p.q][1]}break;case 104:switch(d.length){case 1:case 2:H=1+(p.H+11)%12,T=d.length;break;default:throw"bad hour format: "+d}break;case 72:switch(d.length){case 1:case 2:H=p.H,T=d.length;break;default:throw"bad hour format: "+d}break;case 77:switch(d.length){case 1:case 2:H=p.M,T=d.length;break;default:throw"bad minute format: "+d}break;case 115:if(d!="s"&&d!="ss"&&d!=".0"&&d!=".00"&&d!=".000")throw"bad second format: "+d;return p.u===0&&(d=="s"||d=="ss")?a(p.S,d.length):(F>=2?g=F===3?1e3:100:g=F===1?10:1,B=Math.round(g*(p.S+p.u)),B>=60*g&&(B=0),d==="s"?B===0?"0":""+B/g:(m=a(B,2+F),d==="ss"?m.substr(0,2):"."+m.substr(2,d.length-1)));case 90:switch(d){case"[h]":case"[hh]":H=p.D*24+p.H;break;case"[m]":case"[mm]":H=(p.D*24+p.H)*60+p.M;break;case"[s]":case"[ss]":H=((p.D*24+p.H)*60+p.M)*60+Math.round(p.S+p.u);break;default:throw"bad abstime format: "+d}T=d.length===3?1:2;break;case 101:H=E,T=1;break}var Q=T>0?a(H,T):"";return Q}function Ye(f){var d=3;if(f.length<=d)return f;for(var p=f.length%d,F=f.substr(0,p);p!=f.length;p+=d)F+=(F.length>0?",":"")+f.substr(p,d);return F}var Be=function(){var d=/%/g;function p(v,y,_){var R=y.replace(d,""),w=y.length-R.length;return Be(v,R,_*Math.pow(10,2*w))+o("%",w)}function F(v,y,_){for(var R=y.length-1;y.charCodeAt(R-1)===44;)--R;return Be(v,y.substr(0,R),_/Math.pow(10,3*(y.length-R)))}function m(v,y){var _,R=v.indexOf("E")-v.indexOf(".")-1;if(v.match(/^#+0.0E\+0$/)){if(y==0)return"0.0E+0";if(y<0)return"-"+m(v,-y);var w=v.indexOf(".");w===-1&&(w=v.indexOf("E"));var I=Math.floor(Math.log(y)*Math.LOG10E)%w;if(I<0&&(I+=w),_=(y/Math.pow(10,I)).toPrecision(R+1+(w+I)%w),_.indexOf("e")===-1){var Z=Math.floor(Math.log(y)*Math.LOG10E);for(_.indexOf(".")===-1?_=_.charAt(0)+"."+_.substr(1)+"E+"+(Z-_.length+I):_+="E+"+(Z-I);_.substr(0,2)==="0.";)_=_.charAt(0)+_.substr(2,w)+"."+_.substr(2+w),_=_.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");_=_.replace(/\+-/,"-")}_=_.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(wt,Ft,Ot,st){return Ft+Ot+st.substr(0,(w+I)%w)+"."+st.substr(I)+"E"})}else _=y.toExponential(R);return v.match(/E\+00$/)&&_.match(/e[+-]\d$/)&&(_=_.substr(0,_.length-1)+"0"+_.charAt(_.length-1)),v.match(/E\-/)&&_.match(/e\+/)&&(_=_.replace(/e\+/,"e")),_.replace("e","E")}var B=/# (\?+)( ?)\/( ?)(\d+)/;function g(v,y,_){var R=parseInt(v[4],10),w=Math.round(y*R),I=Math.floor(w/R),Z=w-I*R,wt=R;return _+(I===0?"":""+I)+" "+(Z===0?o(" ",v[1].length+1+v[4].length):c(Z,v[1].length)+v[2]+"/"+v[3]+a(wt,v[4].length))}function E(v,y,_){return _+(y===0?"":""+y)+o(" ",v[1].length+2+v[4].length)}var H=/^#*0*\.([0#]+)/,T=/\).*[0#]/,Q=/\(###\) ###\\?-####/;function et(v){for(var y="",_,R=0;R!=v.length;++R)switch(_=v.charCodeAt(R)){case 35:break;case 63:y+=" ";break;case 48:y+="0";break;default:y+=String.fromCharCode(_)}return y}function he(v,y){var _=Math.pow(10,y);return""+Math.round(v*_)/_}function ln(v,y){var _=v-Math.floor(v),R=Math.pow(10,y);return y<(""+Math.round(_*R)).length?0:Math.round(_*R)}function is(v,y){return y<(""+Math.round((v-Math.floor(v))*Math.pow(10,y))).length?1:0}function de(v){return v<2147483647&&v>-2147483648?""+(v>=0?v|0:v-1|0):""+Math.floor(v)}function yt(v,y,_){if(v.charCodeAt(0)===40&&!y.match(T)){var R=y.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return _>=0?yt("n",R,_):"("+yt("n",R,-_)+")"}if(y.charCodeAt(y.length-1)===44)return F(v,y,_);if(y.indexOf("%")!==-1)return p(v,y,_);if(y.indexOf("E")!==-1)return m(y,_);if(y.charCodeAt(0)===36)return"$"+yt(v,y.substr(y.charAt(1)==" "?2:1),_);var w,I,Z,wt,Ft=Math.abs(_),Ot=_<0?"-":"";if(y.match(/^00+$/))return Ot+O(Ft,y.length);if(y.match(/^[#?]+$/))return w=O(_,0),w==="0"&&(w=""),w.length>y.length?w:et(y.substr(0,y.length-w.length))+w;if(I=y.match(B))return g(I,Ft,Ot);if(y.match(/^#+0+$/))return Ot+O(Ft,y.length-y.indexOf("0"));if(I=y.match(H))return w=he(_,I[1].length).replace(/^([^\.]+)$/,"$1."+et(I[1])).replace(/\.$/,"."+et(I[1])).replace(/\.(\d*)$/,function(Zt,ci){return"."+ci+o("0",et(I[1]).length-ci.length)}),y.indexOf("0.")!==-1?w:w.replace(/^0\./,".");if(y=y.replace(/^#+([0.])/,"$1"),I=y.match(/^(0*)\.(#*)$/))return Ot+he(Ft,I[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,I[1].length?"0.":".");if(I=y.match(/^#{1,3},##0(\.?)$/))return Ot+Ye(O(Ft,0));if(I=y.match(/^#,##0\.([#0]*0)$/))return _<0?"-"+yt(v,y,-_):Ye(""+(Math.floor(_)+is(_,I[1].length)))+"."+a(ln(_,I[1].length),I[1].length);if(I=y.match(/^#,#*,#0/))return yt(v,y.replace(/^#,#*,/,""),_);if(I=y.match(/^([0#]+)(\\?-([0#]+))+$/))return w=s(yt(v,y.replace(/[\\-]/g,""),_)),Z=0,s(s(y.replace(/\\/g,"")).replace(/[0#]/g,function(Zt){return Z<w.length?w.charAt(Z++):Zt==="0"?"0":""}));if(y.match(Q))return w=yt(v,"##########",_),"("+w.substr(0,3)+") "+w.substr(3,3)+"-"+w.substr(6);var st="";if(I=y.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return Z=Math.min(I[4].length,7),wt=Yi(Ft,Math.pow(10,Z)-1,!1),w=""+Ot,st=Be("n",I[1],wt[1]),st.charAt(st.length-1)==" "&&(st=st.substr(0,st.length-1)+"0"),w+=st+I[2]+"/"+I[3],st=l(wt[2],Z),st.length<I[4].length&&(st=et(I[4].substr(I[4].length-st.length))+st),w+=st,w;if(I=y.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return Z=Math.min(Math.max(I[1].length,I[4].length),7),wt=Yi(Ft,Math.pow(10,Z)-1,!0),Ot+(wt[0]||(wt[1]?"":"0"))+" "+(wt[1]?c(wt[1],Z)+I[2]+"/"+I[3]+l(wt[2],Z):o(" ",2*Z+1+I[2].length+I[3].length));if(I=y.match(/^[#0?]+$/))return w=O(_,0),y.length<=w.length?w:et(y.substr(0,y.length-w.length))+w;if(I=y.match(/^([#0?]+)\.([#0]+)$/)){w=""+_.toFixed(Math.min(I[2].length,10)).replace(/([^0])0+$/,"$1"),Z=w.indexOf(".");var oi=y.indexOf(".")-Z,rs=y.length-w.length-oi;return et(y.substr(0,oi)+w+y.substr(y.length-rs))}if(I=y.match(/^00,000\.([#0]*0)$/))return Z=ln(_,I[1].length),_<0?"-"+yt(v,y,-_):Ye(de(_)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(Zt){return"00,"+(Zt.length<3?a(0,3-Zt.length):"")+Zt})+"."+a(Z,I[1].length);switch(y){case"###,##0.00":return yt(v,"#,##0.00",_);case"###,###":case"##,###":case"#,###":var ai=Ye(O(Ft,0));return ai!=="0"?Ot+ai:"";case"###,###.00":return yt(v,"###,##0.00",_).replace(/^0\./,".");case"#,###.00":return yt(v,"#,##0.00",_).replace(/^0\./,".")}throw new Error("unsupported format |"+y+"|")}function si(v,y,_){for(var R=y.length-1;y.charCodeAt(R-1)===44;)--R;return Be(v,y.substr(0,R),_/Math.pow(10,3*(y.length-R)))}function qi(v,y,_){var R=y.replace(d,""),w=y.length-R.length;return Be(v,R,_*Math.pow(10,2*w))+o("%",w)}function Re(v,y){var _,R=v.indexOf("E")-v.indexOf(".")-1;if(v.match(/^#+0.0E\+0$/)){if(y==0)return"0.0E+0";if(y<0)return"-"+Re(v,-y);var w=v.indexOf(".");w===-1&&(w=v.indexOf("E"));var I=Math.floor(Math.log(y)*Math.LOG10E)%w;if(I<0&&(I+=w),_=(y/Math.pow(10,I)).toPrecision(R+1+(w+I)%w),!_.match(/[Ee]/)){var Z=Math.floor(Math.log(y)*Math.LOG10E);_.indexOf(".")===-1?_=_.charAt(0)+"."+_.substr(1)+"E+"+(Z-_.length+I):_+="E+"+(Z-I),_=_.replace(/\+-/,"-")}_=_.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(wt,Ft,Ot,st){return Ft+Ot+st.substr(0,(w+I)%w)+"."+st.substr(I)+"E"})}else _=y.toExponential(R);return v.match(/E\+00$/)&&_.match(/e[+-]\d$/)&&(_=_.substr(0,_.length-1)+"0"+_.charAt(_.length-1)),v.match(/E\-/)&&_.match(/e\+/)&&(_=_.replace(/e\+/,"e")),_.replace("e","E")}function G(v,y,_){if(v.charCodeAt(0)===40&&!y.match(T)){var R=y.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return _>=0?G("n",R,_):"("+G("n",R,-_)+")"}if(y.charCodeAt(y.length-1)===44)return si(v,y,_);if(y.indexOf("%")!==-1)return qi(v,y,_);if(y.indexOf("E")!==-1)return Re(y,_);if(y.charCodeAt(0)===36)return"$"+G(v,y.substr(y.charAt(1)==" "?2:1),_);var w,I,Z,wt,Ft=Math.abs(_),Ot=_<0?"-":"";if(y.match(/^00+$/))return Ot+a(Ft,y.length);if(y.match(/^[#?]+$/))return w=""+_,_===0&&(w=""),w.length>y.length?w:et(y.substr(0,y.length-w.length))+w;if(I=y.match(B))return E(I,Ft,Ot);if(y.match(/^#+0+$/))return Ot+a(Ft,y.length-y.indexOf("0"));if(I=y.match(H))return w=(""+_).replace(/^([^\.]+)$/,"$1."+et(I[1])).replace(/\.$/,"."+et(I[1])),w=w.replace(/\.(\d*)$/,function(Zt,ci){return"."+ci+o("0",et(I[1]).length-ci.length)}),y.indexOf("0.")!==-1?w:w.replace(/^0\./,".");if(y=y.replace(/^#+([0.])/,"$1"),I=y.match(/^(0*)\.(#*)$/))return Ot+(""+Ft).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,I[1].length?"0.":".");if(I=y.match(/^#{1,3},##0(\.?)$/))return Ot+Ye(""+Ft);if(I=y.match(/^#,##0\.([#0]*0)$/))return _<0?"-"+G(v,y,-_):Ye(""+_)+"."+o("0",I[1].length);if(I=y.match(/^#,#*,#0/))return G(v,y.replace(/^#,#*,/,""),_);if(I=y.match(/^([0#]+)(\\?-([0#]+))+$/))return w=s(G(v,y.replace(/[\\-]/g,""),_)),Z=0,s(s(y.replace(/\\/g,"")).replace(/[0#]/g,function(Zt){return Z<w.length?w.charAt(Z++):Zt==="0"?"0":""}));if(y.match(Q))return w=G(v,"##########",_),"("+w.substr(0,3)+") "+w.substr(3,3)+"-"+w.substr(6);var st="";if(I=y.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return Z=Math.min(I[4].length,7),wt=Yi(Ft,Math.pow(10,Z)-1,!1),w=""+Ot,st=Be("n",I[1],wt[1]),st.charAt(st.length-1)==" "&&(st=st.substr(0,st.length-1)+"0"),w+=st+I[2]+"/"+I[3],st=l(wt[2],Z),st.length<I[4].length&&(st=et(I[4].substr(I[4].length-st.length))+st),w+=st,w;if(I=y.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return Z=Math.min(Math.max(I[1].length,I[4].length),7),wt=Yi(Ft,Math.pow(10,Z)-1,!0),Ot+(wt[0]||(wt[1]?"":"0"))+" "+(wt[1]?c(wt[1],Z)+I[2]+"/"+I[3]+l(wt[2],Z):o(" ",2*Z+1+I[2].length+I[3].length));if(I=y.match(/^[#0?]+$/))return w=""+_,y.length<=w.length?w:et(y.substr(0,y.length-w.length))+w;if(I=y.match(/^([#0]+)\.([#0]+)$/)){w=""+_.toFixed(Math.min(I[2].length,10)).replace(/([^0])0+$/,"$1"),Z=w.indexOf(".");var oi=y.indexOf(".")-Z,rs=y.length-w.length-oi;return et(y.substr(0,oi)+w+y.substr(y.length-rs))}if(I=y.match(/^00,000\.([#0]*0)$/))return _<0?"-"+G(v,y,-_):Ye(""+_).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(Zt){return"00,"+(Zt.length<3?a(0,3-Zt.length):"")+Zt})+"."+a(0,I[1].length);switch(y){case"###,###":case"##,###":case"#,###":var ai=Ye(""+Ft);return ai!=="0"?Ot+ai:"";default:if(y.match(/\.[0#?]*$/))return G(v,y.slice(0,y.lastIndexOf(".")),_)+et(y.slice(y.lastIndexOf(".")))}throw new Error("unsupported format |"+y+"|")}return function(y,_,R){return(R|0)===R?G(y,_,R):yt(y,_,R)}}();function Io(f){for(var d=[],p=!1,F=0,m=0;F<f.length;++F)switch(f.charCodeAt(F)){case 34:p=!p;break;case 95:case 42:case 92:++F;break;case 59:d[d.length]=f.substr(m,F-m),m=F+1}if(d[d.length]=f.substr(m),p===!0)throw new Error("Format |"+f+"| unterminated string ");return d}r._split=Io;var vo=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function So(f){for(var d=0,p="",F="";d<f.length;)switch(p=f.charAt(d)){case"G":W(f,d)&&(d+=6),d++;break;case'"':for(;f.charCodeAt(++d)!==34&&d<f.length;);++d;break;case"\\":d+=2;break;case"_":d+=2;break;case"@":++d;break;case"B":case"b":if(f.charAt(d+1)==="1"||f.charAt(d+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(f.substr(d,3).toUpperCase()==="A/P"||f.substr(d,5).toUpperCase()==="AM/PM"||f.substr(d,5).toUpperCase()==="上午/下午")return!0;++d;break;case"[":for(F=p;f.charAt(d++)!=="]"&&d<f.length;)F+=f.charAt(d);if(F.match(vo))return!0;break;case".":case"0":case"#":for(;d<f.length&&("0#?.,E+-%".indexOf(p=f.charAt(++d))>-1||p=="\\"&&f.charAt(d+1)=="-"&&"0#".indexOf(f.charAt(d+2))>-1););break;case"?":for(;f.charAt(++d)===p;);break;case"*":++d,(f.charAt(d)==" "||f.charAt(d)=="*")&&++d;break;case"(":case")":++d;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;d<f.length&&"0123456789".indexOf(f.charAt(++d))>-1;);break;case" ":++d;break;default:++d;break}return!1}r.is_date=So;function Eo(f,d,p,F){for(var m=[],B="",g=0,E="",H="t",T,Q,et,he="H";g<f.length;)switch(E=f.charAt(g)){case"G":if(!W(f,g))throw new Error("unrecognized character "+E+" in "+f);m[m.length]={t:"G",v:"General"},g+=7;break;case'"':for(B="";(et=f.charCodeAt(++g))!==34&&g<f.length;)B+=String.fromCharCode(et);m[m.length]={t:"t",v:B},++g;break;case"\\":var ln=f.charAt(++g),is=ln==="("||ln===")"?ln:"t";m[m.length]={t:is,v:ln},++g;break;case"_":m[m.length]={t:"t",v:" "},g+=2;break;case"@":m[m.length]={t:"T",v:d},++g;break;case"B":case"b":if(f.charAt(g+1)==="1"||f.charAt(g+1)==="2"){if(T==null&&(T=ri(d,p,f.charAt(g+1)==="2"),T==null))return"";m[m.length]={t:"X",v:f.substr(g,2)},H=E,g+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":E=E.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(d<0||T==null&&(T=ri(d,p),T==null))return"";for(B=E;++g<f.length&&f.charAt(g).toLowerCase()===E;)B+=E;E==="m"&&H.toLowerCase()==="h"&&(E="M"),E==="h"&&(E=he),m[m.length]={t:E,v:B},H=E;break;case"A":case"a":case"上":var de={t:E,v:E};if(T==null&&(T=ri(d,p)),f.substr(g,3).toUpperCase()==="A/P"?(T!=null&&(de.v=T.H>=12?"P":"A"),de.t="T",he="h",g+=3):f.substr(g,5).toUpperCase()==="AM/PM"?(T!=null&&(de.v=T.H>=12?"PM":"AM"),de.t="T",g+=5,he="h"):f.substr(g,5).toUpperCase()==="上午/下午"?(T!=null&&(de.v=T.H>=12?"下午":"上午"),de.t="T",g+=5,he="h"):(de.t="t",++g),T==null&&de.t==="T")return"";m[m.length]=de,H=E;break;case"[":for(B=E;f.charAt(g++)!=="]"&&g<f.length;)B+=f.charAt(g);if(B.slice(-1)!=="]")throw'unterminated "[" block: |'+B+"|";if(B.match(vo)){if(T==null&&(T=ri(d,p),T==null))return"";m[m.length]={t:"Z",v:B.toLowerCase()},H=B.charAt(1)}else B.indexOf("$")>-1&&(B=(B.match(/\$([^-\[\]]*)/)||[])[1]||"$",So(f)||(m[m.length]={t:"t",v:B}));break;case".":if(T!=null){for(B=E;++g<f.length&&(E=f.charAt(g))==="0";)B+=E;m[m.length]={t:"s",v:B};break}case"0":case"#":for(B=E;++g<f.length&&"0#?.,E+-%".indexOf(E=f.charAt(g))>-1;)B+=E;m[m.length]={t:"n",v:B};break;case"?":for(B=E;f.charAt(++g)===E;)B+=E;m[m.length]={t:E,v:B},H=E;break;case"*":++g,(f.charAt(g)==" "||f.charAt(g)=="*")&&++g;break;case"(":case")":m[m.length]={t:F===1?"t":E,v:E},++g;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(B=E;g<f.length&&"0123456789".indexOf(f.charAt(++g))>-1;)B+=f.charAt(g);m[m.length]={t:"D",v:B};break;case" ":m[m.length]={t:E,v:E},++g;break;case"$":m[m.length]={t:"t",v:"$"},++g;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(E)===-1)throw new Error("unrecognized character "+E+" in "+f);m[m.length]={t:"t",v:E},++g;break}var yt=0,si=0,qi;for(g=m.length-1,H="t";g>=0;--g)switch(m[g].t){case"h":case"H":m[g].t=he,H="h",yt<1&&(yt=1);break;case"s":(qi=m[g].v.match(/\.0+$/))&&(si=Math.max(si,qi[0].length-1)),yt<3&&(yt=3);case"d":case"y":case"M":case"e":H=m[g].t;break;case"m":H==="s"&&(m[g].t="M",yt<2&&(yt=2));break;case"X":break;case"Z":yt<1&&m[g].v.match(/[Hh]/)&&(yt=1),yt<2&&m[g].v.match(/[Mm]/)&&(yt=2),yt<3&&m[g].v.match(/[Ss]/)&&(yt=3)}switch(yt){case 0:break;case 1:T.u>=.5&&(T.u=0,++T.S),T.S>=60&&(T.S=0,++T.M),T.M>=60&&(T.M=0,++T.H);break;case 2:T.u>=.5&&(T.u=0,++T.S),T.S>=60&&(T.S=0,++T.M);break}var Re="",G;for(g=0;g<m.length;++g)switch(m[g].t){case"t":case"T":case" ":case"D":break;case"X":m[g].v="",m[g].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":m[g].v=Ll(m[g].t.charCodeAt(0),m[g].v,T,si),m[g].t="t";break;case"n":case"?":for(G=g+1;m[G]!=null&&((E=m[G].t)==="?"||E==="D"||(E===" "||E==="t")&&m[G+1]!=null&&(m[G+1].t==="?"||m[G+1].t==="t"&&m[G+1].v==="/")||m[g].t==="("&&(E===" "||E==="n"||E===")")||E==="t"&&(m[G].v==="/"||m[G].v===" "&&m[G+1]!=null&&m[G+1].t=="?"));)m[g].v+=m[G].v,m[G]={v:"",t:";"},++G;Re+=m[g].v,g=G-1;break;case"G":m[g].t="t",m[g].v=Gi(d,p);break}var v="",y,_;if(Re.length>0){Re.charCodeAt(0)==40?(y=d<0&&Re.charCodeAt(0)===45?-d:d,_=Be("n",Re,y)):(y=d<0&&F>1?-d:d,_=Be("n",Re,y),y<0&&m[0]&&m[0].t=="t"&&(_=_.substr(1),m[0].v="-"+m[0].v)),G=_.length-1;var R=m.length;for(g=0;g<m.length;++g)if(m[g]!=null&&m[g].t!="t"&&m[g].v.indexOf(".")>-1){R=g;break}var w=m.length;if(R===m.length&&_.indexOf("E")===-1){for(g=m.length-1;g>=0;--g)m[g]==null||"n?".indexOf(m[g].t)===-1||(G>=m[g].v.length-1?(G-=m[g].v.length,m[g].v=_.substr(G+1,m[g].v.length)):G<0?m[g].v="":(m[g].v=_.substr(0,G+1),G=-1),m[g].t="t",w=g);G>=0&&w<m.length&&(m[w].v=_.substr(0,G+1)+m[w].v)}else if(R!==m.length&&_.indexOf("E")===-1){for(G=_.indexOf(".")-1,g=R;g>=0;--g)if(!(m[g]==null||"n?".indexOf(m[g].t)===-1)){for(Q=m[g].v.indexOf(".")>-1&&g===R?m[g].v.indexOf(".")-1:m[g].v.length-1,v=m[g].v.substr(Q+1);Q>=0;--Q)G>=0&&(m[g].v.charAt(Q)==="0"||m[g].v.charAt(Q)==="#")&&(v=_.charAt(G--)+v);m[g].v=v,m[g].t="t",w=g}for(G>=0&&w<m.length&&(m[w].v=_.substr(0,G+1)+m[w].v),G=_.indexOf(".")+1,g=R;g<m.length;++g)if(!(m[g]==null||"n?(".indexOf(m[g].t)===-1&&g!==R)){for(Q=m[g].v.indexOf(".")>-1&&g===R?m[g].v.indexOf(".")+1:0,v=m[g].v.substr(0,Q);Q<m[g].v.length;++Q)G<_.length&&(v+=_.charAt(G++));m[g].v=v,m[g].t="t",w=g}}}for(g=0;g<m.length;++g)m[g]!=null&&"n?".indexOf(m[g].t)>-1&&(y=F>1&&d<0&&g>0&&m[g-1].v==="-"?-d:d,m[g].v=Be(m[g].t,m[g].v,y),m[g].t="t");var I="";for(g=0;g!==m.length;++g)m[g]!=null&&(I+=m[g].v);return I}r._eval=Eo;var To=/\[[=<>]/,Ao=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function Fo(f,d){if(d==null)return!1;var p=parseFloat(d[2]);switch(d[1]){case"=":if(f==p)return!0;break;case">":if(f>p)return!0;break;case"<":if(f<p)return!0;break;case"<>":if(f!=p)return!0;break;case">=":if(f>=p)return!0;break;case"<=":if(f<=p)return!0;break}return!1}function Ul(f,d){var p=Io(f),F=p.length,m=p[F-1].indexOf("@");if(F<4&&m>-1&&--F,p.length>4)throw new Error("cannot find right format for |"+p.join("|")+"|");if(typeof d!="number")return[4,p.length===4||m>-1?p[p.length-1]:"@"];switch(p.length){case 1:p=m>-1?["General","General","General",p[0]]:[p[0],p[0],p[0],"@"];break;case 2:p=m>-1?[p[0],p[0],p[0],p[1]]:[p[0],p[1],p[0],"@"];break;case 3:p=m>-1?[p[0],p[1],p[0],p[2]]:[p[0],p[1],p[2],"@"];break}var B=d>0?p[0]:d<0?p[1]:p[2];if(p[0].indexOf("[")===-1&&p[1].indexOf("[")===-1)return[F,B];if(p[0].match(To)!=null||p[1].match(To)!=null){var g=p[0].match(Ao),E=p[1].match(Ao);return Fo(d,g)?[F,p[0]]:Fo(d,E)?[F,p[1]]:[F,p[g!=null&&E!=null?2:1]]}return[F,B]}function Oo(f,d,p){p==null&&(p={});var F="";switch(typeof f){case"string":f=="m/d/yy"&&p.dateNF?F=p.dateNF:F=f;break;case"number":f==14&&p.dateNF?F=p.dateNF:F=(p.table!=null?p.table:gt)[f],F==null&&(F=p.table&&p.table[At[f]]||gt[At[f]]),F==null&&(F=kt[f]||"General");break}if(W(F,0))return Gi(d,p);d instanceof Date&&(d=_o(d,p.date1904));var m=Ul(F,d);if(W(m[1]))return Gi(d,p);if(d===!0)d="TRUE";else if(d===!1)d="FALSE";else if(d===""||d==null)return"";return Eo(m[1],d,p,m[0])}function Do(f,d){if(typeof d!="number"){d=+d||-1;for(var p=0;p<392;++p){if(gt[p]==null){d<0&&(d=p);continue}if(gt[p]==f){d=p;break}}d<0&&(d=391)}return gt[d]=f,d}r.load=Do,r._table=gt,r.get_table=function(){return gt},r.load_table=function(d){for(var p=0;p!=392;++p)d[p]!==void 0&&Do(d[p],p)},r.init_table=it,r.format=Oo};e(t),typeof DO_NOT_EXPORT_SSF>"u"&&(n.exports=t)})(Dl);var Rf=Dl.exports;const Ni=Nf(Rf),op="customFormattingSettings",nn="auto",Mf=3,Cf=[{name:"year",description:'When lowerCase(columnName)="year" with the column having numeric values will result in no formatting',matchingFunction:(n,t,e)=>n&&t?n.toLowerCase()==="year"&&((t==null?void 0:t.evidenceType)==="number"||(e==null?void 0:e.unitType)==="number"):!1,format:{formatCode:nn,valueType:"number",exampleInput:2013,_autoFormat:{autoFormatCode:"@",truncateUnits:!1}}},{name:"id",description:'When lowerCase(columnName)="id" with the column having numeric values, then values will have no formatting',matchingFunction:(n,t,e)=>n&&t?n.toLowerCase()==="id"&&((t==null?void 0:t.evidenceType)==="number"||(e==null?void 0:e.unitType)==="number"):!1,format:{formatCode:nn,valueType:"number",exampleInput:93120121,_autoFormat:{autoFormatFunction:n=>n!=null&&!isNaN(n)?n.toLocaleString("fullwide",{useGrouping:!1}):n}}},{name:"defaultDate",description:"Formatting for Default Date",matchingFunction:(n,t,e)=>t?(t==null?void 0:t.evidenceType)==="date"||(e==null?void 0:e.unitType)==="date":!1,format:{formatCode:nn,valueType:"date",exampleInput:"Sat Jan 01 2022 03:15:00 GMT-0500",_autoFormat:{autoFormatCode:"YYYY-MM-DD",truncateUnits:!1}}}],mo=(n,t)=>{switch(t){case"T":return n/1e12;case"B":return n/1e9;case"M":return n/1e6;case"k":return n/1e3;default:return n}},ap=(n,t)=>{var r,s,o;let e=((r=t||n.formatCode)==null?void 0:r.toLowerCase())===nn,i=((s=n._autoFormat)==null?void 0:s.autoFormatFunction)||((o=n._autoFormat)==null?void 0:o.autoFormatCode);return!!(e&&i!==void 0)},go=(n,t=7)=>{let e,i="",r=n==null?void 0:n.median,s;if(r!==void 0){let o;i=Nl(r),i?(o=mo(r,i),s=!0):(o=r,s=!1),n.maxDecimals===0&&!s?e="#,##0":e=Lf(o,t)}else e="#,##0",s=!1;return{formatCode:nn,valueType:"number",_autoFormat:{autoFormatCode:e,truncateUnits:s,columnUnits:i}}},cp=(n,t,e)=>{let i=Cf.find(r=>r.matchingFunction(n,t,e));if(i)return i.format;if((e==null?void 0:e.unitType)==="number")return go(e)},lp=(n,t,e=void 0)=>{var i,r,s;if((i=t._autoFormat)!=null&&i.autoFormatFunction)return t._autoFormat.autoFormatFunction(n,t,e);if(t._autoFormat.autoFormatCode){let o=(r=t==null?void 0:t._autoFormat)==null?void 0:r.autoFormatCode;if(t.valueType==="number"){let c=(s=t==null?void 0:t._autoFormat)==null?void 0:s.truncateUnits,l=n,b="";return c&&(e==null?void 0:e.median)!==void 0&&(b=Nl(e.median),l=mo(n,b)),Ni.format(o,l)+b}else return Ni.format(o,n)}else console.warn("autoFormat called without a formatCode or function");return n},up=n=>typeof n=="number"?n.toLocaleString(void 0,{minimumFractionDigits:0,maximumFractionDigits:2}):n!=null?n==null?void 0:n.toString():"-";function Lf(n,t=7,e=Mf){let i="#,##0",r=Uf(n),s=0;return r-e<0&&(s=Math.min(Math.max(Math.abs(r-e+1),0),t)),s>0&&(i+=".",i+="0".repeat(s)),i}function Nl(n){let t=Math.abs(n);return t>=5e12?"T":t>=5e9?"B":t>=5e6?"M":t>=5e3?"k":""}function Uf(n){return n===0?0:Math.floor(Math.log10(n))}const xf=[{primaryCode:"usd",currencySymbol:"$",displayName:"USD - United States Dollar"},{primaryCode:"aud",currencySymbol:"A$",displayName:"AUD - Australian Dollar",escapeCurrencySymbol:!0},{primaryCode:"brl",currencySymbol:"R$",displayName:"BRL - Brazilian Real",escapeCurrencySymbol:!0},{primaryCode:"cad",currencySymbol:"C$",displayName:"CAD - Canadian Dollar",escapeCurrencySymbol:!0},{primaryCode:"cny",currencySymbol:"¥",displayName:"CNY - Renminbi",escapeCurrencySymbol:!0},{primaryCode:"eur",currencySymbol:"€",displayName:"EUR - Euro"},{primaryCode:"gbp",currencySymbol:"£",displayName:"GBP - Pound Sterling",escapeCurrencySymbol:!0},{primaryCode:"jpy",currencySymbol:"¥",displayName:"JPY - Japanese Yen",escapeCurrencySymbol:!0},{primaryCode:"inr",currencySymbol:"₹",displayName:"INR - Indian Rupee",escapeCurrencySymbol:!0},{primaryCode:"krw",currencySymbol:"₩",displayName:"KRW - South Korean won",escapeCurrencySymbol:!0},{primaryCode:"ngn",currencySymbol:"₦",displayName:"NGN -  Nigerian Naira",escapeCurrencySymbol:!0},{primaryCode:"rub",currencySymbol:"rub",displayName:"RUB - Russian Ruble",escapeCurrencySymbol:!0},{primaryCode:"sek",currencySymbol:"kr",displayName:"SEK - Swedish Krona",escapeCurrencySymbol:!0}],Pf=[{derivedSuffix:"",valueFormatCode:"#,##0",exampleInput:412.17,auto:!0},{derivedSuffix:"0",valueFormatCode:"#,##0",exampleInput:7043.123},{derivedSuffix:"1",valueFormatCode:"#,##0.0",exampleInput:7043.123},{derivedSuffix:"2",valueFormatCode:"#,##0.00",exampleInput:7043.123},{derivedSuffix:"0k",valueFormatCode:'#,##0,"k"',exampleInput:64301.12},{derivedSuffix:"1k",valueFormatCode:'#,##0.0,"k"',exampleInput:64301.12},{derivedSuffix:"2k",valueFormatCode:'#,##0.00,"k"',exampleInput:64301.12},{derivedSuffix:"0m",valueFormatCode:'#,##0,,"M"',exampleInput:456430112e-2},{derivedSuffix:"1m",valueFormatCode:'#,##0.0,,"M"',exampleInput:456430112e-2},{derivedSuffix:"2m",valueFormatCode:'#,##0.00,,"M"',exampleInput:456430112e-2},{derivedSuffix:"0b",valueFormatCode:'#,##0,,,"B"',exampleInput:978456430112e-2},{derivedSuffix:"1b",valueFormatCode:'#,##0.0,,,"B"',exampleInput:978456430112e-2},{derivedSuffix:"2b",valueFormatCode:'#,##0.00,,,"B"',exampleInput:978456430112e-2}],kf=xf.map(n=>{let t=[];return Pf.forEach(e=>{let i={formatTag:n.primaryCode+e.derivedSuffix,parentFormat:n.primaryCode,formatCategory:"currency",valueType:"number",exampleInput:e.exampleInput,titleTagReplacement:` (${n.currencySymbol})`},r=n.escapeCurrencySymbol?`"${n.currencySymbol}"`:n.currencySymbol;e.auto||nn===e.formatCode?(i.formatCode=nn,i._autoFormat={autoFormatFunction:(s,o,a)=>{let c=go(a,2),l=`${r}${c._autoFormat.autoFormatCode}`,b="",S=s;return c._autoFormat.truncateUnits&&c._autoFormat.columnUnits?(b=c._autoFormat.columnUnits,S=mo(s,c._autoFormat.columnUnits)):l.endsWith(".0")&&(l=l+"0"),Ni.format(l,S)+b}}):i.formatCode=`${r}${e.valueFormatCode}`,e.axisValueFormatCode&&(i.axisFormatCode=e.axisValueFormatCode),t.push(i)}),t}).flat(),hp=[...kf,{formatTag:"ddd",formatCode:"ddd",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"dddd",formatCode:"dddd",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"mmm",formatCode:"mmm",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"mmmm",formatCode:"mmmm",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"yyyy",formatCode:"yyyy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"shortdate",formatCode:"mmm d/yy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"longdate",formatCode:"mmmm d, yyyy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"fulldate",formatCode:"dddd mmmm d, yyyy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"mdy",formatCode:"m/d/y",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"dmy",formatCode:"d/m/y",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"hms",formatCode:"H:MM:SS AM/PM",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09T11:45:03"},{formatTag:"num0",formatCode:"#,##0",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num1",formatCode:"#,##0.0",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num2",formatCode:"#,##0.00",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num3",formatCode:"#,##0.000",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num4",formatCode:"#,##0.0000",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num0k",formatCode:'#,##0,"k"',formatCategory:"number",valueType:"number",exampleInput:64201},{formatTag:"num1k",formatCode:'#,##0.0,"k"',formatCategory:"number",valueType:"number",exampleInput:64201},{formatTag:"num2k",formatCode:'#,##0.00,"k"',formatCategory:"number",valueType:"number",exampleInput:64201},{formatTag:"num0m",formatCode:'#,##0,,"M"',formatCategory:"number",valueType:"number",exampleInput:42539483},{formatTag:"num1m",formatCode:'#,##0.0,,"M"',formatCategory:"number",valueType:"number",exampleInput:42539483},{formatTag:"num2m",formatCode:'#,##0.00,,"M"',formatCategory:"number",valueType:"number",exampleInput:42539483},{formatTag:"num0b",formatCode:'#,##0,,,"B"',formatCategory:"number",valueType:"number",exampleInput:1384937584},{formatTag:"num1b",formatCode:'#,##0.0,,,"B"',formatCategory:"number",valueType:"number",exampleInput:1384937584},{formatTag:"num2b",formatCode:'#,##0.00,,,"B"',formatCategory:"number",valueType:"number",exampleInput:1384937584},{formatTag:"id",formatCode:"0",formatCategory:"number",valueType:"number",exampleInput:"921594675",titleTagReplacement:" id"},{formatTag:"fract",formatCode:"# ?/?",formatCategory:"number",valueType:"number",exampleInput:"0.25"},{formatTag:"mult",formatCode:'#,##0.0"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"mult0",formatCode:'#,##0"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"mult1",formatCode:'#,##0.0"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"mult2",formatCode:'#,##0.00"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"sci",formatCode:"0.00E+0",formatCategory:"number",valueType:"number",exampleInput:"16546.1561"},{formatTag:"pct",formatCode:nn,formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:"",_autoFormat:{autoFormatFunction:(n,t,e)=>{if((e==null?void 0:e.unitType)==="number"){let i={min:e.min*100,max:e.max*100,median:e.median*100,maxDecimals:Math.max(e.maxDecimals-2,0),unitType:e.unitType},r=go(i);return Ni.format(r._autoFormat.autoFormatCode,n*100)+"%"}else return Ni.format("#,##0%",n)}}},{formatTag:"pct0",formatCode:"#,##0%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""},{formatTag:"pct1",formatCode:"#,##0.0%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""},{formatTag:"pct2",formatCode:"#,##0.00%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""},{formatTag:"pct3",formatCode:"#,##0.000%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""}];var mn;(function(n){n.BOOLEAN="boolean",n.NUMBER="number",n.STRING="string",n.DATE="date"})(mn||(mn={}));var Vr;(function(n){n.INFERRED="inferred",n.PRECISE="precise"})(Vr||(Vr={}));const $f=function(n){return typeof n=="number"?mn.NUMBER:typeof n=="boolean"?mn.BOOLEAN:n instanceof Date?mn.DATE:mn.STRING};function dp(n){if(n!=null&&n._evidenceColumnTypes)return n._evidenceColumnTypes;if(n&&n.length>0){let t=Object.keys(n[0]);return t==null?void 0:t.map(i=>{let r=n.find(s=>s[i]!=null);if(r){let s=$f(r[i]);return{name:i,evidenceType:s,typeFidelity:Vr.INFERRED}}else return{name:i,evidenceType:mn.STRING,typeFidelity:Vr.INFERRED}})}return[]}export{Xf as A,hp as B,op as C,cp as D,ap as E,lp as F,up as G,dp as H,Vr as I,mn as J,Us as Q,xf as S,ye as T,Ae as _,np as a,sp as b,Ni as c,q as d,tp as e,Xi as f,In as g,Y as h,Co as i,ip as j,Nf as k,Hf as l,zl as m,po as n,Zf as o,Qf as p,ef as q,Jf as r,rp as s,Kf as t,qf as u,Df as v,ep as w,bi as x,rf as y,Fl as z};

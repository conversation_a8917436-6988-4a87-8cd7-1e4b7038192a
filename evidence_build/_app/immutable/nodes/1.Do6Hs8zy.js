import{s as J,d as u,i as m,e as p,l as M,b as h,h as _,j as g,k as x,m as d,n as T,p as Q,q as A,r as w,t as W,v as N,w as P,x as C,y}from"../chunks/scheduler.DQwIXrE4.js";import{S as K,i as L,d as S,t as E,a as v,g as D,c as q,m as j,b as I,e as H,f as z}from"../chunks/index.BEt_7cXZ.js";import{p as X}from"../chunks/stores.DZ5VrdAm.js";import{A as Y,a as Z}from"../chunks/AccordionItem.D3vBGfst.js";import{I as tt,C as et,f as F}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.ebVGE6DV.js";import"../chunks/entry.DSMgDpC3.js";function O(f){let t,a="Copied to clipboard",s,n;return{c(){t=d("p"),t.textContent=a,this.h()},l(o){t=_(o,"P",{class:!0,"data-svelte-h":!0}),A(t)!=="svelte-1u5nnc"&&(t.textContent=a),this.h()},h(){h(t,"class","absolute -bottom-14 right-0 text-sm bg-base-200 w-[17ch] text-center font-sans p-2 border border-base-300 rounded-sm")},m(o,l){m(o,t,l),n=!0},i(o){n||(o&&Q(()=>{n&&(s||(s=z(t,F,{duration:250},!0)),s.run(1))}),n=!0)},o(o){o&&(s||(s=z(t,F,{duration:250},!1)),s.run(0)),n=!1},d(o){o&&u(t),o&&s&&s.end()}}}function st(f){let t,a,s,n,o,l,i,e=f[0]&&O();return n=new tt({props:{src:et,class:"w-4 h-4"}}),{c(){t=d("div"),e&&e.c(),a=T(),s=d("button"),H(n.$$.fragment),this.h()},l(r){t=_(r,"DIV",{class:!0});var c=g(t);e&&e.l(c),a=x(c),s=_(c,"BUTTON",{class:!0,title:!0});var k=g(s);I(n.$$.fragment,k),k.forEach(u),c.forEach(u),this.h()},h(){h(s,"class","bg-base-200 border border-base-300 rounded-sm p-2 hover:bg-base-200/80 active:bg-base-200"),h(s,"title","Copy to Clipboard"),h(t,"class","relative")},m(r,c){m(r,t,c),e&&e.m(t,null),p(t,a),p(t,s),j(n,s,null),o=!0,l||(i=M(s,"click",f[1]),l=!0)},p(r,[c]){r[0]?e?c&1&&v(e,1):(e=O(),e.c(),v(e,1),e.m(t,a)):e&&(D(),E(e,1,1,()=>{e=null}),q())},i(r){o||(v(e),v(n.$$.fragment,r),o=!0)},o(r){E(e),E(n.$$.fragment,r),o=!1},d(r){r&&u(t),e&&e.d(),S(n),l=!1,i()}}}function rt(f,t,a){let{textToCopy:s=""}=t,n=!1;const o=()=>{navigator.clipboard.writeText(s),a(0,n=!0),setTimeout(()=>a(0,n=!1),1e3)};return f.$$set=l=>{"textToCopy"in l&&a(2,s=l.textToCopy)},[n,o,s]}class nt extends K{constructor(t){super(),L(this,t,rt,st,J,{textToCopy:2})}}function at(f){let t,a="Unknown Error Encountered",s,n,o,l=f[0].status+"",i;return{c(){t=d("h1"),t.textContent=a,s=T(),n=d("span"),o=y("HTTP "),i=y(l),this.h()},l(e){t=_(e,"H1",{"data-svelte-h":!0}),A(t)!=="svelte-blh3ny"&&(t.textContent=a),s=x(e),n=_(e,"SPAN",{class:!0});var r=g(n);o=C(r,"HTTP "),i=C(r,l),r.forEach(u),this.h()},h(){h(n,"class","font-mono text-base")},m(e,r){m(e,t,r),m(e,s,r),m(e,n,r),p(n,o),p(n,i)},p(e,r){r&1&&l!==(l=e[0].status+"")&&P(i,l)},i:N,o:N,d(e){e&&(u(t),u(s),u(n))}}}function ot(f){let t,a="Application Error",s,n,o,l,i=f[0].error.message&&R(f),e=(f[0].error.stack||f[0].error.cause)&&G(f);return{c(){t=d("h1"),t.textContent=a,s=T(),i&&i.c(),n=T(),e&&e.c(),o=w(),this.h()},l(r){t=_(r,"H1",{class:!0,"data-svelte-h":!0}),A(t)!=="svelte-zh66lr"&&(t.textContent=a),s=x(r),i&&i.l(r),n=x(r),e&&e.l(r),o=w(),this.h()},h(){h(t,"class","mt-0 mb-8 py-0")},m(r,c){m(r,t,c),m(r,s,c),i&&i.m(r,c),m(r,n,c),e&&e.m(r,c),m(r,o,c),l=!0},p(r,c){r[0].error.message?i?i.p(r,c):(i=R(r),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null),r[0].error.stack||r[0].error.cause?e?(e.p(r,c),c&1&&v(e,1)):(e=G(r),e.c(),v(e,1),e.m(o.parentNode,o)):e&&(D(),E(e,1,1,()=>{e=null}),q())},i(r){l||(v(e),l=!0)},o(r){E(e),l=!1},d(r){r&&(u(t),u(s),u(n),u(o)),i&&i.d(r),e&&e.d(r)}}}function lt(f){let t,a="Page Not Found",s,n,o,l=f[0].status+"",i,e,r,c=f[0].url.pathname+"",k,B;return{c(){t=d("h1"),t.textContent=a,s=T(),n=d("p"),o=d("span"),i=y(l),e=y(`: The page
		`),r=d("span"),k=y(c),B=y(" can't be found in the project."),this.h()},l(b){t=_(b,"H1",{class:!0,"data-svelte-h":!0}),A(t)!=="svelte-s9jbdv"&&(t.textContent=a),s=x(b),n=_(b,"P",{});var $=g(n);o=_($,"SPAN",{class:!0});var U=g(o);i=C(U,l),U.forEach(u),e=C($,`: The page
		`),r=_($,"SPAN",{class:!0});var V=g(r);k=C(V,c),V.forEach(u),B=C($," can't be found in the project."),$.forEach(u),this.h()},h(){h(t,"class","mt-0 mb-8 py-0"),h(o,"class","font-mono text-base"),h(r,"class","font-mono text-base bg-base-200")},m(b,$){m(b,t,$),m(b,s,$),m(b,n,$),p(n,o),p(o,i),p(n,e),p(n,r),p(r,k),p(n,B)},p(b,$){$&1&&l!==(l=b[0].status+"")&&P(i,l),$&1&&c!==(c=b[0].url.pathname+"")&&P(k,c)},i:N,o:N,d(b){b&&(u(t),u(s),u(n))}}}function R(f){let t,a,s=f[0].status+"",n,o,l=f[0].error.message+"",i;return{c(){t=d("p"),a=d("span"),n=y(s),o=y(":"),i=y(l),this.h()},l(e){t=_(e,"P",{class:!0});var r=g(t);a=_(r,"SPAN",{class:!0});var c=g(a);n=C(c,s),c.forEach(u),o=C(r,":"),i=C(r,l),r.forEach(u),this.h()},h(){h(a,"class","font-mono text-base"),h(t,"class","font-mono text-sm bg-base-200 px-2 py-2")},m(e,r){m(e,t,r),p(t,a),p(a,n),p(t,o),p(t,i)},p(e,r){r&1&&s!==(s=e[0].status+"")&&P(n,s),r&1&&l!==(l=e[0].error.message+"")&&P(i,l)},d(e){e&&u(t)}}}function G(f){let t,a;return t=new Y({props:{$$slots:{default:[ct]},$$scope:{ctx:f}}}),{c(){H(t.$$.fragment)},l(s){I(t.$$.fragment,s)},m(s,n){j(t,s,n),a=!0},p(s,n){const o={};n&10&&(o.$$scope={dirty:n,ctx:s}),t.$set(o)},i(s){a||(v(t.$$.fragment,s),a=!0)},o(s){E(t.$$.fragment,s),a=!1},d(s){S(t,s)}}}function it(f){let t,a,s,n,o,l,i;return s=new nt({props:{textToCopy:f[1]}}),{c(){t=d("div"),a=d("span"),H(s.$$.fragment),n=T(),o=d("pre"),l=y(f[1]),this.h()},l(e){t=_(e,"DIV",{class:!0});var r=g(t);a=_(r,"SPAN",{class:!0});var c=g(a);I(s.$$.fragment,c),c.forEach(u),n=x(r),o=_(r,"PRE",{class:!0});var k=g(o);l=C(k,f[1]),k.forEach(u),r.forEach(u),this.h()},h(){h(a,"class","absolute top-2 right-2"),h(o,"class","font-mono text-sm bg-base-200 px-2 py-2 overflow-auto"),h(t,"class","relative")},m(e,r){m(e,t,r),p(t,a),j(s,a,null),p(t,n),p(t,o),p(o,l),i=!0},p(e,r){const c={};r&2&&(c.textToCopy=e[1]),s.$set(c),(!i||r&2)&&P(l,e[1])},i(e){i||(v(s.$$.fragment,e),i=!0)},o(e){E(s.$$.fragment,e),i=!1},d(e){e&&u(t),S(s)}}}function ct(f){let t,a;return t=new Z({props:{title:"Error Details",$$slots:{default:[it]},$$scope:{ctx:f}}}),{c(){H(t.$$.fragment)},l(s){I(t.$$.fragment,s)},m(s,n){j(t,s,n),a=!0},p(s,n){const o={};n&10&&(o.$$scope={dirty:n,ctx:s}),t.$set(o)},i(s){a||(v(t.$$.fragment,s),a=!0)},o(s){E(t.$$.fragment,s),a=!1},d(s){S(t,s)}}}function ft(f){let t,a,s,n;const o=[lt,ot,at],l=[];function i(e,r){return e[0].status===404?0:e[0].status===500?1:2}return t=i(f),a=l[t]=o[t](f),{c(){a.c(),s=w()},l(e){a.l(e),s=w()},m(e,r){l[t].m(e,r),m(e,s,r),n=!0},p(e,[r]){let c=t;t=i(e),t===c?l[t].p(e,r):(D(),E(l[c],1,1,()=>{l[c]=null}),q(),a=l[t],a?a.p(e,r):(a=l[t]=o[t](e),a.c()),v(a,1),a.m(s.parentNode,s))},i(e){n||(v(a),n=!0)},o(e){E(a),n=!1},d(e){e&&u(s),l[t].d(e)}}}function ut(f,t,a){let s,n;W(f,X,l=>a(0,n=l));{const l=document.getElementById("__evidence_project_splash");l==null||l.remove()}const o=l=>{let i="";return l.stack&&(i+=l.stack),l.cause&&(i+=`

Caused By:
	`,i+=o(l.cause).split(`
`).join(`
	`)),i};return f.$$.update=()=>{f.$$.dirty&1&&a(1,s=o(n.error))},[n,s]}class $t extends K{constructor(t){super(),L(this,t,ut,ft,J,{})}}export{$t as component};

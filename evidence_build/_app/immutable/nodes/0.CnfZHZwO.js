var Lg=Object.defineProperty;var zc=n=>{throw TypeError(n)};var Rg=(n,t,e)=>t in n?Lg(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var Ja=(n,t,e)=>Rg(n,typeof t!="symbol"?t+"":t,e),Wc=(n,t,e)=>t.has(n)||zc("Cannot "+e);var Ar=(n,t,e)=>(Wc(n,t,"read from private field"),e?e.call(n):t.get(n)),no=(n,t,e)=>t.has(n)?zc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(n):t.set(n,e),Qa=(n,t,e,r)=>(Wc(n,t,"write to private field"),r?r.call(n,e):t.set(n,e),e);var Hc=(n,t,e,r)=>({set _(i){Qa(n,t,i,e)},get _(){return Ar(n,t,r)}});import{_ as $n,d as xt,f as js,g as Bi,h as Mt,T as A,i as xr,j as Ug,k as Vg,a as Wt,l as vh,m as zg,q as Wg,n as Hg,u as xg,t as xc,o as qc,v as wh,w as Sh,x as qg,e as Yg,Q as Yc}from"../chunks/inferColumnTypes.DiSvbcQ7.js";import{p as ko}from"../chunks/profile.BW8tN6E9.js";import{R as ho,s as Zt,d as w,i as L,r as ut,a6 as Ue,a7 as te,a8 as Sr,o as ne,J as kn,c as ge,G as zn,u as _e,g as be,a as ye,ao as ji,l as Pt,H as Ir,h as q,j as J,m as Y,K as Kg,E as Ih,a5 as Jg,t as Te,a9 as Ge,p as En,w as Re,b as C,e as W,k as nt,x as vt,n as rt,y as wt,ag as Qg,v as Xt,ap as vs,F as Cr,A as Gs,q as vr,Q as Gg,ae as An,P as Fr,D as Xg,I as Zg,ab as Oh}from"../chunks/scheduler.D0cbHTIG.js";import{S as re,i as ie,t as E,a as I,g as jt,c as Lt,f as Dn,h as is,j as qi,d as ot,m as st,b as at,e as lt,p as kh}from"../chunks/index.YnsWT1Qn.js";import{l as ni,bj as $g,m as Nt,w as tn,t as Do,r as Kc,Z as Jc,x as an,a8 as Yn,A as ar,B as $t,a0 as Qc,H as ln,bk as t_,G as ro,y as e_,bl as wl,a6 as n_,n as io,a1 as lr,aa as Gc,v as Ls,z as Ga,q as Dh,k as gi,af as Xc,ag as Zc,bm as r_,bn as qr,N as rn,M as i_,K as o_,L as s_,ah as a_,O as tc,bo as l_,Q as jr,bp as $c,R as Ma,ak as Sl,bb as ii,e as he,u as Eh,o as c_,aj as u_,g as Ah,bq as f_,br as d_,I as Pn,bs as h_,bt as Xs,bu as m_,f as xo,bv as p_,bw as g_,bx as __,by as b_,bz as y_,bA as v_,bB as w_,bC as Il,bg as S_,bD as I_}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.p4QJCV1Q.js";import{w as Ke,d as mo,b as O_,c as k_}from"../chunks/entry.DRa3JAkG.js";import{p as Na,n as Th}from"../chunks/stores.CAjSrnTs.js";import{d as Ol}from"../chunks/index.rV6zwFgL.js";import{c as D_,h as $e,g as E_,d as A_,r as ci,a as T_,b as B_,G as P_,X as M_,B as N_,S as C_}from"../chunks/index.DqkuJHjx.js";import{A as F_,a as Xa}from"../chunks/AccordionItem.BQf1Ecnr.js";const j_=new TextDecoder("utf-8"),kl=n=>j_.decode(n),L_=new TextEncoder,ec=n=>L_.encode(n),R_=n=>typeof n=="number",U_=n=>typeof n=="boolean",en=n=>typeof n=="function",Un=n=>n!=null&&Object(n)===n,qo=n=>Un(n)&&en(n.then),Ca=n=>Un(n)&&en(n[Symbol.iterator]),nc=n=>Un(n)&&en(n[Symbol.asyncIterator]),Dl=n=>Un(n)&&Un(n.schema),Bh=n=>Un(n)&&"done"in n&&"value"in n,Ph=n=>Un(n)&&en(n.stat)&&R_(n.fd),Mh=n=>Un(n)&&rc(n.body),Nh=n=>"_getDOMStream"in n&&"_getNodeStream"in n,rc=n=>Un(n)&&en(n.cancel)&&en(n.getReader)&&!Nh(n),Ch=n=>Un(n)&&en(n.read)&&en(n.pipe)&&U_(n.readable)&&!Nh(n),V_=n=>Un(n)&&en(n.clear)&&en(n.bytes)&&en(n.position)&&en(n.setPosition)&&en(n.capacity)&&en(n.getBufferIdentifier)&&en(n.createLong),ic=typeof SharedArrayBuffer<"u"?SharedArrayBuffer:ArrayBuffer;function z_(n){const t=n[0]?[n[0]]:[];let e,r,i,o;for(let s,a,l=0,c=0,u=n.length;++l<u;){if(s=t[c],a=n[l],!s||!a||s.buffer!==a.buffer||a.byteOffset<s.byteOffset){a&&(t[++c]=a);continue}if({byteOffset:e,byteLength:i}=s,{byteOffset:r,byteLength:o}=a,e+i<r||r+o<e){a&&(t[++c]=a);continue}t[c]=new Uint8Array(s.buffer,e,r-e+o)}return t}function tu(n,t,e=0,r=t.byteLength){const i=n.byteLength,o=new Uint8Array(n.buffer,n.byteOffset,i),s=new Uint8Array(t.buffer,t.byteOffset,Math.min(r,i));return o.set(s,e),n}function rr(n,t){const e=z_(n),r=e.reduce((u,f)=>u+f.byteLength,0);let i,o,s,a=0,l=-1;const c=Math.min(t||Number.POSITIVE_INFINITY,r);for(const u=e.length;++l<u;){if(i=e[l],o=i.subarray(0,Math.min(i.length,c-a)),c<=a+o.length){o.length<i.length?e[l]=i.subarray(o.length):o.length===i.length&&l++,s?tu(s,o,a):s=o;break}tu(s||(s=new Uint8Array(c)),o,a),a+=o.length}return[s||new Uint8Array(0),e.slice(l),r-(s?s.byteLength:0)]}function fe(n,t){let e=Bh(t)?t.value:t;return e instanceof n?n===Uint8Array?new n(e.buffer,e.byteOffset,e.byteLength):e:e?(typeof e=="string"&&(e=ec(e)),e instanceof ArrayBuffer?new n(e):e instanceof ic?new n(e):V_(e)?fe(n,e.bytes()):ArrayBuffer.isView(e)?e.byteLength<=0?new n(0):new n(e.buffer,e.byteOffset,e.byteLength/n.BYTES_PER_ELEMENT):n.from(e)):new n(0)}const oo=n=>fe(Int32Array,n),eu=n=>fe(BigInt64Array,n),Gt=n=>fe(Uint8Array,n),El=n=>(n.next(),n);function*W_(n,t){const e=function*(i){yield i},r=typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof ic?e(t):Ca(t)?t:e(t);return yield*El(function*(i){let o=null;do o=i.next(yield fe(n,o));while(!o.done)}(r[Symbol.iterator]())),new n}const H_=n=>W_(Uint8Array,n);function Fh(n,t){return $n(this,arguments,function*(){if(qo(t))return yield xt(yield xt(yield*js(Bi(Fh(n,yield xt(t))))));const r=function(s){return $n(this,arguments,function*(){yield yield xt(yield xt(s))})},i=function(s){return $n(this,arguments,function*(){yield xt(yield*js(Bi(El(function*(a){let l=null;do l=a.next(yield l==null?void 0:l.value);while(!l.done)}(s[Symbol.iterator]())))))})},o=typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof ic?r(t):Ca(t)?i(t):nc(t)?t:r(t);return yield xt(yield*js(Bi(El(function(s){return $n(this,arguments,function*(){let a=null;do a=yield xt(s.next(yield yield xt(fe(n,a))));while(!a.done)})}(o[Symbol.asyncIterator]()))))),yield xt(new n)})}const x_=n=>Fh(Uint8Array,n);function q_(n,t){let e=0;const r=n.length;if(r!==t.length)return!1;if(r>0)do if(n[e]!==t[e])return!1;while(++e<r);return!0}const wn={fromIterable(n){return ws(Y_(n))},fromAsyncIterable(n){return ws(K_(n))},fromDOMStream(n){return ws(J_(n))},fromNodeStream(n){return ws(G_(n))},toDOMStream(n,t){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(n,t){throw new Error('"toNodeStream" not available in this environment')}},ws=n=>(n.next(),n);function*Y_(n){let t,e=!1,r=[],i,o,s,a=0;function l(){return o==="peek"?rr(r,s)[0]:([i,r,a]=rr(r,s),i)}({cmd:o,size:s}=(yield null)||{cmd:"read",size:0});const c=H_(n)[Symbol.iterator]();try{do if({done:t,value:i}=Number.isNaN(s-a)?c.next():c.next(s-a),!t&&i.byteLength>0&&(r.push(i),a+=i.byteLength),t||s<=a)do({cmd:o,size:s}=yield l());while(s<a);while(!t)}catch(u){(e=!0)&&typeof c.throw=="function"&&c.throw(u)}finally{e===!1&&typeof c.return=="function"&&c.return(null)}return null}function K_(n){return $n(this,arguments,function*(){let e,r=!1,i=[],o,s,a,l=0;function c(){return s==="peek"?rr(i,a)[0]:([o,i,l]=rr(i,a),o)}({cmd:s,size:a}=(yield yield xt(null))||{cmd:"read",size:0});const u=x_(n)[Symbol.asyncIterator]();try{do if({done:e,value:o}=Number.isNaN(a-l)?yield xt(u.next()):yield xt(u.next(a-l)),!e&&o.byteLength>0&&(i.push(o),l+=o.byteLength),e||a<=l)do({cmd:s,size:a}=yield yield xt(c()));while(a<l);while(!e)}catch(f){(r=!0)&&typeof u.throw=="function"&&(yield xt(u.throw(f)))}finally{r===!1&&typeof u.return=="function"&&(yield xt(u.return(new Uint8Array(0))))}return yield xt(null)})}function J_(n){return $n(this,arguments,function*(){let e=!1,r=!1,i=[],o,s,a,l=0;function c(){return s==="peek"?rr(i,a)[0]:([o,i,l]=rr(i,a),o)}({cmd:s,size:a}=(yield yield xt(null))||{cmd:"read",size:0});const u=new Q_(n);try{do if({done:e,value:o}=Number.isNaN(a-l)?yield xt(u.read()):yield xt(u.read(a-l)),!e&&o.byteLength>0&&(i.push(Gt(o)),l+=o.byteLength),e||a<=l)do({cmd:s,size:a}=yield yield xt(c()));while(a<l);while(!e)}catch(f){(r=!0)&&(yield xt(u.cancel(f)))}finally{r===!1?yield xt(u.cancel()):n.locked&&u.releaseLock()}return yield xt(null)})}class Q_{constructor(t){this.source=t,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch(()=>{})}get closed(){return this.reader?this.reader.closed.catch(()=>{}):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(t){return Mt(this,void 0,void 0,function*(){const{reader:e,source:r}=this;e&&(yield e.cancel(t).catch(()=>{})),r&&r.locked&&this.releaseLock()})}read(t){return Mt(this,void 0,void 0,function*(){if(t===0)return{done:this.reader==null,value:new Uint8Array(0)};const e=yield this.reader.read();return!e.done&&(e.value=Gt(e)),e})}}const Za=(n,t)=>{const e=i=>r([t,i]);let r;return[t,e,new Promise(i=>(r=i)&&n.once(t,e))]};function G_(n){return $n(this,arguments,function*(){const e=[];let r="error",i=!1,o=null,s,a,l=0,c=[],u;function f(){return s==="peek"?rr(c,a)[0]:([u,c,l]=rr(c,a),u)}if({cmd:s,size:a}=(yield yield xt(null))||{cmd:"read",size:0},n.isTTY)return yield yield xt(new Uint8Array(0)),yield xt(null);try{e[0]=Za(n,"end"),e[1]=Za(n,"error");do{if(e[2]=Za(n,"readable"),[r,o]=yield xt(Promise.race(e.map(d=>d[2]))),r==="error")break;if((i=r==="end")||(Number.isFinite(a-l)?(u=Gt(n.read(a-l)),u.byteLength<a-l&&(u=Gt(n.read()))):u=Gt(n.read()),u.byteLength>0&&(c.push(u),l+=u.byteLength)),i||a<=l)do({cmd:s,size:a}=yield yield xt(f()));while(a<l)}while(!i)}finally{yield xt(h(e,r==="error"?o:null))}return yield xt(null);function h(d,g){return u=c=null,new Promise((m,p)=>{for(const[v,b]of d)n.off(v,b);try{const v=n.destroy;v&&v.call(n,g),g=void 0}catch(v){g=v||g}finally{g!=null?p(g):m()}})}})}var Fe;(function(n){n[n.V1=0]="V1",n[n.V2=1]="V2",n[n.V3=2]="V3",n[n.V4=3]="V4",n[n.V5=4]="V5"})(Fe||(Fe={}));var dn;(function(n){n[n.Sparse=0]="Sparse",n[n.Dense=1]="Dense"})(dn||(dn={}));var Je;(function(n){n[n.HALF=0]="HALF",n[n.SINGLE=1]="SINGLE",n[n.DOUBLE=2]="DOUBLE"})(Je||(Je={}));var Tn;(function(n){n[n.DAY=0]="DAY",n[n.MILLISECOND=1]="MILLISECOND"})(Tn||(Tn={}));var yt;(function(n){n[n.SECOND=0]="SECOND",n[n.MILLISECOND=1]="MILLISECOND",n[n.MICROSECOND=2]="MICROSECOND",n[n.NANOSECOND=3]="NANOSECOND"})(yt||(yt={}));var ir;(function(n){n[n.YEAR_MONTH=0]="YEAR_MONTH",n[n.DAY_TIME=1]="DAY_TIME",n[n.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"})(ir||(ir={}));const $a=2,Gn=4,dr=4,ae=4,Mr=new Int32Array(2),nu=new Float32Array(Mr.buffer),ru=new Float64Array(Mr.buffer),Ss=new Uint16Array(new Uint8Array([1,0]).buffer)[0]===1;var Al;(function(n){n[n.UTF8_BYTES=1]="UTF8_BYTES",n[n.UTF16_STRING=2]="UTF16_STRING"})(Al||(Al={}));let Li=class jh{constructor(t){this.bytes_=t,this.position_=0,this.text_decoder_=new TextDecoder}static allocate(t){return new jh(new Uint8Array(t))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return BigInt.asIntN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readUint64(t){return BigInt.asUintN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readFloat32(t){return Mr[0]=this.readInt32(t),nu[0]}readFloat64(t){return Mr[Ss?0:1]=this.readInt32(t),Mr[Ss?1:0]=this.readInt32(t+4),ru[0]}writeInt8(t,e){this.bytes_[t]=e}writeUint8(t,e){this.bytes_[t]=e}writeInt16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeUint16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeInt32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeUint32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeInt64(t,e){this.writeInt32(t,Number(BigInt.asIntN(32,e))),this.writeInt32(t+4,Number(BigInt.asIntN(32,e>>BigInt(32))))}writeUint64(t,e){this.writeUint32(t,Number(BigInt.asUintN(32,e))),this.writeUint32(t+4,Number(BigInt.asUintN(32,e>>BigInt(32))))}writeFloat32(t,e){nu[0]=e,this.writeInt32(t,Mr[0])}writeFloat64(t,e){ru[0]=e,this.writeInt32(t,Mr[Ss?0:1]),this.writeInt32(t+4,Mr[Ss?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+Gn+dr)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let e=0;e<dr;e++)t+=String.fromCharCode(this.readInt8(this.position_+Gn+e));return t}__offset(t,e){const r=t-this.readInt32(t);return e<this.readInt16(r)?this.readInt16(r+e):0}__union(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t}__string(t,e){t+=this.readInt32(t);const r=this.readInt32(t);t+=Gn;const i=this.bytes_.subarray(t,t+r);return e===Al.UTF8_BYTES?i:this.text_decoder_.decode(i)}__union_with_string(t,e){return typeof t=="string"?this.__string(e):this.__union(t,e)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+Gn}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(t.length!=dr)throw new Error("FlatBuffers: file identifier must be length "+dr);for(let e=0;e<dr;e++)if(t.charCodeAt(e)!=this.readInt8(this.position()+Gn+e))return!1;return!0}createScalarList(t,e){const r=[];for(let i=0;i<e;++i){const o=t(i);o!==null&&r.push(o)}return r}createObjList(t,e){const r=[];for(let i=0;i<e;++i){const o=t(i);o!==null&&r.push(o.unpack())}return r}},Lh=class Rh{constructor(t){this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,this.text_encoder=new TextEncoder;let e;t?e=t:e=1024,this.bb=Li.allocate(e),this.space=e}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(t,e){t>this.minalign&&(this.minalign=t);const r=~(this.bb.capacity()-this.space+e)+1&t-1;for(;this.space<r+t+e;){const i=this.bb.capacity();this.bb=Rh.growByteBuffer(this.bb),this.space+=this.bb.capacity()-i}this.pad(r)}pad(t){for(let e=0;e<t;e++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,e,r){(this.force_defaults||e!=r)&&(this.addInt8(e),this.slot(t))}addFieldInt16(t,e,r){(this.force_defaults||e!=r)&&(this.addInt16(e),this.slot(t))}addFieldInt32(t,e,r){(this.force_defaults||e!=r)&&(this.addInt32(e),this.slot(t))}addFieldInt64(t,e,r){(this.force_defaults||e!==r)&&(this.addInt64(e),this.slot(t))}addFieldFloat32(t,e,r){(this.force_defaults||e!=r)&&(this.addFloat32(e),this.slot(t))}addFieldFloat64(t,e,r){(this.force_defaults||e!=r)&&(this.addFloat64(e),this.slot(t))}addFieldOffset(t,e,r){(this.force_defaults||e!=r)&&(this.addOffset(e),this.slot(t))}addFieldStruct(t,e,r){e!=r&&(this.nested(e),this.slot(t))}nested(t){if(t!=this.offset())throw new TypeError("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new TypeError("FlatBuffers: object serialization must not be nested.")}slot(t){this.vtable!==null&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){const e=t.capacity();if(e&3221225472)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const r=e<<1,i=Li.allocate(r);return i.setPosition(r-e),i.bytes().set(t.bytes(),r-e),i}addOffset(t){this.prep(Gn,0),this.writeInt32(this.offset()-t+Gn)}startObject(t){this.notNested(),this.vtable==null&&(this.vtable=[]),this.vtable_in_use=t;for(let e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(this.vtable==null||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const t=this.offset();let e=this.vtable_in_use-1;for(;e>=0&&this.vtable[e]==0;e--);const r=e+1;for(;e>=0;e--)this.addInt16(this.vtable[e]!=0?t-this.vtable[e]:0);const i=2;this.addInt16(t-this.object_start);const o=(r+i)*$a;this.addInt16(o);let s=0;const a=this.space;t:for(e=0;e<this.vtables.length;e++){const l=this.bb.capacity()-this.vtables[e];if(o==this.bb.readInt16(l)){for(let c=$a;c<o;c+=$a)if(this.bb.readInt16(a+c)!=this.bb.readInt16(l+c))continue t;s=this.vtables[e];break}}return s?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,s-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,e,r){const i=r?ae:0;if(e){const o=e;if(this.prep(this.minalign,Gn+dr+i),o.length!=dr)throw new TypeError("FlatBuffers: file identifier must be length "+dr);for(let s=dr-1;s>=0;s--)this.writeInt8(o.charCodeAt(s))}this.prep(this.minalign,Gn+i),this.addOffset(t),i&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,e){this.finish(t,e,!0)}requiredField(t,e){const r=this.bb.capacity()-t,i=r-this.bb.readInt32(r);if(!(e<this.bb.readInt16(i)&&this.bb.readInt16(i+e)!=0))throw new TypeError("FlatBuffers: field "+e+" must be set")}startVector(t,e,r){this.notNested(),this.vector_num_elems=e,this.prep(Gn,t*e),this.prep(r,t*e)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);const e=this.createString(t);return this.string_maps.set(t,e),e}createString(t){if(t==null)return 0;let e;return t instanceof Uint8Array?e=t:e=this.text_encoder.encode(t),this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length),this.bb.bytes().set(e,this.space),this.endVector()}createByteVector(t){return t==null?0:(this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length),this.bb.bytes().set(t,this.space),this.endVector())}createObjectOffset(t){return t===null?0:typeof t=="string"?this.createString(t):t.pack(this)}createObjectOffsetList(t){const e=[];for(let r=0;r<t.length;++r){const i=t[r];if(i!==null)e.push(this.createObjectOffset(i));else throw new TypeError("FlatBuffers: Argument for createObjectOffsetList cannot contain null.")}return e}createStructOffsetList(t,e){return e(this,t.length),this.createObjectOffsetList(t.slice().reverse()),this.endVector()}};var Zs;(function(n){n[n.BUFFER=0]="BUFFER"})(Zs||(Zs={}));var $s;(function(n){n[n.LZ4_FRAME=0]="LZ4_FRAME",n[n.ZSTD=1]="ZSTD"})($s||($s={}));class Nr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBodyCompression(t,e){return(e||new Nr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBodyCompression(t,e){return t.setPosition(t.position()+ae),(e||new Nr).__init(t.readInt32(t.position())+t.position(),t)}codec(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):$s.LZ4_FRAME}method(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt8(this.bb_pos+t):Zs.BUFFER}static startBodyCompression(t){t.startObject(2)}static addCodec(t,e){t.addFieldInt8(0,e,$s.LZ4_FRAME)}static addMethod(t,e){t.addFieldInt8(1,e,Zs.BUFFER)}static endBodyCompression(t){return t.endObject()}static createBodyCompression(t,e,r){return Nr.startBodyCompression(t),Nr.addCodec(t,e),Nr.addMethod(t,r),Nr.endBodyCompression(t)}}class Uh{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(t,e,r){return t.prep(8,16),t.writeInt64(BigInt(r??0)),t.writeInt64(BigInt(e??0)),t.offset()}}let Vh=class{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(t,e,r){return t.prep(8,16),t.writeInt64(BigInt(r??0)),t.writeInt64(BigInt(e??0)),t.offset()}},ur=class Tl{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsRecordBatch(t,e){return(e||new Tl).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsRecordBatch(t,e){return t.setPosition(t.position()+ae),(e||new Tl).__init(t.readInt32(t.position())+t.position(),t)}length(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}nodes(t,e){const r=this.bb.__offset(this.bb_pos,6);return r?(e||new Vh).__init(this.bb.__vector(this.bb_pos+r)+t*16,this.bb):null}nodesLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,e){const r=this.bb.__offset(this.bb_pos,8);return r?(e||new Uh).__init(this.bb.__vector(this.bb_pos+r)+t*16,this.bb):null}buffersLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}compression(t){const e=this.bb.__offset(this.bb_pos,10);return e?(t||new Nr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}static startRecordBatch(t){t.startObject(4)}static addLength(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addNodes(t,e){t.addFieldOffset(1,e,0)}static startNodesVector(t,e){t.startVector(16,e,8)}static addBuffers(t,e){t.addFieldOffset(2,e,0)}static startBuffersVector(t,e){t.startVector(16,e,8)}static addCompression(t,e){t.addFieldOffset(3,e,0)}static endRecordBatch(t){return t.endObject()}},_i=class Bl{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryBatch(t,e){return(e||new Bl).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryBatch(t,e){return t.setPosition(t.position()+ae),(e||new Bl).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}data(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new ur).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isDelta(){const t=this.bb.__offset(this.bb_pos,8);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startDictionaryBatch(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addData(t,e){t.addFieldOffset(1,e,0)}static addIsDelta(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryBatch(t){return t.endObject()}};var Ri;(function(n){n[n.Little=0]="Little",n[n.Big=1]="Big"})(Ri||(Ri={}));var ta;(function(n){n[n.DenseArray=0]="DenseArray"})(ta||(ta={}));class gn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInt(t,e){return(e||new gn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInt(t,e){return t.setPosition(t.position()+ae),(e||new gn).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){const t=this.bb.__offset(this.bb_pos,6);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startInt(t){t.startObject(2)}static addBitWidth(t,e){t.addFieldInt32(0,e,0)}static addIsSigned(t,e){t.addFieldInt8(1,+e,0)}static endInt(t){return t.endObject()}static createInt(t,e,r){return gn.startInt(t),gn.addBitWidth(t,e),gn.addIsSigned(t,r),gn.endInt(t)}}class hr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryEncoding(t,e){return(e||new hr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryEncoding(t,e){return t.setPosition(t.position()+ae),(e||new hr).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}indexType(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new gn).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isOrdered(){const t=this.bb.__offset(this.bb_pos,8);return t?!!this.bb.readInt8(this.bb_pos+t):!1}dictionaryKind(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt16(this.bb_pos+t):ta.DenseArray}static startDictionaryEncoding(t){t.startObject(4)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addIndexType(t,e){t.addFieldOffset(1,e,0)}static addIsOrdered(t,e){t.addFieldInt8(2,+e,0)}static addDictionaryKind(t,e){t.addFieldInt16(3,e,ta.DenseArray)}static endDictionaryEncoding(t){return t.endObject()}}class Le{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKeyValue(t,e){return(e||new Le).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKeyValue(t,e){return t.setPosition(t.position()+ae),(e||new Le).__init(t.readInt32(t.position())+t.position(),t)}key(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}value(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,e){t.addFieldOffset(0,e,0)}static addValue(t,e){t.addFieldOffset(1,e,0)}static endKeyValue(t){return t.endObject()}static createKeyValue(t,e,r){return Le.startKeyValue(t),Le.addKey(t,e),Le.addValue(t,r),Le.endKeyValue(t)}}let iu=class po{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBinary(t,e){return(e||new po).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBinary(t,e){return t.setPosition(t.position()+ae),(e||new po).__init(t.readInt32(t.position())+t.position(),t)}static startBinary(t){t.startObject(0)}static endBinary(t){return t.endObject()}static createBinary(t){return po.startBinary(t),po.endBinary(t)}},ou=class go{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBool(t,e){return(e||new go).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBool(t,e){return t.setPosition(t.position()+ae),(e||new go).__init(t.readInt32(t.position())+t.position(),t)}static startBool(t){t.startObject(0)}static endBool(t){return t.endObject()}static createBool(t){return go.startBool(t),go.endBool(t)}},Rs=class bi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDate(t,e){return(e||new bi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDate(t,e){return t.setPosition(t.position()+ae),(e||new bi).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Tn.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,Tn.MILLISECOND)}static endDate(t){return t.endObject()}static createDate(t,e){return bi.startDate(t),bi.addUnit(t,e),bi.endDate(t)}},yi=class Pr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDecimal(t,e){return(e||new Pr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDecimal(t,e){return t.setPosition(t.position()+ae),(e||new Pr).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}bitWidth(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):128}static startDecimal(t){t.startObject(3)}static addPrecision(t,e){t.addFieldInt32(0,e,0)}static addScale(t,e){t.addFieldInt32(1,e,0)}static addBitWidth(t,e){t.addFieldInt32(2,e,128)}static endDecimal(t){return t.endObject()}static createDecimal(t,e,r,i){return Pr.startDecimal(t),Pr.addPrecision(t,e),Pr.addScale(t,r),Pr.addBitWidth(t,i),Pr.endDecimal(t)}},Us=class vi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDuration(t,e){return(e||new vi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDuration(t,e){return t.setPosition(t.position()+ae),(e||new vi).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.MILLISECOND}static startDuration(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,yt.MILLISECOND)}static endDuration(t){return t.endObject()}static createDuration(t,e){return vi.startDuration(t),vi.addUnit(t,e),vi.endDuration(t)}},Vs=class wi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeBinary(t,e){return(e||new wi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeBinary(t,e){return t.setPosition(t.position()+ae),(e||new wi).__init(t.readInt32(t.position())+t.position(),t)}byteWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeBinary(t){return t.endObject()}static createFixedSizeBinary(t,e){return wi.startFixedSizeBinary(t),wi.addByteWidth(t,e),wi.endFixedSizeBinary(t)}},zs=class Si{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeList(t,e){return(e||new Si).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeList(t,e){return t.setPosition(t.position()+ae),(e||new Si).__init(t.readInt32(t.position())+t.position(),t)}listSize(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeList(t){return t.endObject()}static createFixedSizeList(t,e){return Si.startFixedSizeList(t),Si.addListSize(t,e),Si.endFixedSizeList(t)}};class Xn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFloatingPoint(t,e){return(e||new Xn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFloatingPoint(t,e){return t.setPosition(t.position()+ae),(e||new Xn).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Je.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(t,e){t.addFieldInt16(0,e,Je.HALF)}static endFloatingPoint(t){return t.endObject()}static createFloatingPoint(t,e){return Xn.startFloatingPoint(t),Xn.addPrecision(t,e),Xn.endFloatingPoint(t)}}class Zn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInterval(t,e){return(e||new Zn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInterval(t,e){return t.setPosition(t.position()+ae),(e||new Zn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):ir.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,ir.YEAR_MONTH)}static endInterval(t){return t.endObject()}static createInterval(t,e){return Zn.startInterval(t),Zn.addUnit(t,e),Zn.endInterval(t)}}let su=class _o{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeBinary(t,e){return(e||new _o).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsLargeBinary(t,e){return t.setPosition(t.position()+ae),(e||new _o).__init(t.readInt32(t.position())+t.position(),t)}static startLargeBinary(t){t.startObject(0)}static endLargeBinary(t){return t.endObject()}static createLargeBinary(t){return _o.startLargeBinary(t),_o.endLargeBinary(t)}},au=class bo{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeUtf8(t,e){return(e||new bo).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsLargeUtf8(t,e){return t.setPosition(t.position()+ae),(e||new bo).__init(t.readInt32(t.position())+t.position(),t)}static startLargeUtf8(t){t.startObject(0)}static endLargeUtf8(t){return t.endObject()}static createLargeUtf8(t){return bo.startLargeUtf8(t),bo.endLargeUtf8(t)}},lu=class yo{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsList(t,e){return(e||new yo).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsList(t,e){return t.setPosition(t.position()+ae),(e||new yo).__init(t.readInt32(t.position())+t.position(),t)}static startList(t){t.startObject(0)}static endList(t){return t.endObject()}static createList(t){return yo.startList(t),yo.endList(t)}},Ws=class Ii{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMap(t,e){return(e||new Ii).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMap(t,e){return t.setPosition(t.position()+ae),(e||new Ii).__init(t.readInt32(t.position())+t.position(),t)}keysSorted(){const t=this.bb.__offset(this.bb_pos,4);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startMap(t){t.startObject(1)}static addKeysSorted(t,e){t.addFieldInt8(0,+e,0)}static endMap(t){return t.endObject()}static createMap(t,e){return Ii.startMap(t),Ii.addKeysSorted(t,e),Ii.endMap(t)}},cu=class vo{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNull(t,e){return(e||new vo).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNull(t,e){return t.setPosition(t.position()+ae),(e||new vo).__init(t.readInt32(t.position())+t.position(),t)}static startNull(t){t.startObject(0)}static endNull(t){return t.endObject()}static createNull(t){return vo.startNull(t),vo.endNull(t)}};class ri{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsStruct_(t,e){return(e||new ri).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStruct_(t,e){return t.setPosition(t.position()+ae),(e||new ri).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){return t.endObject()}static createStruct_(t){return ri.startStruct_(t),ri.endStruct_(t)}}class Sn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTime(t,e){return(e||new Sn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTime(t,e){return t.setPosition(t.position()+ae),(e||new Sn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.MILLISECOND}bitWidth(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,yt.MILLISECOND)}static addBitWidth(t,e){t.addFieldInt32(1,e,32)}static endTime(t){return t.endObject()}static createTime(t,e,r){return Sn.startTime(t),Sn.addUnit(t,e),Sn.addBitWidth(t,r),Sn.endTime(t)}}class In{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTimestamp(t,e){return(e||new In).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTimestamp(t,e){return t.setPosition(t.position()+ae),(e||new In).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.SECOND}timezone(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,yt.SECOND)}static addTimezone(t,e){t.addFieldOffset(1,e,0)}static endTimestamp(t){return t.endObject()}static createTimestamp(t,e,r){return In.startTimestamp(t),In.addUnit(t,e),In.addTimezone(t,r),In.endTimestamp(t)}}class un{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUnion(t,e){return(e||new un).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUnion(t,e){return t.setPosition(t.position()+ae),(e||new un).__init(t.readInt32(t.position())+t.position(),t)}mode(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):dn.Sparse}typeIds(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+t*4):0}typeIdsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){const t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(t,e){t.addFieldInt16(0,e,dn.Sparse)}static addTypeIds(t,e){t.addFieldOffset(1,e,0)}static createTypeIdsVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addInt32(e[r]);return t.endVector()}static startTypeIdsVector(t,e){t.startVector(4,e,4)}static endUnion(t){return t.endObject()}static createUnion(t,e,r){return un.startUnion(t),un.addMode(t,e),un.addTypeIds(t,r),un.endUnion(t)}}let uu=class wo{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUtf8(t,e){return(e||new wo).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUtf8(t,e){return t.setPosition(t.position()+ae),(e||new wo).__init(t.readInt32(t.position())+t.position(),t)}static startUtf8(t){t.startObject(0)}static endUtf8(t){return t.endObject()}static createUtf8(t){return wo.startUtf8(t),wo.endUtf8(t)}};var pe;(function(n){n[n.NONE=0]="NONE",n[n.Null=1]="Null",n[n.Int=2]="Int",n[n.FloatingPoint=3]="FloatingPoint",n[n.Binary=4]="Binary",n[n.Utf8=5]="Utf8",n[n.Bool=6]="Bool",n[n.Decimal=7]="Decimal",n[n.Date=8]="Date",n[n.Time=9]="Time",n[n.Timestamp=10]="Timestamp",n[n.Interval=11]="Interval",n[n.List=12]="List",n[n.Struct_=13]="Struct_",n[n.Union=14]="Union",n[n.FixedSizeBinary=15]="FixedSizeBinary",n[n.FixedSizeList=16]="FixedSizeList",n[n.Map=17]="Map",n[n.Duration=18]="Duration",n[n.LargeBinary=19]="LargeBinary",n[n.LargeUtf8=20]="LargeUtf8",n[n.LargeList=21]="LargeList",n[n.RunEndEncoded=22]="RunEndEncoded"})(pe||(pe={}));let vn=class Hs{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsField(t,e){return(e||new Hs).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsField(t,e){return t.setPosition(t.position()+ae),(e||new Hs).__init(t.readInt32(t.position())+t.position(),t)}name(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}nullable(){const t=this.bb.__offset(this.bb_pos,6);return t?!!this.bb.readInt8(this.bb_pos+t):!1}typeType(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):pe.NONE}type(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__union(t,this.bb_pos+e):null}dictionary(t){const e=this.bb.__offset(this.bb_pos,12);return e?(t||new hr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}children(t,e){const r=this.bb.__offset(this.bb_pos,14);return r?(e||new Hs).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}childrenLength(){const t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const r=this.bb.__offset(this.bb_pos,16);return r?(e||new Le).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,e){t.addFieldOffset(0,e,0)}static addNullable(t,e){t.addFieldInt8(1,+e,0)}static addTypeType(t,e){t.addFieldInt8(2,e,pe.NONE)}static addType(t,e){t.addFieldOffset(3,e,0)}static addDictionary(t,e){t.addFieldOffset(4,e,0)}static addChildren(t,e){t.addFieldOffset(5,e,0)}static createChildrenVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startChildrenVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(6,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endField(t){return t.endObject()}},Jn=class cr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSchema(t,e){return(e||new cr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSchema(t,e){return t.setPosition(t.position()+ae),(e||new cr).__init(t.readInt32(t.position())+t.position(),t)}endianness(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ri.Little}fields(t,e){const r=this.bb.__offset(this.bb_pos,6);return r?(e||new vn).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}fieldsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const r=this.bb.__offset(this.bb_pos,8);return r?(e||new Le).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}features(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+t*8):BigInt(0)}featuresLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(4)}static addEndianness(t,e){t.addFieldInt16(0,e,Ri.Little)}static addFields(t,e){t.addFieldOffset(1,e,0)}static createFieldsVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startFieldsVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(2,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static addFeatures(t,e){t.addFieldOffset(3,e,0)}static createFeaturesVector(t,e){t.startVector(8,e.length,8);for(let r=e.length-1;r>=0;r--)t.addInt64(e[r]);return t.endVector()}static startFeaturesVector(t,e){t.startVector(8,e,8)}static endSchema(t){return t.endObject()}static finishSchemaBuffer(t,e){t.finish(e)}static finishSizePrefixedSchemaBuffer(t,e){t.finish(e,void 0,!0)}static createSchema(t,e,r,i,o){return cr.startSchema(t),cr.addEndianness(t,e),cr.addFields(t,r),cr.addCustomMetadata(t,i),cr.addFeatures(t,o),cr.endSchema(t)}};var oe;(function(n){n[n.NONE=0]="NONE",n[n.Schema=1]="Schema",n[n.DictionaryBatch=2]="DictionaryBatch",n[n.RecordBatch=3]="RecordBatch",n[n.Tensor=4]="Tensor",n[n.SparseTensor=5]="SparseTensor"})(oe||(oe={}));const X_=void 0;function Yo(n){if(n===null)return"null";if(n===X_)return"undefined";switch(typeof n){case"number":return`${n}`;case"bigint":return`${n}`;case"string":return`"${n}"`}return typeof n[Symbol.toPrimitive]=="function"?n[Symbol.toPrimitive]("string"):ArrayBuffer.isView(n)?n instanceof BigInt64Array||n instanceof BigUint64Array?`[${[...n].map(t=>Yo(t))}]`:`[${n}]`:ArrayBuffer.isView(n)?`[${n}]`:JSON.stringify(n,(t,e)=>typeof e=="bigint"?`${e}`:e)}function Se(n){if(typeof n=="bigint"&&(n<Number.MIN_SAFE_INTEGER||n>Number.MAX_SAFE_INTEGER))throw new TypeError(`${n} is not safe to convert to a number.`);return Number(n)}function zh(n,t){return Se(n/t)+Se(n%t)/Se(t)}const Z_=Symbol.for("isArrowBigNum");function Wn(n,...t){return t.length===0?Object.setPrototypeOf(fe(this.TypedArray,n),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(n,...t),this.constructor.prototype)}Wn.prototype[Z_]=!0;Wn.prototype.toJSON=function(){return`"${Jo(this)}"`};Wn.prototype.valueOf=function(n){return Wh(this,n)};Wn.prototype.toString=function(){return Jo(this)};Wn.prototype[Symbol.toPrimitive]=function(n="default"){switch(n){case"number":return Wh(this);case"string":return Jo(this);case"default":return eb(this)}return Jo(this)};function Pi(...n){return Wn.apply(this,n)}function Mi(...n){return Wn.apply(this,n)}function Ko(...n){return Wn.apply(this,n)}Object.setPrototypeOf(Pi.prototype,Object.create(Int32Array.prototype));Object.setPrototypeOf(Mi.prototype,Object.create(Uint32Array.prototype));Object.setPrototypeOf(Ko.prototype,Object.create(Uint32Array.prototype));Object.assign(Pi.prototype,Wn.prototype,{constructor:Pi,signed:!0,TypedArray:Int32Array,BigIntArray:BigInt64Array});Object.assign(Mi.prototype,Wn.prototype,{constructor:Mi,signed:!1,TypedArray:Uint32Array,BigIntArray:BigUint64Array});Object.assign(Ko.prototype,Wn.prototype,{constructor:Ko,signed:!0,TypedArray:Uint32Array,BigIntArray:BigUint64Array});const $_=BigInt(4294967296)*BigInt(4294967296),tb=$_-BigInt(1);function Wh(n,t){const{buffer:e,byteOffset:r,byteLength:i,signed:o}=n,s=new BigUint64Array(e,r,i/8),a=o&&s.at(-1)&BigInt(1)<<BigInt(63);let l=BigInt(0),c=0;if(a){for(const u of s)l|=(u^tb)*(BigInt(1)<<BigInt(64*c++));l*=BigInt(-1),l-=BigInt(1)}else for(const u of s)l|=u*(BigInt(1)<<BigInt(64*c++));if(typeof t=="number"){const u=BigInt(Math.pow(10,t)),f=l/u,h=l%u;return Se(f)+Se(h)/Se(u)}return Se(l)}function Jo(n){if(n.byteLength===8)return`${new n.BigIntArray(n.buffer,n.byteOffset,1)[0]}`;if(!n.signed)return tl(n);let t=new Uint16Array(n.buffer,n.byteOffset,n.byteLength/2);if(new Int16Array([t.at(-1)])[0]>=0)return tl(n);t=t.slice();let r=1;for(let o=0;o<t.length;o++){const s=t[o],a=~s+r;t[o]=a,r&=s===0?1:0}return`-${tl(t)}`}function eb(n){return n.byteLength===8?new n.BigIntArray(n.buffer,n.byteOffset,1)[0]:Jo(n)}function tl(n){let t="";const e=new Uint32Array(2);let r=new Uint16Array(n.buffer,n.byteOffset,n.byteLength/2);const i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer);let o=-1;const s=r.length-1;do{for(e[0]=r[o=0];o<s;)r[o++]=e[1]=e[0]/10,e[0]=(e[0]-e[1]*10<<16)+r[o];r[o]=e[1]=e[0]/10,e[0]=e[0]-e[1]*10,t=`${e[0]}${t}`}while(i[0]||i[1]||i[2]||i[3]);return t??"0"}class oc{static new(t,e){switch(e){case!0:return new Pi(t);case!1:return new Mi(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case BigInt64Array:return new Pi(t)}return t.byteLength===16?new Ko(t):new Mi(t)}static signed(t){return new Pi(t)}static unsigned(t){return new Mi(t)}static decimal(t){return new Ko(t)}constructor(t,e){return oc.new(t,e)}}var Hh,xh,qh,Yh,Kh,Jh,Qh,Gh,Xh,Zh,$h,tm,em,nm,rm,im,om,sm,am,lm,cm,um;class ft{static isNull(t){return(t==null?void 0:t.typeId)===A.Null}static isInt(t){return(t==null?void 0:t.typeId)===A.Int}static isFloat(t){return(t==null?void 0:t.typeId)===A.Float}static isBinary(t){return(t==null?void 0:t.typeId)===A.Binary}static isLargeBinary(t){return(t==null?void 0:t.typeId)===A.LargeBinary}static isUtf8(t){return(t==null?void 0:t.typeId)===A.Utf8}static isLargeUtf8(t){return(t==null?void 0:t.typeId)===A.LargeUtf8}static isBool(t){return(t==null?void 0:t.typeId)===A.Bool}static isDecimal(t){return(t==null?void 0:t.typeId)===A.Decimal}static isDate(t){return(t==null?void 0:t.typeId)===A.Date}static isTime(t){return(t==null?void 0:t.typeId)===A.Time}static isTimestamp(t){return(t==null?void 0:t.typeId)===A.Timestamp}static isInterval(t){return(t==null?void 0:t.typeId)===A.Interval}static isDuration(t){return(t==null?void 0:t.typeId)===A.Duration}static isList(t){return(t==null?void 0:t.typeId)===A.List}static isStruct(t){return(t==null?void 0:t.typeId)===A.Struct}static isUnion(t){return(t==null?void 0:t.typeId)===A.Union}static isFixedSizeBinary(t){return(t==null?void 0:t.typeId)===A.FixedSizeBinary}static isFixedSizeList(t){return(t==null?void 0:t.typeId)===A.FixedSizeList}static isMap(t){return(t==null?void 0:t.typeId)===A.Map}static isDictionary(t){return(t==null?void 0:t.typeId)===A.Dictionary}static isDenseUnion(t){return ft.isUnion(t)&&t.mode===dn.Dense}static isSparseUnion(t){return ft.isUnion(t)&&t.mode===dn.Sparse}constructor(t){this.typeId=t}}Hh=Symbol.toStringTag;ft[Hh]=(n=>(n.children=null,n.ArrayType=Array,n.OffsetArrayType=Int32Array,n[Symbol.toStringTag]="DataType"))(ft.prototype);class Lr extends ft{constructor(){super(A.Null)}toString(){return"Null"}}xh=Symbol.toStringTag;Lr[xh]=(n=>n[Symbol.toStringTag]="Null")(Lr.prototype);class oi extends ft{constructor(t,e){super(A.Int),this.isSigned=t,this.bitWidth=e}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?BigInt64Array:BigUint64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}qh=Symbol.toStringTag;oi[qh]=(n=>(n.isSigned=null,n.bitWidth=null,n[Symbol.toStringTag]="Int"))(oi.prototype);class Qo extends oi{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(Qo.prototype,"ArrayType",{value:Int32Array});class ea extends ft{constructor(t){super(A.Float),this.precision=t}get ArrayType(){switch(this.precision){case Je.HALF:return Uint16Array;case Je.SINGLE:return Float32Array;case Je.DOUBLE:return Float64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`Float${this.precision<<5||16}`}}Yh=Symbol.toStringTag;ea[Yh]=(n=>(n.precision=null,n[Symbol.toStringTag]="Float"))(ea.prototype);class na extends ft{constructor(){super(A.Binary)}toString(){return"Binary"}}Kh=Symbol.toStringTag;na[Kh]=(n=>(n.ArrayType=Uint8Array,n[Symbol.toStringTag]="Binary"))(na.prototype);class ra extends ft{constructor(){super(A.LargeBinary)}toString(){return"LargeBinary"}}Jh=Symbol.toStringTag;ra[Jh]=(n=>(n.ArrayType=Uint8Array,n.OffsetArrayType=BigInt64Array,n[Symbol.toStringTag]="LargeBinary"))(ra.prototype);class ia extends ft{constructor(){super(A.Utf8)}toString(){return"Utf8"}}Qh=Symbol.toStringTag;ia[Qh]=(n=>(n.ArrayType=Uint8Array,n[Symbol.toStringTag]="Utf8"))(ia.prototype);class oa extends ft{constructor(){super(A.LargeUtf8)}toString(){return"LargeUtf8"}}Gh=Symbol.toStringTag;oa[Gh]=(n=>(n.ArrayType=Uint8Array,n.OffsetArrayType=BigInt64Array,n[Symbol.toStringTag]="LargeUtf8"))(oa.prototype);class sa extends ft{constructor(){super(A.Bool)}toString(){return"Bool"}}Xh=Symbol.toStringTag;sa[Xh]=(n=>(n.ArrayType=Uint8Array,n[Symbol.toStringTag]="Bool"))(sa.prototype);class aa extends ft{constructor(t,e,r=128){super(A.Decimal),this.scale=t,this.precision=e,this.bitWidth=r}toString(){return`Decimal[${this.precision}e${this.scale>0?"+":""}${this.scale}]`}}Zh=Symbol.toStringTag;aa[Zh]=(n=>(n.scale=null,n.precision=null,n.ArrayType=Uint32Array,n[Symbol.toStringTag]="Decimal"))(aa.prototype);class la extends ft{constructor(t){super(A.Date),this.unit=t}toString(){return`Date${(this.unit+1)*32}<${Tn[this.unit]}>`}get ArrayType(){return this.unit===Tn.DAY?Int32Array:BigInt64Array}}$h=Symbol.toStringTag;la[$h]=(n=>(n.unit=null,n[Symbol.toStringTag]="Date"))(la.prototype);class ca extends ft{constructor(t,e){super(A.Time),this.unit=t,this.bitWidth=e}toString(){return`Time${this.bitWidth}<${yt[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return BigInt64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}}tm=Symbol.toStringTag;ca[tm]=(n=>(n.unit=null,n.bitWidth=null,n[Symbol.toStringTag]="Time"))(ca.prototype);class ua extends ft{constructor(t,e){super(A.Timestamp),this.unit=t,this.timezone=e}toString(){return`Timestamp<${yt[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}em=Symbol.toStringTag;ua[em]=(n=>(n.unit=null,n.timezone=null,n.ArrayType=BigInt64Array,n[Symbol.toStringTag]="Timestamp"))(ua.prototype);class fa extends ft{constructor(t){super(A.Interval),this.unit=t}toString(){return`Interval<${ir[this.unit]}>`}}nm=Symbol.toStringTag;fa[nm]=(n=>(n.unit=null,n.ArrayType=Int32Array,n[Symbol.toStringTag]="Interval"))(fa.prototype);class da extends ft{constructor(t){super(A.Duration),this.unit=t}toString(){return`Duration<${yt[this.unit]}>`}}rm=Symbol.toStringTag;da[rm]=(n=>(n.unit=null,n.ArrayType=BigInt64Array,n[Symbol.toStringTag]="Duration"))(da.prototype);class ha extends ft{constructor(t){super(A.List),this.children=[t]}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}im=Symbol.toStringTag;ha[im]=(n=>(n.children=null,n[Symbol.toStringTag]="List"))(ha.prototype);class nn extends ft{constructor(t){super(A.Struct),this.children=t}toString(){return`Struct<{${this.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}om=Symbol.toStringTag;nn[om]=(n=>(n.children=null,n[Symbol.toStringTag]="Struct"))(nn.prototype);class ma extends ft{constructor(t,e,r){super(A.Union),this.mode=t,this.children=r,this.typeIds=e=Int32Array.from(e),this.typeIdToChildIndex=e.reduce((i,o,s)=>(i[o]=s)&&i||i,Object.create(null))}toString(){return`${this[Symbol.toStringTag]}<${this.children.map(t=>`${t.type}`).join(" | ")}>`}}sm=Symbol.toStringTag;ma[sm]=(n=>(n.mode=null,n.typeIds=null,n.children=null,n.typeIdToChildIndex=null,n.ArrayType=Int8Array,n[Symbol.toStringTag]="Union"))(ma.prototype);class pa extends ft{constructor(t){super(A.FixedSizeBinary),this.byteWidth=t}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}am=Symbol.toStringTag;pa[am]=(n=>(n.byteWidth=null,n.ArrayType=Uint8Array,n[Symbol.toStringTag]="FixedSizeBinary"))(pa.prototype);class ga extends ft{constructor(t,e){super(A.FixedSizeList),this.listSize=t,this.children=[e]}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}lm=Symbol.toStringTag;ga[lm]=(n=>(n.children=null,n.listSize=null,n[Symbol.toStringTag]="FixedSizeList"))(ga.prototype);class _a extends ft{constructor(t,e=!1){var r,i,o;if(super(A.Map),this.children=[t],this.keysSorted=e,t&&(t.name="entries",!((r=t==null?void 0:t.type)===null||r===void 0)&&r.children)){const s=(i=t==null?void 0:t.type)===null||i===void 0?void 0:i.children[0];s&&(s.name="key");const a=(o=t==null?void 0:t.type)===null||o===void 0?void 0:o.children[1];a&&(a.name="value")}}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}cm=Symbol.toStringTag;_a[cm]=(n=>(n.children=null,n.keysSorted=null,n[Symbol.toStringTag]="Map_"))(_a.prototype);const nb=(n=>()=>++n)(-1);class Ui extends ft{constructor(t,e,r,i){super(A.Dictionary),this.indices=e,this.dictionary=t,this.isOrdered=i||!1,this.id=r==null?nb():Se(r)}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}um=Symbol.toStringTag;Ui[um]=(n=>(n.id=null,n.indices=null,n.isOrdered=null,n.dictionary=null,n[Symbol.toStringTag]="Dictionary"))(Ui.prototype);function fr(n){const t=n;switch(n.typeId){case A.Decimal:return n.bitWidth/32;case A.Interval:return 1+t.unit;case A.FixedSizeList:return t.listSize;case A.FixedSizeBinary:return t.byteWidth;default:return 1}}class Ht{visitMany(t,...e){return t.map((r,i)=>this.visit(r,...e.map(o=>o[i])))}visit(...t){return this.getVisitFn(t[0],!1).apply(this,t)}getVisitFn(t,e=!0){return rb(this,t,e)}getVisitFnByTypeId(t,e=!0){return Oi(this,t,e)}visitNull(t,...e){return null}visitBool(t,...e){return null}visitInt(t,...e){return null}visitFloat(t,...e){return null}visitUtf8(t,...e){return null}visitLargeUtf8(t,...e){return null}visitBinary(t,...e){return null}visitLargeBinary(t,...e){return null}visitFixedSizeBinary(t,...e){return null}visitDate(t,...e){return null}visitTimestamp(t,...e){return null}visitTime(t,...e){return null}visitDecimal(t,...e){return null}visitList(t,...e){return null}visitStruct(t,...e){return null}visitUnion(t,...e){return null}visitDictionary(t,...e){return null}visitInterval(t,...e){return null}visitDuration(t,...e){return null}visitFixedSizeList(t,...e){return null}visitMap(t,...e){return null}}function rb(n,t,e=!0){return typeof t=="number"?Oi(n,t,e):typeof t=="string"&&t in A?Oi(n,A[t],e):t&&t instanceof ft?Oi(n,fu(t),e):t!=null&&t.type&&t.type instanceof ft?Oi(n,fu(t.type),e):Oi(n,A.NONE,e)}function Oi(n,t,e=!0){let r=null;switch(t){case A.Null:r=n.visitNull;break;case A.Bool:r=n.visitBool;break;case A.Int:r=n.visitInt;break;case A.Int8:r=n.visitInt8||n.visitInt;break;case A.Int16:r=n.visitInt16||n.visitInt;break;case A.Int32:r=n.visitInt32||n.visitInt;break;case A.Int64:r=n.visitInt64||n.visitInt;break;case A.Uint8:r=n.visitUint8||n.visitInt;break;case A.Uint16:r=n.visitUint16||n.visitInt;break;case A.Uint32:r=n.visitUint32||n.visitInt;break;case A.Uint64:r=n.visitUint64||n.visitInt;break;case A.Float:r=n.visitFloat;break;case A.Float16:r=n.visitFloat16||n.visitFloat;break;case A.Float32:r=n.visitFloat32||n.visitFloat;break;case A.Float64:r=n.visitFloat64||n.visitFloat;break;case A.Utf8:r=n.visitUtf8;break;case A.LargeUtf8:r=n.visitLargeUtf8;break;case A.Binary:r=n.visitBinary;break;case A.LargeBinary:r=n.visitLargeBinary;break;case A.FixedSizeBinary:r=n.visitFixedSizeBinary;break;case A.Date:r=n.visitDate;break;case A.DateDay:r=n.visitDateDay||n.visitDate;break;case A.DateMillisecond:r=n.visitDateMillisecond||n.visitDate;break;case A.Timestamp:r=n.visitTimestamp;break;case A.TimestampSecond:r=n.visitTimestampSecond||n.visitTimestamp;break;case A.TimestampMillisecond:r=n.visitTimestampMillisecond||n.visitTimestamp;break;case A.TimestampMicrosecond:r=n.visitTimestampMicrosecond||n.visitTimestamp;break;case A.TimestampNanosecond:r=n.visitTimestampNanosecond||n.visitTimestamp;break;case A.Time:r=n.visitTime;break;case A.TimeSecond:r=n.visitTimeSecond||n.visitTime;break;case A.TimeMillisecond:r=n.visitTimeMillisecond||n.visitTime;break;case A.TimeMicrosecond:r=n.visitTimeMicrosecond||n.visitTime;break;case A.TimeNanosecond:r=n.visitTimeNanosecond||n.visitTime;break;case A.Decimal:r=n.visitDecimal;break;case A.List:r=n.visitList;break;case A.Struct:r=n.visitStruct;break;case A.Union:r=n.visitUnion;break;case A.DenseUnion:r=n.visitDenseUnion||n.visitUnion;break;case A.SparseUnion:r=n.visitSparseUnion||n.visitUnion;break;case A.Dictionary:r=n.visitDictionary;break;case A.Interval:r=n.visitInterval;break;case A.IntervalDayTime:r=n.visitIntervalDayTime||n.visitInterval;break;case A.IntervalYearMonth:r=n.visitIntervalYearMonth||n.visitInterval;break;case A.Duration:r=n.visitDuration;break;case A.DurationSecond:r=n.visitDurationSecond||n.visitDuration;break;case A.DurationMillisecond:r=n.visitDurationMillisecond||n.visitDuration;break;case A.DurationMicrosecond:r=n.visitDurationMicrosecond||n.visitDuration;break;case A.DurationNanosecond:r=n.visitDurationNanosecond||n.visitDuration;break;case A.FixedSizeList:r=n.visitFixedSizeList;break;case A.Map:r=n.visitMap;break}if(typeof r=="function")return r;if(!e)return()=>null;throw new Error(`Unrecognized type '${A[t]}'`)}function fu(n){switch(n.typeId){case A.Null:return A.Null;case A.Int:{const{bitWidth:t,isSigned:e}=n;switch(t){case 8:return e?A.Int8:A.Uint8;case 16:return e?A.Int16:A.Uint16;case 32:return e?A.Int32:A.Uint32;case 64:return e?A.Int64:A.Uint64}return A.Int}case A.Float:switch(n.precision){case Je.HALF:return A.Float16;case Je.SINGLE:return A.Float32;case Je.DOUBLE:return A.Float64}return A.Float;case A.Binary:return A.Binary;case A.LargeBinary:return A.LargeBinary;case A.Utf8:return A.Utf8;case A.LargeUtf8:return A.LargeUtf8;case A.Bool:return A.Bool;case A.Decimal:return A.Decimal;case A.Time:switch(n.unit){case yt.SECOND:return A.TimeSecond;case yt.MILLISECOND:return A.TimeMillisecond;case yt.MICROSECOND:return A.TimeMicrosecond;case yt.NANOSECOND:return A.TimeNanosecond}return A.Time;case A.Timestamp:switch(n.unit){case yt.SECOND:return A.TimestampSecond;case yt.MILLISECOND:return A.TimestampMillisecond;case yt.MICROSECOND:return A.TimestampMicrosecond;case yt.NANOSECOND:return A.TimestampNanosecond}return A.Timestamp;case A.Date:switch(n.unit){case Tn.DAY:return A.DateDay;case Tn.MILLISECOND:return A.DateMillisecond}return A.Date;case A.Interval:switch(n.unit){case ir.DAY_TIME:return A.IntervalDayTime;case ir.YEAR_MONTH:return A.IntervalYearMonth}return A.Interval;case A.Duration:switch(n.unit){case yt.SECOND:return A.DurationSecond;case yt.MILLISECOND:return A.DurationMillisecond;case yt.MICROSECOND:return A.DurationMicrosecond;case yt.NANOSECOND:return A.DurationNanosecond}return A.Duration;case A.Map:return A.Map;case A.List:return A.List;case A.Struct:return A.Struct;case A.Union:switch(n.mode){case dn.Dense:return A.DenseUnion;case dn.Sparse:return A.SparseUnion}return A.Union;case A.FixedSizeBinary:return A.FixedSizeBinary;case A.FixedSizeList:return A.FixedSizeList;case A.Dictionary:return A.Dictionary}throw new Error(`Unrecognized type '${A[n.typeId]}'`)}Ht.prototype.visitInt8=null;Ht.prototype.visitInt16=null;Ht.prototype.visitInt32=null;Ht.prototype.visitInt64=null;Ht.prototype.visitUint8=null;Ht.prototype.visitUint16=null;Ht.prototype.visitUint32=null;Ht.prototype.visitUint64=null;Ht.prototype.visitFloat16=null;Ht.prototype.visitFloat32=null;Ht.prototype.visitFloat64=null;Ht.prototype.visitDateDay=null;Ht.prototype.visitDateMillisecond=null;Ht.prototype.visitTimestampSecond=null;Ht.prototype.visitTimestampMillisecond=null;Ht.prototype.visitTimestampMicrosecond=null;Ht.prototype.visitTimestampNanosecond=null;Ht.prototype.visitTimeSecond=null;Ht.prototype.visitTimeMillisecond=null;Ht.prototype.visitTimeMicrosecond=null;Ht.prototype.visitTimeNanosecond=null;Ht.prototype.visitDenseUnion=null;Ht.prototype.visitSparseUnion=null;Ht.prototype.visitIntervalDayTime=null;Ht.prototype.visitIntervalYearMonth=null;Ht.prototype.visitDuration=null;Ht.prototype.visitDurationSecond=null;Ht.prototype.visitDurationMillisecond=null;Ht.prototype.visitDurationMicrosecond=null;Ht.prototype.visitDurationNanosecond=null;const fm=new Float64Array(1),ui=new Uint32Array(fm.buffer);function dm(n){const t=(n&31744)>>10,e=(n&1023)/1024,r=Math.pow(-1,(n&32768)>>15);switch(t){case 31:return r*(e?Number.NaN:1/0);case 0:return r*(e?6103515625e-14*e:0)}return r*Math.pow(2,t-15)*(1+e)}function ib(n){if(n!==n)return 32256;fm[0]=n;const t=(ui[1]&2147483648)>>16&65535;let e=ui[1]&2146435072,r=0;return e>=1089470464?ui[0]>0?e=31744:(e=(e&2080374784)>>16,r=(ui[1]&1048575)>>10):e<=1056964608?(r=1048576+(ui[1]&1048575),r=1048576+(r<<(e>>20)-998)>>21,e=0):(e=e-1056964608>>10,r=(ui[1]&1048575)+512>>10),t|e|r&65535}class St extends Ht{}function kt(n){return(t,e,r)=>{if(t.setValid(e,r!=null))return n(t,e,r)}}const ob=(n,t,e)=>{n[t]=Math.floor(e/864e5)},hm=(n,t,e,r)=>{if(e+1<t.length){const i=Se(t[e]),o=Se(t[e+1]);n.set(r.subarray(0,o-i),i)}},sb=({offset:n,values:t},e,r)=>{const i=n+e;r?t[i>>3]|=1<<i%8:t[i>>3]&=~(1<<i%8)},Or=({values:n},t,e)=>{n[t]=e},sc=({values:n},t,e)=>{n[t]=e},mm=({values:n},t,e)=>{n[t]=ib(e)},ab=(n,t,e)=>{switch(n.type.precision){case Je.HALF:return mm(n,t,e);case Je.SINGLE:case Je.DOUBLE:return sc(n,t,e)}},pm=({values:n},t,e)=>{ob(n,t,e.valueOf())},gm=({values:n},t,e)=>{n[t]=BigInt(e)},lb=({stride:n,values:t},e,r)=>{t.set(r.subarray(0,n),n*e)},_m=({values:n,valueOffsets:t},e,r)=>hm(n,t,e,r),bm=({values:n,valueOffsets:t},e,r)=>hm(n,t,e,ec(r)),cb=(n,t,e)=>{n.type.unit===Tn.DAY?pm(n,t,e):gm(n,t,e)},ym=({values:n},t,e)=>{n[t]=BigInt(e/1e3)},vm=({values:n},t,e)=>{n[t]=BigInt(e)},wm=({values:n},t,e)=>{n[t]=BigInt(e*1e3)},Sm=({values:n},t,e)=>{n[t]=BigInt(e*1e6)},ub=(n,t,e)=>{switch(n.type.unit){case yt.SECOND:return ym(n,t,e);case yt.MILLISECOND:return vm(n,t,e);case yt.MICROSECOND:return wm(n,t,e);case yt.NANOSECOND:return Sm(n,t,e)}},Im=({values:n},t,e)=>{n[t]=e},Om=({values:n},t,e)=>{n[t]=e},km=({values:n},t,e)=>{n[t]=e},Dm=({values:n},t,e)=>{n[t]=e},fb=(n,t,e)=>{switch(n.type.unit){case yt.SECOND:return Im(n,t,e);case yt.MILLISECOND:return Om(n,t,e);case yt.MICROSECOND:return km(n,t,e);case yt.NANOSECOND:return Dm(n,t,e)}},db=({values:n,stride:t},e,r)=>{n.set(r.subarray(0,t),t*e)},hb=(n,t,e)=>{const r=n.children[0],i=n.valueOffsets,o=Bn.getVisitFn(r);if(Array.isArray(e))for(let s=-1,a=i[t],l=i[t+1];a<l;)o(r,a++,e[++s]);else for(let s=-1,a=i[t],l=i[t+1];a<l;)o(r,a++,e.get(++s))},mb=(n,t,e)=>{const r=n.children[0],{valueOffsets:i}=n,o=Bn.getVisitFn(r);let{[t]:s,[t+1]:a}=i;const l=e instanceof Map?e.entries():Object.entries(e);for(const c of l)if(o(r,s,c),++s>=a)break},pb=(n,t)=>(e,r,i,o)=>r&&e(r,n,t[o]),gb=(n,t)=>(e,r,i,o)=>r&&e(r,n,t.get(o)),_b=(n,t)=>(e,r,i,o)=>r&&e(r,n,t.get(i.name)),bb=(n,t)=>(e,r,i,o)=>r&&e(r,n,t[i.name]),yb=(n,t,e)=>{const r=n.type.children.map(o=>Bn.getVisitFn(o.type)),i=e instanceof Map?_b(t,e):e instanceof de?gb(t,e):Array.isArray(e)?pb(t,e):bb(t,e);n.type.children.forEach((o,s)=>i(r[s],n.children[s],o,s))},vb=(n,t,e)=>{n.type.mode===dn.Dense?Em(n,t,e):Am(n,t,e)},Em=(n,t,e)=>{const r=n.type.typeIdToChildIndex[n.typeIds[t]],i=n.children[r];Bn.visit(i,n.valueOffsets[t],e)},Am=(n,t,e)=>{const r=n.type.typeIdToChildIndex[n.typeIds[t]],i=n.children[r];Bn.visit(i,t,e)},wb=(n,t,e)=>{var r;(r=n.dictionary)===null||r===void 0||r.set(n.values[t],e)},Sb=(n,t,e)=>{n.type.unit===ir.DAY_TIME?Tm(n,t,e):Bm(n,t,e)},Tm=({values:n},t,e)=>{n.set(e.subarray(0,2),2*t)},Bm=({values:n},t,e)=>{n[t]=e[0]*12+e[1]%12},Pm=({values:n},t,e)=>{n[t]=e},Mm=({values:n},t,e)=>{n[t]=e},Nm=({values:n},t,e)=>{n[t]=e},Cm=({values:n},t,e)=>{n[t]=e},Ib=(n,t,e)=>{switch(n.type.unit){case yt.SECOND:return Pm(n,t,e);case yt.MILLISECOND:return Mm(n,t,e);case yt.MICROSECOND:return Nm(n,t,e);case yt.NANOSECOND:return Cm(n,t,e)}},Ob=(n,t,e)=>{const{stride:r}=n,i=n.children[0],o=Bn.getVisitFn(i);if(Array.isArray(e))for(let s=-1,a=t*r;++s<r;)o(i,a+s,e[s]);else for(let s=-1,a=t*r;++s<r;)o(i,a+s,e.get(s))};St.prototype.visitBool=kt(sb);St.prototype.visitInt=kt(Or);St.prototype.visitInt8=kt(Or);St.prototype.visitInt16=kt(Or);St.prototype.visitInt32=kt(Or);St.prototype.visitInt64=kt(Or);St.prototype.visitUint8=kt(Or);St.prototype.visitUint16=kt(Or);St.prototype.visitUint32=kt(Or);St.prototype.visitUint64=kt(Or);St.prototype.visitFloat=kt(ab);St.prototype.visitFloat16=kt(mm);St.prototype.visitFloat32=kt(sc);St.prototype.visitFloat64=kt(sc);St.prototype.visitUtf8=kt(bm);St.prototype.visitLargeUtf8=kt(bm);St.prototype.visitBinary=kt(_m);St.prototype.visitLargeBinary=kt(_m);St.prototype.visitFixedSizeBinary=kt(lb);St.prototype.visitDate=kt(cb);St.prototype.visitDateDay=kt(pm);St.prototype.visitDateMillisecond=kt(gm);St.prototype.visitTimestamp=kt(ub);St.prototype.visitTimestampSecond=kt(ym);St.prototype.visitTimestampMillisecond=kt(vm);St.prototype.visitTimestampMicrosecond=kt(wm);St.prototype.visitTimestampNanosecond=kt(Sm);St.prototype.visitTime=kt(fb);St.prototype.visitTimeSecond=kt(Im);St.prototype.visitTimeMillisecond=kt(Om);St.prototype.visitTimeMicrosecond=kt(km);St.prototype.visitTimeNanosecond=kt(Dm);St.prototype.visitDecimal=kt(db);St.prototype.visitList=kt(hb);St.prototype.visitStruct=kt(yb);St.prototype.visitUnion=kt(vb);St.prototype.visitDenseUnion=kt(Em);St.prototype.visitSparseUnion=kt(Am);St.prototype.visitDictionary=kt(wb);St.prototype.visitInterval=kt(Sb);St.prototype.visitIntervalDayTime=kt(Tm);St.prototype.visitIntervalYearMonth=kt(Bm);St.prototype.visitDuration=kt(Ib);St.prototype.visitDurationSecond=kt(Pm);St.prototype.visitDurationMillisecond=kt(Mm);St.prototype.visitDurationMicrosecond=kt(Nm);St.prototype.visitDurationNanosecond=kt(Cm);St.prototype.visitFixedSizeList=kt(Ob);St.prototype.visitMap=kt(mb);const Bn=new St,jn=Symbol.for("parent"),Ni=Symbol.for("rowIndex");class ac{constructor(t,e){return this[jn]=t,this[Ni]=e,new Proxy(this,new Db)}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Ni],e=this[jn],r=e.type.children,i={};for(let o=-1,s=r.length;++o<s;)i[r[o].name]=hn.visit(e.children[o],t);return i}toString(){return`{${[...this].map(([t,e])=>`${Yo(t)}: ${Yo(e)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new kb(this[jn],this[Ni])}}class kb{constructor(t,e){this.childIndex=0,this.children=t.children,this.rowIndex=e,this.childFields=t.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const t=this.childIndex;return t<this.numChildren?(this.childIndex=t+1,{done:!1,value:[this.childFields[t].name,hn.visit(this.children[t],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(ac.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[jn]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Ni]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class Db{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[jn].type.children.map(e=>e.name)}has(t,e){return t[jn].type.children.findIndex(r=>r.name===e)!==-1}getOwnPropertyDescriptor(t,e){if(t[jn].type.children.findIndex(r=>r.name===e)!==-1)return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const r=t[jn].type.children.findIndex(i=>i.name===e);if(r!==-1){const i=hn.visit(t[jn].children[r],t[Ni]);return Reflect.set(t,e,i),i}}set(t,e,r){const i=t[jn].type.children.findIndex(o=>o.name===e);return i!==-1?(Bn.visit(t[jn].children[i],t[Ni],r),Reflect.set(t,e,r)):Reflect.has(t,e)||typeof e=="symbol"?Reflect.set(t,e,r):!1}}class pt extends Ht{}function It(n){return(t,e)=>t.getValid(e)?n(t,e):null}const Eb=(n,t)=>864e5*n[t],Ab=(n,t)=>null,Fm=(n,t,e)=>{if(e+1>=t.length)return null;const r=Se(t[e]),i=Se(t[e+1]);return n.subarray(r,i)},Tb=({offset:n,values:t},e)=>{const r=n+e;return(t[r>>3]&1<<r%8)!==0},jm=({values:n},t)=>Eb(n,t),Lm=({values:n},t)=>Se(n[t]),zr=({stride:n,values:t},e)=>t[n*e],Bb=({stride:n,values:t},e)=>dm(t[n*e]),Rm=({values:n},t)=>n[t],Pb=({stride:n,values:t},e)=>t.subarray(n*e,n*(e+1)),Um=({values:n,valueOffsets:t},e)=>Fm(n,t,e),Vm=({values:n,valueOffsets:t},e)=>{const r=Fm(n,t,e);return r!==null?kl(r):null},Mb=({values:n},t)=>n[t],Nb=({type:n,values:t},e)=>n.precision!==Je.HALF?t[e]:dm(t[e]),Cb=(n,t)=>n.type.unit===Tn.DAY?jm(n,t):Lm(n,t),zm=({values:n},t)=>1e3*Se(n[t]),Wm=({values:n},t)=>Se(n[t]),Hm=({values:n},t)=>zh(n[t],BigInt(1e3)),xm=({values:n},t)=>zh(n[t],BigInt(1e6)),Fb=(n,t)=>{switch(n.type.unit){case yt.SECOND:return zm(n,t);case yt.MILLISECOND:return Wm(n,t);case yt.MICROSECOND:return Hm(n,t);case yt.NANOSECOND:return xm(n,t)}},qm=({values:n},t)=>n[t],Ym=({values:n},t)=>n[t],Km=({values:n},t)=>n[t],Jm=({values:n},t)=>n[t],jb=(n,t)=>{switch(n.type.unit){case yt.SECOND:return qm(n,t);case yt.MILLISECOND:return Ym(n,t);case yt.MICROSECOND:return Km(n,t);case yt.NANOSECOND:return Jm(n,t)}},Lb=({values:n,stride:t},e)=>oc.decimal(n.subarray(t*e,t*(e+1))),Rb=(n,t)=>{const{valueOffsets:e,stride:r,children:i}=n,{[t*r]:o,[t*r+1]:s}=e,l=i[0].slice(o,s-o);return new de([l])},Ub=(n,t)=>{const{valueOffsets:e,children:r}=n,{[t]:i,[t+1]:o}=e,s=r[0];return new lc(s.slice(i,o-i))},Vb=(n,t)=>new ac(n,t),zb=(n,t)=>n.type.mode===dn.Dense?Qm(n,t):Gm(n,t),Qm=(n,t)=>{const e=n.type.typeIdToChildIndex[n.typeIds[t]],r=n.children[e];return hn.visit(r,n.valueOffsets[t])},Gm=(n,t)=>{const e=n.type.typeIdToChildIndex[n.typeIds[t]],r=n.children[e];return hn.visit(r,t)},Wb=(n,t)=>{var e;return(e=n.dictionary)===null||e===void 0?void 0:e.get(n.values[t])},Hb=(n,t)=>n.type.unit===ir.DAY_TIME?Xm(n,t):Zm(n,t),Xm=({values:n},t)=>n.subarray(2*t,2*(t+1)),Zm=({values:n},t)=>{const e=n[t],r=new Int32Array(2);return r[0]=Math.trunc(e/12),r[1]=Math.trunc(e%12),r},$m=({values:n},t)=>n[t],tp=({values:n},t)=>n[t],ep=({values:n},t)=>n[t],np=({values:n},t)=>n[t],xb=(n,t)=>{switch(n.type.unit){case yt.SECOND:return $m(n,t);case yt.MILLISECOND:return tp(n,t);case yt.MICROSECOND:return ep(n,t);case yt.NANOSECOND:return np(n,t)}},qb=(n,t)=>{const{stride:e,children:r}=n,o=r[0].slice(t*e,e);return new de([o])};pt.prototype.visitNull=It(Ab);pt.prototype.visitBool=It(Tb);pt.prototype.visitInt=It(Mb);pt.prototype.visitInt8=It(zr);pt.prototype.visitInt16=It(zr);pt.prototype.visitInt32=It(zr);pt.prototype.visitInt64=It(Rm);pt.prototype.visitUint8=It(zr);pt.prototype.visitUint16=It(zr);pt.prototype.visitUint32=It(zr);pt.prototype.visitUint64=It(Rm);pt.prototype.visitFloat=It(Nb);pt.prototype.visitFloat16=It(Bb);pt.prototype.visitFloat32=It(zr);pt.prototype.visitFloat64=It(zr);pt.prototype.visitUtf8=It(Vm);pt.prototype.visitLargeUtf8=It(Vm);pt.prototype.visitBinary=It(Um);pt.prototype.visitLargeBinary=It(Um);pt.prototype.visitFixedSizeBinary=It(Pb);pt.prototype.visitDate=It(Cb);pt.prototype.visitDateDay=It(jm);pt.prototype.visitDateMillisecond=It(Lm);pt.prototype.visitTimestamp=It(Fb);pt.prototype.visitTimestampSecond=It(zm);pt.prototype.visitTimestampMillisecond=It(Wm);pt.prototype.visitTimestampMicrosecond=It(Hm);pt.prototype.visitTimestampNanosecond=It(xm);pt.prototype.visitTime=It(jb);pt.prototype.visitTimeSecond=It(qm);pt.prototype.visitTimeMillisecond=It(Ym);pt.prototype.visitTimeMicrosecond=It(Km);pt.prototype.visitTimeNanosecond=It(Jm);pt.prototype.visitDecimal=It(Lb);pt.prototype.visitList=It(Rb);pt.prototype.visitStruct=It(Vb);pt.prototype.visitUnion=It(zb);pt.prototype.visitDenseUnion=It(Qm);pt.prototype.visitSparseUnion=It(Gm);pt.prototype.visitDictionary=It(Wb);pt.prototype.visitInterval=It(Hb);pt.prototype.visitIntervalDayTime=It(Xm);pt.prototype.visitIntervalYearMonth=It(Zm);pt.prototype.visitDuration=It(xb);pt.prototype.visitDurationSecond=It($m);pt.prototype.visitDurationMillisecond=It(tp);pt.prototype.visitDurationMicrosecond=It(ep);pt.prototype.visitDurationNanosecond=It(np);pt.prototype.visitFixedSizeList=It(qb);pt.prototype.visitMap=It(Ub);const hn=new pt,ki=Symbol.for("keys"),Ci=Symbol.for("vals"),Di=Symbol.for("kKeysAsStrings"),Pl=Symbol.for("_kKeysAsStrings");class lc{constructor(t){return this[ki]=new de([t.children[0]]).memoize(),this[Ci]=t.children[1],new Proxy(this,new Kb)}get[Di](){return this[Pl]||(this[Pl]=Array.from(this[ki].toArray(),String))}[Symbol.iterator](){return new Yb(this[ki],this[Ci])}get size(){return this[ki].length}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[ki],e=this[Ci],r={};for(let i=-1,o=t.length;++i<o;)r[t.get(i)]=hn.visit(e,i);return r}toString(){return`{${[...this].map(([t,e])=>`${Yo(t)}: ${Yo(e)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class Yb{constructor(t,e){this.keys=t,this.vals=e,this.keyIndex=0,this.numKeys=t.length}[Symbol.iterator](){return this}next(){const t=this.keyIndex;return t===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(t),hn.visit(this.vals,t)]})}}class Kb{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Di]}has(t,e){return t[Di].includes(e)}getOwnPropertyDescriptor(t,e){if(t[Di].indexOf(e)!==-1)return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const r=t[Di].indexOf(e);if(r!==-1){const i=hn.visit(Reflect.get(t,Ci),r);return Reflect.set(t,e,i),i}}set(t,e,r){const i=t[Di].indexOf(e);return i!==-1?(Bn.visit(Reflect.get(t,Ci),i,r),Reflect.set(t,e,r)):Reflect.has(t,e)?Reflect.set(t,e,r):!1}}Object.defineProperties(lc.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[ki]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Ci]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Pl]:{writable:!0,enumerable:!1,configurable:!1,value:null}});let du;function rp(n,t,e,r){const{length:i=0}=n;let o=typeof t!="number"?0:t,s=typeof e!="number"?i:e;return o<0&&(o=(o%i+i)%i),s<0&&(s=(s%i+i)%i),s<o&&(du=o,o=s,s=du),s>i&&(s=i),r?r(n,o,s):[o,s]}const cc=(n,t)=>n<0?t+n:n,hu=n=>n!==n;function Yi(n){if(typeof n!=="object"||n===null)return hu(n)?hu:e=>e===n;if(n instanceof Date){const e=n.valueOf();return r=>r instanceof Date?r.valueOf()===e:!1}return ArrayBuffer.isView(n)?e=>e?q_(n,e):!1:n instanceof Map?Qb(n):Array.isArray(n)?Jb(n):n instanceof de?Gb(n):Xb(n,!0)}function Jb(n){const t=[];for(let e=-1,r=n.length;++e<r;)t[e]=Yi(n[e]);return Fa(t)}function Qb(n){let t=-1;const e=[];for(const r of n.values())e[++t]=Yi(r);return Fa(e)}function Gb(n){const t=[];for(let e=-1,r=n.length;++e<r;)t[e]=Yi(n.get(e));return Fa(t)}function Xb(n,t=!1){const e=Object.keys(n);if(!t&&e.length===0)return()=>!1;const r=[];for(let i=-1,o=e.length;++i<o;)r[i]=Yi(n[e[i]]);return Fa(r,e)}function Fa(n,t){return e=>{if(!e||typeof e!="object")return!1;switch(e.constructor){case Array:return Zb(n,e);case Map:return mu(n,e,e.keys());case lc:case ac:case Object:case void 0:return mu(n,e,t||Object.keys(e))}return e instanceof de?$b(n,e):!1}}function Zb(n,t){const e=n.length;if(t.length!==e)return!1;for(let r=-1;++r<e;)if(!n[r](t[r]))return!1;return!0}function $b(n,t){const e=n.length;if(t.length!==e)return!1;for(let r=-1;++r<e;)if(!n[r](t.get(r)))return!1;return!0}function mu(n,t,e){const r=e[Symbol.iterator](),i=t instanceof Map?t.keys():Object.keys(t)[Symbol.iterator](),o=t instanceof Map?t.values():Object.values(t)[Symbol.iterator]();let s=0;const a=n.length;let l=o.next(),c=r.next(),u=i.next();for(;s<a&&!c.done&&!u.done&&!l.done&&!(c.value!==u.value||!n[s](l.value));++s,c=r.next(),u=i.next(),l=o.next());return s===a&&c.done&&u.done&&l.done?!0:(r.return&&r.return(),i.return&&i.return(),o.return&&o.return(),!1)}function ip(n,t,e,r){return(e&1<<r)!==0}function ty(n,t,e,r){return(e&1<<r)>>r}function pu(n,t,e){const r=e.byteLength+7&-8;if(n>0||e.byteLength<r){const i=new Uint8Array(r);return i.set(n%8===0?e.subarray(n>>3):Ml(new uc(e,n,t,null,ip)).subarray(0,r)),i}return e}function Ml(n){const t=[];let e=0,r=0,i=0;for(const s of n)s&&(i|=1<<r),++r===8&&(t[e++]=i,i=r=0);(e===0||r>0)&&(t[e++]=i);const o=new Uint8Array(t.length+7&-8);return o.set(t),o}class uc{constructor(t,e,r,i,o){this.bytes=t,this.length=r,this.context=i,this.get=o,this.bit=e%8,this.byteIndex=e>>3,this.byte=t[this.byteIndex++],this.index=0}next(){return this.index<this.length?(this.bit===8&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function Nl(n,t,e){if(e-t<=0)return 0;if(e-t<8){let o=0;for(const s of new uc(n,t,e-t,n,ty))o+=s;return o}const r=e>>3<<3,i=t+(t%8===0?0:8-t%8);return Nl(n,t,i)+Nl(n,r,e)+ey(n,i>>3,r-i>>3)}function ey(n,t,e){let r=0,i=Math.trunc(t);const o=new DataView(n.buffer,n.byteOffset,n.byteLength),s=e===void 0?n.byteLength:i+e;for(;s-i>=4;)r+=el(o.getUint32(i)),i+=4;for(;s-i>=2;)r+=el(o.getUint16(i)),i+=2;for(;s-i>=1;)r+=el(o.getUint8(i)),i+=1;return r}function el(n){let t=Math.trunc(n);return t=t-(t>>>1&1431655765),t=(t&858993459)+(t>>>2&858993459),(t+(t>>>4)&252645135)*16843009>>>24}const ny=-1;class le{get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get nullable(){if(this._nullCount!==0){const{type:t}=this;return ft.isSparseUnion(t)?this.children.some(e=>e.nullable):ft.isDenseUnion(t)?this.children.some(e=>e.nullable):this.nullBitmap&&this.nullBitmap.byteLength>0}return!0}get byteLength(){let t=0;const{valueOffsets:e,values:r,nullBitmap:i,typeIds:o}=this;return e&&(t+=e.byteLength),r&&(t+=r.byteLength),i&&(t+=i.byteLength),o&&(t+=o.byteLength),this.children.reduce((s,a)=>s+a.byteLength,t)}get nullCount(){if(ft.isUnion(this.type))return this.children.reduce((r,i)=>r+i.nullCount,0);let t=this._nullCount,e;return t<=ny&&(e=this.nullBitmap)&&(this._nullCount=t=e.length===0?0:this.length-Nl(e,this.offset,this.offset+this.length)),t}constructor(t,e,r,i,o,s=[],a){this.type=t,this.children=s,this.dictionary=a,this.offset=Math.floor(Math.max(e||0,0)),this.length=Math.floor(Math.max(r||0,0)),this._nullCount=Math.floor(Math.max(i||0,-1));let l;o instanceof le?(this.stride=o.stride,this.values=o.values,this.typeIds=o.typeIds,this.nullBitmap=o.nullBitmap,this.valueOffsets=o.valueOffsets):(this.stride=fr(t),o&&((l=o[0])&&(this.valueOffsets=l),(l=o[1])&&(this.values=l),(l=o[2])&&(this.nullBitmap=l),(l=o[3])&&(this.typeIds=l)))}getValid(t){const{type:e}=this;if(ft.isUnion(e)){const r=e,i=this.children[r.typeIdToChildIndex[this.typeIds[t]]],o=r.mode===dn.Dense?this.valueOffsets[t]:t;return i.getValid(o)}if(this.nullable&&this.nullCount>0){const r=this.offset+t;return(this.nullBitmap[r>>3]&1<<r%8)!==0}return!0}setValid(t,e){let r;const{type:i}=this;if(ft.isUnion(i)){const o=i,s=this.children[o.typeIdToChildIndex[this.typeIds[t]]],a=o.mode===dn.Dense?this.valueOffsets[t]:t;r=s.getValid(a),s.setValid(a,e)}else{let{nullBitmap:o}=this;const{offset:s,length:a}=this,l=s+t,c=1<<l%8,u=l>>3;(!o||o.byteLength<=u)&&(o=new Uint8Array((s+a+63&-64)>>3).fill(255),this.nullCount>0?(o.set(pu(s,a,this.nullBitmap),0),Object.assign(this,{nullBitmap:o})):Object.assign(this,{nullBitmap:o,_nullCount:0}));const f=o[u];r=(f&c)!==0,o[u]=e?f|c:f&~c}return r!==!!e&&(this._nullCount=this.nullCount+(e?-1:1)),e}clone(t=this.type,e=this.offset,r=this.length,i=this._nullCount,o=this,s=this.children){return new le(t,e,r,i,o,s,this.dictionary)}slice(t,e){const{stride:r,typeId:i,children:o}=this,s=+(this._nullCount===0)-1,a=i===16?r:1,l=this._sliceBuffers(t,e,r,i);return this.clone(this.type,this.offset+t,e,s,l,o.length===0||this.valueOffsets?o:this._sliceChildren(o,a*t,a*e))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===A.Null)return this.clone(this.type,0,t,0);const{length:e,nullCount:r}=this,i=new Uint8Array((t+63&-64)>>3).fill(255,0,e>>3);i[e>>3]=(1<<e-(e&-8))-1,r>0&&i.set(pu(this.offset,e,this.nullBitmap),0);const o=this.buffers;return o[xr.VALIDITY]=i,this.clone(this.type,0,t,r+(t-e),o)}_sliceBuffers(t,e,r,i){let o;const{buffers:s}=this;return(o=s[xr.TYPE])&&(s[xr.TYPE]=o.subarray(t,t+e)),(o=s[xr.OFFSET])&&(s[xr.OFFSET]=o.subarray(t,t+e+1))||(o=s[xr.DATA])&&(s[xr.DATA]=i===6?o:o.subarray(r*t,r*(t+e))),s}_sliceChildren(t,e,r){return t.map(i=>i.slice(e,r))}}le.prototype.children=Object.freeze([]);class Eo extends Ht{visit(t){return this.getVisitFn(t.type).call(this,t)}visitNull(t){const{["type"]:e,["offset"]:r=0,["length"]:i=0}=t;return new le(e,r,i,i)}visitBool(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length>>3,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitInt(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitFloat(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitUtf8(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.data),o=Gt(t.nullBitmap),s=oo(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new le(e,r,a,l,[s,i,o])}visitLargeUtf8(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.data),o=Gt(t.nullBitmap),s=eu(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new le(e,r,a,l,[s,i,o])}visitBinary(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.data),o=Gt(t.nullBitmap),s=oo(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new le(e,r,a,l,[s,i,o])}visitLargeBinary(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.data),o=Gt(t.nullBitmap),s=eu(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new le(e,r,a,l,[s,i,o])}visitFixedSizeBinary(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length/fr(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitDate(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length/fr(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitTimestamp(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length/fr(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitTime(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length/fr(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitDecimal(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length/fr(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitList(t){const{["type"]:e,["offset"]:r=0,["child"]:i}=t,o=Gt(t.nullBitmap),s=oo(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new le(e,r,a,l,[s,void 0,o],[i])}visitStruct(t){const{["type"]:e,["offset"]:r=0,["children"]:i=[]}=t,o=Gt(t.nullBitmap),{length:s=i.reduce((l,{length:c})=>Math.max(l,c),0),nullCount:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,void 0,o],i)}visitUnion(t){const{["type"]:e,["offset"]:r=0,["children"]:i=[]}=t,o=fe(e.ArrayType,t.typeIds),{["length"]:s=o.length,["nullCount"]:a=-1}=t;if(ft.isSparseUnion(e))return new le(e,r,s,a,[void 0,void 0,void 0,o],i);const l=oo(t.valueOffsets);return new le(e,r,s,a,[l,void 0,void 0,o],i)}visitDictionary(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.indices.ArrayType,t.data),{["dictionary"]:s=new de([new Eo().visit({type:e.dictionary})])}=t,{["length"]:a=o.length,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new le(e,r,a,l,[void 0,o,i],[],s)}visitInterval(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length/fr(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitDuration(t){const{["type"]:e,["offset"]:r=0}=t,i=Gt(t.nullBitmap),o=fe(e.ArrayType,t.data),{["length"]:s=o.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,o,i])}visitFixedSizeList(t){const{["type"]:e,["offset"]:r=0,["child"]:i=new Eo().visit({type:e.valueType})}=t,o=Gt(t.nullBitmap),{["length"]:s=i.length/fr(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new le(e,r,s,a,[void 0,void 0,o],[i])}visitMap(t){const{["type"]:e,["offset"]:r=0,["child"]:i=new Eo().visit({type:e.childType})}=t,o=Gt(t.nullBitmap),s=oo(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new le(e,r,a,l,[s,void 0,o],[i])}}const ry=new Eo;function zt(n){return ry.visit(n)}class gu{constructor(t=0,e){this.numChunks=t,this.getChunkIterator=e,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const t=this.chunkIterator.next();if(!t.done)return t;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function iy(n){return n.some(t=>t.nullable)}function op(n){return n.reduce((t,e)=>t+e.nullCount,0)}function sp(n){return n.reduce((t,e,r)=>(t[r+1]=t[r]+e.length,t),new Uint32Array(n.length+1))}function ap(n,t,e,r){const i=[];for(let o=-1,s=n.length;++o<s;){const a=n[o],l=t[o],{length:c}=a;if(l>=r)break;if(e>=l+c)continue;if(l>=e&&l+c<=r){i.push(a);continue}const u=Math.max(0,e-l),f=Math.min(r-l,c);i.push(a.slice(u,f-u))}return i.length===0&&i.push(n[0].slice(0,0)),i}function fc(n,t,e,r){let i=0,o=0,s=t.length-1;do{if(i>=s-1)return e<t[s]?r(n,i,e-t[i]):null;o=i+Math.trunc((s-i)*.5),e<t[o]?s=o:i=o}while(i<s)}function dc(n,t){return n.getValid(t)}function ba(n){function t(e,r,i){return n(e[r],i)}return function(e){const r=this.data;return fc(r,this._offsets,e,t)}}function lp(n){let t;function e(r,i,o){return n(r[i],o,t)}return function(r,i){const o=this.data;t=i;const s=fc(o,this._offsets,r,e);return t=void 0,s}}function cp(n){let t;function e(r,i,o){let s=o,a=0,l=0;for(let c=i-1,u=r.length;++c<u;){const f=r[c];if(~(a=n(f,t,s)))return l+a;s=0,l+=f.length}return-1}return function(r,i){t=r;const o=this.data,s=typeof i!="number"?e(o,0,0):fc(o,this._offsets,i,e);return t=void 0,s}}class gt extends Ht{}function oy(n,t){return t===null&&n.length>0?0:-1}function sy(n,t){const{nullBitmap:e}=n;if(!e||n.nullCount<=0)return-1;let r=0;for(const i of new uc(e,n.offset+(t||0),n.length,e,ip)){if(!i)return r;++r}return-1}function Tt(n,t,e){if(t===void 0)return-1;if(t===null)switch(n.typeId){case A.Union:break;case A.Dictionary:break;default:return sy(n,e)}const r=hn.getVisitFn(n),i=Yi(t);for(let o=(e||0)-1,s=n.length;++o<s;)if(i(r(n,o)))return o;return-1}function up(n,t,e){const r=hn.getVisitFn(n),i=Yi(t);for(let o=(e||0)-1,s=n.length;++o<s;)if(i(r(n,o)))return o;return-1}gt.prototype.visitNull=oy;gt.prototype.visitBool=Tt;gt.prototype.visitInt=Tt;gt.prototype.visitInt8=Tt;gt.prototype.visitInt16=Tt;gt.prototype.visitInt32=Tt;gt.prototype.visitInt64=Tt;gt.prototype.visitUint8=Tt;gt.prototype.visitUint16=Tt;gt.prototype.visitUint32=Tt;gt.prototype.visitUint64=Tt;gt.prototype.visitFloat=Tt;gt.prototype.visitFloat16=Tt;gt.prototype.visitFloat32=Tt;gt.prototype.visitFloat64=Tt;gt.prototype.visitUtf8=Tt;gt.prototype.visitLargeUtf8=Tt;gt.prototype.visitBinary=Tt;gt.prototype.visitLargeBinary=Tt;gt.prototype.visitFixedSizeBinary=Tt;gt.prototype.visitDate=Tt;gt.prototype.visitDateDay=Tt;gt.prototype.visitDateMillisecond=Tt;gt.prototype.visitTimestamp=Tt;gt.prototype.visitTimestampSecond=Tt;gt.prototype.visitTimestampMillisecond=Tt;gt.prototype.visitTimestampMicrosecond=Tt;gt.prototype.visitTimestampNanosecond=Tt;gt.prototype.visitTime=Tt;gt.prototype.visitTimeSecond=Tt;gt.prototype.visitTimeMillisecond=Tt;gt.prototype.visitTimeMicrosecond=Tt;gt.prototype.visitTimeNanosecond=Tt;gt.prototype.visitDecimal=Tt;gt.prototype.visitList=Tt;gt.prototype.visitStruct=Tt;gt.prototype.visitUnion=Tt;gt.prototype.visitDenseUnion=up;gt.prototype.visitSparseUnion=up;gt.prototype.visitDictionary=Tt;gt.prototype.visitInterval=Tt;gt.prototype.visitIntervalDayTime=Tt;gt.prototype.visitIntervalYearMonth=Tt;gt.prototype.visitDuration=Tt;gt.prototype.visitDurationSecond=Tt;gt.prototype.visitDurationMillisecond=Tt;gt.prototype.visitDurationMicrosecond=Tt;gt.prototype.visitDurationNanosecond=Tt;gt.prototype.visitFixedSizeList=Tt;gt.prototype.visitMap=Tt;const ya=new gt;class _t extends Ht{}function Ot(n){const{type:t}=n;if(n.nullCount===0&&n.stride===1&&(ft.isInt(t)&&t.bitWidth!==64||ft.isTime(t)&&t.bitWidth!==64||ft.isFloat(t)&&t.precision!==Je.HALF))return new gu(n.data.length,r=>{const i=n.data[r];return i.values.subarray(0,i.length)[Symbol.iterator]()});let e=0;return new gu(n.data.length,r=>{const o=n.data[r].length,s=n.slice(e,e+o);return e+=o,new ay(s)})}class ay{constructor(t){this.vector=t,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}_t.prototype.visitNull=Ot;_t.prototype.visitBool=Ot;_t.prototype.visitInt=Ot;_t.prototype.visitInt8=Ot;_t.prototype.visitInt16=Ot;_t.prototype.visitInt32=Ot;_t.prototype.visitInt64=Ot;_t.prototype.visitUint8=Ot;_t.prototype.visitUint16=Ot;_t.prototype.visitUint32=Ot;_t.prototype.visitUint64=Ot;_t.prototype.visitFloat=Ot;_t.prototype.visitFloat16=Ot;_t.prototype.visitFloat32=Ot;_t.prototype.visitFloat64=Ot;_t.prototype.visitUtf8=Ot;_t.prototype.visitLargeUtf8=Ot;_t.prototype.visitBinary=Ot;_t.prototype.visitLargeBinary=Ot;_t.prototype.visitFixedSizeBinary=Ot;_t.prototype.visitDate=Ot;_t.prototype.visitDateDay=Ot;_t.prototype.visitDateMillisecond=Ot;_t.prototype.visitTimestamp=Ot;_t.prototype.visitTimestampSecond=Ot;_t.prototype.visitTimestampMillisecond=Ot;_t.prototype.visitTimestampMicrosecond=Ot;_t.prototype.visitTimestampNanosecond=Ot;_t.prototype.visitTime=Ot;_t.prototype.visitTimeSecond=Ot;_t.prototype.visitTimeMillisecond=Ot;_t.prototype.visitTimeMicrosecond=Ot;_t.prototype.visitTimeNanosecond=Ot;_t.prototype.visitDecimal=Ot;_t.prototype.visitList=Ot;_t.prototype.visitStruct=Ot;_t.prototype.visitUnion=Ot;_t.prototype.visitDenseUnion=Ot;_t.prototype.visitSparseUnion=Ot;_t.prototype.visitDictionary=Ot;_t.prototype.visitInterval=Ot;_t.prototype.visitIntervalDayTime=Ot;_t.prototype.visitIntervalYearMonth=Ot;_t.prototype.visitDuration=Ot;_t.prototype.visitDurationSecond=Ot;_t.prototype.visitDurationMillisecond=Ot;_t.prototype.visitDurationMicrosecond=Ot;_t.prototype.visitDurationNanosecond=Ot;_t.prototype.visitFixedSizeList=Ot;_t.prototype.visitMap=Ot;const hc=new _t;var fp;const dp={},hp={};class de{constructor(t){var e,r,i;const o=t[0]instanceof de?t.flatMap(a=>a.data):t;if(o.length===0||o.some(a=>!(a instanceof le)))throw new TypeError("Vector constructor expects an Array of Data instances.");const s=(e=o[0])===null||e===void 0?void 0:e.type;switch(o.length){case 0:this._offsets=[0];break;case 1:{const{get:a,set:l,indexOf:c}=dp[s.typeId],u=o[0];this.isValid=f=>dc(u,f),this.get=f=>a(u,f),this.set=(f,h)=>l(u,f,h),this.indexOf=f=>c(u,f),this._offsets=[0,u.length];break}default:Object.setPrototypeOf(this,hp[s.typeId]),this._offsets=sp(o);break}this.data=o,this.type=s,this.stride=fr(s),this.numChildren=(i=(r=s.children)===null||r===void 0?void 0:r.length)!==null&&i!==void 0?i:0,this.length=this._offsets.at(-1)}get byteLength(){return this.data.reduce((t,e)=>t+e.byteLength,0)}get nullable(){return iy(this.data)}get nullCount(){return op(this.data)}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${A[this.type.typeId]}Vector`}isValid(t){return!1}get(t){return null}at(t){return this.get(cc(t,this.length))}set(t,e){}indexOf(t,e){return-1}includes(t,e){return this.indexOf(t,e)>-1}[Symbol.iterator](){return hc.visit(this)}concat(...t){return new de(this.data.concat(t.flatMap(e=>e.data).flat(Number.POSITIVE_INFINITY)))}slice(t,e){return new de(rp(this,t,e,({data:r,_offsets:i},o,s)=>ap(r,i,o,s)))}toJSON(){return[...this]}toArray(){const{type:t,data:e,length:r,stride:i,ArrayType:o}=this;switch(t.typeId){case A.Int:case A.Float:case A.Decimal:case A.Time:case A.Timestamp:switch(e.length){case 0:return new o;case 1:return e[0].values.subarray(0,r*i);default:return e.reduce((s,{values:a,length:l})=>(s.array.set(a.subarray(0,l*i),s.offset),s.offset+=l*i,s),{array:new o(r*i),offset:0}).array}}return[...this]}toString(){return`[${[...this].join(",")}]`}getChild(t){var e;return this.getChildAt((e=this.type.children)===null||e===void 0?void 0:e.findIndex(r=>r.name===t))}getChildAt(t){return t>-1&&t<this.numChildren?new de(this.data.map(({children:e})=>e[t])):null}get isMemoized(){return ft.isDictionary(this.type)?this.data[0].dictionary.isMemoized:!1}memoize(){if(ft.isDictionary(this.type)){const t=new va(this.data[0].dictionary),e=this.data.map(r=>{const i=r.clone();return i.dictionary=t,i});return new de(e)}return new va(this)}unmemoize(){if(ft.isDictionary(this.type)&&this.isMemoized){const t=this.data[0].dictionary.unmemoize(),e=this.data.map(r=>{const i=r.clone();return i.dictionary=t,i});return new de(e)}return this}}fp=Symbol.toStringTag;de[fp]=(n=>{n.type=ft.prototype,n.data=[],n.length=0,n.stride=1,n.numChildren=0,n._offsets=new Uint32Array([0]),n[Symbol.isConcatSpreadable]=!0;const t=Object.keys(A).map(e=>A[e]).filter(e=>typeof e=="number"&&e!==A.NONE);for(const e of t){const r=hn.getVisitFnByTypeId(e),i=Bn.getVisitFnByTypeId(e),o=ya.getVisitFnByTypeId(e);dp[e]={get:r,set:i,indexOf:o},hp[e]=Object.create(n,{isValid:{value:ba(dc)},get:{value:ba(hn.getVisitFnByTypeId(e))},set:{value:lp(Bn.getVisitFnByTypeId(e))},indexOf:{value:cp(ya.getVisitFnByTypeId(e))}})}return"Vector"})(de.prototype);class va extends de{constructor(t){super(t.data);const e=this.get,r=this.set,i=this.slice,o=new Array(this.length);Object.defineProperty(this,"get",{value(s){const a=o[s];if(a!==void 0)return a;const l=e.call(this,s);return o[s]=l,l}}),Object.defineProperty(this,"set",{value(s,a){r.call(this,s,a),o[s]=a}}),Object.defineProperty(this,"slice",{value:(s,a)=>new va(i.call(this,s,a))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new de(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Cl{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(t,e,r,i){return t.prep(8,24),t.writeInt64(BigInt(i??0)),t.pad(4),t.writeInt32(r),t.writeInt64(BigInt(e??0)),t.offset()}}class pn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFooter(t,e){return(e||new pn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFooter(t,e){return t.setPosition(t.position()+ae),(e||new pn).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Fe.V1}schema(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Jn).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}dictionaries(t,e){const r=this.bb.__offset(this.bb_pos,8);return r?(e||new Cl).__init(this.bb.__vector(this.bb_pos+r)+t*24,this.bb):null}dictionariesLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(t,e){const r=this.bb.__offset(this.bb_pos,10);return r?(e||new Cl).__init(this.bb.__vector(this.bb_pos+r)+t*24,this.bb):null}recordBatchesLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const r=this.bb.__offset(this.bb_pos,12);return r?(e||new Le).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Fe.V1)}static addSchema(t,e){t.addFieldOffset(1,e,0)}static addDictionaries(t,e){t.addFieldOffset(2,e,0)}static startDictionariesVector(t,e){t.startVector(24,e,8)}static addRecordBatches(t,e){t.addFieldOffset(3,e,0)}static startRecordBatchesVector(t,e){t.startVector(24,e,8)}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endFooter(t){return t.endObject()}static finishFooterBuffer(t,e){t.finish(e)}static finishSizePrefixedFooterBuffer(t,e){t.finish(e,void 0,!0)}}class se{constructor(t=[],e,r,i=Fe.V5){this.fields=t||[],this.metadata=e||new Map,r||(r=Fl(this.fields)),this.dictionaries=r,this.metadataVersion=i}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map(t=>t.name)}toString(){return`Schema<{ ${this.fields.map((t,e)=>`${e}: ${t}`).join(", ")} }>`}select(t){const e=new Set(t),r=this.fields.filter(i=>e.has(i.name));return new se(r,this.metadata)}selectAt(t){const e=t.map(r=>this.fields[r]).filter(Boolean);return new se(e,this.metadata)}assign(...t){const e=t[0]instanceof se?t[0]:Array.isArray(t[0])?new se(t[0]):new se(t),r=[...this.fields],i=Is(Is(new Map,this.metadata),e.metadata),o=e.fields.filter(a=>{const l=r.findIndex(c=>c.name===a.name);return~l?(r[l]=a.clone({metadata:Is(Is(new Map,r[l].metadata),a.metadata)}))&&!1:!0}),s=Fl(o,new Map);return new se([...r,...o],i,new Map([...this.dictionaries,...s]))}}se.prototype.fields=null;se.prototype.metadata=null;se.prototype.dictionaries=null;class Ie{static new(...t){let[e,r,i,o]=t;return t[0]&&typeof t[0]=="object"&&({name:e}=t[0],r===void 0&&(r=t[0].type),i===void 0&&(i=t[0].nullable),o===void 0&&(o=t[0].metadata)),new Ie(`${e}`,r,i,o)}constructor(t,e,r=!1,i){this.name=t,this.type=e,this.nullable=r,this.metadata=i||new Map}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(...t){let[e,r,i,o]=t;return!t[0]||typeof t[0]!="object"?[e=this.name,r=this.type,i=this.nullable,o=this.metadata]=t:{name:e=this.name,type:r=this.type,nullable:i=this.nullable,metadata:o=this.metadata}=t[0],Ie.new(e,r,i,o)}}Ie.prototype.type=null;Ie.prototype.name=null;Ie.prototype.nullable=null;Ie.prototype.metadata=null;function Is(n,t){return new Map([...n||new Map,...t||new Map])}function Fl(n,t=new Map){for(let e=-1,r=n.length;++e<r;){const o=n[e].type;if(ft.isDictionary(o)){if(!t.has(o.id))t.set(o.id,o.dictionary);else if(t.get(o.id)!==o.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}o.children&&o.children.length>0&&Fl(o.children,t)}return t}var ly=Lh,cy=Li;class mc{static decode(t){t=new cy(Gt(t));const e=pn.getRootAsFooter(t),r=se.decode(e.schema(),new Map,e.version());return new uy(r,e)}static encode(t){const e=new ly,r=se.encode(e,t.schema);pn.startRecordBatchesVector(e,t.numRecordBatches);for(const s of[...t.recordBatches()].slice().reverse())Vi.encode(e,s);const i=e.endVector();pn.startDictionariesVector(e,t.numDictionaries);for(const s of[...t.dictionaryBatches()].slice().reverse())Vi.encode(e,s);const o=e.endVector();return pn.startFooter(e),pn.addSchema(e,r),pn.addVersion(e,Fe.V5),pn.addRecordBatches(e,i),pn.addDictionaries(e,o),pn.finishFooterBuffer(e,pn.endFooter(e)),e.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}constructor(t,e=Fe.V5,r,i){this.schema=t,this.version=e,r&&(this._recordBatches=r),i&&(this._dictionaryBatches=i)}*recordBatches(){for(let t,e=-1,r=this.numRecordBatches;++e<r;)(t=this.getRecordBatch(e))&&(yield t)}*dictionaryBatches(){for(let t,e=-1,r=this.numDictionaries;++e<r;)(t=this.getDictionaryBatch(e))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class uy extends mc{get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}constructor(t,e){super(t,e.version()),this._footer=e}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const e=this._footer.recordBatches(t);if(e)return Vi.decode(e)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const e=this._footer.dictionaries(t);if(e)return Vi.decode(e)}return null}}class Vi{static decode(t){return new Vi(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,e){const{metaDataLength:r}=e,i=BigInt(e.offset),o=BigInt(e.bodyLength);return Cl.createBlock(t,i,r,o)}constructor(t,e,r){this.metaDataLength=t,this.offset=Se(r),this.bodyLength=Se(e)}}const Ae=Object.freeze({done:!0,value:void 0});class _u{constructor(t){this._json=t}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class mp{tee(){return this._getDOMStream().tee()}pipe(t,e){return this._getNodeStream().pipe(t,e)}pipeTo(t,e){return this._getDOMStream().pipeTo(t,e)}pipeThrough(t,e){return this._getDOMStream().pipeThrough(t,e)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class fy extends mp{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise(t=>this._closedPromiseResolve=t)}get closed(){return this._closedPromise}cancel(t){return Mt(this,void 0,void 0,function*(){yield this.return(t)})}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;for(;t.length>0;)t.shift().resolve(Ae);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return wn.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return wn.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}throw(t){return Mt(this,void 0,void 0,function*(){return yield this.abort(t),Ae})}return(t){return Mt(this,void 0,void 0,function*(){return yield this.close(),Ae})}read(t){return Mt(this,void 0,void 0,function*(){return(yield this.next(t,"read")).value})}peek(t){return Mt(this,void 0,void 0,function*(){return(yield this.next(t,"peek")).value})}next(...t){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise((e,r)=>{this.resolvers.push({resolve:e,reject:r})}):Promise.resolve(Ae)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class dy extends fy{write(t){if((t=Gt(t)).byteLength>0)return super.write(t)}toString(t=!1){return t?kl(this.toUint8Array(!0)):this.toUint8Array(!1).then(kl)}toUint8Array(t=!1){return t?rr(this._values)[0]:Mt(this,void 0,void 0,function*(){var e,r,i,o;const s=[];let a=0;try{for(var l=!0,c=Bi(this),u;u=yield c.next(),e=u.done,!e;l=!0){o=u.value,l=!1;const f=o;s.push(f),a+=f.byteLength}}catch(f){r={error:f}}finally{try{!l&&!e&&(i=c.return)&&(yield i.call(c))}finally{if(r)throw r.error}}return rr(s,a)[0]})}}class wa{constructor(t){t&&(this.source=new hy(wn.fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class zi{constructor(t){t instanceof zi?this.source=t.source:t instanceof dy?this.source=new Yr(wn.fromAsyncIterable(t)):Ch(t)?this.source=new Yr(wn.fromNodeStream(t)):rc(t)?this.source=new Yr(wn.fromDOMStream(t)):Mh(t)?this.source=new Yr(wn.fromDOMStream(t.body)):Ca(t)?this.source=new Yr(wn.fromIterable(t)):qo(t)?this.source=new Yr(wn.fromAsyncIterable(t)):nc(t)&&(this.source=new Yr(wn.fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class hy{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t,e="read"){return this.source.next({cmd:e,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||Ae)}return(t){return Object.create(this.source.return&&this.source.return(t)||Ae)}}class Yr{constructor(t){this.source=t,this._closedPromise=new Promise(e=>this._closedPromiseResolve=e)}cancel(t){return Mt(this,void 0,void 0,function*(){yield this.return(t)})}get closed(){return this._closedPromise}read(t){return Mt(this,void 0,void 0,function*(){return(yield this.next(t,"read")).value})}peek(t){return Mt(this,void 0,void 0,function*(){return(yield this.next(t,"peek")).value})}next(t){return Mt(this,arguments,void 0,function*(e,r="read"){return yield this.source.next({cmd:r,size:e})})}throw(t){return Mt(this,void 0,void 0,function*(){const e=this.source.throw&&(yield this.source.throw(t))||Ae;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)})}return(t){return Mt(this,void 0,void 0,function*(){const e=this.source.return&&(yield this.source.return(t))||Ae;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)})}}class bu extends wa{constructor(t,e){super(),this.position=0,this.buffer=Gt(t),this.size=e===void 0?this.buffer.byteLength:e}readInt32(t){const{buffer:e,byteOffset:r}=this.readAt(t,4);return new DataView(e,r).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:e,size:r,position:i}=this;return e&&i<r?(typeof t!="number"&&(t=Number.POSITIVE_INFINITY),this.position=Math.min(r,i+Math.min(r-i,t)),e.subarray(i,this.position)):null}readAt(t,e){const r=this.buffer,i=Math.min(this.size,t+e);return r?r.subarray(t,i):new Uint8Array(e)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class Sa extends zi{constructor(t,e){super(),this.position=0,this._handle=t,typeof e=="number"?this.size=e:this._pending=Mt(this,void 0,void 0,function*(){this.size=(yield t.stat()).size,delete this._pending})}readInt32(t){return Mt(this,void 0,void 0,function*(){const{buffer:e,byteOffset:r}=yield this.readAt(t,4);return new DataView(e,r).getInt32(0,!0)})}seek(t){return Mt(this,void 0,void 0,function*(){return this._pending&&(yield this._pending),this.position=Math.min(t,this.size),t<this.size})}read(t){return Mt(this,void 0,void 0,function*(){this._pending&&(yield this._pending);const{_handle:e,size:r,position:i}=this;if(e&&i<r){typeof t!="number"&&(t=Number.POSITIVE_INFINITY);let o=i,s=0,a=0;const l=Math.min(r,o+Math.min(r-o,t)),c=new Uint8Array(Math.max(0,(this.position=l)-o));for(;(o+=a)<l&&(s+=a)<c.byteLength;)({bytesRead:a}=yield e.read(c,s,c.byteLength-s,o));return c}return null})}readAt(t,e){return Mt(this,void 0,void 0,function*(){this._pending&&(yield this._pending);const{_handle:r,size:i}=this;if(r&&t+e<i){const o=Math.min(i,t+e),s=new Uint8Array(o-t);return(yield r.read(s,0,e,t)).buffer}return new Uint8Array(e)})}close(){return Mt(this,void 0,void 0,function*(){const t=this._handle;this._handle=null,t&&(yield t.close())})}throw(t){return Mt(this,void 0,void 0,function*(){return yield this.close(),{done:!0,value:t}})}return(t){return Mt(this,void 0,void 0,function*(){return yield this.close(),{done:!0,value:t}})}}const my=65536;function Ei(n){return n<0&&(n=4294967295+n+1),`0x${n.toString(16)}`}const Wi=8,pc=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class pp{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const e=new Uint32Array([this.buffer[1]>>>16,this.buffer[1]&65535,this.buffer[0]>>>16,this.buffer[0]&65535]),r=new Uint32Array([t.buffer[1]>>>16,t.buffer[1]&65535,t.buffer[0]>>>16,t.buffer[0]&65535]);let i=e[3]*r[3];this.buffer[0]=i&65535;let o=i>>>16;return i=e[2]*r[3],o+=i,i=e[3]*r[2]>>>0,o+=i,this.buffer[0]+=o<<16,this.buffer[1]=o>>>0<i?my:0,this.buffer[1]+=o>>>16,this.buffer[1]+=e[1]*r[3]+e[2]*r[2]+e[3]*r[1],this.buffer[1]+=e[0]*r[3]+e[1]*r[2]+e[2]*r[1]+e[3]*r[0]<<16,this}_plus(t){const e=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],e<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=e}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return`${Ei(this.buffer[1])} ${Ei(this.buffer[0])}`}}class ce extends pp{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t,e=new Uint32Array(2)){return ce.fromString(typeof t=="string"?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return ce.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const r=t.length,i=new ce(e);for(let o=0;o<r;){const s=Wi<r-o?Wi:r-o,a=new ce(new Uint32Array([Number.parseInt(t.slice(o,o+s),10),0])),l=new ce(new Uint32Array([pc[s],0]));i.times(l),i.plus(a),o+=s}return i}static convertArray(t){const e=new Uint32Array(t.length*2);for(let r=-1,i=t.length;++r<i;)ce.from(t[r],new Uint32Array(e.buffer,e.byteOffset+2*r*4,2));return e}static multiply(t,e){return new ce(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new ce(new Uint32Array(t.buffer)).plus(e)}}class cn extends pp{negate(){return this.buffer[0]=~this.buffer[0]+1,this.buffer[1]=~this.buffer[1],this.buffer[0]==0&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const e=this.buffer[1]<<0,r=t.buffer[1]<<0;return e<r||e===r&&this.buffer[0]<t.buffer[0]}static from(t,e=new Uint32Array(2)){return cn.fromString(typeof t=="string"?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return cn.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const r=t.startsWith("-"),i=t.length,o=new cn(e);for(let s=r?1:0;s<i;){const a=Wi<i-s?Wi:i-s,l=new cn(new Uint32Array([Number.parseInt(t.slice(s,s+a),10),0])),c=new cn(new Uint32Array([pc[a],0]));o.times(c),o.plus(l),s+=a}return r?o.negate():o}static convertArray(t){const e=new Uint32Array(t.length*2);for(let r=-1,i=t.length;++r<i;)cn.from(t[r],new Uint32Array(e.buffer,e.byteOffset+2*r*4,2));return e}static multiply(t,e){return new cn(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new cn(new Uint32Array(t.buffer)).plus(e)}}class Qn{constructor(t){this.buffer=t}high(){return new cn(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new cn(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=~this.buffer[0]+1,this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],this.buffer[0]==0&&++this.buffer[1],this.buffer[1]==0&&++this.buffer[2],this.buffer[2]==0&&++this.buffer[3],this}times(t){const e=new ce(new Uint32Array([this.buffer[3],0])),r=new ce(new Uint32Array([this.buffer[2],0])),i=new ce(new Uint32Array([this.buffer[1],0])),o=new ce(new Uint32Array([this.buffer[0],0])),s=new ce(new Uint32Array([t.buffer[3],0])),a=new ce(new Uint32Array([t.buffer[2],0])),l=new ce(new Uint32Array([t.buffer[1],0])),c=new ce(new Uint32Array([t.buffer[0],0]));let u=ce.multiply(o,c);this.buffer[0]=u.low();const f=new ce(new Uint32Array([u.high(),0]));return u=ce.multiply(i,c),f.plus(u),u=ce.multiply(o,l),f.plus(u),this.buffer[1]=f.low(),this.buffer[3]=f.lessThan(u)?1:0,this.buffer[2]=f.high(),new ce(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(ce.multiply(r,c)).plus(ce.multiply(i,l)).plus(ce.multiply(o,a)),this.buffer[3]+=ce.multiply(e,c).plus(ce.multiply(r,l)).plus(ce.multiply(i,a)).plus(ce.multiply(o,s)).low(),this}plus(t){const e=new Uint32Array(4);return e[3]=this.buffer[3]+t.buffer[3]>>>0,e[2]=this.buffer[2]+t.buffer[2]>>>0,e[1]=this.buffer[1]+t.buffer[1]>>>0,e[0]=this.buffer[0]+t.buffer[0]>>>0,e[0]<this.buffer[0]>>>0&&++e[1],e[1]<this.buffer[1]>>>0&&++e[2],e[2]<this.buffer[2]>>>0&&++e[3],this.buffer[3]=e[3],this.buffer[2]=e[2],this.buffer[1]=e[1],this.buffer[0]=e[0],this}hex(){return`${Ei(this.buffer[3])} ${Ei(this.buffer[2])} ${Ei(this.buffer[1])} ${Ei(this.buffer[0])}`}static multiply(t,e){return new Qn(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new Qn(new Uint32Array(t.buffer)).plus(e)}static from(t,e=new Uint32Array(4)){return Qn.fromString(typeof t=="string"?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(4)){return Qn.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(4)){const r=t.startsWith("-"),i=t.length,o=new Qn(e);for(let s=r?1:0;s<i;){const a=Wi<i-s?Wi:i-s,l=new Qn(new Uint32Array([Number.parseInt(t.slice(s,s+a),10),0,0,0])),c=new Qn(new Uint32Array([pc[a],0,0,0]));o.times(c),o.plus(l),s+=a}return r?o.negate():o}static convertArray(t){const e=new Uint32Array(t.length*4);for(let r=-1,i=t.length;++r<i;)Qn.from(t[r],new Uint32Array(e.buffer,e.byteOffset+4*4*r,4));return e}}class gp extends Ht{constructor(t,e,r,i,o=Fe.V5){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=e,this.buffers=r,this.dictionaries=i,this.metadataVersion=o}visit(t){return super.visit(t instanceof Ie?t.type:t)}visitNull(t,{length:e}=this.nextFieldNode()){return zt({type:t,length:e})}visitBool(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitInt(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitFloat(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitUtf8(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeUtf8(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitBinary(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeBinary(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitFixedSizeBinary(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitDate(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitTimestamp(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitTime(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitDecimal(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitList(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}visitStruct(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),children:this.visitMany(t.children)})}visitUnion(t,{length:e,nullCount:r}=this.nextFieldNode()){return this.metadataVersion<Fe.V5&&this.readNullBitmap(t,r),t.mode===dn.Sparse?this.visitSparseUnion(t,{length:e,nullCount:r}):this.visitDenseUnion(t,{length:e,nullCount:r})}visitDenseUnion(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,typeIds:this.readTypeIds(t),valueOffsets:this.readOffsets(t),children:this.visitMany(t.children)})}visitSparseUnion(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,typeIds:this.readTypeIds(t),children:this.visitMany(t.children)})}visitDictionary(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t.indices),dictionary:this.readDictionary(t)})}visitInterval(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitDuration(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitFixedSizeList(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),child:this.visit(t.children[0])})}visitMap(t,{length:e,nullCount:r}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,e,r=this.nextBufferRange()){return e>0&&this.readData(t,r)||new Uint8Array(0)}readOffsets(t,e){return this.readData(t,e)}readTypeIds(t,e){return this.readData(t,e)}readData(t,{length:e,offset:r}=this.nextBufferRange()){return this.bytes.subarray(r,r+e)}readDictionary(t){return this.dictionaries.get(t.id)}}class py extends gp{constructor(t,e,r,i,o){super(new Uint8Array(0),e,r,i,o),this.sources=t}readNullBitmap(t,e,{offset:r}=this.nextBufferRange()){return e<=0?new Uint8Array(0):Ml(this.sources[r])}readOffsets(t,{offset:e}=this.nextBufferRange()){return fe(Uint8Array,fe(t.OffsetArrayType,this.sources[e]))}readTypeIds(t,{offset:e}=this.nextBufferRange()){return fe(Uint8Array,fe(t.ArrayType,this.sources[e]))}readData(t,{offset:e}=this.nextBufferRange()){const{sources:r}=this;return ft.isTimestamp(t)||(ft.isInt(t)||ft.isTime(t))&&t.bitWidth===64||ft.isDuration(t)||ft.isDate(t)&&t.unit===Tn.MILLISECOND?fe(Uint8Array,cn.convertArray(r[e])):ft.isDecimal(t)?fe(Uint8Array,Qn.convertArray(r[e])):ft.isBinary(t)||ft.isLargeBinary(t)||ft.isFixedSizeBinary(t)?gy(r[e]):ft.isBool(t)?Ml(r[e]):ft.isUtf8(t)||ft.isLargeUtf8(t)?ec(r[e].join("")):fe(Uint8Array,fe(t.ArrayType,r[e].map(i=>+i)))}}function gy(n){const t=n.join(""),e=new Uint8Array(t.length/2);for(let r=0;r<t.length;r+=2)e[r>>1]=Number.parseInt(t.slice(r,r+2),16);return e}class bt extends Ht{compareSchemas(t,e){return t===e||e instanceof t.constructor&&this.compareManyFields(t.fields,e.fields)}compareManyFields(t,e){return t===e||Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every((r,i)=>this.compareFields(r,e[i]))}compareFields(t,e){return t===e||e instanceof t.constructor&&t.name===e.name&&t.nullable===e.nullable&&this.visit(t.type,e.type)}}function on(n,t){return t instanceof n.constructor}function si(n,t){return n===t||on(n,t)}function kr(n,t){return n===t||on(n,t)&&n.bitWidth===t.bitWidth&&n.isSigned===t.isSigned}function ja(n,t){return n===t||on(n,t)&&n.precision===t.precision}function _y(n,t){return n===t||on(n,t)&&n.byteWidth===t.byteWidth}function gc(n,t){return n===t||on(n,t)&&n.unit===t.unit}function os(n,t){return n===t||on(n,t)&&n.unit===t.unit&&n.timezone===t.timezone}function ss(n,t){return n===t||on(n,t)&&n.unit===t.unit&&n.bitWidth===t.bitWidth}function by(n,t){return n===t||on(n,t)&&n.children.length===t.children.length&&Rr.compareManyFields(n.children,t.children)}function yy(n,t){return n===t||on(n,t)&&n.children.length===t.children.length&&Rr.compareManyFields(n.children,t.children)}function _c(n,t){return n===t||on(n,t)&&n.mode===t.mode&&n.typeIds.every((e,r)=>e===t.typeIds[r])&&Rr.compareManyFields(n.children,t.children)}function vy(n,t){return n===t||on(n,t)&&n.id===t.id&&n.isOrdered===t.isOrdered&&Rr.visit(n.indices,t.indices)&&Rr.visit(n.dictionary,t.dictionary)}function bc(n,t){return n===t||on(n,t)&&n.unit===t.unit}function as(n,t){return n===t||on(n,t)&&n.unit===t.unit}function wy(n,t){return n===t||on(n,t)&&n.listSize===t.listSize&&n.children.length===t.children.length&&Rr.compareManyFields(n.children,t.children)}function Sy(n,t){return n===t||on(n,t)&&n.keysSorted===t.keysSorted&&n.children.length===t.children.length&&Rr.compareManyFields(n.children,t.children)}bt.prototype.visitNull=si;bt.prototype.visitBool=si;bt.prototype.visitInt=kr;bt.prototype.visitInt8=kr;bt.prototype.visitInt16=kr;bt.prototype.visitInt32=kr;bt.prototype.visitInt64=kr;bt.prototype.visitUint8=kr;bt.prototype.visitUint16=kr;bt.prototype.visitUint32=kr;bt.prototype.visitUint64=kr;bt.prototype.visitFloat=ja;bt.prototype.visitFloat16=ja;bt.prototype.visitFloat32=ja;bt.prototype.visitFloat64=ja;bt.prototype.visitUtf8=si;bt.prototype.visitLargeUtf8=si;bt.prototype.visitBinary=si;bt.prototype.visitLargeBinary=si;bt.prototype.visitFixedSizeBinary=_y;bt.prototype.visitDate=gc;bt.prototype.visitDateDay=gc;bt.prototype.visitDateMillisecond=gc;bt.prototype.visitTimestamp=os;bt.prototype.visitTimestampSecond=os;bt.prototype.visitTimestampMillisecond=os;bt.prototype.visitTimestampMicrosecond=os;bt.prototype.visitTimestampNanosecond=os;bt.prototype.visitTime=ss;bt.prototype.visitTimeSecond=ss;bt.prototype.visitTimeMillisecond=ss;bt.prototype.visitTimeMicrosecond=ss;bt.prototype.visitTimeNanosecond=ss;bt.prototype.visitDecimal=si;bt.prototype.visitList=by;bt.prototype.visitStruct=yy;bt.prototype.visitUnion=_c;bt.prototype.visitDenseUnion=_c;bt.prototype.visitSparseUnion=_c;bt.prototype.visitDictionary=vy;bt.prototype.visitInterval=bc;bt.prototype.visitIntervalDayTime=bc;bt.prototype.visitIntervalYearMonth=bc;bt.prototype.visitDuration=as;bt.prototype.visitDurationSecond=as;bt.prototype.visitDurationMillisecond=as;bt.prototype.visitDurationMicrosecond=as;bt.prototype.visitDurationNanosecond=as;bt.prototype.visitFixedSizeList=wy;bt.prototype.visitMap=Sy;const Rr=new bt;function Iy(n,t){return Rr.compareSchemas(n,t)}function nl(n,t){return Oy(n,t.map(e=>e.data.concat()))}function Oy(n,t){const e=[...n.fields],r=[],i={numBatches:t.reduce((f,h)=>Math.max(f,h.length),0)};let o=0,s=0,a=-1;const l=t.length;let c,u=[];for(;i.numBatches-- >0;){for(s=Number.POSITIVE_INFINITY,a=-1;++a<l;)u[a]=c=t[a].shift(),s=Math.min(s,c?c.length:s);Number.isFinite(s)&&(u=ky(e,s,u,t,i),s>0&&(r[o++]=zt({type:new nn(e),length:s,nullCount:0,children:u.slice()})))}return[n=n.assign(e),r.map(f=>new Ln(n,f))]}function ky(n,t,e,r,i){var o;const s=(t+63&-64)>>3;for(let a=-1,l=r.length;++a<l;){const c=e[a],u=c==null?void 0:c.length;if(u>=t)u===t?e[a]=c:(e[a]=c.slice(0,t),i.numBatches=Math.max(i.numBatches,r[a].unshift(c.slice(t,u-t))));else{const f=n[a];n[a]=f.clone({nullable:!0}),e[a]=(o=c==null?void 0:c._changeLengthAndBackfillNullBitmap(t))!==null&&o!==void 0?o:zt({type:f.type,length:t,nullCount:t,nullBitmap:new Uint8Array(s)})}}return e}var _p;class On{constructor(...t){var e,r;if(t.length===0)return this.batches=[],this.schema=new se([]),this._offsets=[0],this;let i,o;t[0]instanceof se&&(i=t.shift()),t.at(-1)instanceof Uint32Array&&(o=t.pop());const s=l=>{if(l){if(l instanceof Ln)return[l];if(l instanceof On)return l.batches;if(l instanceof le){if(l.type instanceof nn)return[new Ln(new se(l.type.children),l)]}else{if(Array.isArray(l))return l.flatMap(c=>s(c));if(typeof l[Symbol.iterator]=="function")return[...l].flatMap(c=>s(c));if(typeof l=="object"){const c=Object.keys(l),u=c.map(d=>new de([l[d]])),f=i??new se(c.map((d,g)=>new Ie(String(d),u[g].type,u[g].nullable))),[,h]=nl(f,u);return h.length===0?[new Ln(l)]:h}}}return[]},a=t.flatMap(l=>s(l));if(i=(r=i??((e=a[0])===null||e===void 0?void 0:e.schema))!==null&&r!==void 0?r:new se([]),!(i instanceof se))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const l of a){if(!(l instanceof Ln))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!Iy(i,l.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=i,this.batches=a,this._offsets=o??sp(this.data)}get data(){return this.batches.map(({data:t})=>t)}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce((t,e)=>t+e.length,0)}get nullCount(){return this._nullCount===-1&&(this._nullCount=op(this.data)),this._nullCount}isValid(t){return!1}get(t){return null}at(t){return this.get(cc(t,this.numRows))}set(t,e){}indexOf(t,e){return-1}[Symbol.iterator](){return this.batches.length>0?hc.visit(new de(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return`[
  ${this.toArray().join(`,
  `)}
]`}concat(...t){const e=this.schema,r=this.data.concat(t.flatMap(({data:i})=>i));return new On(e,r.map(i=>new Ln(e,i)))}slice(t,e){const r=this.schema;[t,e]=rp({length:this.numRows},t,e);const i=ap(this.data,this._offsets,t,e);return new On(r,i.map(o=>new Ln(r,o)))}getChild(t){return this.getChildAt(this.schema.fields.findIndex(e=>e.name===t))}getChildAt(t){if(t>-1&&t<this.schema.fields.length){const e=this.data.map(r=>r.children[t]);if(e.length===0){const{type:r}=this.schema.fields[t],i=zt({type:r,length:0,nullCount:0});e.push(i._changeLengthAndBackfillNullBitmap(this.numRows))}return new de(e)}return null}setChild(t,e){var r;return this.setChildAt((r=this.schema.fields)===null||r===void 0?void 0:r.findIndex(i=>i.name===t),e)}setChildAt(t,e){let r=this.schema,i=[...this.batches];if(t>-1&&t<this.numCols){e||(e=new de([zt({type:new Lr,length:this.numRows})]));const o=r.fields.slice(),s=o[t].clone({type:e.type}),a=this.schema.fields.map((l,c)=>this.getChildAt(c));[o[t],a[t]]=[s,e],[r,i]=nl(r,a)}return new On(r,i)}select(t){const e=this.schema.fields.reduce((r,i,o)=>r.set(i.name,o),new Map);return this.selectAt(t.map(r=>e.get(r)).filter(r=>r>-1))}selectAt(t){const e=this.schema.selectAt(t),r=this.batches.map(i=>i.selectAt(t));return new On(e,r)}assign(t){const e=this.schema.fields,[r,i]=t.schema.fields.reduce((a,l,c)=>{const[u,f]=a,h=e.findIndex(d=>d.name===l.name);return~h?f[h]=c:u.push(c),a},[[],[]]),o=this.schema.assign(t.schema),s=[...e.map((a,l)=>[l,i[l]]).map(([a,l])=>l===void 0?this.getChildAt(a):t.getChildAt(l)),...r.map(a=>t.getChildAt(a))].filter(Boolean);return new On(...nl(o,s))}}_p=Symbol.toStringTag;On[_p]=(n=>(n.schema=null,n.batches=[],n._offsets=new Uint32Array([0]),n._nullCount=-1,n[Symbol.isConcatSpreadable]=!0,n.isValid=ba(dc),n.get=ba(hn.getVisitFn(A.Struct)),n.set=lp(Bn.getVisitFn(A.Struct)),n.indexOf=cp(ya.getVisitFn(A.Struct)),"Table"))(On.prototype);var bp;let Ln=class So{constructor(...t){switch(t.length){case 2:{if([this.schema]=t,!(this.schema instanceof se))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=zt({nullCount:0,type:new nn(this.schema.fields),children:this.schema.fields.map(e=>zt({type:e.type,nullCount:0}))})]=t,!(this.data instanceof le))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=yu(this.schema,this.data.children);break}case 1:{const[e]=t,{fields:r,children:i,length:o}=Object.keys(e).reduce((l,c,u)=>(l.children[u]=e[c],l.length=Math.max(l.length,e[c].length),l.fields[u]=Ie.new({name:c,type:e[c].type,nullable:!0}),l),{length:0,fields:new Array,children:new Array}),s=new se(r),a=zt({type:new nn(r),length:o,children:i,nullCount:0});[this.schema,this.data]=yu(s,a.children,o);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=yp(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(t){return this.data.getValid(t)}get(t){return hn.visit(this.data,t)}at(t){return this.get(cc(t,this.numRows))}set(t,e){return Bn.visit(this.data,t,e)}indexOf(t,e){return ya.visit(this.data,t,e)}[Symbol.iterator](){return hc.visit(new de([this.data]))}toArray(){return[...this]}concat(...t){return new On(this.schema,[this,...t])}slice(t,e){const[r]=new de([this.data]).slice(t,e).data;return new So(this.schema,r)}getChild(t){var e;return this.getChildAt((e=this.schema.fields)===null||e===void 0?void 0:e.findIndex(r=>r.name===t))}getChildAt(t){return t>-1&&t<this.schema.fields.length?new de([this.data.children[t]]):null}setChild(t,e){var r;return this.setChildAt((r=this.schema.fields)===null||r===void 0?void 0:r.findIndex(i=>i.name===t),e)}setChildAt(t,e){let r=this.schema,i=this.data;if(t>-1&&t<this.numCols){e||(e=new de([zt({type:new Lr,length:this.numRows})]));const o=r.fields.slice(),s=i.children.slice(),a=o[t].clone({type:e.type});[o[t],s[t]]=[a,e.data[0]],r=new se(o,new Map(this.schema.metadata)),i=zt({type:new nn(o),children:s})}return new So(r,i)}select(t){const e=this.schema.select(t),r=new nn(e.fields),i=[];for(const o of t){const s=this.schema.fields.findIndex(a=>a.name===o);~s&&(i[s]=this.data.children[s])}return new So(e,zt({type:r,length:this.numRows,children:i}))}selectAt(t){const e=this.schema.selectAt(t),r=t.map(o=>this.data.children[o]).filter(Boolean),i=zt({type:new nn(e.fields),length:this.numRows,children:r});return new So(e,i)}};bp=Symbol.toStringTag;Ln[bp]=(n=>(n._nullCount=-1,n[Symbol.isConcatSpreadable]=!0,"RecordBatch"))(Ln.prototype);function yu(n,t,e=t.reduce((r,i)=>Math.max(r,i.length),0)){var r;const i=[...n.fields],o=[...t],s=(e+63&-64)>>3;for(const[a,l]of n.fields.entries()){const c=t[a];(!c||c.length!==e)&&(i[a]=l.clone({nullable:!0}),o[a]=(r=c==null?void 0:c._changeLengthAndBackfillNullBitmap(e))!==null&&r!==void 0?r:zt({type:l.type,length:e,nullCount:e,nullBitmap:new Uint8Array(s)}))}return[n.assign(i),zt({type:new nn(i),length:e,children:o})]}function yp(n,t,e=new Map){var r,i;if(((r=n==null?void 0:n.length)!==null&&r!==void 0?r:0)>0&&(n==null?void 0:n.length)===(t==null?void 0:t.length))for(let o=-1,s=n.length;++o<s;){const{type:a}=n[o],l=t[o];for(const c of[l,...((i=l==null?void 0:l.dictionary)===null||i===void 0?void 0:i.data)||[]])yp(a.children,c==null?void 0:c.children,e);if(ft.isDictionary(a)){const{id:c}=a;if(!e.has(c))l!=null&&l.dictionary&&e.set(c,l.dictionary);else if(e.get(c)!==l.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}}return e}class vp extends Ln{constructor(t){const e=t.fields.map(i=>zt({type:i.type})),r=zt({type:new nn(t.fields),nullCount:0,children:e});super(t,r)}}let Tr=class Kn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMessage(t,e){return(e||new Kn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMessage(t,e){return t.setPosition(t.position()+ae),(e||new Kn).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Fe.V1}headerType(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):oe.NONE}header(t){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}bodyLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}customMetadata(t,e){const r=this.bb.__offset(this.bb_pos,12);return r?(e||new Le).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Fe.V1)}static addHeaderType(t,e){t.addFieldInt8(1,e,oe.NONE)}static addHeader(t,e){t.addFieldOffset(2,e,0)}static addBodyLength(t,e){t.addFieldInt64(3,e,BigInt("0"))}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endMessage(t){return t.endObject()}static finishMessageBuffer(t,e){t.finish(e)}static finishSizePrefixedMessageBuffer(t,e){t.finish(e,void 0,!0)}static createMessage(t,e,r,i,o,s){return Kn.startMessage(t),Kn.addVersion(t,e),Kn.addHeaderType(t,r),Kn.addHeader(t,i),Kn.addBodyLength(t,o),Kn.addCustomMetadata(t,s),Kn.endMessage(t)}};class Dy extends Ht{visit(t,e){return t==null||e==null?void 0:super.visit(t,e)}visitNull(t,e){return cu.startNull(e),cu.endNull(e)}visitInt(t,e){return gn.startInt(e),gn.addBitWidth(e,t.bitWidth),gn.addIsSigned(e,t.isSigned),gn.endInt(e)}visitFloat(t,e){return Xn.startFloatingPoint(e),Xn.addPrecision(e,t.precision),Xn.endFloatingPoint(e)}visitBinary(t,e){return iu.startBinary(e),iu.endBinary(e)}visitLargeBinary(t,e){return su.startLargeBinary(e),su.endLargeBinary(e)}visitBool(t,e){return ou.startBool(e),ou.endBool(e)}visitUtf8(t,e){return uu.startUtf8(e),uu.endUtf8(e)}visitLargeUtf8(t,e){return au.startLargeUtf8(e),au.endLargeUtf8(e)}visitDecimal(t,e){return yi.startDecimal(e),yi.addScale(e,t.scale),yi.addPrecision(e,t.precision),yi.addBitWidth(e,t.bitWidth),yi.endDecimal(e)}visitDate(t,e){return Rs.startDate(e),Rs.addUnit(e,t.unit),Rs.endDate(e)}visitTime(t,e){return Sn.startTime(e),Sn.addUnit(e,t.unit),Sn.addBitWidth(e,t.bitWidth),Sn.endTime(e)}visitTimestamp(t,e){const r=t.timezone&&e.createString(t.timezone)||void 0;return In.startTimestamp(e),In.addUnit(e,t.unit),r!==void 0&&In.addTimezone(e,r),In.endTimestamp(e)}visitInterval(t,e){return Zn.startInterval(e),Zn.addUnit(e,t.unit),Zn.endInterval(e)}visitDuration(t,e){return Us.startDuration(e),Us.addUnit(e,t.unit),Us.endDuration(e)}visitList(t,e){return lu.startList(e),lu.endList(e)}visitStruct(t,e){return ri.startStruct_(e),ri.endStruct_(e)}visitUnion(t,e){un.startTypeIdsVector(e,t.typeIds.length);const r=un.createTypeIdsVector(e,t.typeIds);return un.startUnion(e),un.addMode(e,t.mode),un.addTypeIds(e,r),un.endUnion(e)}visitDictionary(t,e){const r=this.visit(t.indices,e);return hr.startDictionaryEncoding(e),hr.addId(e,BigInt(t.id)),hr.addIsOrdered(e,t.isOrdered),r!==void 0&&hr.addIndexType(e,r),hr.endDictionaryEncoding(e)}visitFixedSizeBinary(t,e){return Vs.startFixedSizeBinary(e),Vs.addByteWidth(e,t.byteWidth),Vs.endFixedSizeBinary(e)}visitFixedSizeList(t,e){return zs.startFixedSizeList(e),zs.addListSize(e,t.listSize),zs.endFixedSizeList(e)}visitMap(t,e){return Ws.startMap(e),Ws.addKeysSorted(e,t.keysSorted),Ws.endMap(e)}}const rl=new Dy;function Ey(n,t=new Map){return new se(Ty(n,t),xs(n.metadata),t)}function wp(n){return new Vn(n.count,Sp(n.columns),Ip(n.columns))}function Ay(n){return new wr(wp(n.data),n.id,n.isDelta)}function Ty(n,t){return(n.fields||[]).filter(Boolean).map(e=>Ie.fromJSON(e,t))}function vu(n,t){return(n.children||[]).filter(Boolean).map(e=>Ie.fromJSON(e,t))}function Sp(n){return(n||[]).reduce((t,e)=>[...t,new Ki(e.count,By(e.VALIDITY)),...Sp(e.children)],[])}function Ip(n,t=[]){for(let e=-1,r=(n||[]).length;++e<r;){const i=n[e];i.VALIDITY&&t.push(new mr(t.length,i.VALIDITY.length)),i.TYPE_ID&&t.push(new mr(t.length,i.TYPE_ID.length)),i.OFFSET&&t.push(new mr(t.length,i.OFFSET.length)),i.DATA&&t.push(new mr(t.length,i.DATA.length)),t=Ip(i.children,t)}return t}function By(n){return(n||[]).reduce((t,e)=>t+ +(e===0),0)}function Py(n,t){let e,r,i,o,s,a;return!t||!(o=n.dictionary)?(s=Su(n,vu(n,t)),i=new Ie(n.name,s,n.nullable,xs(n.metadata))):t.has(e=o.id)?(r=(r=o.indexType)?wu(r):new Qo,a=new Ui(t.get(e),r,e,o.isOrdered),i=new Ie(n.name,a,n.nullable,xs(n.metadata))):(r=(r=o.indexType)?wu(r):new Qo,t.set(e,s=Su(n,vu(n,t))),a=new Ui(s,r,e,o.isOrdered),i=new Ie(n.name,a,n.nullable,xs(n.metadata))),i||null}function xs(n=[]){return new Map(n.map(({key:t,value:e})=>[t,e]))}function wu(n){return new oi(n.isSigned,n.bitWidth)}function Su(n,t){const e=n.type.name;switch(e){case"NONE":return new Lr;case"null":return new Lr;case"binary":return new na;case"largebinary":return new ra;case"utf8":return new ia;case"largeutf8":return new oa;case"bool":return new sa;case"list":return new ha((t||[])[0]);case"struct":return new nn(t||[]);case"struct_":return new nn(t||[])}switch(e){case"int":{const r=n.type;return new oi(r.isSigned,r.bitWidth)}case"floatingpoint":{const r=n.type;return new ea(Je[r.precision])}case"decimal":{const r=n.type;return new aa(r.scale,r.precision,r.bitWidth)}case"date":{const r=n.type;return new la(Tn[r.unit])}case"time":{const r=n.type;return new ca(yt[r.unit],r.bitWidth)}case"timestamp":{const r=n.type;return new ua(yt[r.unit],r.timezone)}case"interval":{const r=n.type;return new fa(ir[r.unit])}case"duration":{const r=n.type;return new da(yt[r.unit])}case"union":{const r=n.type,[i,...o]=(r.mode+"").toLowerCase(),s=i.toUpperCase()+o.join("");return new ma(dn[s],r.typeIds||[],t||[])}case"fixedsizebinary":{const r=n.type;return new pa(r.byteWidth)}case"fixedsizelist":{const r=n.type;return new ga(r.listSize,(t||[])[0])}case"map":{const r=n.type;return new _a((t||[])[0],r.keysSorted)}}throw new Error(`Unrecognized type: "${e}"`)}var My=Lh,Ny=Li;class Rn{static fromJSON(t,e){const r=new Rn(0,Fe.V5,e);return r._createHeader=Cy(t,e),r}static decode(t){t=new Ny(Gt(t));const e=Tr.getRootAsMessage(t),r=e.bodyLength(),i=e.version(),o=e.headerType(),s=new Rn(r,i,o);return s._createHeader=Fy(e,o),s}static encode(t){const e=new My;let r=-1;return t.isSchema()?r=se.encode(e,t.header()):t.isRecordBatch()?r=Vn.encode(e,t.header()):t.isDictionaryBatch()&&(r=wr.encode(e,t.header())),Tr.startMessage(e),Tr.addVersion(e,Fe.V5),Tr.addHeader(e,r),Tr.addHeaderType(e,t.headerType),Tr.addBodyLength(e,BigInt(t.bodyLength)),Tr.finishMessageBuffer(e,Tr.endMessage(e)),e.asUint8Array()}static from(t,e=0){if(t instanceof se)return new Rn(0,Fe.V5,oe.Schema,t);if(t instanceof Vn)return new Rn(e,Fe.V5,oe.RecordBatch,t);if(t instanceof wr)return new Rn(e,Fe.V5,oe.DictionaryBatch,t);throw new Error(`Unrecognized Message header: ${t}`)}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===oe.Schema}isRecordBatch(){return this.headerType===oe.RecordBatch}isDictionaryBatch(){return this.headerType===oe.DictionaryBatch}constructor(t,e,r,i){this._version=e,this._headerType=r,this.body=new Uint8Array(0),i&&(this._createHeader=()=>i),this._bodyLength=Se(t)}}class Vn{get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}constructor(t,e,r){this._nodes=e,this._buffers=r,this._length=Se(t)}}class wr{get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}constructor(t,e,r=!1){this._data=t,this._isDelta=r,this._id=Se(e)}}class mr{constructor(t,e){this.offset=Se(t),this.length=Se(e)}}class Ki{constructor(t,e){this.length=Se(t),this.nullCount=Se(e)}}function Cy(n,t){return()=>{switch(t){case oe.Schema:return se.fromJSON(n);case oe.RecordBatch:return Vn.fromJSON(n);case oe.DictionaryBatch:return wr.fromJSON(n)}throw new Error(`Unrecognized Message type: { name: ${oe[t]}, type: ${t} }`)}}function Fy(n,t){return()=>{switch(t){case oe.Schema:return se.decode(n.header(new Jn),new Map,n.version());case oe.RecordBatch:return Vn.decode(n.header(new ur),n.version());case oe.DictionaryBatch:return wr.decode(n.header(new _i),n.version())}throw new Error(`Unrecognized Message type: { name: ${oe[t]}, type: ${t} }`)}}Ie.encode=Yy;Ie.decode=xy;Ie.fromJSON=Py;se.encode=qy;se.decode=jy;se.fromJSON=Ey;Vn.encode=Ky;Vn.decode=Ly;Vn.fromJSON=wp;wr.encode=Jy;wr.decode=Ry;wr.fromJSON=Ay;Ki.encode=Qy;Ki.decode=Vy;mr.encode=Gy;mr.decode=Uy;function jy(n,t=new Map,e=Fe.V5){const r=Hy(n,t);return new se(r,qs(n),t,e)}function Ly(n,t=Fe.V5){if(n.compression()!==null)throw new Error("Record batch compression not implemented");return new Vn(n.length(),zy(n),Wy(n,t))}function Ry(n,t=Fe.V5){return new wr(Vn.decode(n.data(),t),n.id(),n.isDelta())}function Uy(n){return new mr(n.offset(),n.length())}function Vy(n){return new Ki(n.length(),n.nullCount())}function zy(n){const t=[];for(let e,r=-1,i=-1,o=n.nodesLength();++r<o;)(e=n.nodes(r))&&(t[++i]=Ki.decode(e));return t}function Wy(n,t){const e=[];for(let r,i=-1,o=-1,s=n.buffersLength();++i<s;)(r=n.buffers(i))&&(t<Fe.V4&&(r.bb_pos+=8*(i+1)),e[++o]=mr.decode(r));return e}function Hy(n,t){const e=[];for(let r,i=-1,o=-1,s=n.fieldsLength();++i<s;)(r=n.fields(i))&&(e[++o]=Ie.decode(r,t));return e}function Iu(n,t){const e=[];for(let r,i=-1,o=-1,s=n.childrenLength();++i<s;)(r=n.children(i))&&(e[++o]=Ie.decode(r,t));return e}function xy(n,t){let e,r,i,o,s,a;return!t||!(a=n.dictionary())?(i=ku(n,Iu(n,t)),r=new Ie(n.name(),i,n.nullable(),qs(n))):t.has(e=Se(a.id()))?(o=(o=a.indexType())?Ou(o):new Qo,s=new Ui(t.get(e),o,e,a.isOrdered()),r=new Ie(n.name(),s,n.nullable(),qs(n))):(o=(o=a.indexType())?Ou(o):new Qo,t.set(e,i=ku(n,Iu(n,t))),s=new Ui(i,o,e,a.isOrdered()),r=new Ie(n.name(),s,n.nullable(),qs(n))),r||null}function qs(n){const t=new Map;if(n)for(let e,r,i=-1,o=Math.trunc(n.customMetadataLength());++i<o;)(e=n.customMetadata(i))&&(r=e.key())!=null&&t.set(r,e.value());return t}function Ou(n){return new oi(n.isSigned(),n.bitWidth())}function ku(n,t){const e=n.typeType();switch(e){case pe.NONE:return new Lr;case pe.Null:return new Lr;case pe.Binary:return new na;case pe.LargeBinary:return new ra;case pe.Utf8:return new ia;case pe.LargeUtf8:return new oa;case pe.Bool:return new sa;case pe.List:return new ha((t||[])[0]);case pe.Struct_:return new nn(t||[])}switch(e){case pe.Int:{const r=n.type(new gn);return new oi(r.isSigned(),r.bitWidth())}case pe.FloatingPoint:{const r=n.type(new Xn);return new ea(r.precision())}case pe.Decimal:{const r=n.type(new yi);return new aa(r.scale(),r.precision(),r.bitWidth())}case pe.Date:{const r=n.type(new Rs);return new la(r.unit())}case pe.Time:{const r=n.type(new Sn);return new ca(r.unit(),r.bitWidth())}case pe.Timestamp:{const r=n.type(new In);return new ua(r.unit(),r.timezone())}case pe.Interval:{const r=n.type(new Zn);return new fa(r.unit())}case pe.Duration:{const r=n.type(new Us);return new da(r.unit())}case pe.Union:{const r=n.type(new un);return new ma(r.mode(),r.typeIdsArray()||[],t||[])}case pe.FixedSizeBinary:{const r=n.type(new Vs);return new pa(r.byteWidth())}case pe.FixedSizeList:{const r=n.type(new zs);return new ga(r.listSize(),(t||[])[0])}case pe.Map:{const r=n.type(new Ws);return new _a((t||[])[0],r.keysSorted())}}throw new Error(`Unrecognized type: "${pe[e]}" (${e})`)}function qy(n,t){const e=t.fields.map(o=>Ie.encode(n,o));Jn.startFieldsVector(n,e.length);const r=Jn.createFieldsVector(n,e),i=t.metadata&&t.metadata.size>0?Jn.createCustomMetadataVector(n,[...t.metadata].map(([o,s])=>{const a=n.createString(`${o}`),l=n.createString(`${s}`);return Le.startKeyValue(n),Le.addKey(n,a),Le.addValue(n,l),Le.endKeyValue(n)})):-1;return Jn.startSchema(n),Jn.addFields(n,r),Jn.addEndianness(n,Xy?Ri.Little:Ri.Big),i!==-1&&Jn.addCustomMetadata(n,i),Jn.endSchema(n)}function Yy(n,t){let e=-1,r=-1,i=-1;const o=t.type;let s=t.typeId;ft.isDictionary(o)?(s=o.dictionary.typeId,i=rl.visit(o,n),r=rl.visit(o.dictionary,n)):r=rl.visit(o,n);const a=(o.children||[]).map(u=>Ie.encode(n,u)),l=vn.createChildrenVector(n,a),c=t.metadata&&t.metadata.size>0?vn.createCustomMetadataVector(n,[...t.metadata].map(([u,f])=>{const h=n.createString(`${u}`),d=n.createString(`${f}`);return Le.startKeyValue(n),Le.addKey(n,h),Le.addValue(n,d),Le.endKeyValue(n)})):-1;return t.name&&(e=n.createString(t.name)),vn.startField(n),vn.addType(n,r),vn.addTypeType(n,s),vn.addChildren(n,l),vn.addNullable(n,!!t.nullable),e!==-1&&vn.addName(n,e),i!==-1&&vn.addDictionary(n,i),c!==-1&&vn.addCustomMetadata(n,c),vn.endField(n)}function Ky(n,t){const e=t.nodes||[],r=t.buffers||[];ur.startNodesVector(n,e.length);for(const s of e.slice().reverse())Ki.encode(n,s);const i=n.endVector();ur.startBuffersVector(n,r.length);for(const s of r.slice().reverse())mr.encode(n,s);const o=n.endVector();return ur.startRecordBatch(n),ur.addLength(n,BigInt(t.length)),ur.addNodes(n,i),ur.addBuffers(n,o),ur.endRecordBatch(n)}function Jy(n,t){const e=Vn.encode(n,t.data);return _i.startDictionaryBatch(n),_i.addId(n,BigInt(t.id)),_i.addIsDelta(n,t.isDelta),_i.addData(n,e),_i.endDictionaryBatch(n)}function Qy(n,t){return Vh.createFieldNode(n,BigInt(t.length),BigInt(t.nullCount))}function Gy(n,t){return Uh.createBuffer(n,BigInt(t.offset),BigInt(t.length))}const Xy=(()=>{const n=new ArrayBuffer(2);return new DataView(n).setInt16(0,256,!0),new Int16Array(n)[0]===256})(),yc=n=>`Expected ${oe[n]} Message in stream, but was null or length 0.`,vc=n=>`Header pointer of flatbuffer-encoded ${oe[n]} Message is null or length 0.`,Op=(n,t)=>`Expected to read ${n} metadata bytes, but only read ${t}.`,kp=(n,t)=>`Expected to read ${n} bytes for message body, but only read ${t}.`;class Dp{constructor(t){this.source=t instanceof wa?t:new wa(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||t.value===-1&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?Ae:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let e;if((e=this.next()).done)return null;if(t!=null&&e.value.headerType!==t)throw new Error(yc(t));return e.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=Gt(this.source.read(t));if(e.byteLength<t)throw new Error(kp(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}readSchema(t=!1){const e=oe.Schema,r=this.readMessage(e),i=r==null?void 0:r.header();if(t&&!i)throw new Error(vc(e));return i}readMetadataLength(){const t=this.source.read(La),e=t&&new Li(t),r=(e==null?void 0:e.readInt32(0))||0;return{done:r===0,value:r}}readMetadata(t){const e=this.source.read(t);if(!e)return Ae;if(e.byteLength<t)throw new Error(Op(t,e.byteLength));return{done:!1,value:Rn.decode(e)}}}class Zy{constructor(t,e){this.source=t instanceof zi?t:Ph(t)?new Sa(t,e):new zi(t)}[Symbol.asyncIterator](){return this}next(){return Mt(this,void 0,void 0,function*(){let t;return(t=yield this.readMetadataLength()).done||t.value===-1&&(t=yield this.readMetadataLength()).done||(t=yield this.readMetadata(t.value)).done?Ae:t})}throw(t){return Mt(this,void 0,void 0,function*(){return yield this.source.throw(t)})}return(t){return Mt(this,void 0,void 0,function*(){return yield this.source.return(t)})}readMessage(t){return Mt(this,void 0,void 0,function*(){let e;if((e=yield this.next()).done)return null;if(t!=null&&e.value.headerType!==t)throw new Error(yc(t));return e.value})}readMessageBody(t){return Mt(this,void 0,void 0,function*(){if(t<=0)return new Uint8Array(0);const e=Gt(yield this.source.read(t));if(e.byteLength<t)throw new Error(kp(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()})}readSchema(){return Mt(this,arguments,void 0,function*(t=!1){const e=oe.Schema,r=yield this.readMessage(e),i=r==null?void 0:r.header();if(t&&!i)throw new Error(vc(e));return i})}readMetadataLength(){return Mt(this,void 0,void 0,function*(){const t=yield this.source.read(La),e=t&&new Li(t),r=(e==null?void 0:e.readInt32(0))||0;return{done:r===0,value:r}})}readMetadata(t){return Mt(this,void 0,void 0,function*(){const e=yield this.source.read(t);if(!e)return Ae;if(e.byteLength<t)throw new Error(Op(t,e.byteLength));return{done:!1,value:Rn.decode(e)}})}}class $y extends Dp{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof _u?t:new _u(t)}next(){const{_json:t}=this;if(!this._schema)return this._schema=!0,{done:!1,value:Rn.fromJSON(t.schema,oe.Schema)};if(this._dictionaryIndex<t.dictionaries.length){const e=t.dictionaries[this._dictionaryIndex++];return this._body=e.data.columns,{done:!1,value:Rn.fromJSON(e,oe.DictionaryBatch)}}if(this._batchIndex<t.batches.length){const e=t.batches[this._batchIndex++];return this._body=e.columns,{done:!1,value:Rn.fromJSON(e,oe.RecordBatch)}}return this._body=[],Ae}readMessageBody(t){return e(this._body);function e(r){return(r||[]).reduce((i,o)=>[...i,...o.VALIDITY&&[o.VALIDITY]||[],...o.TYPE_ID&&[o.TYPE_ID]||[],...o.OFFSET&&[o.OFFSET]||[],...o.DATA&&[o.DATA]||[],...e(o.children)],[])}}readMessage(t){let e;if((e=this.next()).done)return null;if(t!=null&&e.value.headerType!==t)throw new Error(yc(t));return e.value}readSchema(){const t=oe.Schema,e=this.readMessage(t),r=e==null?void 0:e.header();if(!e||!r)throw new Error(vc(t));return r}}const La=4,jl="ARROW1",Ia=new Uint8Array(jl.length);for(let n=0;n<jl.length;n+=1)Ia[n]=jl.codePointAt(n);function wc(n,t=0){for(let e=-1,r=Ia.length;++e<r;)if(Ia[e]!==n[t+e])return!1;return!0}const ls=Ia.length,Ep=ls+La,tv=ls*2+La;class gr extends mp{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const e=this._impl.open(t);return qo(e)?e.then(()=>this):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return wn.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return wn.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof gr?t:Dl(t)?iv(t):Ph(t)?av(t):qo(t)?Mt(this,void 0,void 0,function*(){return yield gr.from(yield t)}):Mh(t)||rc(t)||Ch(t)||nc(t)?sv(new zi(t)):ov(new wa(t))}static readAll(t){return t instanceof gr?t.isSync()?Du(t):Eu(t):Dl(t)||ArrayBuffer.isView(t)||Ca(t)||Bh(t)?Du(t):Eu(t)}}class Oa extends gr{constructor(t){super(t),this._impl=t}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return $n(this,arguments,function*(){yield xt(yield*js(Bi(this[Symbol.iterator]())))})}}class ka extends gr{constructor(t){super(t),this._impl=t}readAll(){return Mt(this,void 0,void 0,function*(){var t,e,r,i;const o=new Array;try{for(var s=!0,a=Bi(this),l;l=yield a.next(),t=l.done,!t;s=!0){i=l.value,s=!1;const c=i;o.push(c)}}catch(c){e={error:c}}finally{try{!s&&!t&&(r=a.return)&&(yield r.call(a))}finally{if(e)throw e.error}}return o})}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class Ap extends Oa{constructor(t){super(t),this._impl=t}}class ev extends ka{constructor(t){super(t),this._impl=t}}class Tp{get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}constructor(t=new Map){this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,e){const r=this._loadVectors(t,e,this.schema.fields),i=zt({type:new nn(this.schema.fields),length:t.length,children:r});return new Ln(this.schema,i)}_loadDictionaryBatch(t,e){const{id:r,isDelta:i}=t,{dictionaries:o,schema:s}=this,a=o.get(r),l=s.dictionaries.get(r),c=this._loadVectors(t.data,e,[l]);return(a&&i?a.concat(new de(c)):new de(c)).memoize()}_loadVectors(t,e,r){return new gp(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(r)}}class Da extends Tp{constructor(t,e){super(e),this._reader=Dl(t)?new $y(this._handle=t):new Dp(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=Pp(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):Ae}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):Ae}next(){if(this.closed)return Ae;let t;const{_reader:e}=this;for(;t=this._readNextMessageAndValidate();)if(t.isSchema())this.reset(t.header());else if(t.isRecordBatch()){this._recordBatchIndex++;const r=t.header(),i=e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(r,i)}}else if(t.isDictionaryBatch()){this._dictionaryIndex++;const r=t.header(),i=e.readMessageBody(t.bodyLength),o=this._loadDictionaryBatch(r,i);this.dictionaries.set(r.id,o)}return this.schema&&this._recordBatchIndex===0?(this._recordBatchIndex++,{done:!1,value:new vp(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class Ea extends Tp{constructor(t,e){super(e),this._reader=new Zy(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return Mt(this,void 0,void 0,function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)})}open(t){return Mt(this,void 0,void 0,function*(){return this.closed||(this.autoDestroy=Pp(this,t),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this})}throw(t){return Mt(this,void 0,void 0,function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(t):Ae})}return(t){return Mt(this,void 0,void 0,function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(t):Ae})}next(){return Mt(this,void 0,void 0,function*(){if(this.closed)return Ae;let t;const{_reader:e}=this;for(;t=yield this._readNextMessageAndValidate();)if(t.isSchema())yield this.reset(t.header());else if(t.isRecordBatch()){this._recordBatchIndex++;const r=t.header(),i=yield e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(r,i)}}else if(t.isDictionaryBatch()){this._dictionaryIndex++;const r=t.header(),i=yield e.readMessageBody(t.bodyLength),o=this._loadDictionaryBatch(r,i);this.dictionaries.set(r.id,o)}return this.schema&&this._recordBatchIndex===0?(this._recordBatchIndex++,{done:!1,value:new vp(this.schema)}):yield this.return()})}_readNextMessageAndValidate(t){return Mt(this,void 0,void 0,function*(){return yield this._reader.readMessage(t)})}}class Bp extends Da{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,e){super(t instanceof bu?t:new bu(t),e)}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const e of this._footer.dictionaryBatches())e&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){var e;if(this.closed)return null;this._footer||this.open();const r=(e=this._footer)===null||e===void 0?void 0:e.getRecordBatch(t);if(r&&this._handle.seek(r.offset)){const i=this._reader.readMessage(oe.RecordBatch);if(i!=null&&i.isRecordBatch()){const o=i.header(),s=this._reader.readMessageBody(i.bodyLength);return this._loadRecordBatch(o,s)}}return null}_readDictionaryBatch(t){var e;const r=(e=this._footer)===null||e===void 0?void 0:e.getDictionaryBatch(t);if(r&&this._handle.seek(r.offset)){const i=this._reader.readMessage(oe.DictionaryBatch);if(i!=null&&i.isDictionaryBatch()){const o=i.header(),s=this._reader.readMessageBody(i.bodyLength),a=this._loadDictionaryBatch(o,s);this.dictionaries.set(o.id,a)}}}_readFooter(){const{_handle:t}=this,e=t.size-Ep,r=t.readInt32(e),i=t.readAt(e-r,r);return mc.decode(i)}_readNextMessageAndValidate(t){var e;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const r=(e=this._footer)===null||e===void 0?void 0:e.getRecordBatch(this._recordBatchIndex);if(r&&this._handle.seek(r.offset))return this._reader.readMessage(t)}return null}}class nv extends Ea{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,...e){const r=typeof e[0]!="number"?e.shift():void 0,i=e[0]instanceof Map?e.shift():void 0;super(t instanceof Sa?t:new Sa(t,r),i)}isFile(){return!0}isAsync(){return!0}open(t){const e=Object.create(null,{open:{get:()=>super.open}});return Mt(this,void 0,void 0,function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const r of this._footer.dictionaryBatches())r&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield e.open.call(this,t)})}readRecordBatch(t){return Mt(this,void 0,void 0,function*(){var e;if(this.closed)return null;this._footer||(yield this.open());const r=(e=this._footer)===null||e===void 0?void 0:e.getRecordBatch(t);if(r&&(yield this._handle.seek(r.offset))){const i=yield this._reader.readMessage(oe.RecordBatch);if(i!=null&&i.isRecordBatch()){const o=i.header(),s=yield this._reader.readMessageBody(i.bodyLength);return this._loadRecordBatch(o,s)}}return null})}_readDictionaryBatch(t){return Mt(this,void 0,void 0,function*(){var e;const r=(e=this._footer)===null||e===void 0?void 0:e.getDictionaryBatch(t);if(r&&(yield this._handle.seek(r.offset))){const i=yield this._reader.readMessage(oe.DictionaryBatch);if(i!=null&&i.isDictionaryBatch()){const o=i.header(),s=yield this._reader.readMessageBody(i.bodyLength),a=this._loadDictionaryBatch(o,s);this.dictionaries.set(o.id,a)}}})}_readFooter(){return Mt(this,void 0,void 0,function*(){const{_handle:t}=this;t._pending&&(yield t._pending);const e=t.size-Ep,r=yield t.readInt32(e),i=yield t.readAt(e-r,r);return mc.decode(i)})}_readNextMessageAndValidate(t){return Mt(this,void 0,void 0,function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer.getRecordBatch(this._recordBatchIndex);if(e&&(yield this._handle.seek(e.offset)))return yield this._reader.readMessage(t)}return null})}}class rv extends Da{constructor(t,e){super(t,e)}_loadVectors(t,e,r){return new py(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(r)}}function Pp(n,t){return t&&typeof t.autoDestroy=="boolean"?t.autoDestroy:n.autoDestroy}function*Du(n){const t=gr.from(n);try{if(!t.open({autoDestroy:!1}).closed)do yield t;while(!t.reset().open().closed)}finally{t.cancel()}}function Eu(n){return $n(this,arguments,function*(){const e=yield xt(gr.from(n));try{if(!(yield xt(e.open({autoDestroy:!1}))).closed)do yield yield xt(e);while(!(yield xt(e.reset().open())).closed)}finally{yield xt(e.cancel())}})}function iv(n){return new Oa(new rv(n))}function ov(n){const t=n.peek(ls+7&-8);return t&&t.byteLength>=4?wc(t)?new Ap(new Bp(n.read())):new Oa(new Da(n)):new Oa(new Da(function*(){}()))}function sv(n){return Mt(this,void 0,void 0,function*(){const t=yield n.peek(ls+7&-8);return t&&t.byteLength>=4?wc(t)?new Ap(new Bp(yield n.read())):new ka(new Ea(n)):new ka(new Ea(function(){return $n(this,arguments,function*(){})}()))})}function av(n){return Mt(this,void 0,void 0,function*(){const{size:t}=yield n.stat(),e=new Sa(n,t);return t>=tv&&wc(yield e.readAt(0,ls+7&-8))?new ev(new nv(e)):new ka(new Ea(e))})}function Mp(n){const t=gr.from(n);return qo(t)?t.then(e=>Mp(e)):t.isAsync()?t.readAll().then(e=>new On(e)):new On(t.readAll())}const Au=Symbol("Unset"),Tu=Symbol("IsSetTracked"),Bu=Symbol("GetModKeys"),Pu=Symbol("GetOwnKey"),lv=Symbol("GetOwnPath"),Mu=Symbol("GetParent"),Ll=(n={},t={},e=void 0,r=void 0)=>{if(e&&!e[Tu])throw new Error("SetTracked parent must be SetTracked");const i=Object.assign(()=>{},t??{}),o=Object.keys(i),s=new Proxy(i,{get(a,l){switch(l){case Au:return!(e!=null&&e[Bu].includes(r));case Bu:return o;case Pu:return r;case Mu:return e;case lv:{const c=[r];let u=e;for(;u!==void 0;)c.unshift(u[Pu]),u=u[Mu];return c.join(".")}case Tu:return!0;case"toJSON":return()=>({...a});case"toString":case"toPrimitive":case Symbol.toPrimitive:return s[Au]?r&&r in n?()=>n[r]:()=>"":t.toString.bind(t);default:return l in a||(a[l]=Ll(n,void 0,s,l)),a[l]}},set(a,l,c){return o.push(l),typeof c=="object"&&(c=Ll(n,c,s,l)),a[l]=c,!0}});return s};var Np={exports:{}};(function(n){(function(t){function e(y,T){var S=(y&65535)+(T&65535),F=(y>>16)+(T>>16)+(S>>16);return F<<16|S&65535}function r(y,T){return y<<T|y>>>32-T}function i(y,T,S,F,B,N){return e(r(e(e(T,y),e(F,N)),B),S)}function o(y,T,S,F,B,N,R){return i(T&S|~T&F,y,T,B,N,R)}function s(y,T,S,F,B,N,R){return i(T&F|S&~F,y,T,B,N,R)}function a(y,T,S,F,B,N,R){return i(T^S^F,y,T,B,N,R)}function l(y,T,S,F,B,N,R){return i(S^(T|~F),y,T,B,N,R)}function c(y,T){y[T>>5]|=128<<T%32,y[(T+64>>>9<<4)+14]=T;var S,F,B,N,R,P=1732584193,M=-271733879,j=-1732584194,V=271733878;for(S=0;S<y.length;S+=16)F=P,B=M,N=j,R=V,P=o(P,M,j,V,y[S],7,-680876936),V=o(V,P,M,j,y[S+1],12,-389564586),j=o(j,V,P,M,y[S+2],17,606105819),M=o(M,j,V,P,y[S+3],22,-1044525330),P=o(P,M,j,V,y[S+4],7,-176418897),V=o(V,P,M,j,y[S+5],12,1200080426),j=o(j,V,P,M,y[S+6],17,-1473231341),M=o(M,j,V,P,y[S+7],22,-45705983),P=o(P,M,j,V,y[S+8],7,1770035416),V=o(V,P,M,j,y[S+9],12,-1958414417),j=o(j,V,P,M,y[S+10],17,-42063),M=o(M,j,V,P,y[S+11],22,-1990404162),P=o(P,M,j,V,y[S+12],7,1804603682),V=o(V,P,M,j,y[S+13],12,-40341101),j=o(j,V,P,M,y[S+14],17,-1502002290),M=o(M,j,V,P,y[S+15],22,1236535329),P=s(P,M,j,V,y[S+1],5,-165796510),V=s(V,P,M,j,y[S+6],9,-1069501632),j=s(j,V,P,M,y[S+11],14,643717713),M=s(M,j,V,P,y[S],20,-373897302),P=s(P,M,j,V,y[S+5],5,-701558691),V=s(V,P,M,j,y[S+10],9,38016083),j=s(j,V,P,M,y[S+15],14,-660478335),M=s(M,j,V,P,y[S+4],20,-405537848),P=s(P,M,j,V,y[S+9],5,568446438),V=s(V,P,M,j,y[S+14],9,-1019803690),j=s(j,V,P,M,y[S+3],14,-187363961),M=s(M,j,V,P,y[S+8],20,1163531501),P=s(P,M,j,V,y[S+13],5,-1444681467),V=s(V,P,M,j,y[S+2],9,-51403784),j=s(j,V,P,M,y[S+7],14,1735328473),M=s(M,j,V,P,y[S+12],20,-1926607734),P=a(P,M,j,V,y[S+5],4,-378558),V=a(V,P,M,j,y[S+8],11,-2022574463),j=a(j,V,P,M,y[S+11],16,1839030562),M=a(M,j,V,P,y[S+14],23,-35309556),P=a(P,M,j,V,y[S+1],4,-1530992060),V=a(V,P,M,j,y[S+4],11,1272893353),j=a(j,V,P,M,y[S+7],16,-155497632),M=a(M,j,V,P,y[S+10],23,-1094730640),P=a(P,M,j,V,y[S+13],4,681279174),V=a(V,P,M,j,y[S],11,-358537222),j=a(j,V,P,M,y[S+3],16,-722521979),M=a(M,j,V,P,y[S+6],23,76029189),P=a(P,M,j,V,y[S+9],4,-640364487),V=a(V,P,M,j,y[S+12],11,-421815835),j=a(j,V,P,M,y[S+15],16,530742520),M=a(M,j,V,P,y[S+2],23,-995338651),P=l(P,M,j,V,y[S],6,-198630844),V=l(V,P,M,j,y[S+7],10,1126891415),j=l(j,V,P,M,y[S+14],15,-1416354905),M=l(M,j,V,P,y[S+5],21,-57434055),P=l(P,M,j,V,y[S+12],6,1700485571),V=l(V,P,M,j,y[S+3],10,-1894986606),j=l(j,V,P,M,y[S+10],15,-1051523),M=l(M,j,V,P,y[S+1],21,-2054922799),P=l(P,M,j,V,y[S+8],6,1873313359),V=l(V,P,M,j,y[S+15],10,-30611744),j=l(j,V,P,M,y[S+6],15,-1560198380),M=l(M,j,V,P,y[S+13],21,1309151649),P=l(P,M,j,V,y[S+4],6,-145523070),V=l(V,P,M,j,y[S+11],10,-1120210379),j=l(j,V,P,M,y[S+2],15,718787259),M=l(M,j,V,P,y[S+9],21,-343485551),P=e(P,F),M=e(M,B),j=e(j,N),V=e(V,R);return[P,M,j,V]}function u(y){var T,S="",F=y.length*32;for(T=0;T<F;T+=8)S+=String.fromCharCode(y[T>>5]>>>T%32&255);return S}function f(y){var T,S=[];for(S[(y.length>>2)-1]=void 0,T=0;T<S.length;T+=1)S[T]=0;var F=y.length*8;for(T=0;T<F;T+=8)S[T>>5]|=(y.charCodeAt(T/8)&255)<<T%32;return S}function h(y){return u(c(f(y),y.length*8))}function d(y,T){var S,F=f(y),B=[],N=[],R;for(B[15]=N[15]=void 0,F.length>16&&(F=c(F,y.length*8)),S=0;S<16;S+=1)B[S]=F[S]^909522486,N[S]=F[S]^1549556828;return R=c(B.concat(f(T)),512+T.length*8),u(c(N.concat(R),640))}function g(y){var T="0123456789abcdef",S="",F,B;for(B=0;B<y.length;B+=1)F=y.charCodeAt(B),S+=T.charAt(F>>>4&15)+T.charAt(F&15);return S}function m(y){return unescape(encodeURIComponent(y))}function p(y){return h(m(y))}function v(y){return g(p(y))}function b(y,T){return d(m(y),m(T))}function _(y,T){return g(b(y,T))}function O(y,T,S){return T?S?b(T,y):_(T,y):S?p(y):v(y)}n.exports?n.exports=O:t.md5=O})(Ug)})(Np);var cv=Np.exports;const Nu=Vg(cv),uv=!0,Cp=!0,fv="always",dv=async()=>{let n={};{const t=await fetch(Wt("/data/manifest.json"));t.ok&&({renderedFiles:n}=await t.json())}await ko(Hg),Object.keys(n??{}).length===0?console.warn('No sources found, execute "npm run sources" to generate'.trim()):(await ko(vh,n,{addBasePath:Wt}),await ko(xg,Object.keys(n)))},Cu=ko(dv);async function hv(n,t,e){const r=await e(Wt(`/api/${n}/${t}/all-queries.json`));if(!r.ok)return{};const i=await r.json(),o=await Promise.all(Object.entries(i).map(async([s,a])=>{const l=await e(Wt(`/api/prerendered_queries/${a}.arrow`));if(!l.ok)return null;const c=await Mp(l);return[s,zg(c)]}));return Object.fromEntries(o.filter(Boolean))}const mv=["/settings","/explore"],Fu=new Map,pv=async({fetch:n,route:t,params:e,url:r})=>{var g,m,p;const[{customFormattingSettings:i},o,s]=await Promise.all([n(Wt("/api/customFormattingSettings.json/GET.json")).then(v=>v.json()),n(Wt("/api/pagesManifest.json")).then(v=>v.json()),n(Wt(`/api/${t.id}/evidencemeta.json`)).then(v=>v.json()).catch(()=>({queries:[]}))]),a=Nu(t.id),l=Nu(Object.entries(e).sort().map(([v,b])=>`${v}${b}`).join("")),c=t.id&&mv.every(v=>!t.id.startsWith(v));let u={};const{inputs:f=Ll({label:"",value:"(SELECT NULL WHERE 0 /* An Input has not been set */)"})}=Fu.get(r.pathname)??{};Fu.has(r.pathname),c&&Cp&&(u=await hv(a,l,n));function h(v,{query_name:b,callback:_=O=>O}={}){return(async()=>{await Cu;const O=await Wg(v);return _(O)})()}let d=o;for(const v of(t.id??"").split("/").slice(1)){if(d=d.children[v],!d)break;if((g=d.frontMatter)!=null&&g.title)d.title=d.frontMatter.title;else if((m=d.frontMatter)!=null&&m.breadcrumb){let{breadcrumb:b}=d.frontMatter;for(const[_,O]of Object.entries(e))b=b.replaceAll(`\${params.${_}}`,O);d.title=(p=(await h(b))[0])==null?void 0:p.breadcrumb}}return{__db:{query:h,async load(){return Cu},async updateParquetURLs(v){const{renderedFiles:b}=JSON.parse(v);await ko(vh,b,{addBasePath:Wt})}},inputs:f,data:u,customFormattingSettings:i,isUserPage:c,evidencemeta:s,pagesManifest:o}},RO=Object.freeze(Object.defineProperty({__proto__:null,load:pv,prerender:Cp,ssr:uv,trailingSlash:fv},Symbol.toStringTag,{value:"Module"}));async function gv(n){const{prop:t,defaultEl:e}=n;if(await Promise.all([ni(1),ho]),t===void 0){e==null||e.focus();return}const r=$g(t)?t(e):t;if(typeof r=="string"){const i=document.querySelector(r);if(!Nt(i))return;i.focus()}else Nt(r)&&r.focus()}const _v={ltr:[...wl,ln.ARROW_RIGHT]},bv={ltr:[ln.ARROW_LEFT]},ju=["menu","trigger"],yv={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,typeahead:!0,closeOnItemClick:!0,onOutsideClick:void 0};function vv(n){const{name:t,selector:e}=e_(n.selector),{preventScroll:r,arrowSize:i,positioning:o,closeOnEscape:s,closeOnOutsideClick:a,portal:l,forceVisible:c,typeahead:u,loop:f,closeFocus:h,disableFocusFirstItem:d,closeOnItemClick:g,onOutsideClick:m}=n.rootOptions,p=n.rootOpen,v=n.rootActiveTrigger,b=n.nextFocusable,_=n.prevFocusable,O=tn.writable(!1),y=tn(Ke(0)),T=tn(Ke(null)),S=tn(Ke("right")),F=tn(Ke(null)),B=tn(mo([S,T],([z,$])=>x=>z===($==null?void 0:$.side)&&wv(x,$==null?void 0:$.area))),{typed:N,handleTypeaheadSearch:R}=D_(),P=Do({...Kc(ju),...n.ids}),M=Jc({open:p,forceVisible:c,activeTrigger:v}),j=an(t(),{stores:[M,l,P.menu,P.trigger],returned:([z,$,x,tt])=>({role:"menu",hidden:z?void 0:!0,style:ro({display:z?void 0:"none"}),id:x,"aria-labelledby":tt,"data-state":z?"open":"closed","data-portal":t_($),tabindex:-1}),action:z=>{let $=io;const x=Yn([M,v,o,a,l,s],([it,Qt,ke,qt,Et,At])=>{$(),!(!it||!Qt)&&ho().then(()=>{$(),ao(z,e),$=Xc(z,{anchorElement:Qt,open:p,options:{floating:ke,modal:{closeOnInteractOutside:qt,shouldCloseOnInteractOutside:G=>{var ct;return(ct=m.get())==null||ct(G),!(G.defaultPrevented||Nt(Qt)&&Qt.contains(G.target))},onClose:()=>{p.set(!1),Qt.focus()},open:it},portal:Zc(z,Et),escapeKeydown:At?void 0:null}}).destroy})}),tt=ar($t(z,"keydown",it=>{const Qt=it.target,ke=it.currentTarget;if(!Nt(Qt)||!Nt(ke)||!(Qt.closest('[role="menu"]')===ke))return;if(Qc.includes(it.key)&&Ru(it,f.get()??!1),it.key===ln.TAB){it.preventDefault(),p.set(!1),Lu(it,b,_);return}const Et=it.key.length===1;!(it.ctrlKey||it.altKey||it.metaKey)&&Et&&u.get()===!0&&R(it.key,Xr(ke))}));return{destroy(){x(),tt(),$()}}}}),V=an(t("trigger"),{stores:[p,P.menu,P.trigger],returned:([z,$,x])=>({"aria-controls":$,"aria-expanded":z,"data-state":z?"open":"closed",id:x,tabindex:0}),action:z=>(Os(z),v.update(x=>x||z),{destroy:ar($t(z,"click",x=>{const tt=p.get(),it=x.currentTarget;Nt(it)&&(Kt(it),tt||x.preventDefault())}),$t(z,"keydown",x=>{const tt=x.currentTarget;if(!Nt(tt)||!(wl.includes(x.key)||x.key===ln.ARROW_DOWN))return;x.preventDefault(),Kt(tt);const it=tt.getAttribute("aria-controls");if(!it)return;const Qt=document.getElementById(it);if(!Qt)return;const ke=Xr(Qt);ke.length&&$e(ke[0])}))})}),U=an(t("arrow"),{stores:i,returned:z=>({"data-arrow":!0,style:ro({position:"absolute",width:`var(--arrow-size, ${z}px)`,height:`var(--arrow-size, ${z}px)`})})}),H=an(t("overlay"),{stores:[M],returned:([z])=>({hidden:z?void 0:!0,tabindex:-1,style:ro({display:z?void 0:"none"}),"aria-hidden":"true","data-state":Ov(z)}),action:z=>{let $=io;if(s.get()){const tt=n_(z,{handler:()=>{p.set(!1);const it=v.get();it&&it.focus()}});tt&&tt.destroy&&($=tt.destroy)}const x=Yn([l],([tt])=>{if(tt===null)return io;const it=Zc(z,tt);return it===null?io:r_(z,it).destroy});return{destroy(){$(),x()}}}}),D=an(t("item"),{returned:()=>({role:"menuitem",tabindex:-1,"data-orientation":"vertical"}),action:z=>(ao(z,e),Os(z),{destroy:ar($t(z,"pointerdown",x=>{const tt=x.currentTarget;if(Nt(tt)&&lr(tt)){x.preventDefault();return}}),$t(z,"click",x=>{const tt=x.currentTarget;if(Nt(tt)){if(lr(tt)){x.preventDefault();return}if(x.defaultPrevented){$e(tt);return}g.get()&&ni(1).then(()=>{p.set(!1)})}}),$t(z,"keydown",x=>{et(x)}),$t(z,"pointermove",x=>{Hn(x)}),$t(z,"pointerleave",x=>{Wr(x)}),$t(z,"focusin",x=>{Be(x)}),$t(z,"focusout",x=>{qe(x)}))})}),X=an(t("group"),{returned:()=>z=>({role:"group","aria-labelledby":z})}),Q=an(t("group-label"),{returned:()=>z=>({id:z})}),Z={defaultChecked:!1,disabled:!1},Dt=z=>{const $={...Z,...z},x=$.checked??Ke($.defaultChecked??null),tt=Ls(x,$.onCheckedChange),it=Ke($.disabled),Qt=an(t("checkbox-item"),{stores:[tt,it],returned:([Et,At])=>({role:"menuitemcheckbox",tabindex:-1,"data-orientation":"vertical","aria-checked":dt(Et)?"mixed":Et?"true":"false","data-disabled":Ga(At),"data-state":Jt(Et)}),action:Et=>(ao(Et,e),Os(Et),{destroy:ar($t(Et,"pointerdown",G=>{const ct=G.currentTarget;if(Nt(ct)&&lr(ct)){G.preventDefault();return}}),$t(Et,"click",G=>{const ct=G.currentTarget;if(Nt(ct)){if(lr(ct)){G.preventDefault();return}if(G.defaultPrevented){$e(ct);return}tt.update(me=>dt(me)?!0:!me),g.get()&&ho().then(()=>{p.set(!1)})}}),$t(Et,"keydown",G=>{et(G)}),$t(Et,"pointermove",G=>{const ct=G.currentTarget;if(Nt(ct)){if(lr(ct)){Ve(G);return}Hn(G,ct)}}),$t(Et,"pointerleave",G=>{Wr(G)}),$t(Et,"focusin",G=>{Be(G)}),$t(Et,"focusout",G=>{qe(G)}))})}),ke=mo(tt,Et=>Et===!0),qt=mo(tt,Et=>Et==="indeterminate");return{elements:{checkboxItem:Qt},states:{checked:tt},helpers:{isChecked:ke,isIndeterminate:qt},options:{disabled:it}}},Bt=(z={})=>{const $=z.value??Ke(z.defaultValue??null),x=Ls($,z.onValueChange),tt=an(t("radio-group"),{returned:()=>({role:"group"})}),it={disabled:!1},Qt=an(t("radio-item"),{stores:[x],returned:([qt])=>Et=>{const{value:At,disabled:G}={...it,...Et},ct=qt===At;return{disabled:G,role:"menuitemradio","data-state":ct?"checked":"unchecked","aria-checked":ct,"data-disabled":Ga(G),"data-value":At,"data-orientation":"vertical",tabindex:-1}},action:qt=>(ao(qt,e),{destroy:ar($t(qt,"pointerdown",At=>{const G=At.currentTarget;if(!Nt(G))return;const ct=qt.dataset.value;if(qt.dataset.disabled||ct===void 0){At.preventDefault();return}}),$t(qt,"click",At=>{const G=At.currentTarget;if(!Nt(G))return;const ct=qt.dataset.value;if(qt.dataset.disabled||ct===void 0){At.preventDefault();return}if(At.defaultPrevented){if(!Nt(G))return;$e(G);return}x.set(ct),g.get()&&ho().then(()=>{p.set(!1)})}),$t(qt,"keydown",At=>{et(At)}),$t(qt,"pointermove",At=>{const G=At.currentTarget;if(!Nt(G))return;const ct=qt.dataset.value;if(qt.dataset.disabled||ct===void 0){Ve(At);return}Hn(At,G)}),$t(qt,"pointerleave",At=>{Wr(At)}),$t(qt,"focusin",At=>{Be(At)}),$t(qt,"focusout",At=>{qe(At)}))})}),ke=mo(x,qt=>Et=>qt===Et);return{elements:{radioGroup:tt,radioItem:Qt},states:{value:x},helpers:{isChecked:ke}}},{elements:{root:Ct}}=Av({orientation:"horizontal"}),K={...yv,disabled:!1,positioning:{placement:"right-start",gutter:8}},Oe=z=>{const $={...K,...z},x=$.open??Ke(!1),tt=Ls(x,$==null?void 0:$.onOpenChange),it=Do(Dh($,"ids")),{positioning:Qt,arrowSize:ke,disabled:qt}=it,Et=tn(Ke(null)),At=tn(Ke(null)),G=tn(Ke(0)),ct=Do({...Kc(ju),...$.ids});Gc(()=>{const Ft=document.getElementById(ct.trigger.get());Ft&&Et.set(Ft)});const me=Jc({open:tt,forceVisible:c,activeTrigger:Et}),Xe=an(t("submenu"),{stores:[me,ct.menu,ct.trigger],returned:([Ft,Pe,We])=>({role:"menu",hidden:Ft?void 0:!0,style:ro({display:Ft?void 0:"none"}),id:Pe,"aria-labelledby":We,"data-state":Ft?"open":"closed","data-id":Pe,tabindex:-1}),action:Ft=>{let Pe=io;const We=Yn([me,Qt],([ht,ee])=>{if(Pe(),!ht)return;const ve=Et.get();ve&&ho().then(()=>{Pe();const De=ze(ve);Pe=Xc(Ft,{anchorElement:ve,open:tt,options:{floating:ee,portal:Nt(De)?De:void 0,modal:null,focusTrap:null,escapeKeydown:null}}).destroy})}),Rt=ar($t(Ft,"keydown",ht=>{if(ht.key===ln.ESCAPE)return;const ee=ht.target,ve=ht.currentTarget;if(!Nt(ee)||!Nt(ve)||!(ee.closest('[role="menu"]')===ve))return;if(Qc.includes(ht.key)){ht.stopImmediatePropagation(),Ru(ht,f.get()??!1);return}const qn=bv.ltr.includes(ht.key),je=ht.ctrlKey||ht.altKey||ht.metaKey,Me=ht.key.length===1;if(qn){const Ne=Et.get();ht.preventDefault(),tt.update(()=>(Ne&&$e(Ne),!1));return}if(ht.key===ln.TAB){ht.preventDefault(),p.set(!1),Lu(ht,b,_);return}!je&&Me&&u.get()===!0&&R(ht.key,Xr(ve))}),$t(Ft,"pointermove",ht=>{mn(ht)}),$t(Ft,"focusout",ht=>{const ee=Et.get();if(O.get()){const ve=ht.target,De=document.getElementById(ct.menu.get());if(!Nt(De)||!Nt(ve))return;!De.contains(ve)&&ve!==ee&&tt.set(!1)}else{const ve=ht.currentTarget,De=ht.relatedTarget;if(!Nt(De)||!Nt(ve))return;!ve.contains(De)&&De!==ee&&tt.set(!1)}}));return{destroy(){We(),Pe(),Rt()}}}}),bn=an(t("subtrigger"),{stores:[tt,qt,ct.menu,ct.trigger],returned:([Ft,Pe,We,Rt])=>({role:"menuitem",id:Rt,tabindex:-1,"aria-controls":We,"aria-expanded":Ft,"data-state":Ft?"open":"closed","data-disabled":Ga(Pe),"aria-haspopop":"menu"}),action:Ft=>{ao(Ft,e),Os(Ft),Et.update(Rt=>Rt||Ft);const Pe=()=>{il(At),window.clearTimeout(G.get()),T.set(null)},We=ar($t(Ft,"click",Rt=>{if(Rt.defaultPrevented)return;const ht=Rt.currentTarget;!Nt(ht)||lr(ht)||($e(ht),tt.get()||tt.update(ee=>ee||(Et.set(ht),!ee)))}),$t(Ft,"keydown",Rt=>{const ht=N.get(),ee=Rt.currentTarget;if(!(!Nt(ee)||lr(ee)||ht.length>0&&Rt.key===ln.SPACE)&&_v.ltr.includes(Rt.key)){if(!tt.get()){ee.click(),Rt.preventDefault();return}const De=ee.getAttribute("aria-controls");if(!De)return;const qn=document.getElementById(De);if(!Nt(qn))return;const je=Xr(qn)[0];$e(je)}}),$t(Ft,"pointermove",Rt=>{if(!so(Rt)||(_n(Rt),Rt.defaultPrevented))return;const ht=Rt.currentTarget;if(!Nt(ht))return;Iv(ct.menu.get())||$e(ht);const ee=At.get();!tt.get()&&!ee&&!lr(ht)&&At.set(window.setTimeout(()=>{tt.update(()=>(Et.set(ht),!0)),il(At)},100))}),$t(Ft,"pointerleave",Rt=>{if(!so(Rt))return;il(At);const ht=document.getElementById(ct.menu.get()),ee=ht==null?void 0:ht.getBoundingClientRect();if(ee){const ve=ht==null?void 0:ht.dataset.side,De=ve==="right",qn=De?-5:5,je=ee[De?"left":"right"],Me=ee[De?"right":"left"];T.set({area:[{x:Rt.clientX+qn,y:Rt.clientY},{x:je,y:ee.top},{x:Me,y:ee.top},{x:Me,y:ee.bottom},{x:je,y:ee.bottom}],side:ve}),window.clearTimeout(G.get()),G.set(window.setTimeout(()=>{T.set(null)},300))}else{if(Nn(Rt),Rt.defaultPrevented)return;T.set(null)}}),$t(Ft,"focusout",Rt=>{const ht=Rt.currentTarget;if(!Nt(ht))return;ci(ht);const ee=Rt.relatedTarget;if(!Nt(ee))return;const ve=ht.getAttribute("aria-controls");if(!ve)return;const De=document.getElementById(ve);De&&!De.contains(ee)&&tt.set(!1)}),$t(Ft,"focusin",Rt=>{Be(Rt)}));return{destroy(){Pe(),We()}}}}),xn=an(t("subarrow"),{stores:ke,returned:Ft=>({"data-arrow":!0,style:ro({position:"absolute",width:`var(--arrow-size, ${Ft}px)`,height:`var(--arrow-size, ${Ft}px)`})})});return Yn([p],([Ft])=>{Ft||(Et.set(null),tt.set(!1))}),Yn([T],([Ft])=>{!gi||Ft||window.clearTimeout(G.get())}),Yn([tt],([Ft])=>{if(gi&&(Ft&&O.get()&&ni(1).then(()=>{const Pe=document.getElementById(ct.menu.get());if(!Pe)return;const We=Xr(Pe);We.length&&$e(We[0])}),!Ft)){const Pe=F.get(),We=document.getElementById(ct.trigger.get());if(Pe&&ni(1).then(()=>{const Rt=document.getElementById(ct.menu.get());Rt&&Rt.contains(Pe)&&ci(Pe)}),!We||document.activeElement===We)return;ci(We)}}),{ids:ct,elements:{subTrigger:bn,subMenu:Xe,subArrow:xn},states:{subOpen:tt},options:it}};Gc(()=>{const z=document.getElementById(P.trigger.get());Nt(z)&&p.get()&&v.set(z);const $=[],x=()=>O.set(!1),tt=()=>{O.set(!0),$.push(ar(qr(document,"pointerdown",x,{capture:!0,once:!0}),qr(document,"pointermove",x,{capture:!0,once:!0})))},it=Qt=>{if(Qt.key===ln.ESCAPE&&s.get()){p.set(!1);return}};return $.push(qr(document,"keydown",tt,{capture:!0})),$.push(qr(document,"keydown",it)),()=>{$.forEach(Qt=>Qt())}}),Yn([p,F],([z,$])=>{!z&&$&&ci($)}),Yn([p],([z])=>{if(gi&&!z){const $=v.get();if(!$)return;const x=h.get();!z&&$&&gv({prop:x,defaultEl:$})}}),Yn([p,r],([z,$])=>{if(!gi)return;const x=[];return z&&$&&x.push(B_()),ni(1).then(()=>{const tt=document.getElementById(P.menu.get());if(tt&&z&&O.get()){if(d.get()){$e(tt);return}const it=Xr(tt);if(!it.length)return;$e(it[0])}}),()=>{x.forEach(tt=>tt())}}),Yn(p,z=>{if(!gi)return;const $=()=>O.set(!1),x=tt=>{if(O.set(!0),tt.key===ln.ESCAPE&&z&&s.get()){p.set(!1);return}};return ar(qr(document,"pointerdown",$,{capture:!0,once:!0}),qr(document,"pointermove",$,{capture:!0,once:!0}),qr(document,"keydown",x,{capture:!0}))});function Kt(z){p.update($=>{const x=!$;return x&&(b.set(E_(z)),_.set(A_(z)),v.set(z)),x})}function Be(z){const $=z.currentTarget;if(!Nt($))return;const x=F.get();x&&ci(x),T_($),F.set($)}function qe(z){const $=z.currentTarget;Nt($)&&ci($)}function _n(z){Ce(z)&&z.preventDefault()}function Ve(z){if(Ce(z))return;const $=z.target;if(!Nt($))return;const x=ze($);x&&$e(x)}function Nn(z){Ce(z)&&z.preventDefault()}function mn(z){if(!so(z))return;const $=z.target,x=z.currentTarget;if(!Nt(x)||!Nt($))return;const tt=y.get(),it=tt!==z.clientX;if(x.contains($)&&it){const Qt=z.clientX>tt?"right":"left";S.set(Qt),y.set(z.clientX)}}function Hn(z,$=null){if(!so(z)||(_n(z),z.defaultPrevented))return;if($){$e($);return}const x=z.currentTarget;Nt(x)&&$e(x)}function Wr(z){so(z)&&Ve(z)}function et(z){if(N.get().length>0&&z.key===ln.SPACE){z.preventDefault();return}if(wl.includes(z.key)){z.preventDefault();const tt=z.currentTarget;if(!Nt(tt))return;tt.click()}}function dt(z){return z==="indeterminate"}function Jt(z){return dt(z)?"indeterminate":z?"checked":"unchecked"}function Ce(z){return B.get()(z)}function ze(z){const $=z.closest('[role="menu"]');return Nt($)?$:null}return{elements:{trigger:V,menu:j,overlay:H,item:D,group:X,groupLabel:Q,arrow:U,separator:Ct},builders:{createCheckboxItem:Dt,createSubmenu:Oe,createMenuRadioGroup:Bt},states:{open:p},helpers:{handleTypeaheadSearch:R},ids:P,options:n.rootOptions}}function Lu(n,t,e){if(n.shiftKey){const r=e.get();r&&(n.preventDefault(),ni(1).then(()=>r.focus()),e.set(null))}else{const r=t.get();r&&(n.preventDefault(),ni(1).then(()=>r.focus()),t.set(null))}}function Xr(n){return Array.from(n.querySelectorAll(`[data-melt-menu-id="${n.id}"]`)).filter(t=>Nt(t))}function Os(n){!n||!lr(n)||(n.setAttribute("data-disabled",""),n.setAttribute("aria-disabled","true"))}function il(n){if(!gi)return;const t=n.get();t&&(window.clearTimeout(t),n.set(null))}function so(n){return n.pointerType==="mouse"}function ao(n,t){if(!n)return;const e=n.closest(`${t()}, ${t("submenu")}`);Nt(e)&&n.setAttribute("data-melt-menu-id",e.id)}function Ru(n,t){n.preventDefault();const e=document.activeElement,r=n.currentTarget;if(!Nt(e)||!Nt(r))return;const i=Xr(r);if(!i.length)return;const o=i.filter(l=>!(l.hasAttribute("data-disabled")||l.getAttribute("disabled")==="true")),s=o.indexOf(e);let a;switch(n.key){case ln.ARROW_DOWN:t?a=s<o.length-1?s+1:0:a=s<o.length-1?s+1:s;break;case ln.ARROW_UP:t?a=s>0?s-1:o.length-1:a=s<0?o.length-1:s>0?s-1:0;break;case ln.HOME:a=0;break;case ln.END:a=o.length-1;break;default:return}$e(o[a])}function wv(n,t){if(!t)return!1;const e={x:n.clientX,y:n.clientY};return Sv(e,t)}function Sv(n,t){const{x:e,y:r}=n;let i=!1;for(let o=0,s=t.length-1;o<t.length;s=o++){const a=t[o].x,l=t[o].y,c=t[s].x,u=t[s].y;l>r!=u>r&&e<(c-a)*(r-l)/(u-l)+a&&(i=!i)}return i}function Iv(n){const t=document.activeElement;if(!Nt(t))return!1;const e=t.closest(`[data-id="${n}"]`);return Nt(e)}function Ov(n){return n?"open":"closed"}const kv={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,forceVisible:!1,typeahead:!0,closeFocus:void 0,disableFocusFirstItem:!1,closeOnItemClick:!0,onOutsideClick:void 0};function Dv(n){const t={...kv,...n},e=Do(Dh(t,"ids")),r=t.open??Ke(t.defaultOpen),i=Ls(r,t==null?void 0:t.onOpenChange),o=tn(Ke(null)),s=tn(Ke(null)),a=tn(Ke(null)),{elements:l,builders:c,ids:u,states:f,options:h}=vv({rootOptions:e,rootOpen:i,rootActiveTrigger:tn(o),nextFocusable:tn(s),prevFocusable:tn(a),selector:"dropdown-menu",ids:t.ids});return{ids:u,elements:l,states:f,builders:c,options:h}}const Ev={orientation:"horizontal",decorative:!1},Av=n=>{const t={...Ev,...n},e=Do(t),{orientation:r,decorative:i}=e;return{elements:{root:an("separator",{stores:[r,i],returned:([s,a])=>({role:a?"none":"separator","aria-orientation":s==="vertical"?s:void 0,"aria-hidden":a,"data-orientation":s})})},options:e}};let Tv="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",Bv=(n=21)=>{let t="",e=n|0;for(;e--;)t+=Tv[Math.random()*64|0];return t};function Pv(){return Bv(10)}function Mv(n,t){const e=[];return t.builders.forEach(r=>{const i=r.action(n);i&&e.push(i)}),{destroy:()=>{e.forEach(r=>{r.destroy&&r.destroy()})}}}function Uu(n){const t={};return n.forEach(e=>{Object.keys(e).forEach(r=>{r!=="action"&&(t[r]=e[r])})}),t}function Nv(n){let t=n[1]?"a":"button",e,r,i=(n[1]?"a":"button")&&ol(n);return{c(){i&&i.c(),e=ut()},l(o){i&&i.l(o),e=ut()},m(o,s){i&&i.m(o,s),L(o,e,s),r=!0},p(o,s){o[1],t?Zt(t,o[1]?"a":"button")?(i.d(1),i=ol(o),t=o[1]?"a":"button",i.c(),i.m(e.parentNode,e)):i.p(o,s):(i=ol(o),t=o[1]?"a":"button",i.c(),i.m(e.parentNode,e))},i(o){r||(I(i,o),r=!0)},o(o){E(i,o),r=!1},d(o){o&&w(e),i&&i.d(o)}}}function Cv(n){let t=n[1]?"a":"button",e,r,i=(n[1]?"a":"button")&&sl(n);return{c(){i&&i.c(),e=ut()},l(o){i&&i.l(o),e=ut()},m(o,s){i&&i.m(o,s),L(o,e,s),r=!0},p(o,s){o[1],t?Zt(t,o[1]?"a":"button")?(i.d(1),i=sl(o),t=o[1]?"a":"button",i.c(),i.m(e.parentNode,e)):i.p(o,s):(i=sl(o),t=o[1]?"a":"button",i.c(),i.m(e.parentNode,e))},i(o){r||(I(i,o),r=!0)},o(o){E(i,o),r=!1},d(o){o&&w(e),i&&i.d(o)}}}function ol(n){let t,e,r,i,o;const s=n[7].default,a=ge(s,n,n[6],null);let l=[{type:e=n[1]?void 0:n[2]},{href:n[1]},{tabindex:"0"},n[5],n[4]],c={};for(let u=0;u<l.length;u+=1)c=te(c,l[u]);return{c(){t=Y(n[1]?"a":"button"),a&&a.c(),this.h()},l(u){t=q(u,((n[1]?"a":"button")||"null").toUpperCase(),{type:!0,href:!0,tabindex:!0});var f=J(t);a&&a.l(f),f.forEach(w),this.h()},h(){ji(n[1]?"a":"button")(t,c)},m(u,f){L(u,t,f),a&&a.m(t,null),n[29](t),r=!0,i||(o=[Pt(t,"click",n[18]),Pt(t,"change",n[19]),Pt(t,"keydown",n[20]),Pt(t,"keyup",n[21]),Pt(t,"mouseenter",n[22]),Pt(t,"mouseleave",n[23]),Pt(t,"mousedown",n[24]),Pt(t,"pointerdown",n[25]),Pt(t,"mouseup",n[26]),Pt(t,"pointerup",n[27])],i=!0)},p(u,f){a&&a.p&&(!r||f&64)&&_e(a,s,u,u[6],r?ye(s,u[6],f,null):be(u[6]),null),ji(u[1]?"a":"button")(t,c=rn(l,[(!r||f&6&&e!==(e=u[1]?void 0:u[2]))&&{type:e},(!r||f&2)&&{href:u[1]},{tabindex:"0"},f&32&&u[5],u[4]]))},i(u){r||(I(a,u),r=!0)},o(u){E(a,u),r=!1},d(u){u&&w(t),a&&a.d(u),n[29](null),i=!1,zn(o)}}}function sl(n){let t,e,r,i,o,s;const a=n[7].default,l=ge(a,n,n[6],null);let c=[{type:e=n[1]?void 0:n[2]},{href:n[1]},{tabindex:"0"},Uu(n[3]),n[5],n[4]],u={};for(let f=0;f<c.length;f+=1)u=te(u,c[f]);return{c(){t=Y(n[1]?"a":"button"),l&&l.c(),this.h()},l(f){t=q(f,((n[1]?"a":"button")||"null").toUpperCase(),{type:!0,href:!0,tabindex:!0});var h=J(t);l&&l.l(h),h.forEach(w),this.h()},h(){ji(n[1]?"a":"button")(t,u)},m(f,h){L(f,t,h),l&&l.m(t,null),n[28](t),i=!0,o||(s=[Pt(t,"click",n[8]),Pt(t,"change",n[9]),Pt(t,"keydown",n[10]),Pt(t,"keyup",n[11]),Pt(t,"mouseenter",n[12]),Pt(t,"mouseleave",n[13]),Pt(t,"mousedown",n[14]),Pt(t,"pointerdown",n[15]),Pt(t,"mouseup",n[16]),Pt(t,"pointerup",n[17]),Ir(r=Mv.call(null,t,{builders:n[3]}))],o=!0)},p(f,h){l&&l.p&&(!i||h&64)&&_e(l,a,f,f[6],i?ye(a,f[6],h,null):be(f[6]),null),ji(f[1]?"a":"button")(t,u=rn(c,[(!i||h&6&&e!==(e=f[1]?void 0:f[2]))&&{type:e},(!i||h&2)&&{href:f[1]},{tabindex:"0"},h&8&&Uu(f[3]),h&32&&f[5],f[4]])),r&&Kg(r.update)&&h&8&&r.update.call(null,{builders:f[3]})},i(f){i||(I(l,f),i=!0)},o(f){E(l,f),i=!1},d(f){f&&w(t),l&&l.d(f),n[28](null),o=!1,zn(s)}}}function Fv(n){let t,e,r,i;const o=[Cv,Nv],s=[];function a(l,c){return l[3]&&l[3].length?0:1}return t=a(n),e=s[t]=o[t](n),{c(){e.c(),r=ut()},l(l){e.l(l),r=ut()},m(l,c){s[t].m(l,c),L(l,r,c),i=!0},p(l,[c]){let u=t;t=a(l),t===u?s[t].p(l,c):(jt(),E(s[u],1,1,()=>{s[u]=null}),Lt(),e=s[t],e?e.p(l,c):(e=s[t]=o[t](l),e.c()),I(e,1),e.m(r.parentNode,r))},i(l){i||(I(e),i=!0)},o(l){E(e),i=!1},d(l){l&&w(r),s[t].d(l)}}}function jv(n,t,e){const r=["href","type","builders","el"];let i=Ue(t,r),{$$slots:o={},$$scope:s}=t,{href:a=void 0}=t,{type:l=void 0}=t,{builders:c=[]}=t,{el:u=void 0}=t;const f={"data-button-root":""};function h(D){ne.call(this,n,D)}function d(D){ne.call(this,n,D)}function g(D){ne.call(this,n,D)}function m(D){ne.call(this,n,D)}function p(D){ne.call(this,n,D)}function v(D){ne.call(this,n,D)}function b(D){ne.call(this,n,D)}function _(D){ne.call(this,n,D)}function O(D){ne.call(this,n,D)}function y(D){ne.call(this,n,D)}function T(D){ne.call(this,n,D)}function S(D){ne.call(this,n,D)}function F(D){ne.call(this,n,D)}function B(D){ne.call(this,n,D)}function N(D){ne.call(this,n,D)}function R(D){ne.call(this,n,D)}function P(D){ne.call(this,n,D)}function M(D){ne.call(this,n,D)}function j(D){ne.call(this,n,D)}function V(D){ne.call(this,n,D)}function U(D){kn[D?"unshift":"push"](()=>{u=D,e(0,u)})}function H(D){kn[D?"unshift":"push"](()=>{u=D,e(0,u)})}return n.$$set=D=>{t=te(te({},t),Sr(D)),e(5,i=Ue(t,r)),"href"in D&&e(1,a=D.href),"type"in D&&e(2,l=D.type),"builders"in D&&e(3,c=D.builders),"el"in D&&e(0,u=D.el),"$$scope"in D&&e(6,s=D.$$scope)},[u,a,l,c,f,i,s,o,h,d,g,m,p,v,b,_,O,y,T,S,F,B,N,R,P,M,j,V,U,H]}let Lv=class extends re{constructor(t){super(),ie(this,t,jv,Fv,Zt,{href:1,type:2,builders:3,el:0})}};function Sc(){return{NAME:"menu",SUB_NAME:"menu-submenu",RADIO_GROUP_NAME:"menu-radiogroup",CHECKBOX_ITEM_NAME:"menu-checkboxitem",RADIO_ITEM_NAME:"menu-radioitem",GROUP_NAME:"menu-group",PARTS:["arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","radio-indicator","separator","sub-content","sub-trigger","trigger"]}}function cs(){const{NAME:n}=Sc();return Jg(n)}function Rv(n){const{NAME:t,PARTS:e}=Sc(),r=i_("menu",e),i={...Dv({...o_(n),forceVisible:!0}),getAttrs:r};return Ih(t,i),{...i,updateOption:s_(i.options)}}function Uv(){const{GROUP_NAME:n}=Sc(),{elements:{group:t},getAttrs:e}=cs(),r=Pv();return Ih(n,r),{group:t,id:r,getAttrs:e}}function Vv(n){const e={...{side:"bottom",align:"center"},...n},{options:{positioning:r}}=cs();a_(r)(e)}const zv=n=>({builder:n&8}),Vu=n=>({builder:n[3]}),Wv=n=>({builder:n&8}),zu=n=>({builder:n[3]});function Hv(n){let t=n[1]?"a":"div",e,r,i=(n[1]?"a":"div")&&al(n);return{c(){i&&i.c(),e=ut()},l(o){i&&i.l(o),e=ut()},m(o,s){i&&i.m(o,s),L(o,e,s),r=!0},p(o,s){o[1],t?Zt(t,o[1]?"a":"div")?(i.d(1),i=al(o),t=o[1]?"a":"div",i.c(),i.m(e.parentNode,e)):i.p(o,s):(i=al(o),t=o[1]?"a":"div",i.c(),i.m(e.parentNode,e))},i(o){r||(I(i,o),r=!0)},o(o){E(i,o),r=!1},d(o){o&&w(e),i&&i.d(o)}}}function xv(n){let t;const e=n[11].default,r=ge(e,n,n[10],zu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&1032)&&_e(r,e,i,i[10],t?ye(e,i[10],o,Wv):be(i[10]),zu)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function al(n){let t,e,r,i;const o=n[11].default,s=ge(o,n,n[10],Vu);let a=[{href:n[1]},n[3],n[6]],l={};for(let c=0;c<a.length;c+=1)l=te(l,a[c]);return{c(){t=Y(n[1]?"a":"div"),s&&s.c(),this.h()},l(c){t=q(c,((n[1]?"a":"div")||"null").toUpperCase(),{href:!0});var u=J(t);s&&s.l(u),u.forEach(w),this.h()},h(){ji(n[1]?"a":"div")(t,l)},m(c,u){L(c,t,u),s&&s.m(t,null),n[13](t),e=!0,r||(i=[Ir(n[3].action(t)),Pt(t,"m-click",n[5]),Pt(t,"m-focusin",n[5]),Pt(t,"m-focusout",n[5]),Pt(t,"m-keydown",n[5]),Pt(t,"m-pointerdown",n[5]),Pt(t,"m-pointerleave",n[5]),Pt(t,"m-pointermove",n[5]),Pt(t,"pointerenter",n[12])],r=!0)},p(c,u){s&&s.p&&(!e||u&1032)&&_e(s,o,c,c[10],e?ye(o,c[10],u,zv):be(c[10]),Vu),ji(c[1]?"a":"div")(t,l=rn(a,[(!e||u&2)&&{href:c[1]},u&8&&c[3],u&64&&c[6]]))},i(c){e||(I(s,c),e=!0)},o(c){E(s,c),e=!1},d(c){c&&w(t),s&&s.d(c),n[13](null),r=!1,zn(i)}}}function qv(n){let t,e,r,i;const o=[xv,Hv],s=[];function a(l,c){return l[2]?0:1}return t=a(n),e=s[t]=o[t](n),{c(){e.c(),r=ut()},l(l){e.l(l),r=ut()},m(l,c){s[t].m(l,c),L(l,r,c),i=!0},p(l,[c]){let u=t;t=a(l),t===u?s[t].p(l,c):(jt(),E(s[u],1,1,()=>{s[u]=null}),Lt(),e=s[t],e?e.p(l,c):(e=s[t]=o[t](l),e.c()),I(e,1),e.m(r.parentNode,r))},i(l){i||(I(e),i=!0)},o(l){E(e),i=!1},d(l){l&&w(r),s[t].d(l)}}}function Yv(n,t,e){let r,i;const o=["href","asChild","disabled","el"];let s=Ue(t,o),a,{$$slots:l={},$$scope:c}=t,{href:u=void 0}=t,{asChild:f=!1}=t,{disabled:h=!1}=t,{el:d=void 0}=t;const{elements:{item:g},getAttrs:m}=cs();Te(n,g,_=>e(9,a=_));const p=tc();function v(_){ne.call(this,n,_)}function b(_){kn[_?"unshift":"push"](()=>{d=_,e(0,d)})}return n.$$set=_=>{t=te(te({},t),Sr(_)),e(6,s=Ue(t,o)),"href"in _&&e(1,u=_.href),"asChild"in _&&e(2,f=_.asChild),"disabled"in _&&e(7,h=_.disabled),"el"in _&&e(0,d=_.el),"$$scope"in _&&e(10,c=_.$$scope)},n.$$.update=()=>{n.$$.dirty&512&&e(3,r=a),n.$$.dirty&128&&e(8,i={...m("item"),...l_(h)}),n.$$.dirty&264&&Object.assign(r,i)},[d,u,f,r,g,p,s,h,i,a,c,l,v,b]}class Kv extends re{constructor(t){super(),ie(this,t,Yv,qv,Zt,{href:1,asChild:2,disabled:7,el:0})}}const Jv=n=>({builder:n&4}),Wu=n=>({builder:n[2]}),Qv=n=>({builder:n&4}),Hu=n=>({builder:n[2]});function Gv(n){let t,e,r,i;const o=n[7].default,s=ge(o,n,n[6],Wu);let a=[n[2],n[4]],l={};for(let c=0;c<a.length;c+=1)l=te(l,a[c]);return{c(){t=Y("div"),s&&s.c(),this.h()},l(c){t=q(c,"DIV",{});var u=J(t);s&&s.l(u),u.forEach(w),this.h()},h(){Ge(t,l)},m(c,u){L(c,t,u),s&&s.m(t,null),n[8](t),e=!0,r||(i=Ir(n[2].action(t)),r=!0)},p(c,u){s&&s.p&&(!e||u&68)&&_e(s,o,c,c[6],e?ye(o,c[6],u,Jv):be(c[6]),Wu),Ge(t,l=rn(a,[u&4&&c[2],u&16&&c[4]]))},i(c){e||(I(s,c),e=!0)},o(c){E(s,c),e=!1},d(c){c&&w(t),s&&s.d(c),n[8](null),r=!1,i()}}}function Xv(n){let t;const e=n[7].default,r=ge(e,n,n[6],Hu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&68)&&_e(r,e,i,i[6],t?ye(e,i[6],o,Qv):be(i[6]),Hu)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function Zv(n){let t,e,r,i;const o=[Xv,Gv],s=[];function a(l,c){return l[1]?0:1}return t=a(n),e=s[t]=o[t](n),{c(){e.c(),r=ut()},l(l){e.l(l),r=ut()},m(l,c){s[t].m(l,c),L(l,r,c),i=!0},p(l,[c]){let u=t;t=a(l),t===u?s[t].p(l,c):(jt(),E(s[u],1,1,()=>{s[u]=null}),Lt(),e=s[t],e?e.p(l,c):(e=s[t]=o[t](l),e.c()),I(e,1),e.m(r.parentNode,r))},i(l){i||(I(e),i=!0)},o(l){E(e),i=!1},d(l){l&&w(r),s[t].d(l)}}}function $v(n,t,e){let r;const i=["asChild","el"];let o=Ue(t,i),s,{$$slots:a={},$$scope:l}=t,{asChild:c=!1}=t,{el:u=void 0}=t;const{group:f,id:h,getAttrs:d}=Uv();Te(n,f,p=>e(5,s=p));const g=d("group");function m(p){kn[p?"unshift":"push"](()=>{u=p,e(0,u)})}return n.$$set=p=>{t=te(te({},t),Sr(p)),e(4,o=Ue(t,i)),"asChild"in p&&e(1,c=p.asChild),"el"in p&&e(0,u=p.el),"$$scope"in p&&e(6,l=p.$$scope)},n.$$.update=()=>{n.$$.dirty&32&&e(2,r=s(h)),n.$$.dirty&4&&Object.assign(r,g)},[u,c,r,f,o,s,l,a,m]}class t0 extends re{constructor(t){super(),ie(this,t,$v,Zv,Zt,{asChild:1,el:0})}}const e0=n=>({ids:n&1}),xu=n=>({ids:n[0]});function n0(n){let t;const e=n[16].default,r=ge(e,n,n[15],xu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,[o]){r&&r.p&&(!t||o&32769)&&_e(r,e,i,i[15],t?ye(e,i[15],o,e0):be(i[15]),xu)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function r0(n,t,e){let r,{$$slots:i={},$$scope:o}=t,{closeOnOutsideClick:s=void 0}=t,{closeOnEscape:a=void 0}=t,{portal:l=void 0}=t,{open:c=void 0}=t,{onOpenChange:u=void 0}=t,{preventScroll:f=void 0}=t,{loop:h=void 0}=t,{dir:d=void 0}=t,{typeahead:g=void 0}=t,{closeFocus:m=void 0}=t,{disableFocusFirstItem:p=void 0}=t,{closeOnItemClick:v=void 0}=t,{onOutsideClick:b=void 0}=t;const{states:{open:_},updateOption:O,ids:y}=Rv({closeOnOutsideClick:s,closeOnEscape:a,portal:l,forceVisible:!0,defaultOpen:c,preventScroll:f,loop:h,dir:d,typeahead:g,closeFocus:m,disableFocusFirstItem:p,closeOnItemClick:v,onOutsideClick:b,onOpenChange:({next:S})=>(c!==S&&(u==null||u(S),e(2,c=S)),S)}),T=mo([y.menu,y.trigger],([S,F])=>({menu:S,trigger:F}));return Te(n,T,S=>e(0,r=S)),n.$$set=S=>{"closeOnOutsideClick"in S&&e(3,s=S.closeOnOutsideClick),"closeOnEscape"in S&&e(4,a=S.closeOnEscape),"portal"in S&&e(5,l=S.portal),"open"in S&&e(2,c=S.open),"onOpenChange"in S&&e(6,u=S.onOpenChange),"preventScroll"in S&&e(7,f=S.preventScroll),"loop"in S&&e(8,h=S.loop),"dir"in S&&e(9,d=S.dir),"typeahead"in S&&e(10,g=S.typeahead),"closeFocus"in S&&e(11,m=S.closeFocus),"disableFocusFirstItem"in S&&e(12,p=S.disableFocusFirstItem),"closeOnItemClick"in S&&e(13,v=S.closeOnItemClick),"onOutsideClick"in S&&e(14,b=S.onOutsideClick),"$$scope"in S&&e(15,o=S.$$scope)},n.$$.update=()=>{n.$$.dirty&4&&c!==void 0&&_.set(c),n.$$.dirty&8&&O("closeOnOutsideClick",s),n.$$.dirty&16&&O("closeOnEscape",a),n.$$.dirty&32&&O("portal",l),n.$$.dirty&128&&O("preventScroll",f),n.$$.dirty&256&&O("loop",h),n.$$.dirty&512&&O("dir",d),n.$$.dirty&2048&&O("closeFocus",m),n.$$.dirty&4096&&O("disableFocusFirstItem",p),n.$$.dirty&1024&&O("typeahead",g),n.$$.dirty&8192&&O("closeOnItemClick",v),n.$$.dirty&16384&&O("onOutsideClick",b)},[r,T,c,s,a,l,u,f,h,d,g,m,p,v,b,o,i]}class i0 extends re{constructor(t){super(),ie(this,t,r0,n0,Zt,{closeOnOutsideClick:3,closeOnEscape:4,portal:5,open:2,onOpenChange:6,preventScroll:7,loop:8,dir:9,typeahead:10,closeFocus:11,disableFocusFirstItem:12,closeOnItemClick:13,onOutsideClick:14})}}const o0=n=>({builder:n[0]&256}),qu=n=>({builder:n[8]}),s0=n=>({builder:n[0]&256}),Yu=n=>({builder:n[8]}),a0=n=>({builder:n[0]&256}),Ku=n=>({builder:n[8]}),l0=n=>({builder:n[0]&256}),Ju=n=>({builder:n[8]}),c0=n=>({builder:n[0]&256}),Qu=n=>({builder:n[8]}),u0=n=>({builder:n[0]&256}),Gu=n=>({builder:n[8]});function f0(n){let t,e,r,i;const o=n[28].default,s=ge(o,n,n[27],qu);let a=[n[8],n[13]],l={};for(let c=0;c<a.length;c+=1)l=te(l,a[c]);return{c(){t=Y("div"),s&&s.c(),this.h()},l(c){t=q(c,"DIV",{});var u=J(t);s&&s.l(u),u.forEach(w),this.h()},h(){Ge(t,l)},m(c,u){L(c,t,u),s&&s.m(t,null),n[33](t),e=!0,r||(i=[Ir(n[8].action(t)),Pt(t,"m-keydown",n[12])],r=!0)},p(c,u){s&&s.p&&(!e||u[0]&134217984)&&_e(s,o,c,c[27],e?ye(o,c[27],u,o0):be(c[27]),qu),Ge(t,l=rn(a,[u[0]&256&&c[8],u[0]&8192&&c[13]]))},i(c){e||(I(s,c),e=!0)},o(c){E(s,c),e=!1},d(c){c&&w(t),s&&s.d(c),n[33](null),r=!1,zn(i)}}}function d0(n){let t,e,r,i,o;const s=n[28].default,a=ge(s,n,n[27],Yu);let l=[n[8],n[13]],c={};for(let u=0;u<l.length;u+=1)c=te(c,l[u]);return{c(){t=Y("div"),a&&a.c(),this.h()},l(u){t=q(u,"DIV",{});var f=J(t);a&&a.l(f),f.forEach(w),this.h()},h(){Ge(t,c)},m(u,f){L(u,t,f),a&&a.m(t,null),n[32](t),r=!0,i||(o=[Ir(n[8].action(t)),Pt(t,"m-keydown",n[12])],i=!0)},p(u,f){n=u,a&&a.p&&(!r||f[0]&134217984)&&_e(a,s,n,n[27],r?ye(s,n[27],f,s0):be(n[27]),Yu),Ge(t,c=rn(l,[f[0]&256&&n[8],f[0]&8192&&n[13]]))},i(u){r||(I(a,u),e&&e.end(1),r=!0)},o(u){E(a,u),u&&(e=is(t,n[5],n[6])),r=!1},d(u){u&&w(t),a&&a.d(u),n[32](null),u&&e&&e.end(),i=!1,zn(o)}}}function h0(n){let t,e,r,i,o;const s=n[28].default,a=ge(s,n,n[27],Ku);let l=[n[8],n[13]],c={};for(let u=0;u<l.length;u+=1)c=te(c,l[u]);return{c(){t=Y("div"),a&&a.c(),this.h()},l(u){t=q(u,"DIV",{});var f=J(t);a&&a.l(f),f.forEach(w),this.h()},h(){Ge(t,c)},m(u,f){L(u,t,f),a&&a.m(t,null),n[31](t),r=!0,i||(o=[Ir(n[8].action(t)),Pt(t,"m-keydown",n[12])],i=!0)},p(u,f){n=u,a&&a.p&&(!r||f[0]&134217984)&&_e(a,s,n,n[27],r?ye(s,n[27],f,a0):be(n[27]),Ku),Ge(t,c=rn(l,[f[0]&256&&n[8],f[0]&8192&&n[13]]))},i(u){r||(I(a,u),u&&(e||En(()=>{e=qi(t,n[3],n[4]),e.start()})),r=!0)},o(u){E(a,u),r=!1},d(u){u&&w(t),a&&a.d(u),n[31](null),i=!1,zn(o)}}}function m0(n){let t,e,r,i,o,s;const a=n[28].default,l=ge(a,n,n[27],Ju);let c=[n[8],n[13]],u={};for(let f=0;f<c.length;f+=1)u=te(u,c[f]);return{c(){t=Y("div"),l&&l.c(),this.h()},l(f){t=q(f,"DIV",{});var h=J(t);l&&l.l(h),h.forEach(w),this.h()},h(){Ge(t,u)},m(f,h){L(f,t,h),l&&l.m(t,null),n[30](t),i=!0,o||(s=[Ir(n[8].action(t)),Pt(t,"m-keydown",n[12])],o=!0)},p(f,h){n=f,l&&l.p&&(!i||h[0]&134217984)&&_e(l,a,n,n[27],i?ye(a,n[27],h,l0):be(n[27]),Ju),Ge(t,u=rn(c,[h[0]&256&&n[8],h[0]&8192&&n[13]]))},i(f){i||(I(l,f),f&&En(()=>{i&&(r&&r.end(1),e=qi(t,n[3],n[4]),e.start())}),i=!0)},o(f){E(l,f),e&&e.invalidate(),f&&(r=is(t,n[5],n[6])),i=!1},d(f){f&&w(t),l&&l.d(f),n[30](null),f&&r&&r.end(),o=!1,zn(s)}}}function p0(n){let t,e,r,i,o;const s=n[28].default,a=ge(s,n,n[27],Qu);let l=[n[8],n[13]],c={};for(let u=0;u<l.length;u+=1)c=te(c,l[u]);return{c(){t=Y("div"),a&&a.c(),this.h()},l(u){t=q(u,"DIV",{});var f=J(t);a&&a.l(f),f.forEach(w),this.h()},h(){Ge(t,c)},m(u,f){L(u,t,f),a&&a.m(t,null),n[29](t),r=!0,i||(o=[Ir(n[8].action(t)),Pt(t,"m-keydown",n[12])],i=!0)},p(u,f){n=u,a&&a.p&&(!r||f[0]&134217984)&&_e(a,s,n,n[27],r?ye(s,n[27],f,c0):be(n[27]),Qu),Ge(t,c=rn(l,[f[0]&256&&n[8],f[0]&8192&&n[13]]))},i(u){r||(I(a,u),u&&En(()=>{r&&(e||(e=Dn(t,n[1],n[2],!0)),e.run(1))}),r=!0)},o(u){E(a,u),u&&(e||(e=Dn(t,n[1],n[2],!1)),e.run(0)),r=!1},d(u){u&&w(t),a&&a.d(u),n[29](null),u&&e&&e.end(),i=!1,zn(o)}}}function g0(n){let t;const e=n[28].default,r=ge(e,n,n[27],Gu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o[0]&134217984)&&_e(r,e,i,i[27],t?ye(e,i[27],o,u0):be(i[27]),Gu)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function _0(n){let t,e,r,i;const o=[g0,p0,m0,h0,d0,f0],s=[];function a(l,c){return l[7]&&l[9]?0:l[1]&&l[9]?1:l[3]&&l[5]&&l[9]?2:l[3]&&l[9]?3:l[5]&&l[9]?4:l[9]?5:-1}return~(t=a(n))&&(e=s[t]=o[t](n)),{c(){e&&e.c(),r=ut()},l(l){e&&e.l(l),r=ut()},m(l,c){~t&&s[t].m(l,c),L(l,r,c),i=!0},p(l,c){let u=t;t=a(l),t===u?~t&&s[t].p(l,c):(e&&(jt(),E(s[u],1,1,()=>{s[u]=null}),Lt()),~t?(e=s[t],e?e.p(l,c):(e=s[t]=o[t](l),e.c()),I(e,1),e.m(r.parentNode,r)):e=null)},i(l){i||(I(e),i=!0)},o(l){E(e),i=!1},d(l){l&&w(r),~t&&s[t].d(l)}}}function b0(n,t,e){let r;const i=["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"];let o=Ue(t,i),s,a,{$$slots:l={},$$scope:c}=t,{transition:u=void 0}=t,{transitionConfig:f=void 0}=t,{inTransition:h=void 0}=t,{inTransitionConfig:d=void 0}=t,{outTransition:g=void 0}=t,{outTransitionConfig:m=void 0}=t,{asChild:p=!1}=t,{id:v=void 0}=t,{side:b="bottom"}=t,{align:_="center"}=t,{sideOffset:O=0}=t,{alignOffset:y=0}=t,{collisionPadding:T=8}=t,{avoidCollisions:S=!0}=t,{collisionBoundary:F=void 0}=t,{sameWidth:B=!1}=t,{fitViewport:N=!1}=t,{strategy:R="absolute"}=t,{overlap:P=!1}=t,{el:M=void 0}=t;const{elements:{menu:j},states:{open:V},ids:U,getAttrs:H}=cs();Te(n,j,K=>e(26,a=K)),Te(n,V,K=>e(9,s=K));const D=tc(),X=H("content");function Q(K){kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}function Z(K){kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}function Dt(K){kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}function Bt(K){kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}function Ct(K){kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}return n.$$set=K=>{t=te(te({},t),Sr(K)),e(13,o=Ue(t,i)),"transition"in K&&e(1,u=K.transition),"transitionConfig"in K&&e(2,f=K.transitionConfig),"inTransition"in K&&e(3,h=K.inTransition),"inTransitionConfig"in K&&e(4,d=K.inTransitionConfig),"outTransition"in K&&e(5,g=K.outTransition),"outTransitionConfig"in K&&e(6,m=K.outTransitionConfig),"asChild"in K&&e(7,p=K.asChild),"id"in K&&e(14,v=K.id),"side"in K&&e(15,b=K.side),"align"in K&&e(16,_=K.align),"sideOffset"in K&&e(17,O=K.sideOffset),"alignOffset"in K&&e(18,y=K.alignOffset),"collisionPadding"in K&&e(19,T=K.collisionPadding),"avoidCollisions"in K&&e(20,S=K.avoidCollisions),"collisionBoundary"in K&&e(21,F=K.collisionBoundary),"sameWidth"in K&&e(22,B=K.sameWidth),"fitViewport"in K&&e(23,N=K.fitViewport),"strategy"in K&&e(24,R=K.strategy),"overlap"in K&&e(25,P=K.overlap),"el"in K&&e(0,M=K.el),"$$scope"in K&&e(27,c=K.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&16384&&v&&U.menu.set(v),n.$$.dirty[0]&67108864&&e(8,r=a),n.$$.dirty[0]&256&&Object.assign(r,X),n.$$.dirty[0]&67076608&&s&&Vv({side:b,align:_,sideOffset:O,alignOffset:y,collisionPadding:T,avoidCollisions:S,collisionBoundary:F,sameWidth:B,fitViewport:N,strategy:R,overlap:P})},[M,u,f,h,d,g,m,p,r,s,j,V,D,o,v,b,_,O,y,T,S,F,B,N,R,P,a,c,l,Q,Z,Dt,Bt,Ct]}class y0 extends re{constructor(t){super(),ie(this,t,b0,_0,Zt,{transition:1,transitionConfig:2,inTransition:3,inTransitionConfig:4,outTransition:5,outTransitionConfig:6,asChild:7,id:14,side:15,align:16,sideOffset:17,alignOffset:18,collisionPadding:19,avoidCollisions:20,collisionBoundary:21,sameWidth:22,fitViewport:23,strategy:24,overlap:25,el:0},null,[-1,-1])}}const v0=n=>({builder:n&4}),Xu=n=>({builder:n[2]}),w0=n=>({builder:n&4}),Zu=n=>({builder:n[2]});function S0(n){let t,e,r,i;const o=n[9].default,s=ge(o,n,n[8],Xu);let a=[n[2],{type:"button"},n[5]],l={};for(let c=0;c<a.length;c+=1)l=te(l,a[c]);return{c(){t=Y("button"),s&&s.c(),this.h()},l(c){t=q(c,"BUTTON",{type:!0});var u=J(t);s&&s.l(u),u.forEach(w),this.h()},h(){Ge(t,l)},m(c,u){L(c,t,u),s&&s.m(t,null),t.autofocus&&t.focus(),n[10](t),e=!0,r||(i=[Ir(n[2].action(t)),Pt(t,"m-keydown",n[4]),Pt(t,"m-pointerdown",n[4])],r=!0)},p(c,u){s&&s.p&&(!e||u&260)&&_e(s,o,c,c[8],e?ye(o,c[8],u,v0):be(c[8]),Xu),Ge(t,l=rn(a,[u&4&&c[2],{type:"button"},u&32&&c[5]]))},i(c){e||(I(s,c),e=!0)},o(c){E(s,c),e=!1},d(c){c&&w(t),s&&s.d(c),n[10](null),r=!1,zn(i)}}}function I0(n){let t;const e=n[9].default,r=ge(e,n,n[8],Zu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&260)&&_e(r,e,i,i[8],t?ye(e,i[8],o,w0):be(i[8]),Zu)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function O0(n){let t,e,r,i;const o=[I0,S0],s=[];function a(l,c){return l[1]?0:1}return t=a(n),e=s[t]=o[t](n),{c(){e.c(),r=ut()},l(l){e.l(l),r=ut()},m(l,c){s[t].m(l,c),L(l,r,c),i=!0},p(l,[c]){let u=t;t=a(l),t===u?s[t].p(l,c):(jt(),E(s[u],1,1,()=>{s[u]=null}),Lt(),e=s[t],e?e.p(l,c):(e=s[t]=o[t](l),e.c()),I(e,1),e.m(r.parentNode,r))},i(l){i||(I(e),i=!0)},o(l){E(e),i=!1},d(l){l&&w(r),s[t].d(l)}}}function k0(n,t,e){let r;const i=["asChild","id","el"];let o=Ue(t,i),s,{$$slots:a={},$$scope:l}=t,{asChild:c=!1}=t,{id:u=void 0}=t,{el:f=void 0}=t;const{elements:{trigger:h},ids:d,getAttrs:g}=cs();Te(n,h,b=>e(7,s=b));const m=tc(),p=g("trigger");function v(b){kn[b?"unshift":"push"](()=>{f=b,e(0,f)})}return n.$$set=b=>{t=te(te({},t),Sr(b)),e(5,o=Ue(t,i)),"asChild"in b&&e(1,c=b.asChild),"id"in b&&e(6,u=b.id),"el"in b&&e(0,f=b.el),"$$scope"in b&&e(8,l=b.$$scope)},n.$$.update=()=>{n.$$.dirty&64&&u&&d.trigger.set(u),n.$$.dirty&128&&e(2,r=s),n.$$.dirty&4&&Object.assign(r,p)},[f,c,r,h,m,o,u,s,l,a,v]}class D0 extends re{constructor(t){super(),ie(this,t,k0,O0,Zt,{asChild:1,id:6,el:0})}}const ll=n=>n instanceof Date,Ic=n=>Object.keys(n).length===0,_r=n=>n!=null&&typeof n=="object",Oc=(n,...t)=>Object.prototype.hasOwnProperty.call(n,...t),cl=n=>_r(n)&&Ic(n),kc=()=>Object.create(null),Fp=(n,t)=>n===t||!_r(n)||!_r(t)?{}:Object.keys(t).reduce((e,r)=>{if(Oc(n,r)){const i=Fp(n[r],t[r]);return _r(i)&&Ic(i)||(e[r]=i),e}return e[r]=t[r],e},kc()),jp=(n,t)=>n===t||!_r(n)||!_r(t)?{}:Object.keys(n).reduce((e,r)=>{if(Oc(t,r)){const i=jp(n[r],t[r]);return _r(i)&&Ic(i)||(e[r]=i),e}return e[r]=void 0,e},kc()),Lp=(n,t)=>n===t?{}:!_r(n)||!_r(t)?t:ll(n)||ll(t)?n.valueOf()==t.valueOf()?{}:t:Object.keys(t).reduce((e,r)=>{if(Oc(n,r)){const i=Lp(n[r],t[r]);return cl(i)&&!ll(i)&&(cl(n[r])||!cl(t[r]))||(e[r]=i),e}return e},kc()),E0=(n,t)=>({added:Fp(n,t),deleted:jp(n,t),updated:Lp(n,t)});var ns,rs,Fi,Pa;class A0{constructor(){no(this,ns,[]);no(this,rs,{});no(this,Fi,new Set);Ja(this,"subscribe",t=>(Ar(this,Fi).add(t),t(this.generations),()=>Ar(this,Fi).delete(t)));no(this,Pa,0);Ja(this,"publish",()=>{if(Hc(this,Pa)._++>1e5)throw new Error("History published too many times.");Ar(this,Fi).forEach(t=>t(this.generations))})}get generations(){return[...Ar(this,ns)]}push(t){const e=s=>{let a=Object.entries(s);a.sort((c,u)=>c[0].localeCompare(u[0]));const l=Object.fromEntries(a);return JSON.parse(JSON.stringify(l))},r=e(Ar(this,rs)),i=e(t),o=E0(r,i);Ar(this,ns).push({...o,before:r,after:i,asof:new Date}),Qa(this,rs,i),this.publish()}}ns=new WeakMap,rs=new WeakMap,Fi=new WeakMap,Pa=new WeakMap;function T0(n){let t;const e=n[5].default,r=ge(e,n,n[8],null);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&256)&&_e(r,e,i,i[8],t?ye(e,i[8],o,null):be(i[8]),null)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function B0(n){let t,e;const r=[{builders:n[3]},{class:jr($c({variant:n[1],size:n[2],className:n[0]}),"hover:bg-base-200 shadow-base-200")},{type:"button"},n[4]];let i={$$slots:{default:[T0]},$$scope:{ctx:n}};for(let o=0;o<r.length;o+=1)i=te(i,r[o]);return t=new Lv({props:i}),t.$on("click",n[6]),t.$on("keydown",n[7]),{c(){lt(t.$$.fragment)},l(o){at(t.$$.fragment,o)},m(o,s){st(t,o,s),e=!0},p(o,[s]){const a=s&31?rn(r,[s&8&&{builders:o[3]},s&7&&{class:jr($c({variant:o[1],size:o[2],className:o[0]}),"hover:bg-base-200 shadow-base-200")},r[2],s&16&&Ma(o[4])]):{};s&256&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(o){e||(I(t.$$.fragment,o),e=!0)},o(o){E(t.$$.fragment,o),e=!1},d(o){ot(t,o)}}}function P0(n,t,e){const r=["class","variant","size","builders"];let i=Ue(t,r),{$$slots:o={},$$scope:s}=t,{class:a=void 0}=t,{variant:l="default"}=t,{size:c="default"}=t,{builders:u=[]}=t;function f(d){ne.call(this,n,d)}function h(d){ne.call(this,n,d)}return n.$$set=d=>{t=te(te({},t),Sr(d)),e(4,i=Ue(t,r)),"class"in d&&e(0,a=d.class),"variant"in d&&e(1,l=d.variant),"size"in d&&e(2,c=d.size),"builders"in d&&e(3,u=d.builders),"$$scope"in d&&e(8,s=d.$$scope)},[a,l,c,u,i,o,f,h,s]}class M0 extends re{constructor(t){super(),ie(this,t,P0,B0,Zt,{class:0,variant:1,size:2,builders:3})}}function $u(n){let t,e;return{c(){t=Y("span"),e=wt(n[1]),this.h()},l(r){t=q(r,"SPAN",{class:!0});var i=J(t);e=vt(i,n[1]),i.forEach(w),this.h()},h(){C(t,"class","cursor-pointer font-bold pr-8 flex items-center")},m(r,i){L(r,t,i),W(t,e)},p(r,i){i&2&&Re(e,r[1])},d(r){r&&w(t)}}}function N0(n){let t,e,r,i,o,s,a,l,c,u,f=n[1]&&$u(n);return{c(){t=Y("div"),f&&f.c(),e=rt(),r=Y("span"),i=wt(n[2]),this.h()},l(h){t=q(h,"DIV",{role:!0,class:!0});var d=J(t);f&&f.l(d),e=nt(d),r=q(d,"SPAN",{class:!0});var g=J(r);i=vt(g,n[2]),g.forEach(w),d.forEach(w),this.h()},h(){C(r,"class","cursor-pointer"),C(t,"role","none"),C(t,"class",o="print:hidden rounded py-1 px-3 my-4 mx-0 shadow-md text-xs font-mono flex justify-between transition-all duration-300 border "+tf[n[0]])},m(h,d){L(h,t,d),f&&f.m(t,null),W(t,e),W(t,r),W(r,i),l=!0,c||(u=[Pt(t,"click",n[3]),Pt(t,"keypress",n[3])],c=!0)},p(h,[d]){h[1]?f?f.p(h,d):(f=$u(h),f.c(),f.m(t,e)):f&&(f.d(1),f=null),(!l||d&4)&&Re(i,h[2]),(!l||d&1&&o!==(o="print:hidden rounded py-1 px-3 my-4 mx-0 shadow-md text-xs font-mono flex justify-between transition-all duration-300 border "+tf[h[0]]))&&C(t,"class",o)},i(h){l||(h&&En(()=>{l&&(a&&a.end(1),s=qi(t,Sl,{}),s.start())}),l=!0)},o(h){s&&s.invalidate(),h&&(a=is(t,ii,{x:1e3,duration:1e3,delay:0,opacity:.8})),l=!1},d(h){h&&w(t),f&&f.d(),h&&a&&a.end(),c=!1,zn(u)}}}const Rl={error:"negative",success:"positive"},C0=n=>Object.keys(Rl).includes(n),F0=n=>C0(n)?(console.warn(`[Toast] The status "${n}" is deprecated. Please use "${Rl[n]}" instead.`),Rl[n]):n,tf={negative:"border-negative/50 bg-negative/10 text-negative",positive:"border-positive/50 bg-positive/10 text-positive",info:"border-info/50 bg-info/10 text-info",warning:"border-warning/50 bg-warning/10 text-warning"};function j0(n,t,e){let{id:r}=t,{status:i="info"}=t,{title:o}=t,{message:s}=t,{dismissable:a=!0}=t;const l=Qg(),c=()=>{a&&l("dismiss",{id:r})};return n.$$set=u=>{"id"in u&&e(4,r=u.id),"status"in u&&e(0,i=u.status),"title"in u&&e(1,o=u.title),"message"in u&&e(2,s=u.message),"dismissable"in u&&e(5,a=u.dismissable)},n.$$.update=()=>{n.$$.dirty&1&&e(0,i=F0(i))},[i,o,s,c,r,a]}class L0 extends re{constructor(t){super(),ie(this,t,j0,N0,Zt,{id:4,status:0,title:1,message:2,dismissable:5})}}function ef(n,t,e){const r=n.slice();return r[2]=t[e],r}function nf(n,t){let e,r,i;const o=[t[2]];let s={};for(let a=0;a<o.length;a+=1)s=te(s,o[a]);return r=new L0({props:s}),r.$on("dismiss",t[1]),{key:n,first:null,c(){e=ut(),lt(r.$$.fragment),this.h()},l(a){e=ut(),at(r.$$.fragment,a),this.h()},h(){this.first=e},m(a,l){L(a,e,l),st(r,a,l),i=!0},p(a,l){t=a;const c=l&1?rn(o,[Ma(t[2])]):{};r.$set(c)},i(a){i||(I(r.$$.fragment,a),i=!0)},o(a){E(r.$$.fragment,a),i=!1},d(a){a&&w(e),ot(r,a)}}}function R0(n){let t,e=[],r=new Map,i,o=he(n[0]);const s=a=>a[2].id;for(let a=0;a<o.length;a+=1){let l=ef(n,o,a),c=s(l);r.set(c,e[a]=nf(c,l))}return{c(){t=Y("div");for(let a=0;a<e.length;a+=1)e[a].c();this.h()},l(a){t=q(a,"DIV",{class:!0});var l=J(t);for(let c=0;c<e.length;c+=1)e[c].l(l);l.forEach(w),this.h()},h(){C(t,"class","z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80")},m(a,l){L(a,t,l);for(let c=0;c<e.length;c+=1)e[c]&&e[c].m(t,null);i=!0},p(a,[l]){l&1&&(o=he(a[0]),jt(),e=Eh(e,l,s,1,a,o,r,t,c_,nf,null,ef),Lt())},i(a){if(!i){for(let l=0;l<o.length;l+=1)I(e[l]);i=!0}},o(a){for(let l=0;l<e.length;l+=1)E(e[l]);i=!1},d(a){a&&w(t);for(let l=0;l<e.length;l+=1)e[l].d()}}}function U0(n,t,e){let r;return Te(n,xc,o=>e(0,r=o)),[r,({detail:o})=>xc.dismiss(o.id)]}class V0 extends re{constructor(t){super(),ie(this,t,U0,R0,Zt,{})}}const rf="/_app/immutable/assets/wordmark-white.C8ZS96Ri.png",of="/_app/immutable/assets/wordmark-black.rfl-FBgf.png";function z0(n){let t,e,r,i,o;return{c(){t=Y("img"),r=rt(),i=Y("img"),this.h()},l(s){t=q(s,"IMG",{src:!0,alt:!0,class:!0,href:!0}),r=nt(s),i=q(s,"IMG",{src:!0,alt:!0,class:!0,href:!0}),this.h()},h(){vs(t.src,e=n[0]??n[1]??of)||C(t,"src",e),C(t,"alt","Home"),C(t,"class","h-5 aspect-auto block dark:hidden"),C(t,"href",Wt("/")),vs(i.src,o=n[0]??n[2]??rf)||C(i,"src",o),C(i,"alt","Home"),C(i,"class","h-5 aspect-auto hidden dark:block"),C(i,"href",Wt("/"))},m(s,a){L(s,t,a),L(s,r,a),L(s,i,a)},p(s,a){a&3&&!vs(t.src,e=s[0]??s[1]??of)&&C(t,"src",e),a&5&&!vs(i.src,o=s[0]??s[2]??rf)&&C(i,"src",o)},d(s){s&&(w(t),w(r),w(i))}}}function W0(n){let t;return{c(){t=wt(n[3])},l(e){t=vt(e,n[3])},m(e,r){L(e,t,r)},p(e,r){r&8&&Re(t,e[3])},d(e){e&&w(t)}}}function H0(n){let t;function e(o,s){return o[3]?W0:z0}let r=e(n),i=r(n);return{c(){i.c(),t=ut()},l(o){i.l(o),t=ut()},m(o,s){i.m(o,s),L(o,t,s)},p(o,[s]){r===(r=e(o))&&i?i.p(o,s):(i.d(1),i=r(o),i&&(i.c(),i.m(t.parentNode,t)))},i:Xt,o:Xt,d(o){o&&w(t),i.d(o)}}}function x0(n,t,e){let{logo:r}=t,{lightLogo:i}=t,{darkLogo:o}=t,{title:s}=t;return n.$$set=a=>{"logo"in a&&e(0,r=a.logo),"lightLogo"in a&&e(1,i=a.lightLogo),"darkLogo"in a&&e(2,o=a.darkLogo),"title"in a&&e(3,s=a.title)},[r,i,o,s]}class Dc extends re{constructor(t){super(),ie(this,t,x0,H0,Zt,{logo:0,lightLogo:1,darkLogo:2,title:3})}}/*! @docsearch/js 3.8.3 | MIT License | © Algolia, Inc. and contributors | https://docsearch.algolia.com */function Ul(){return Ul=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var r in e)({}).hasOwnProperty.call(e,r)&&(n[r]=e[r])}return n},Ul.apply(null,arguments)}function mt(n){return mt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mt(n)}var us,Ut,Rp,ti,sf,Up,Vl,Vp,Ec,zl,Wl,zp,Go={},Wp=[],q0=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Ra=Array.isArray;function pr(n,t){for(var e in t)n[e]=t[e];return n}function Ac(n){n&&n.parentNode&&n.parentNode.removeChild(n)}function tr(n,t,e){var r,i,o,s={};for(o in t)o=="key"?r=t[o]:o=="ref"?i=t[o]:s[o]=t[o];if(arguments.length>2&&(s.children=arguments.length>3?us.call(arguments,2):e),typeof n=="function"&&n.defaultProps!=null)for(o in n.defaultProps)s[o]===void 0&&(s[o]=n.defaultProps[o]);return Ao(n,s,r,i,null)}function Ao(n,t,e,r,i){var o={type:n,props:t,key:e,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:i??++Rp,__i:-1,__u:0};return i==null&&Ut.vnode!=null&&Ut.vnode(o),o}function br(n){return n.children}function er(n,t){this.props=n,this.context=t}function Hi(n,t){if(t==null)return n.__?Hi(n.__,n.__i+1):null;for(var e;t<n.__k.length;t++)if((e=n.__k[t])!=null&&e.__e!=null)return e.__e;return typeof n.type=="function"?Hi(n):null}function Hp(n){var t,e;if((n=n.__)!=null&&n.__c!=null){for(n.__e=n.__c.base=null,t=0;t<n.__k.length;t++)if((e=n.__k[t])!=null&&e.__e!=null){n.__e=n.__c.base=e.__e;break}return Hp(n)}}function Hl(n){(!n.__d&&(n.__d=!0)&&ti.push(n)&&!Aa.__r++||sf!==Ut.debounceRendering)&&((sf=Ut.debounceRendering)||Up)(Aa)}function Aa(){var n,t,e,r,i,o,s,a;for(ti.sort(Vl);n=ti.shift();)n.__d&&(t=ti.length,r=void 0,o=(i=(e=n).__v).__e,s=[],a=[],e.__P&&((r=pr({},i)).__v=i.__v+1,Ut.vnode&&Ut.vnode(r),Tc(e.__P,r,i,e.__n,e.__P.namespaceURI,32&i.__u?[o]:null,s,o??Hi(i),!!(32&i.__u),a),r.__v=i.__v,r.__.__k[r.__i]=r,Yp(s,r,a),r.__e!=o&&Hp(r)),ti.length>t&&ti.sort(Vl));Aa.__r=0}function xp(n,t,e,r,i,o,s,a,l,c,u){var f,h,d,g,m,p,v=r&&r.__k||Wp,b=t.length;for(l=function(_,O,y,T,S){var F,B,N,R,P,M=y.length,j=M,V=0;for(_.__k=new Array(S),F=0;F<S;F++)(B=O[F])!=null&&typeof B!="boolean"&&typeof B!="function"?(R=F+V,(B=_.__k[F]=typeof B=="string"||typeof B=="number"||typeof B=="bigint"||B.constructor==String?Ao(null,B,null,null,null):Ra(B)?Ao(br,{children:B},null,null,null):B.constructor===void 0&&B.__b>0?Ao(B.type,B.props,B.key,B.ref?B.ref:null,B.__v):B).__=_,B.__b=_.__b+1,N=null,(P=B.__i=Y0(B,y,R,j))!==-1&&(j--,(N=y[P])&&(N.__u|=2)),N==null||N.__v===null?(P==-1&&V--,typeof B.type!="function"&&(B.__u|=4)):P!=R&&(P==R-1?V--:P==R+1?V++:(P>R?V--:V++,B.__u|=4))):_.__k[F]=null;if(j)for(F=0;F<M;F++)(N=y[F])!=null&&!(2&N.__u)&&(N.__e==T&&(T=Hi(N)),Kp(N,N));return T}(e,t,v,l,b),f=0;f<b;f++)(d=e.__k[f])!=null&&(h=d.__i===-1?Go:v[d.__i]||Go,d.__i=f,p=Tc(n,d,h,i,o,s,a,l,c,u),g=d.__e,d.ref&&h.ref!=d.ref&&(h.ref&&Bc(h.ref,null,d),u.push(d.ref,d.__c||g,d)),m==null&&g!=null&&(m=g),4&d.__u||h.__k===d.__k?l=qp(d,l,n):typeof d.type=="function"&&p!==void 0?l=p:g&&(l=g.nextSibling),d.__u&=-7);return e.__e=m,l}function qp(n,t,e){var r,i;if(typeof n.type=="function"){for(r=n.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=n,t=qp(r[i],t,e));return t}n.__e!=t&&(t&&n.type&&!e.contains(t)&&(t=Hi(n)),e.insertBefore(n.__e,t||null),t=n.__e);do t=t&&t.nextSibling;while(t!=null&&t.nodeType==8);return t}function yr(n,t){return t=t||[],n==null||typeof n=="boolean"||(Ra(n)?n.some(function(e){yr(e,t)}):t.push(n)),t}function Y0(n,t,e,r){var i,o,s=n.key,a=n.type,l=t[e];if(l===null||l&&s==l.key&&a===l.type&&!(2&l.__u))return e;if(r>(l==null||2&l.__u?0:1))for(i=e-1,o=e+1;i>=0||o<t.length;){if(i>=0){if((l=t[i])&&!(2&l.__u)&&s==l.key&&a===l.type)return i;i--}if(o<t.length){if((l=t[o])&&!(2&l.__u)&&s==l.key&&a===l.type)return o;o++}}return-1}function af(n,t,e){t[0]=="-"?n.setProperty(t,e??""):n[t]=e==null?"":typeof e!="number"||q0.test(t)?e:e+"px"}function ks(n,t,e,r,i){var o;t:if(t=="style")if(typeof e=="string")n.style.cssText=e;else{if(typeof r=="string"&&(n.style.cssText=r=""),r)for(t in r)e&&t in e||af(n.style,t,"");if(e)for(t in e)r&&e[t]===r[t]||af(n.style,t,e[t])}else if(t[0]=="o"&&t[1]=="n")o=t!=(t=t.replace(Vp,"$1")),t=t.toLowerCase()in n||t=="onFocusOut"||t=="onFocusIn"?t.toLowerCase().slice(2):t.slice(2),n.l||(n.l={}),n.l[t+o]=e,e?r?e.u=r.u:(e.u=Ec,n.addEventListener(t,o?Wl:zl,o)):n.removeEventListener(t,o?Wl:zl,o);else{if(i=="http://www.w3.org/2000/svg")t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!="width"&&t!="height"&&t!="href"&&t!="list"&&t!="form"&&t!="tabIndex"&&t!="download"&&t!="rowSpan"&&t!="colSpan"&&t!="role"&&t!="popover"&&t in n)try{n[t]=e??"";break t}catch{}typeof e=="function"||(e==null||e===!1&&t[4]!="-"?n.removeAttribute(t):n.setAttribute(t,t=="popover"&&e==1?"":e))}}function lf(n){return function(t){if(this.l){var e=this.l[t.type+n];if(t.t==null)t.t=Ec++;else if(t.t<e.u)return;return e(Ut.event?Ut.event(t):t)}}}function Tc(n,t,e,r,i,o,s,a,l,c){var u,f,h,d,g,m,p,v,b,_,O,y,T,S,F,B,N,R=t.type;if(t.constructor!==void 0)return null;128&e.__u&&(l=!!(32&e.__u),o=[a=t.__e=e.__e]),(u=Ut.__b)&&u(t);t:if(typeof R=="function")try{if(v=t.props,b="prototype"in R&&R.prototype.render,_=(u=R.contextType)&&r[u.__c],O=u?_?_.props.value:u.__:r,e.__c?p=(f=t.__c=e.__c).__=f.__E:(b?t.__c=f=new R(v,O):(t.__c=f=new er(v,O),f.constructor=R,f.render=J0),_&&_.sub(f),f.props=v,f.state||(f.state={}),f.context=O,f.__n=r,h=f.__d=!0,f.__h=[],f._sb=[]),b&&f.__s==null&&(f.__s=f.state),b&&R.getDerivedStateFromProps!=null&&(f.__s==f.state&&(f.__s=pr({},f.__s)),pr(f.__s,R.getDerivedStateFromProps(v,f.__s))),d=f.props,g=f.state,f.__v=t,h)b&&R.getDerivedStateFromProps==null&&f.componentWillMount!=null&&f.componentWillMount(),b&&f.componentDidMount!=null&&f.__h.push(f.componentDidMount);else{if(b&&R.getDerivedStateFromProps==null&&v!==d&&f.componentWillReceiveProps!=null&&f.componentWillReceiveProps(v,O),!f.__e&&(f.shouldComponentUpdate!=null&&f.shouldComponentUpdate(v,f.__s,O)===!1||t.__v==e.__v)){for(t.__v!=e.__v&&(f.props=v,f.state=f.__s,f.__d=!1),t.__e=e.__e,t.__k=e.__k,t.__k.some(function(P){P&&(P.__=t)}),y=0;y<f._sb.length;y++)f.__h.push(f._sb[y]);f._sb=[],f.__h.length&&s.push(f);break t}f.componentWillUpdate!=null&&f.componentWillUpdate(v,f.__s,O),b&&f.componentDidUpdate!=null&&f.__h.push(function(){f.componentDidUpdate(d,g,m)})}if(f.context=O,f.props=v,f.__P=n,f.__e=!1,T=Ut.__r,S=0,b){for(f.state=f.__s,f.__d=!1,T&&T(t),u=f.render(f.props,f.state,f.context),F=0;F<f._sb.length;F++)f.__h.push(f._sb[F]);f._sb=[]}else do f.__d=!1,T&&T(t),u=f.render(f.props,f.state,f.context),f.state=f.__s;while(f.__d&&++S<25);f.state=f.__s,f.getChildContext!=null&&(r=pr(pr({},r),f.getChildContext())),b&&!h&&f.getSnapshotBeforeUpdate!=null&&(m=f.getSnapshotBeforeUpdate(d,g)),a=xp(n,Ra(B=u!=null&&u.type===br&&u.key==null?u.props.children:u)?B:[B],t,e,r,i,o,s,a,l,c),f.base=t.__e,t.__u&=-161,f.__h.length&&s.push(f),p&&(f.__E=f.__=null)}catch(P){if(t.__v=null,l||o!=null)if(P.then){for(t.__u|=l?160:128;a&&a.nodeType==8&&a.nextSibling;)a=a.nextSibling;o[o.indexOf(a)]=null,t.__e=a}else for(N=o.length;N--;)Ac(o[N]);else t.__e=e.__e,t.__k=e.__k;Ut.__e(P,t,e)}else o==null&&t.__v==e.__v?(t.__k=e.__k,t.__e=e.__e):a=t.__e=K0(e.__e,t,e,r,i,o,s,l,c);return(u=Ut.diffed)&&u(t),128&t.__u?void 0:a}function Yp(n,t,e){for(var r=0;r<e.length;r++)Bc(e[r],e[++r],e[++r]);Ut.__c&&Ut.__c(t,n),n.some(function(i){try{n=i.__h,i.__h=[],n.some(function(o){o.call(i)})}catch(o){Ut.__e(o,i.__v)}})}function K0(n,t,e,r,i,o,s,a,l){var c,u,f,h,d,g,m,p=e.props,v=t.props,b=t.type;if(b=="svg"?i="http://www.w3.org/2000/svg":b=="math"?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),o!=null){for(c=0;c<o.length;c++)if((d=o[c])&&"setAttribute"in d==!!b&&(b?d.localName==b:d.nodeType==3)){n=d,o[c]=null;break}}if(n==null){if(b==null)return document.createTextNode(v);n=document.createElementNS(i,b,v.is&&v),a&&(Ut.__m&&Ut.__m(t,o),a=!1),o=null}if(b===null)p===v||a&&n.data===v||(n.data=v);else{if(o=o&&us.call(n.childNodes),p=e.props||Go,!a&&o!=null)for(p={},c=0;c<n.attributes.length;c++)p[(d=n.attributes[c]).name]=d.value;for(c in p)if(d=p[c],c!="children"){if(c=="dangerouslySetInnerHTML")f=d;else if(!(c in v)){if(c=="value"&&"defaultValue"in v||c=="checked"&&"defaultChecked"in v)continue;ks(n,c,null,d,i)}}for(c in v)d=v[c],c=="children"?h=d:c=="dangerouslySetInnerHTML"?u=d:c=="value"?g=d:c=="checked"?m=d:a&&typeof d!="function"||p[c]===d||ks(n,c,d,p[c],i);if(u)a||f&&(u.__html===f.__html||u.__html===n.innerHTML)||(n.innerHTML=u.__html),t.__k=[];else if(f&&(n.innerHTML=""),xp(n,Ra(h)?h:[h],t,e,r,b=="foreignObject"?"http://www.w3.org/1999/xhtml":i,o,s,o?o[0]:e.__k&&Hi(e,0),a,l),o!=null)for(c=o.length;c--;)Ac(o[c]);a||(c="value",b=="progress"&&g==null?n.removeAttribute("value"):g!==void 0&&(g!==n[c]||b=="progress"&&!g||b=="option"&&g!==p[c])&&ks(n,c,g,p[c],i),c="checked",m!==void 0&&m!==n[c]&&ks(n,c,m,p[c],i))}return n}function Bc(n,t,e){try{if(typeof n=="function"){var r=typeof n.__u=="function";r&&n.__u(),r&&t==null||(n.__u=n(t))}else n.current=t}catch(i){Ut.__e(i,e)}}function Kp(n,t,e){var r,i;if(Ut.unmount&&Ut.unmount(n),(r=n.ref)&&(r.current&&r.current!==n.__e||Bc(r,null,t)),(r=n.__c)!=null){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(o){Ut.__e(o,t)}r.base=r.__P=null}if(r=n.__k)for(i=0;i<r.length;i++)r[i]&&Kp(r[i],t,e||typeof n.type!="function");e||Ac(n.__e),n.__c=n.__=n.__e=void 0}function J0(n,t,e){return this.constructor(n,e)}function Xo(n,t,e){var r,i,o,s;t==document&&(t=document.documentElement),Ut.__&&Ut.__(n,t),i=(r=typeof e=="function")?null:e&&e.__k||t.__k,o=[],s=[],Tc(t,n=(!r&&e||t).__k=tr(br,null,[n]),i||Go,Go,t.namespaceURI,!r&&e?[e]:i?null:t.firstChild?us.call(t.childNodes):null,o,!r&&e?e:i?i.__e:t.firstChild,r,s),Yp(o,n,s)}function Jp(n,t){Xo(n,t,Jp)}function Q0(n,t,e){var r,i,o,s,a=pr({},n.props);for(o in n.type&&n.type.defaultProps&&(s=n.type.defaultProps),t)o=="key"?r=t[o]:o=="ref"?i=t[o]:a[o]=t[o]===void 0&&s!==void 0?s[o]:t[o];return arguments.length>2&&(a.children=arguments.length>3?us.call(arguments,2):e),Ao(n.type,a,r||n.key,i||n.ref,null)}us=Wp.slice,Ut={__e:function(n,t,e,r){for(var i,o,s;t=t.__;)if((i=t.__c)&&!i.__)try{if((o=i.constructor)&&o.getDerivedStateFromError!=null&&(i.setState(o.getDerivedStateFromError(n)),s=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(n,r||{}),s=i.__d),s)return i.__E=i}catch(a){n=a}throw n}},Rp=0,er.prototype.setState=function(n,t){var e;e=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=pr({},this.state),typeof n=="function"&&(n=n(pr({},e),this.props)),n&&pr(e,n),n!=null&&this.__v&&(t&&this._sb.push(t),Hl(this))},er.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),Hl(this))},er.prototype.render=br,ti=[],Up=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Vl=function(n,t){return n.__v.__b-t.__v.__b},Aa.__r=0,Vp=/(PointerCapture)$|Capture$/i,Ec=0,zl=lf(!1),Wl=lf(!0),zp=0;var Ur,we,ul,cf,xi=0,Qp=[],Ee=Ut,uf=Ee.__b,ff=Ee.__r,df=Ee.diffed,hf=Ee.__c,mf=Ee.unmount,pf=Ee.__;function Ji(n,t){Ee.__h&&Ee.__h(we,n,xi||t),xi=0;var e=we.__H||(we.__H={__:[],__h:[]});return n>=e.__.length&&e.__.push({}),e.__[n]}function fs(n){return xi=1,Pc(ng,n)}function Pc(n,t,e){var r=Ji(Ur++,2);if(r.t=n,!r.__c&&(r.__=[e?e(t):ng(void 0,t),function(a){var l=r.__N?r.__N[0]:r.__[0],c=r.t(l,a);l!==c&&(r.__N=[c,r.__[1]],r.__c.setState({}))}],r.__c=we,!we.u)){var i=function(a,l,c){if(!r.__c.__H)return!0;var u=r.__c.__H.__.filter(function(h){return!!h.__c});if(u.every(function(h){return!h.__N}))return!o||o.call(this,a,l,c);var f=r.__c.props!==a;return u.forEach(function(h){if(h.__N){var d=h.__[0];h.__=h.__N,h.__N=void 0,d!==h.__[0]&&(f=!0)}}),o&&o.call(this,a,l,c)||f};we.u=!0;var o=we.shouldComponentUpdate,s=we.componentWillUpdate;we.componentWillUpdate=function(a,l,c){if(this.__e){var u=o;o=void 0,i(a,l,c),o=u}s&&s.call(this,a,l,c)},we.shouldComponentUpdate=i}return r.__N||r.__}function ds(n,t){var e=Ji(Ur++,3);!Ee.__s&&Mc(e.__H,t)&&(e.__=n,e.i=t,we.__H.__h.push(e))}function hs(n,t){var e=Ji(Ur++,4);!Ee.__s&&Mc(e.__H,t)&&(e.__=n,e.i=t,we.__h.push(e))}function Gp(n){return xi=5,Ua(function(){return{current:n}},[])}function Xp(n,t,e){xi=6,hs(function(){return typeof n=="function"?(n(t()),function(){return n(null)}):n?(n.current=t(),function(){return n.current=null}):void 0},e==null?e:e.concat(n))}function Ua(n,t){var e=Ji(Ur++,7);return Mc(e.__H,t)&&(e.__=n(),e.__H=t,e.__h=n),e.__}function Zp(n,t){return xi=8,Ua(function(){return n},t)}function $p(n){var t=we.context[n.__c],e=Ji(Ur++,9);return e.c=n,t?(e.__==null&&(e.__=!0,t.sub(we)),t.props.value):n.__}function tg(n,t){Ee.useDebugValue&&Ee.useDebugValue(t?t(n):n)}function eg(){var n=Ji(Ur++,11);if(!n.__){for(var t=we.__v;t!==null&&!t.__m&&t.__!==null;)t=t.__;var e=t.__m||(t.__m=[0,0]);n.__="P"+e[0]+"-"+e[1]++}return n.__}function G0(){for(var n;n=Qp.shift();)if(n.__P&&n.__H)try{n.__H.__h.forEach(Ys),n.__H.__h.forEach(xl),n.__H.__h=[]}catch(t){n.__H.__h=[],Ee.__e(t,n.__v)}}Ee.__b=function(n){we=null,uf&&uf(n)},Ee.__=function(n,t){n&&t.__k&&t.__k.__m&&(n.__m=t.__k.__m),pf&&pf(n,t)},Ee.__r=function(n){ff&&ff(n),Ur=0;var t=(we=n.__c).__H;t&&(ul===we?(t.__h=[],we.__h=[],t.__.forEach(function(e){e.__N&&(e.__=e.__N),e.i=e.__N=void 0})):(t.__h.forEach(Ys),t.__h.forEach(xl),t.__h=[],Ur=0)),ul=we},Ee.diffed=function(n){df&&df(n);var t=n.__c;t&&t.__H&&(t.__H.__h.length&&(Qp.push(t)!==1&&cf===Ee.requestAnimationFrame||((cf=Ee.requestAnimationFrame)||X0)(G0)),t.__H.__.forEach(function(e){e.i&&(e.__H=e.i),e.i=void 0})),ul=we=null},Ee.__c=function(n,t){t.some(function(e){try{e.__h.forEach(Ys),e.__h=e.__h.filter(function(r){return!r.__||xl(r)})}catch(r){t.some(function(i){i.__h&&(i.__h=[])}),t=[],Ee.__e(r,e.__v)}}),hf&&hf(n,t)},Ee.unmount=function(n){mf&&mf(n);var t,e=n.__c;e&&e.__H&&(e.__H.__.forEach(function(r){try{Ys(r)}catch(i){t=i}}),e.__H=void 0,t&&Ee.__e(t,e.__v))};var gf=typeof requestAnimationFrame=="function";function X0(n){var t,e=function(){clearTimeout(r),gf&&cancelAnimationFrame(t),setTimeout(n)},r=setTimeout(e,100);gf&&(t=requestAnimationFrame(e))}function Ys(n){var t=we,e=n.__c;typeof e=="function"&&(n.__c=void 0,e()),we=t}function xl(n){var t=we;n.__c=n.__(),we=t}function Mc(n,t){return!n||n.length!==t.length||t.some(function(e,r){return e!==n[r]})}function ng(n,t){return typeof t=="function"?t(n):t}function rg(n,t){for(var e in t)n[e]=t[e];return n}function ql(n,t){for(var e in n)if(e!=="__source"&&!(e in t))return!0;for(var r in t)if(r!=="__source"&&n[r]!==t[r])return!0;return!1}function ig(n,t){var e=t(),r=fs({t:{__:e,u:t}}),i=r[0].t,o=r[1];return hs(function(){i.__=e,i.u=t,fl(i)&&o({t:i})},[n,e,t]),ds(function(){return fl(i)&&o({t:i}),n(function(){fl(i)&&o({t:i})})},[n]),e}function fl(n){var t,e,r=n.u,i=n.__;try{var o=r();return!((t=i)===(e=o)&&(t!==0||1/t==1/e)||t!=t&&e!=e)}catch{return!0}}function og(n){n()}function sg(n){return n}function ag(){return[!1,og]}var lg=hs;function Yl(n,t){this.props=n,this.context=t}(Yl.prototype=new er).isPureReactComponent=!0,Yl.prototype.shouldComponentUpdate=function(n,t){return ql(this.props,n)||ql(this.state,t)};var _f=Ut.__b;Ut.__b=function(n){n.type&&n.type.__f&&n.ref&&(n.props.ref=n.ref,n.ref=null),_f&&_f(n)};var Z0=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911,bf=function(n,t){return n==null?null:yr(yr(n).map(t))},$0={map:bf,forEach:bf,count:function(n){return n?yr(n).length:0},only:function(n){var t=yr(n);if(t.length!==1)throw"Children.only";return t[0]},toArray:yr},t1=Ut.__e;Ut.__e=function(n,t,e,r){if(n.then){for(var i,o=t;o=o.__;)if((i=o.__c)&&i.__c)return t.__e==null&&(t.__e=e.__e,t.__k=e.__k),i.__c(n,t)}t1(n,t,e,r)};var yf=Ut.unmount;function cg(n,t,e){return n&&(n.__c&&n.__c.__H&&(n.__c.__H.__.forEach(function(r){typeof r.__c=="function"&&r.__c()}),n.__c.__H=null),(n=rg({},n)).__c!=null&&(n.__c.__P===e&&(n.__c.__P=t),n.__c=null),n.__k=n.__k&&n.__k.map(function(r){return cg(r,t,e)})),n}function ug(n,t,e){return n&&e&&(n.__v=null,n.__k=n.__k&&n.__k.map(function(r){return ug(r,t,e)}),n.__c&&n.__c.__P===t&&(n.__e&&e.appendChild(n.__e),n.__c.__e=!0,n.__c.__P=e)),n}function Ks(){this.__u=0,this.o=null,this.__b=null}function fg(n){var t=n.__.__c;return t&&t.__a&&t.__a(n)}function Io(){this.i=null,this.l=null}Ut.unmount=function(n){var t=n.__c;t&&t.__R&&t.__R(),t&&32&n.__u&&(n.type=null),yf&&yf(n)},(Ks.prototype=new er).__c=function(n,t){var e=t.__c,r=this;r.o==null&&(r.o=[]),r.o.push(e);var i=fg(r.__v),o=!1,s=function(){o||(o=!0,e.__R=null,i?i(a):a())};e.__R=s;var a=function(){if(!--r.__u){if(r.state.__a){var l=r.state.__a;r.__v.__k[0]=ug(l,l.__c.__P,l.__c.__O)}var c;for(r.setState({__a:r.__b=null});c=r.o.pop();)c.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),n.then(s,s)},Ks.prototype.componentWillUnmount=function(){this.o=[]},Ks.prototype.render=function(n,t){if(this.__b){if(this.__v.__k){var e=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=cg(this.__b,e,r.__O=r.__P)}this.__b=null}var i=t.__a&&tr(br,null,n.fallback);return i&&(i.__u&=-33),[tr(br,null,t.__a?null:n.children),i]};var vf=function(n,t,e){if(++e[1]===e[0]&&n.l.delete(t),n.props.revealOrder&&(n.props.revealOrder[0]!=="t"||!n.l.size))for(e=n.i;e;){for(;e.length>3;)e.pop()();if(e[1]<e[0])break;n.i=e=e[2]}};function e1(n){return this.getChildContext=function(){return n.context},n.children}function n1(n){var t=this,e=n.h;t.componentWillUnmount=function(){Xo(null,t.v),t.v=null,t.h=null},t.h&&t.h!==e&&t.componentWillUnmount(),t.v||(t.h=e,t.v={nodeType:1,parentNode:e,childNodes:[],contains:function(){return!0},appendChild:function(r){this.childNodes.push(r),t.h.appendChild(r)},insertBefore:function(r,i){this.childNodes.push(r),t.h.insertBefore(r,i)},removeChild:function(r){this.childNodes.splice(this.childNodes.indexOf(r)>>>1,1),t.h.removeChild(r)}}),Xo(tr(e1,{context:t.context},n.__v),t.v)}function dg(n,t){var e=tr(n1,{__v:n,h:t});return e.containerInfo=t,e}(Io.prototype=new er).__a=function(n){var t=this,e=fg(t.__v),r=t.l.get(n);return r[0]++,function(i){var o=function(){t.props.revealOrder?(r.push(i),vf(t,n,r)):i()};e?e(o):o()}},Io.prototype.render=function(n){this.i=null,this.l=new Map;var t=yr(n.children);n.revealOrder&&n.revealOrder[0]==="b"&&t.reverse();for(var e=t.length;e--;)this.l.set(t[e],this.i=[1,0,this.i]);return n.children},Io.prototype.componentDidUpdate=Io.prototype.componentDidMount=function(){var n=this;this.l.forEach(function(t,e){vf(n,e,t)})};var hg=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,r1=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,i1=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,o1=/[A-Z0-9]/g,s1=typeof document<"u",a1=function(n){return(typeof Symbol<"u"&&mt(Symbol())=="symbol"?/fil|che|rad/:/fil|che|ra/).test(n)};function mg(n,t,e){return t.__k==null&&(t.textContent=""),Xo(n,t),typeof e=="function"&&e(),n?n.__c:null}er.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(n){Object.defineProperty(er.prototype,n,{configurable:!0,get:function(){return this["UNSAFE_"+n]},set:function(t){Object.defineProperty(this,n,{configurable:!0,writable:!0,value:t})}})});var wf=Ut.event;function l1(){}function c1(){return this.cancelBubble}function u1(){return this.defaultPrevented}Ut.event=function(n){return wf&&(n=wf(n)),n.persist=l1,n.isPropagationStopped=c1,n.isDefaultPrevented=u1,n.nativeEvent=n};var Nc,f1={enumerable:!1,configurable:!0,get:function(){return this.class}},Sf=Ut.vnode;Ut.vnode=function(n){typeof n.type=="string"&&function(t){var e=t.props,r=t.type,i={},o=r.indexOf("-")===-1;for(var s in e){var a=e[s];if(!(s==="value"&&"defaultValue"in e&&a==null||s1&&s==="children"&&r==="noscript"||s==="class"||s==="className")){var l=s.toLowerCase();s==="defaultValue"&&"value"in e&&e.value==null?s="value":s==="download"&&a===!0?a="":l==="translate"&&a==="no"?a=!1:l[0]==="o"&&l[1]==="n"?l==="ondoubleclick"?s="ondblclick":l!=="onchange"||r!=="input"&&r!=="textarea"||a1(e.type)?l==="onfocus"?s="onfocusin":l==="onblur"?s="onfocusout":i1.test(s)&&(s=l):l=s="oninput":o&&r1.test(s)?s=s.replace(o1,"-$&").toLowerCase():a===null&&(a=void 0),l==="oninput"&&i[s=l]&&(s="oninputCapture"),i[s]=a}}r=="select"&&i.multiple&&Array.isArray(i.value)&&(i.value=yr(e.children).forEach(function(c){c.props.selected=i.value.indexOf(c.props.value)!=-1})),r=="select"&&i.defaultValue!=null&&(i.value=yr(e.children).forEach(function(c){c.props.selected=i.multiple?i.defaultValue.indexOf(c.props.value)!=-1:i.defaultValue==c.props.value})),e.class&&!e.className?(i.class=e.class,Object.defineProperty(i,"className",f1)):(e.className&&!e.class||e.class&&e.className)&&(i.class=i.className=e.className),t.props=i}(n),n.$$typeof=hg,Sf&&Sf(n)};var If=Ut.__r;Ut.__r=function(n){If&&If(n),Nc=n.__c};var Of=Ut.diffed;Ut.diffed=function(n){Of&&Of(n);var t=n.props,e=n.__e;e!=null&&n.type==="textarea"&&"value"in t&&t.value!==e.value&&(e.value=t.value==null?"":t.value),Nc=null};var d1={ReactCurrentDispatcher:{current:{readContext:function(n){return Nc.__n[n.__c].props.value},useCallback:Zp,useContext:$p,useDebugValue:tg,useDeferredValue:sg,useEffect:ds,useId:eg,useImperativeHandle:Xp,useInsertionEffect:lg,useLayoutEffect:hs,useMemo:Ua,useReducer:Pc,useRef:Gp,useState:fs,useSyncExternalStore:ig,useTransition:ag}}};function Ds(n){return!!n&&n.$$typeof===hg}var k={useState:fs,useId:eg,useReducer:Pc,useEffect:ds,useLayoutEffect:hs,useInsertionEffect:lg,useTransition:ag,useDeferredValue:sg,useSyncExternalStore:ig,startTransition:og,useRef:Gp,useImperativeHandle:Xp,useMemo:Ua,useCallback:Zp,useContext:$p,useDebugValue:tg,version:"18.3.1",Children:$0,render:mg,hydrate:function(n,t,e){return Jp(n,t),typeof e=="function"&&e(),n?n.__c:null},unmountComponentAtNode:function(n){return!!n.__k&&(Xo(null,n),!0)},createPortal:dg,createElement:tr,createContext:function(n,t){var e={__c:t="__cC"+zp++,__:n,Consumer:function(r,i){return r.children(i)},Provider:function(r){var i,o;return this.getChildContext||(i=new Set,(o={})[t]=this,this.getChildContext=function(){return o},this.componentWillUnmount=function(){i=null},this.shouldComponentUpdate=function(s){this.props.value!==s.value&&i.forEach(function(a){a.__e=!0,Hl(a)})},this.sub=function(s){i.add(s);var a=s.componentWillUnmount;s.componentWillUnmount=function(){i&&i.delete(s),a&&a.call(s)}}),r.children}};return e.Provider.__=e.Consumer.contextType=e},createFactory:function(n){return tr.bind(null,n)},cloneElement:function(n){return Ds(n)?Q0.apply(null,arguments):n},createRef:function(){return{current:null}},Fragment:br,isValidElement:Ds,isElement:Ds,isFragment:function(n){return Ds(n)&&n.type===br},isMemo:function(n){return!!n&&!!n.displayName&&(typeof n.displayName=="string"||n.displayName instanceof String)&&n.displayName.startsWith("Memo(")},findDOMNode:function(n){return n&&(n.base||n.nodeType===1&&n)||null},Component:er,PureComponent:Yl,memo:function(n,t){function e(i){var o=this.props.ref,s=o==i.ref;return!s&&o&&(o.call?o(null):o.current=null),t?!t(this.props,i)||!s:ql(this.props,i)}function r(i){return this.shouldComponentUpdate=e,tr(n,i)}return r.displayName="Memo("+(n.displayName||n.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r},forwardRef:function(n){function t(e){var r=rg({},e);return delete r.ref,n(r,e.ref||null)}return t.$$typeof=Z0,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(n.displayName||n.name)+")",t},flushSync:function(n,t){return n(t)},unstable_batchedUpdates:function(n,t){return n(t)},StrictMode:br,Suspense:Ks,SuspenseList:Io,lazy:function(n){var t,e,r;function i(o){if(t||(t=n()).then(function(s){e=s.default||s},function(s){r=s}),r)throw r;if(!e)throw t;return tr(e,o)}return i.displayName="Lazy",i.__f=!0,i},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:d1};function Kl(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=Array(t);e<t;e++)r[e]=n[e];return r}function kf(n,t,e,r,i,o,s){try{var a=n[o](s),l=a.value}catch(c){return void e(c)}a.done?t(l):Promise.resolve(l).then(r,i)}function dl(n){return function(){var t=this,e=arguments;return new Promise(function(r,i){var o=n.apply(t,e);function s(l){kf(o,r,i,s,a,"next",l)}function a(l){kf(o,r,i,s,a,"throw",l)}s(void 0)})}}function Qi(n,t,e){return t=Zo(t),function(r,i){if(i&&(mt(i)=="object"||typeof i=="function"))return i;if(i!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(o){if(o===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o}(r)}(n,Cc()?Reflect.construct(t,e||[],Zo(n).constructor):t.apply(n,e))}function Gi(n,t){if(!(n instanceof t))throw new TypeError("Cannot call a class as a function")}function Xi(n,t,e){return Object.defineProperty(n,"prototype",{writable:!1}),n}function Zi(n,t,e){return(t=function(r){var i=function(o){if(mt(o)!="object"||!o)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(mt(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return mt(i)=="symbol"?i:i+""}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Qe(){return Qe=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var r in e)({}).hasOwnProperty.call(e,r)&&(n[r]=e[r])}return n},Qe.apply(null,arguments)}function Zo(n){return Zo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Zo(n)}function $i(n,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&$o(n,t)}function Cc(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Cc=function(){return!!n})()}function Df(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Vt(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Df(Object(e),!0).forEach(function(r){Zi(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Df(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function nr(n,t){if(n==null)return{};var e,r,i=function(s,a){if(s==null)return{};var l={};for(var c in s)if({}.hasOwnProperty.call(s,c)){if(a.includes(c))continue;l[c]=s[c]}return l}(n,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(r=0;r<o.length;r++)e=o[r],t.includes(e)||{}.propertyIsEnumerable.call(n,e)&&(i[e]=n[e])}return i}function Zr(){Zr=function(){return t};var n,t={},e=Object.prototype,r=e.hasOwnProperty,i=Object.defineProperty||function(U,H,D){U[H]=D.value},o=typeof Symbol=="function"?Symbol:{},s=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(U,H,D){return Object.defineProperty(U,H,{value:D,enumerable:!0,configurable:!0,writable:!0}),U[H]}try{c({},"")}catch{c=function(H,D,X){return H[D]=X}}function u(U,H,D,X){var Q=H&&H.prototype instanceof v?H:v,Z=Object.create(Q.prototype),Dt=new j(X||[]);return i(Z,"_invoke",{value:N(U,D,Dt)}),Z}function f(U,H,D){try{return{type:"normal",arg:U.call(H,D)}}catch(X){return{type:"throw",arg:X}}}t.wrap=u;var h="suspendedStart",d="suspendedYield",g="executing",m="completed",p={};function v(){}function b(){}function _(){}var O={};c(O,s,function(){return this});var y=Object.getPrototypeOf,T=y&&y(y(V([])));T&&T!==e&&r.call(T,s)&&(O=T);var S=_.prototype=v.prototype=Object.create(O);function F(U){["next","throw","return"].forEach(function(H){c(U,H,function(D){return this._invoke(H,D)})})}function B(U,H){function D(Q,Z,Dt,Bt){var Ct=f(U[Q],U,Z);if(Ct.type!=="throw"){var K=Ct.arg,Oe=K.value;return Oe&&mt(Oe)=="object"&&r.call(Oe,"__await")?H.resolve(Oe.__await).then(function(Kt){D("next",Kt,Dt,Bt)},function(Kt){D("throw",Kt,Dt,Bt)}):H.resolve(Oe).then(function(Kt){K.value=Kt,Dt(K)},function(Kt){return D("throw",Kt,Dt,Bt)})}Bt(Ct.arg)}var X;i(this,"_invoke",{value:function(Q,Z){function Dt(){return new H(function(Bt,Ct){D(Q,Z,Bt,Ct)})}return X=X?X.then(Dt,Dt):Dt()}})}function N(U,H,D){var X=h;return function(Q,Z){if(X===g)throw Error("Generator is already running");if(X===m){if(Q==="throw")throw Z;return{value:n,done:!0}}for(D.method=Q,D.arg=Z;;){var Dt=D.delegate;if(Dt){var Bt=R(Dt,D);if(Bt){if(Bt===p)continue;return Bt}}if(D.method==="next")D.sent=D._sent=D.arg;else if(D.method==="throw"){if(X===h)throw X=m,D.arg;D.dispatchException(D.arg)}else D.method==="return"&&D.abrupt("return",D.arg);X=g;var Ct=f(U,H,D);if(Ct.type==="normal"){if(X=D.done?m:d,Ct.arg===p)continue;return{value:Ct.arg,done:D.done}}Ct.type==="throw"&&(X=m,D.method="throw",D.arg=Ct.arg)}}}function R(U,H){var D=H.method,X=U.iterator[D];if(X===n)return H.delegate=null,D==="throw"&&U.iterator.return&&(H.method="return",H.arg=n,R(U,H),H.method==="throw")||D!=="return"&&(H.method="throw",H.arg=new TypeError("The iterator does not provide a '"+D+"' method")),p;var Q=f(X,U.iterator,H.arg);if(Q.type==="throw")return H.method="throw",H.arg=Q.arg,H.delegate=null,p;var Z=Q.arg;return Z?Z.done?(H[U.resultName]=Z.value,H.next=U.nextLoc,H.method!=="return"&&(H.method="next",H.arg=n),H.delegate=null,p):Z:(H.method="throw",H.arg=new TypeError("iterator result is not an object"),H.delegate=null,p)}function P(U){var H={tryLoc:U[0]};1 in U&&(H.catchLoc=U[1]),2 in U&&(H.finallyLoc=U[2],H.afterLoc=U[3]),this.tryEntries.push(H)}function M(U){var H=U.completion||{};H.type="normal",delete H.arg,U.completion=H}function j(U){this.tryEntries=[{tryLoc:"root"}],U.forEach(P,this),this.reset(!0)}function V(U){if(U||U===""){var H=U[s];if(H)return H.call(U);if(typeof U.next=="function")return U;if(!isNaN(U.length)){var D=-1,X=function Q(){for(;++D<U.length;)if(r.call(U,D))return Q.value=U[D],Q.done=!1,Q;return Q.value=n,Q.done=!0,Q};return X.next=X}}throw new TypeError(mt(U)+" is not iterable")}return b.prototype=_,i(S,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:b,configurable:!0}),b.displayName=c(_,l,"GeneratorFunction"),t.isGeneratorFunction=function(U){var H=typeof U=="function"&&U.constructor;return!!H&&(H===b||(H.displayName||H.name)==="GeneratorFunction")},t.mark=function(U){return Object.setPrototypeOf?Object.setPrototypeOf(U,_):(U.__proto__=_,c(U,l,"GeneratorFunction")),U.prototype=Object.create(S),U},t.awrap=function(U){return{__await:U}},F(B.prototype),c(B.prototype,a,function(){return this}),t.AsyncIterator=B,t.async=function(U,H,D,X,Q){Q===void 0&&(Q=Promise);var Z=new B(u(U,H,D,X),Q);return t.isGeneratorFunction(H)?Z:Z.next().then(function(Dt){return Dt.done?Dt.value:Z.next()})},F(S),c(S,l,"Generator"),c(S,s,function(){return this}),c(S,"toString",function(){return"[object Generator]"}),t.keys=function(U){var H=Object(U),D=[];for(var X in H)D.push(X);return D.reverse(),function Q(){for(;D.length;){var Z=D.pop();if(Z in H)return Q.value=Z,Q.done=!1,Q}return Q.done=!0,Q}},t.values=V,j.prototype={constructor:j,reset:function(U){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(M),!U)for(var H in this)H.charAt(0)==="t"&&r.call(this,H)&&!isNaN(+H.slice(1))&&(this[H]=n)},stop:function(){this.done=!0;var U=this.tryEntries[0].completion;if(U.type==="throw")throw U.arg;return this.rval},dispatchException:function(U){if(this.done)throw U;var H=this;function D(Ct,K){return Z.type="throw",Z.arg=U,H.next=Ct,K&&(H.method="next",H.arg=n),!!K}for(var X=this.tryEntries.length-1;X>=0;--X){var Q=this.tryEntries[X],Z=Q.completion;if(Q.tryLoc==="root")return D("end");if(Q.tryLoc<=this.prev){var Dt=r.call(Q,"catchLoc"),Bt=r.call(Q,"finallyLoc");if(Dt&&Bt){if(this.prev<Q.catchLoc)return D(Q.catchLoc,!0);if(this.prev<Q.finallyLoc)return D(Q.finallyLoc)}else if(Dt){if(this.prev<Q.catchLoc)return D(Q.catchLoc,!0)}else{if(!Bt)throw Error("try statement without catch or finally");if(this.prev<Q.finallyLoc)return D(Q.finallyLoc)}}}},abrupt:function(U,H){for(var D=this.tryEntries.length-1;D>=0;--D){var X=this.tryEntries[D];if(X.tryLoc<=this.prev&&r.call(X,"finallyLoc")&&this.prev<X.finallyLoc){var Q=X;break}}Q&&(U==="break"||U==="continue")&&Q.tryLoc<=H&&H<=Q.finallyLoc&&(Q=null);var Z=Q?Q.completion:{};return Z.type=U,Z.arg=H,Q?(this.method="next",this.next=Q.finallyLoc,p):this.complete(Z)},complete:function(U,H){if(U.type==="throw")throw U.arg;return U.type==="break"||U.type==="continue"?this.next=U.arg:U.type==="return"?(this.rval=this.arg=U.arg,this.method="return",this.next="end"):U.type==="normal"&&H&&(this.next=H),p},finish:function(U){for(var H=this.tryEntries.length-1;H>=0;--H){var D=this.tryEntries[H];if(D.finallyLoc===U)return this.complete(D.completion,D.afterLoc),M(D),p}},catch:function(U){for(var H=this.tryEntries.length-1;H>=0;--H){var D=this.tryEntries[H];if(D.tryLoc===U){var X=D.completion;if(X.type==="throw"){var Q=X.arg;M(D)}return Q}}throw Error("illegal catch attempt")},delegateYield:function(U,H,D){return this.delegate={iterator:V(U),resultName:H,nextLoc:D},this.method==="next"&&(this.arg=n),p}},t}function $o(n,t){return $o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,r){return e.__proto__=r,e},$o(n,t)}function fn(n,t){return function(e){if(Array.isArray(e))return e}(n)||function(e,r){var i=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(i!=null){var o,s,a,l,c=[],u=!0,f=!1;try{if(a=(i=i.call(e)).next,r===0){if(Object(i)!==i)return;u=!1}else for(;!(u=(o=a.call(i)).done)&&(c.push(o.value),c.length!==r);u=!0);}catch(h){f=!0,s=h}finally{try{if(!u&&i.return!=null&&(l=i.return(),Object(l)!==l))return}finally{if(f)throw s}}return c}}(n,t)||pg(n,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function To(n){return function(t){if(Array.isArray(t))return Kl(t)}(n)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(n)||pg(n)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function pg(n,t){if(n){if(typeof n=="string")return Kl(n,t);var e={}.toString.call(n).slice(8,-1);return e==="Object"&&n.constructor&&(e=n.constructor.name),e==="Map"||e==="Set"?Array.from(n):e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Kl(n,t):void 0}}function Jl(n){var t=typeof Map=="function"?new Map:void 0;return Jl=function(e){if(e===null||!function(i){try{return Function.toString.call(i).indexOf("[native code]")!==-1}catch{return typeof i=="function"}}(e))return e;if(typeof e!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(i,o,s){if(Cc())return Reflect.construct.apply(null,arguments);var a=[null];a.push.apply(a,o);var l=new(i.bind.apply(i,a));return s&&$o(l,s.prototype),l}(e,arguments,Zo(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),$o(r,e)},Jl(n)}function h1(){return k.createElement("svg",{width:"15",height:"15",className:"DocSearch-Control-Key-Icon"},k.createElement("path",{d:"M4.505 4.496h2M5.505 5.496v5M8.216 4.496l.055 5.993M10 7.5c.333.333.5.667.5 1v2M12.326 4.5v5.996M8.384 4.496c1.674 0 2.116 0 2.116 1.5s-.442 1.5-2.116 1.5M3.205 9.303c-.09.448-.277 1.21-1.241 1.203C1 10.5.5 9.513.5 8V7c0-1.57.5-2.5 1.464-2.494.964.006 1.134.598 1.24 1.342M12.553 10.5h1.953",strokeWidth:"1.2",stroke:"currentColor",fill:"none",strokeLinecap:"square"}))}function gg(){return k.createElement("svg",{width:"20",height:"20",className:"DocSearch-Search-Icon",viewBox:"0 0 20 20","aria-hidden":"true"},k.createElement("path",{d:"M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}var m1=["translations"],hl="Ctrl",p1=k.forwardRef(function(n,t){var e=n.translations,r=e===void 0?{}:e,i=nr(n,m1),o=r.buttonText,s=o===void 0?"Search":o,a=r.buttonAriaLabel,l=a===void 0?"Search":a,c=fn(fs(null),2),u=c[0],f=c[1];ds(function(){typeof navigator<"u"&&(/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)?f("⌘"):f(hl))},[]);var h=fn(u===hl?[hl,"Ctrl",k.createElement(h1,null)]:["Meta","Command",u],3),d=h[0],g=h[1],m=h[2];return k.createElement("button",Qe({type:"button",className:"DocSearch DocSearch-Button","aria-label":"".concat(l," (").concat(g,"+K)")},i,{ref:t}),k.createElement("span",{className:"DocSearch-Button-Container"},k.createElement(gg,null),k.createElement("span",{className:"DocSearch-Button-Placeholder"},s)),k.createElement("span",{className:"DocSearch-Button-Keys"},u!==null&&k.createElement(k.Fragment,null,k.createElement(Ef,{reactsToKey:d},m),k.createElement(Ef,{reactsToKey:"k"},"K"))))});function Ef(n){var t=n.reactsToKey,e=n.children,r=fn(fs(!1),2),i=r[0],o=r[1];return ds(function(){if(t)return window.addEventListener("keydown",s),window.addEventListener("keyup",a),function(){window.removeEventListener("keydown",s),window.removeEventListener("keyup",a)};function s(l){l.key===t&&o(!0)}function a(l){l.key!==t&&l.key!=="Meta"||o(!1)}},[t]),k.createElement("kbd",{className:i?"DocSearch-Button-Key DocSearch-Button-Key--pressed":"DocSearch-Button-Key"},e)}function _g(n,t){var e=void 0;return function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];e&&clearTimeout(e),e=setTimeout(function(){return n.apply(void 0,i)},t)}}function ts(n){return n.reduce(function(t,e){return t.concat(e)},[])}var g1=0;function Ql(n){return n.collections.length===0?0:n.collections.reduce(function(t,e){return t+e.items.length},0)}function Af(n){return n!==Object(n)}function bg(n,t){if(n===t)return!0;if(Af(n)||Af(t)||typeof n=="function"||typeof t=="function")return n===t;if(Object.keys(n).length!==Object.keys(t).length)return!1;for(var e=0,r=Object.keys(n);e<r.length;e++){var i=r[e];if(!(i in t)||!bg(n[i],t[i]))return!1}return!0}var Js=function(){},_1=[{segment:"autocomplete-core",version:"1.17.9"}];function Tf(n){var t=n.item,e=n.items,r=e===void 0?[]:e;return{index:t.__autocomplete_indexName,items:[t],positions:[1+r.findIndex(function(i){return i.objectID===t.objectID})],queryID:t.__autocomplete_queryID,algoliaSource:["autocomplete"]}}function Bf(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}var b1=["items"],y1=["items"];function Bo(n){return Bo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Bo(n)}function Es(n){return function(t){if(Array.isArray(t))return ml(t)}(n)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(n)||function(t,e){if(t){if(typeof t=="string")return ml(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ml(t,e):void 0}}(n)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function ml(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function yg(n,t){if(n==null)return{};var e,r,i=function(s,a){if(s==null)return{};var l,c,u={},f=Object.keys(s);for(c=0;c<f.length;c++)l=f[c],a.indexOf(l)>=0||(u[l]=s[l]);return u}(n,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(r=0;r<o.length;r++)e=o[r],t.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(n,e)&&(i[e]=n[e])}return i}function Pf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Ai(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Pf(Object(e),!0).forEach(function(r){v1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Pf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function v1(n,t,e){return(t=function(r){var i=function(o){if(Bo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Bo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Bo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function As(n){return n.map(function(t){var e=t.items,r=yg(t,b1);return Ai(Ai({},r),{},{objectIDs:(e==null?void 0:e.map(function(i){return i.objectID}))||r.objectIDs})})}function w1(n){var t=n.items.reduce(function(e,r){var i;return e[r.__autocomplete_indexName]=((i=e[r.__autocomplete_indexName])!==null&&i!==void 0?i:[]).concat(r),e},{});return Object.keys(t).map(function(e){return{index:e,items:t[e],algoliaSource:["autocomplete"]}})}function lo(n){return n.objectID&&n.__autocomplete_indexName&&n.__autocomplete_queryID}function Po(n){return Po=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Po(n)}function Ti(n){return function(t){if(Array.isArray(t))return pl(t)}(n)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(n)||function(t,e){if(t){if(typeof t=="string")return pl(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?pl(t,e):void 0}}(n)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function pl(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function Mf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Fn(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Mf(Object(e),!0).forEach(function(r){S1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Mf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function S1(n,t,e){return(t=function(r){var i=function(o){if(Po(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Po(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Po(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}var vg="2.15.0",I1="https://cdn.jsdelivr.net/npm/search-insights@".concat(vg,"/dist/search-insights.min.js"),O1=_g(function(n){var t=n.onItemsChange,e=n.items,r=n.insights,i=n.state;t({insights:r,insightsEvents:w1({items:e}).map(function(o){return Fn({eventName:"Items Viewed"},o)}),state:i})},400);function Nf(n){var t=function(h){return Fn({onItemsChange:function(d){var g=d.insights,m=d.insightsEvents,p=d.state;g.viewedObjectIDs.apply(g,Ti(m.map(function(v){return Fn(Fn({},v),{},{algoliaSource:Cf(v.algoliaSource,p.context)})})))},onSelect:function(d){var g=d.insights,m=d.insightsEvents,p=d.state;g.clickedObjectIDsAfterSearch.apply(g,Ti(m.map(function(v){return Fn(Fn({},v),{},{algoliaSource:Cf(v.algoliaSource,p.context)})})))},onActive:Js,__autocomplete_clickAnalytics:!0},h)}(n),e=t.insightsClient,r=t.insightsInitParams,i=t.onItemsChange,o=t.onSelect,s=t.onActive,a=t.__autocomplete_clickAnalytics,l=e;if(e||typeof window<"u"&&function(){var h=window,d=h.AlgoliaAnalyticsObject||"aa";typeof d=="string"&&(l=h[d]),l||(h.AlgoliaAnalyticsObject=d,h[d]||(h[d]=function(){h[d].queue||(h[d].queue=[]);for(var g=arguments.length,m=new Array(g),p=0;p<g;p++)m[p]=arguments[p];h[d].queue.push(m)}),h[d].version=vg,l=h[d],function(g){var m="[Autocomplete]: Could not load search-insights.js. Please load it manually following https://alg.li/insights-autocomplete";try{var p=g.document.createElement("script");p.async=!0,p.src=I1,p.onerror=function(){console.error(m)},document.body.appendChild(p)}catch{console.error(m)}}(h))}(),!l)return{};r&&l("init",Fn({partial:!0},r));var c=function(h){var d,g,m,p=(d=function(b){return function(_){if(Array.isArray(_))return _}(b)||function(_){var O=_==null?null:typeof Symbol<"u"&&_[Symbol.iterator]||_["@@iterator"];if(O!=null){var y,T,S,F,B=[],N=!0,R=!1;try{for(S=(O=O.call(_)).next;!(N=(y=S.call(O)).done)&&(B.push(y.value),B.length!==2);N=!0);}catch(P){R=!0,T=P}finally{try{if(!N&&O.return!=null&&(F=O.return(),Object(F)!==F))return}finally{if(R)throw T}}return B}}(b)||function(_){if(_){if(typeof _=="string")return Bf(_,2);var O=Object.prototype.toString.call(_).slice(8,-1);return O==="Object"&&_.constructor&&(O=_.constructor.name),O==="Map"||O==="Set"?Array.from(_):O==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(O)?Bf(_,2):void 0}}(b)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}((h.version||"").split(".").map(Number)),g=d[0],m=d[1],g>=3||g===2&&m>=4||g===1&&m>=10);function v(b,_,O){if(p&&O!==void 0){var y=O[0].__autocomplete_algoliaCredentials,T={"X-Algolia-Application-Id":y.appId,"X-Algolia-API-Key":y.apiKey};h.apply(void 0,[b].concat(Es(_),[{headers:T}]))}else h.apply(void 0,[b].concat(Es(_)))}return{init:function(b,_){h("init",{appId:b,apiKey:_})},setAuthenticatedUserToken:function(b){h("setAuthenticatedUserToken",b)},setUserToken:function(b){h("setUserToken",b)},clickedObjectIDsAfterSearch:function(){for(var b=arguments.length,_=new Array(b),O=0;O<b;O++)_[O]=arguments[O];_.length>0&&v("clickedObjectIDsAfterSearch",As(_),_[0].items)},clickedObjectIDs:function(){for(var b=arguments.length,_=new Array(b),O=0;O<b;O++)_[O]=arguments[O];_.length>0&&v("clickedObjectIDs",As(_),_[0].items)},clickedFilters:function(){for(var b=arguments.length,_=new Array(b),O=0;O<b;O++)_[O]=arguments[O];_.length>0&&h.apply(void 0,["clickedFilters"].concat(_))},convertedObjectIDsAfterSearch:function(){for(var b=arguments.length,_=new Array(b),O=0;O<b;O++)_[O]=arguments[O];_.length>0&&v("convertedObjectIDsAfterSearch",As(_),_[0].items)},convertedObjectIDs:function(){for(var b=arguments.length,_=new Array(b),O=0;O<b;O++)_[O]=arguments[O];_.length>0&&v("convertedObjectIDs",As(_),_[0].items)},convertedFilters:function(){for(var b=arguments.length,_=new Array(b),O=0;O<b;O++)_[O]=arguments[O];_.length>0&&h.apply(void 0,["convertedFilters"].concat(_))},viewedObjectIDs:function(){for(var b=arguments.length,_=new Array(b),O=0;O<b;O++)_[O]=arguments[O];_.length>0&&_.reduce(function(y,T){var S=T.items,F=yg(T,y1);return[].concat(Es(y),Es(function(B){for(var N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:20,R=[],P=0;P<B.objectIDs.length;P+=N)R.push(Ai(Ai({},B),{},{objectIDs:B.objectIDs.slice(P,P+N)}));return R}(Ai(Ai({},F),{},{objectIDs:(S==null?void 0:S.map(function(B){return B.objectID}))||F.objectIDs})).map(function(B){return{items:S,payload:B}})))},[]).forEach(function(y){var T=y.items;return v("viewedObjectIDs",[y.payload],T)})},viewedFilters:function(){for(var b=arguments.length,_=new Array(b),O=0;O<b;O++)_[O]=arguments[O];_.length>0&&h.apply(void 0,["viewedFilters"].concat(_))}}}(l),u={current:[]},f=_g(function(h){var d=h.state;if(d.isOpen){var g=d.collections.reduce(function(m,p){return[].concat(Ti(m),Ti(p.items))},[]).filter(lo);bg(u.current.map(function(m){return m.objectID}),g.map(function(m){return m.objectID}))||(u.current=g,g.length>0&&O1({onItemsChange:i,items:g,insights:c,state:d}))}},0);return{name:"aa.algoliaInsightsPlugin",subscribe:function(h){var d=h.setContext,g=h.onSelect,m=h.onActive;function p(v){d({algoliaInsightsPlugin:{__algoliaSearchParameters:Fn(Fn({},a?{clickAnalytics:!0}:{}),v?{userToken:k1(v)}:{}),insights:c}})}l("addAlgoliaAgent","insights-plugin"),p(),l("onUserTokenChange",function(v){p(v)}),l("getUserToken",null,function(v,b){p(b)}),g(function(v){var b=v.item,_=v.state,O=v.event,y=v.source;lo(b)&&o({state:_,event:O,insights:c,item:b,insightsEvents:[Fn({eventName:"Item Selected"},Tf({item:b,items:y.getItems().filter(lo)}))]})}),m(function(v){var b=v.item,_=v.source,O=v.state,y=v.event;lo(b)&&s({state:O,event:y,insights:c,item:b,insightsEvents:[Fn({eventName:"Item Active"},Tf({item:b,items:_.getItems().filter(lo)}))]})})},onStateChange:function(h){var d=h.state;f({state:d})},__autocomplete_pluginOptions:n}}function Cf(){var n,t=arguments.length>1?arguments[1]:void 0;return[].concat(Ti(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]),["autocomplete-internal"],Ti((n=t.algoliaInsightsPlugin)!==null&&n!==void 0&&n.__automaticInsights?["autocomplete-automatic"]:[]))}function k1(n){return typeof n=="number"?n.toString():n}function Qs(n,t){var e=t;return{then:function(r,i){return Qs(n.then(Ts(r,e,n),Ts(i,e,n)),e)},catch:function(r){return Qs(n.catch(Ts(r,e,n)),e)},finally:function(r){return r&&e.onCancelList.push(r),Qs(n.finally(Ts(r&&function(){return e.onCancelList=[],r()},e,n)),e)},cancel:function(){e.isCanceled=!0;var r=e.onCancelList;e.onCancelList=[],r.forEach(function(i){i()})},isCanceled:function(){return e.isCanceled===!0}}}function Ff(n){return Qs(n,{isCanceled:!1,onCancelList:[]})}function Ts(n,t,e){return n?function(r){return t.isCanceled?r:n(r)}:e}function jf(n,t,e,r){if(!e)return null;if(n<0&&(t===null||r!==null&&t===0))return e+n;var i=(t===null?-1:t)+n;return i<=-1||i>=e?r===null?null:0:i}function Lf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Rf(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Lf(Object(e),!0).forEach(function(r){D1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Lf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function D1(n,t,e){return(t=function(r){var i=function(o){if(Mo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Mo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Mo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Mo(n){return Mo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Mo(n)}function ei(n){var t=function(i){var o=i.collections.map(function(s){return s.items.length}).reduce(function(s,a,l){var c=(s[l-1]||0)+a;return s.push(c),s},[]).reduce(function(s,a){return a<=i.activeItemId?s+1:s},0);return i.collections[o]}(n);if(!t)return null;var e=t.items[function(i){for(var o=i.state,s=i.collection,a=!1,l=0,c=0;a===!1;){var u=o.collections[l];if(u===s){a=!0;break}c+=u.items.length,l++}return o.activeItemId-c}({state:n,collection:t})],r=t.source;return{item:e,itemInputValue:r.getItemInputValue({item:e,state:n}),itemUrl:r.getItemUrl({item:e,state:n}),source:r}}function yn(n,t,e){return[n,e==null?void 0:e.sourceId,t].filter(Boolean).join("-").replace(/\s/g,"")}var E1=/((gt|sm)-|galaxy nexus)|samsung[- ]|samsungbrowser/i;function Uf(n){return n.nativeEvent||n}function No(n){return No=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},No(n)}function Vf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function A1(n,t,e){return(t=function(r){var i=function(o){if(No(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(No(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return No(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Co(n){return Co=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Co(n)}function zf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Bs(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?zf(Object(e),!0).forEach(function(r){T1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):zf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function T1(n,t,e){return(t=function(r){var i=function(o){if(Co(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Co(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Co(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Fo(n){return Fo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Fo(n)}function gl(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function Wf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function fi(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Wf(Object(e),!0).forEach(function(r){B1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Wf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function B1(n,t,e){return(t=function(r){var i=function(o){if(Fo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Fo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Fo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function jo(n){return jo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},jo(n)}function Hf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Ps(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Hf(Object(e),!0).forEach(function(r){wg(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Hf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function wg(n,t,e){return(t=function(r){var i=function(o){if(jo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(jo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return jo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Lo(n){return Lo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Lo(n)}function xf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function di(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?xf(Object(e),!0).forEach(function(r){P1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):xf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function P1(n,t,e){return(t=function(r){var i=function(o){if(Lo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Lo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Lo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Sg(n){return function(t){if(Array.isArray(t))return _l(t)}(n)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(n)||function(t,e){if(t){if(typeof t=="string")return _l(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_l(t,e):void 0}}(n)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function _l(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function Ms(n){return!!n.execute}function M1(n){var t=n.reduce(function(e,r){if(!Ms(r))return e.push(r),e;var i=r.searchClient,o=r.execute,s=r.requesterId,a=r.requests,l=e.find(function(f){return Ms(r)&&Ms(f)&&f.searchClient===i&&!!s&&f.requesterId===s});if(l){var c;(c=l.items).push.apply(c,Sg(a))}else{var u={execute:o,requesterId:s,items:a,searchClient:i};e.push(u)}return e},[]).map(function(e){if(!Ms(e))return Promise.resolve(e);var r=e,i=r.execute,o=r.items;return i({searchClient:r.searchClient,requests:o})});return Promise.all(t).then(function(e){return ts(e)})}function Ro(n){return Ro=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Ro(n)}var N1=["event","nextState","props","query","refresh","store"];function qf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Kr(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?qf(Object(e),!0).forEach(function(r){C1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):qf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function C1(n,t,e){return(t=function(r){var i=function(o){if(Ro(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Ro(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Ro(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}var Yf,bl,Ns,co=null,Kf=(Yf=-1,bl=-1,Ns=void 0,function(n){var t=++Yf;return Promise.resolve(n).then(function(e){return Ns&&t<bl?Ns:(bl=t,Ns=e,e)})});function Jr(n){var t=n.event,e=n.nextState,r=e===void 0?{}:e,i=n.props,o=n.query,s=n.refresh,a=n.store,l=function(_,O){if(_==null)return{};var y,T,S=function(B,N){if(B==null)return{};var R,P,M={},j=Object.keys(B);for(P=0;P<j.length;P++)R=j[P],N.indexOf(R)>=0||(M[R]=B[R]);return M}(_,O);if(Object.getOwnPropertySymbols){var F=Object.getOwnPropertySymbols(_);for(T=0;T<F.length;T++)y=F[T],O.indexOf(y)>=0||Object.prototype.propertyIsEnumerable.call(_,y)&&(S[y]=_[y])}return S}(n,N1);co&&i.environment.clearTimeout(co);var c=l.setCollections,u=l.setIsOpen,f=l.setQuery,h=l.setActiveItemId,d=l.setStatus,g=l.setContext;if(f(o),h(i.defaultActiveItemId),!o&&i.openOnFocus===!1){var m,p=a.getState().collections.map(function(_){return Kr(Kr({},_),{},{items:[]})});d("idle"),c(p),u((m=r.isOpen)!==null&&m!==void 0?m:i.shouldPanelOpen({state:a.getState()}));var v=Ff(Kf(p).then(function(){return Promise.resolve()}));return a.pendingRequests.add(v)}d("loading"),co=i.environment.setTimeout(function(){d("stalled")},i.stallThreshold);var b=Ff(Kf(i.getSources(Kr({query:o,refresh:s,state:a.getState()},l)).then(function(_){return Promise.all(_.map(function(O){return Promise.resolve(O.getItems(Kr({query:o,refresh:s,state:a.getState()},l))).then(function(y){return function(T,S,F){if(N=T,!!(N!=null&&N.execute)){var B=T.requesterId==="algolia"?Object.assign.apply(Object,[{}].concat(Sg(Object.keys(F.context).map(function(R){var P;return(P=F.context[R])===null||P===void 0?void 0:P.__algoliaSearchParameters})))):{};return di(di({},T),{},{requests:T.queries.map(function(R){return{query:T.requesterId==="algolia"?di(di({},R),{},{params:di(di({},B),R.params)}):R,sourceId:S,transformResponse:T.transformResponse}})})}var N;return{items:T,sourceId:S}}(y,O.sourceId,a.getState())})})).then(M1).then(function(O){var y,T=O.some(function(S){return function(F){return!Array.isArray(F)&&!!(F!=null&&F._automaticInsights)}(S.items)});return T&&g({algoliaInsightsPlugin:Kr(Kr({},((y=a.getState().context)===null||y===void 0?void 0:y.algoliaInsightsPlugin)||{}),{},{__automaticInsights:T})}),function(S,F,B){return F.map(function(N){var R,P=S.filter(function(U){return U.sourceId===N.sourceId}),M=P.map(function(U){return U.items}),j=P[0].transformResponse,V=j?j({results:R=M,hits:R.map(function(U){return U.hits}).filter(Boolean),facetHits:R.map(function(U){var H;return(H=U.facetHits)===null||H===void 0?void 0:H.map(function(D){return{label:D.value,count:D.count,_highlightResult:{label:{value:D.highlighted}}}})}).filter(Boolean)}):M;return N.onResolve({source:N,results:M,items:V,state:B.getState()}),V.every(Boolean),'The `getItems` function from source "'.concat(N.sourceId,'" must return an array of items but returned ').concat(JSON.stringify(void 0),`.

Did you forget to return items?

See: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems`),{source:N,items:V}})}(O,_,a)}).then(function(O){return function(y){var T=y.props,S=y.state,F=y.collections.reduce(function(N,R){return Ps(Ps({},N),{},wg({},R.source.sourceId,Ps(Ps({},R.source),{},{getItems:function(){return ts(R.items)}})))},{}),B=T.plugins.reduce(function(N,R){return R.reshape?R.reshape(N):N},{sourcesBySourceId:F,state:S}).sourcesBySourceId;return ts(T.reshape({sourcesBySourceId:B,sources:Object.values(B),state:S})).filter(Boolean).map(function(N){return{source:N,items:N.getItems()}})}({collections:O,props:i,state:a.getState()})})}))).then(function(_){var O;d("idle"),c(_);var y=i.shouldPanelOpen({state:a.getState()});u((O=r.isOpen)!==null&&O!==void 0?O:i.openOnFocus&&!o&&y||y);var T=ei(a.getState());if(a.getState().activeItemId!==null&&T){var S=T.item,F=T.itemInputValue,B=T.itemUrl,N=T.source;N.onActive(Kr({event:t,item:S,itemInputValue:F,itemUrl:B,refresh:s,source:N,state:a.getState()},l))}}).finally(function(){d("idle"),co&&i.environment.clearTimeout(co)});return a.pendingRequests.add(b)}function Uo(n){return Uo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Uo(n)}var F1=["event","props","refresh","store"];function Jf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Qr(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Jf(Object(e),!0).forEach(function(r){j1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Jf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function j1(n,t,e){return(t=function(r){var i=function(o){if(Uo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Uo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Uo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Vo(n){return Vo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Vo(n)}var L1=["props","refresh","store"],R1=["inputElement","formElement","panelElement"],U1=["inputElement"],V1=["inputElement","maxLength"],z1=["source"],W1=["item","source"];function Qf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function He(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Qf(Object(e),!0).forEach(function(r){H1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Qf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function H1(n,t,e){return(t=function(r){var i=function(o){if(Vo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Vo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Vo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function hi(n,t){if(n==null)return{};var e,r,i=function(s,a){if(s==null)return{};var l,c,u={},f=Object.keys(s);for(c=0;c<f.length;c++)l=f[c],a.indexOf(l)>=0||(u[l]=s[l]);return u}(n,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(r=0;r<o.length;r++)e=o[r],t.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(n,e)&&(i[e]=n[e])}return i}function zo(n){return zo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},zo(n)}function Gf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function x1(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Gf(Object(e),!0).forEach(function(r){Ig(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Gf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function Ig(n,t,e){return(t=function(r){var i=function(o){if(zo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(zo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return zo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function q1(n){var t,e,r,i,o=n.plugins,s=n.options,a=(t=(((e=s.__autocomplete_metadata)===null||e===void 0?void 0:e.userAgents)||[])[0])===null||t===void 0?void 0:t.segment,l=a?Ig({},a,Object.keys(((r=s.__autocomplete_metadata)===null||r===void 0?void 0:r.options)||{})):{};return{plugins:o.map(function(c){return{name:c.name,options:Object.keys(c.__autocomplete_pluginOptions||[])}}),options:x1({"autocomplete-core":Object.keys(s)},l),ua:_1.concat(((i=s.__autocomplete_metadata)===null||i===void 0?void 0:i.userAgents)||[])}}function Xf(n){var t,e=n.state;return e.isOpen===!1||e.activeItemId===null?null:((t=ei(e))===null||t===void 0?void 0:t.itemInputValue)||null}function Wo(n){return Wo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Wo(n)}function Zf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Yt(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Zf(Object(e),!0).forEach(function(r){Y1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Zf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function Y1(n,t,e){return(t=function(r){var i=function(o){if(Wo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Wo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Wo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}var K1=function(n,t){switch(t.type){case"setActiveItemId":case"mousemove":return Yt(Yt({},n),{},{activeItemId:t.payload});case"setQuery":return Yt(Yt({},n),{},{query:t.payload,completion:null});case"setCollections":return Yt(Yt({},n),{},{collections:t.payload});case"setIsOpen":return Yt(Yt({},n),{},{isOpen:t.payload});case"setStatus":return Yt(Yt({},n),{},{status:t.payload});case"setContext":return Yt(Yt({},n),{},{context:Yt(Yt({},n.context),t.payload)});case"ArrowDown":var e=Yt(Yt({},n),{},{activeItemId:t.payload.hasOwnProperty("nextActiveItemId")?t.payload.nextActiveItemId:jf(1,n.activeItemId,Ql(n),t.props.defaultActiveItemId)});return Yt(Yt({},e),{},{completion:Xf({state:e})});case"ArrowUp":var r=Yt(Yt({},n),{},{activeItemId:jf(-1,n.activeItemId,Ql(n),t.props.defaultActiveItemId)});return Yt(Yt({},r),{},{completion:Xf({state:r})});case"Escape":return n.isOpen?Yt(Yt({},n),{},{activeItemId:null,isOpen:!1,completion:null}):Yt(Yt({},n),{},{activeItemId:null,query:"",status:"idle",collections:[]});case"submit":return Yt(Yt({},n),{},{activeItemId:null,isOpen:!1,status:"idle"});case"reset":return Yt(Yt({},n),{},{activeItemId:t.props.openOnFocus===!0?t.props.defaultActiveItemId:null,status:"idle",completion:null,query:""});case"focus":return Yt(Yt({},n),{},{activeItemId:t.props.defaultActiveItemId,isOpen:(t.props.openOnFocus||!!n.query)&&t.props.shouldPanelOpen({state:n})});case"blur":return t.props.debug?n:Yt(Yt({},n),{},{isOpen:!1,activeItemId:null});case"mouseleave":return Yt(Yt({},n),{},{activeItemId:t.props.defaultActiveItemId});default:return"The reducer action ".concat(JSON.stringify(t.type)," is not supported."),n}};function Ho(n){return Ho=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Ho(n)}function $f(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Gr(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?$f(Object(e),!0).forEach(function(r){J1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):$f(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function J1(n,t,e){return(t=function(r){var i=function(o){if(Ho(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Ho(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Ho(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Q1(n){var t=[],e=function(u,f){var h,d=typeof window<"u"?window:{},g=u.plugins||[];return fi(fi({debug:!1,openOnFocus:!1,enterKeyHint:void 0,ignoreCompositionEvents:!1,placeholder:"",autoFocus:!1,defaultActiveItemId:null,stallThreshold:300,insights:void 0,environment:d,shouldPanelOpen:function(m){return Ql(m.state)>0},reshape:function(m){return m.sources}},u),{},{id:(h=u.id)!==null&&h!==void 0?h:"autocomplete-".concat(g1++),plugins:g,initialState:fi({activeItemId:null,query:"",completion:null,collections:[],isOpen:!1,status:"idle",context:{}},u.initialState),onStateChange:function(m){var p;(p=u.onStateChange)===null||p===void 0||p.call(u,m),g.forEach(function(v){var b;return(b=v.onStateChange)===null||b===void 0?void 0:b.call(v,m)})},onSubmit:function(m){var p;(p=u.onSubmit)===null||p===void 0||p.call(u,m),g.forEach(function(v){var b;return(b=v.onSubmit)===null||b===void 0?void 0:b.call(v,m)})},onReset:function(m){var p;(p=u.onReset)===null||p===void 0||p.call(u,m),g.forEach(function(v){var b;return(b=v.onReset)===null||b===void 0?void 0:b.call(v,m)})},getSources:function(m){return Promise.all([].concat(function(p){return function(v){if(Array.isArray(v))return gl(v)}(p)||function(v){if(typeof Symbol<"u"&&v[Symbol.iterator]!=null||v["@@iterator"]!=null)return Array.from(v)}(p)||function(v,b){if(v){if(typeof v=="string")return gl(v,b);var _=Object.prototype.toString.call(v).slice(8,-1);return _==="Object"&&v.constructor&&(_=v.constructor.name),_==="Map"||_==="Set"?Array.from(v):_==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?gl(v,b):void 0}}(p)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}(g.map(function(p){return p.getSources})),[u.getSources]).filter(Boolean).map(function(p){return function(v,b){var _=[];return Promise.resolve(v(b)).then(function(O){return Promise.all(O.filter(function(y){return!!y}).map(function(y){if(y.sourceId,_.includes(y.sourceId))throw new Error("[Autocomplete] The `sourceId` ".concat(JSON.stringify(y.sourceId)," is not unique."));_.push(y.sourceId);var T={getItemInputValue:function(F){return F.state.query},getItemUrl:function(){},onSelect:function(F){(0,F.setIsOpen)(!1)},onActive:Js,onResolve:Js};Object.keys(T).forEach(function(F){T[F].__default=!0});var S=Rf(Rf({},T),y);return Promise.resolve(S)}))})}(p,m)})).then(function(p){return ts(p)}).then(function(p){return p.map(function(v){return fi(fi({},v),{},{onSelect:function(b){v.onSelect(b),f.forEach(function(_){var O;return(O=_.onSelect)===null||O===void 0?void 0:O.call(_,b)})},onActive:function(b){v.onActive(b),f.forEach(function(_){var O;return(O=_.onActive)===null||O===void 0?void 0:O.call(_,b)})},onResolve:function(b){v.onResolve(b),f.forEach(function(_){var O;return(O=_.onResolve)===null||O===void 0?void 0:O.call(_,b)})}})})})},navigator:fi({navigate:function(m){var p=m.itemUrl;d.location.assign(p)},navigateNewTab:function(m){var p=m.itemUrl,v=d.open(p,"_blank","noopener");v==null||v.focus()},navigateNewWindow:function(m){var p=m.itemUrl;d.open(p,"_blank","noopener")}},u.navigator)})}(n,t),r=function(u,f,h){var d,g=f.initialState;return{getState:function(){return g},dispatch:function(m,p){var v=function(b){for(var _=1;_<arguments.length;_++){var O=arguments[_]!=null?arguments[_]:{};_%2?Vf(Object(O),!0).forEach(function(y){A1(b,y,O[y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(b,Object.getOwnPropertyDescriptors(O)):Vf(Object(O)).forEach(function(y){Object.defineProperty(b,y,Object.getOwnPropertyDescriptor(O,y))})}return b}({},g);g=u(g,{type:m,props:f,payload:p}),h({state:g,prevState:v})},pendingRequests:(d=[],{add:function(m){return d.push(m),m.finally(function(){d=d.filter(function(p){return p!==m})})},cancelAll:function(){d.forEach(function(m){return m.cancel()})},isEmpty:function(){return d.length===0}})}}(K1,e,function(u){var f,h,d=u.prevState,g=u.state;if(e.onStateChange(Gr({prevState:d,state:g,refresh:s,navigator:e.navigator},i)),!l()&&(f=g.context)!==null&&f!==void 0&&(h=f.algoliaInsightsPlugin)!==null&&h!==void 0&&h.__automaticInsights&&e.insights!==!1){var m=Nf({__autocomplete_clickAnalytics:!1});e.plugins.push(m),a([m])}}),i=function(u){var f=u.store;return{setActiveItemId:function(h){f.dispatch("setActiveItemId",h)},setQuery:function(h){f.dispatch("setQuery",h)},setCollections:function(h){var d=0,g=h.map(function(m){return Bs(Bs({},m),{},{items:ts(m.items).map(function(p){return Bs(Bs({},p),{},{__autocomplete_id:d++})})})});f.dispatch("setCollections",g)},setIsOpen:function(h){f.dispatch("setIsOpen",h)},setStatus:function(h){f.dispatch("setStatus",h)},setContext:function(h){f.dispatch("setContext",h)}}}({store:r}),o=function(u){var f=u.props,h=u.refresh,d=u.store,g=hi(u,L1);return{getEnvironmentProps:function(m){var p=m.inputElement,v=m.formElement,b=m.panelElement;function _(O){!d.getState().isOpen&&d.pendingRequests.isEmpty()||O.target===p||[v,b].some(function(y){return(T=y)===(S=O.target)||T.contains(S);var T,S})===!1&&(d.dispatch("blur",null),f.debug||d.pendingRequests.cancelAll())}return He({onTouchStart:_,onMouseDown:_,onTouchMove:function(O){d.getState().isOpen!==!1&&p===f.environment.document.activeElement&&O.target!==p&&p.blur()}},hi(m,R1))},getRootProps:function(m){return He({role:"combobox","aria-expanded":d.getState().isOpen,"aria-haspopup":"listbox","aria-controls":d.getState().isOpen?d.getState().collections.map(function(p){var v=p.source;return yn(f.id,"list",v)}).join(" "):void 0,"aria-labelledby":yn(f.id,"label")},m)},getFormProps:function(m){return m.inputElement,He({action:"",noValidate:!0,role:"search",onSubmit:function(p){var v;p.preventDefault(),f.onSubmit(He({event:p,refresh:h,state:d.getState()},g)),d.dispatch("submit",null),(v=m.inputElement)===null||v===void 0||v.blur()},onReset:function(p){var v;p.preventDefault(),f.onReset(He({event:p,refresh:h,state:d.getState()},g)),d.dispatch("reset",null),(v=m.inputElement)===null||v===void 0||v.focus()}},hi(m,U1))},getLabelProps:function(m){return He({htmlFor:yn(f.id,"input"),id:yn(f.id,"label")},m)},getInputProps:function(m){var p;function v(B){(f.openOnFocus||d.getState().query)&&Jr(He({event:B,props:f,query:d.getState().completion||d.getState().query,refresh:h,store:d},g)),d.dispatch("focus",null)}var b=m||{};b.inputElement;var _=b.maxLength,O=_===void 0?512:_,y=hi(b,V1),T=ei(d.getState()),S=function(B){return!!(B&&B.match(E1))}(((p=f.environment.navigator)===null||p===void 0?void 0:p.userAgent)||""),F=f.enterKeyHint||(T!=null&&T.itemUrl&&!S?"go":"search");return He({"aria-autocomplete":"both","aria-activedescendant":d.getState().isOpen&&d.getState().activeItemId!==null?yn(f.id,"item-".concat(d.getState().activeItemId),T==null?void 0:T.source):void 0,"aria-controls":d.getState().isOpen?d.getState().collections.map(function(B){var N=B.source;return yn(f.id,"list",N)}).join(" "):void 0,"aria-labelledby":yn(f.id,"label"),value:d.getState().completion||d.getState().query,id:yn(f.id,"input"),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",enterKeyHint:F,spellCheck:"false",autoFocus:f.autoFocus,placeholder:f.placeholder,maxLength:O,type:"search",onChange:function(B){var N=B.currentTarget.value;f.ignoreCompositionEvents&&Uf(B).isComposing?g.setQuery(N):Jr(He({event:B,props:f,query:N.slice(0,O),refresh:h,store:d},g))},onCompositionEnd:function(B){Jr(He({event:B,props:f,query:B.currentTarget.value.slice(0,O),refresh:h,store:d},g))},onKeyDown:function(B){Uf(B).isComposing||function(N){var R=N.event,P=N.props,M=N.refresh,j=N.store,V=function(Bt,Ct){if(Bt==null)return{};var K,Oe,Kt=function(qe,_n){if(qe==null)return{};var Ve,Nn,mn={},Hn=Object.keys(qe);for(Nn=0;Nn<Hn.length;Nn++)Ve=Hn[Nn],_n.indexOf(Ve)>=0||(mn[Ve]=qe[Ve]);return mn}(Bt,Ct);if(Object.getOwnPropertySymbols){var Be=Object.getOwnPropertySymbols(Bt);for(Oe=0;Oe<Be.length;Oe++)K=Be[Oe],Ct.indexOf(K)>=0||Object.prototype.propertyIsEnumerable.call(Bt,K)&&(Kt[K]=Bt[K])}return Kt}(N,F1);if(R.key==="ArrowUp"||R.key==="ArrowDown"){var U=function(){var Bt=ei(j.getState()),Ct=P.environment.document.getElementById(yn(P.id,"item-".concat(j.getState().activeItemId),Bt==null?void 0:Bt.source));Ct&&(Ct.scrollIntoViewIfNeeded?Ct.scrollIntoViewIfNeeded(!1):Ct.scrollIntoView(!1))},H=function(){var Bt=ei(j.getState());if(j.getState().activeItemId!==null&&Bt){var Ct=Bt.item,K=Bt.itemInputValue,Oe=Bt.itemUrl,Kt=Bt.source;Kt.onActive(Qr({event:R,item:Ct,itemInputValue:K,itemUrl:Oe,refresh:M,source:Kt,state:j.getState()},V))}};R.preventDefault(),j.getState().isOpen===!1&&(P.openOnFocus||j.getState().query)?Jr(Qr({event:R,props:P,query:j.getState().query,refresh:M,store:j},V)).then(function(){j.dispatch(R.key,{nextActiveItemId:P.defaultActiveItemId}),H(),setTimeout(U,0)}):(j.dispatch(R.key,{}),H(),U())}else if(R.key==="Escape")R.preventDefault(),j.dispatch(R.key,null),j.pendingRequests.cancelAll();else if(R.key==="Tab")j.dispatch("blur",null),j.pendingRequests.cancelAll();else if(R.key==="Enter"){if(j.getState().activeItemId===null||j.getState().collections.every(function(Bt){return Bt.items.length===0}))return void(P.debug||j.pendingRequests.cancelAll());R.preventDefault();var D=ei(j.getState()),X=D.item,Q=D.itemInputValue,Z=D.itemUrl,Dt=D.source;if(R.metaKey||R.ctrlKey)Z!==void 0&&(Dt.onSelect(Qr({event:R,item:X,itemInputValue:Q,itemUrl:Z,refresh:M,source:Dt,state:j.getState()},V)),P.navigator.navigateNewTab({itemUrl:Z,item:X,state:j.getState()}));else if(R.shiftKey)Z!==void 0&&(Dt.onSelect(Qr({event:R,item:X,itemInputValue:Q,itemUrl:Z,refresh:M,source:Dt,state:j.getState()},V)),P.navigator.navigateNewWindow({itemUrl:Z,item:X,state:j.getState()}));else if(!R.altKey){if(Z!==void 0)return Dt.onSelect(Qr({event:R,item:X,itemInputValue:Q,itemUrl:Z,refresh:M,source:Dt,state:j.getState()},V)),void P.navigator.navigate({itemUrl:Z,item:X,state:j.getState()});Jr(Qr({event:R,nextState:{isOpen:!1},props:P,query:Q,refresh:M,store:j},V)).then(function(){Dt.onSelect(Qr({event:R,item:X,itemInputValue:Q,itemUrl:Z,refresh:M,source:Dt,state:j.getState()},V))})}}}(He({event:B,props:f,refresh:h,store:d},g))},onFocus:v,onBlur:Js,onClick:function(B){m.inputElement!==f.environment.document.activeElement||d.getState().isOpen||v(B)}},y)},getPanelProps:function(m){return He({onMouseDown:function(p){p.preventDefault()},onMouseLeave:function(){d.dispatch("mouseleave",null)}},m)},getListProps:function(m){var p=m||{},v=p.source,b=hi(p,z1);return He({role:"listbox","aria-labelledby":yn(f.id,"label"),id:yn(f.id,"list",v)},b)},getItemProps:function(m){var p=m.item,v=m.source,b=hi(m,W1);return He({id:yn(f.id,"item-".concat(p.__autocomplete_id),v),role:"option","aria-selected":d.getState().activeItemId===p.__autocomplete_id,onMouseMove:function(_){if(p.__autocomplete_id!==d.getState().activeItemId){d.dispatch("mousemove",p.__autocomplete_id);var O=ei(d.getState());if(d.getState().activeItemId!==null&&O){var y=O.item,T=O.itemInputValue,S=O.itemUrl,F=O.source;F.onActive(He({event:_,item:y,itemInputValue:T,itemUrl:S,refresh:h,source:F,state:d.getState()},g))}}},onMouseDown:function(_){_.preventDefault()},onClick:function(_){var O=v.getItemInputValue({item:p,state:d.getState()}),y=v.getItemUrl({item:p,state:d.getState()});(y?Promise.resolve():Jr(He({event:_,nextState:{isOpen:!1},props:f,query:O,refresh:h,store:d},g))).then(function(){v.onSelect(He({event:_,item:p,itemInputValue:O,itemUrl:y,refresh:h,source:v,state:d.getState()},g))})}},b)}}}(Gr({props:e,refresh:s,store:r,navigator:e.navigator},i));function s(){return Jr(Gr({event:new Event("input"),nextState:{isOpen:r.getState().isOpen},props:e,navigator:e.navigator,query:r.getState().query,refresh:s,store:r},i))}function a(u){u.forEach(function(f){var h;return(h=f.subscribe)===null||h===void 0?void 0:h.call(f,Gr(Gr({},i),{},{navigator:e.navigator,refresh:s,onSelect:function(d){t.push({onSelect:d})},onActive:function(d){t.push({onActive:d})},onResolve:function(d){t.push({onResolve:d})}}))})}function l(){return e.plugins.some(function(u){return u.name==="aa.algoliaInsightsPlugin"})}if(e.insights&&!l()){var c=typeof e.insights=="boolean"?{}:e.insights;e.plugins.push(Nf(c))}return a(e.plugins),function(u){var f,h,d=u.metadata,g=u.environment;if(!((f=g.navigator)===null||f===void 0||(h=f.userAgent)===null||h===void 0)&&h.includes("Algolia Crawler")){var m=g.document.createElement("meta"),p=g.document.querySelector("head");m.name="algolia:metadata",setTimeout(function(){m.content=JSON.stringify(d),p.appendChild(m)},0)}}({metadata:q1({plugins:e.plugins,options:n}),environment:e.environment}),Gr(Gr({refresh:s,navigator:e.navigator},o),i)}function G1(n){var t=n.translations,e=(t===void 0?{}:t).searchByText,r=e===void 0?"Search by":e;return k.createElement("a",{href:"https://www.algolia.com/ref/docsearch/?utm_source=".concat(window.location.hostname,"&utm_medium=referral&utm_content=powered_by&utm_campaign=docsearch"),target:"_blank",rel:"noopener noreferrer"},k.createElement("span",{className:"DocSearch-Label"},r),k.createElement("svg",{width:"77",height:"19","aria-label":"Algolia",role:"img",id:"Layer_1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 2196.2 500"},k.createElement("defs",null,k.createElement("style",null,".cls-1,.cls-2{fill:#003dff;}.cls-2{fill-rule:evenodd;}")),k.createElement("path",{className:"cls-2",d:"M1070.38,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),k.createElement("rect",{className:"cls-1",x:"1845.88",y:"104.73",width:"62.58",height:"277.9",rx:"5.9",ry:"5.9"}),k.createElement("path",{className:"cls-2",d:"M1851.78,71.38h50.77c3.26,0,5.9-2.64,5.9-5.9V5.9c0-3.62-3.24-6.39-6.82-5.83l-50.77,7.95c-2.87,.45-4.99,2.92-4.99,5.83v51.62c0,3.26,2.64,5.9,5.9,5.9Z"}),k.createElement("path",{className:"cls-2",d:"M1764.03,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),k.createElement("path",{className:"cls-2",d:"M1631.95,142.72c-11.14-12.25-24.83-21.65-40.78-28.31-15.92-6.53-33.26-9.85-52.07-9.85-18.78,0-36.15,3.17-51.92,9.85-15.59,6.66-29.29,16.05-40.76,28.31-11.47,12.23-20.38,26.87-26.76,44.03-6.38,17.17-9.24,37.37-9.24,58.36,0,20.99,3.19,36.87,9.55,54.21,6.38,17.32,15.14,32.11,26.45,44.36,11.29,12.23,24.83,21.62,40.6,28.46,15.77,6.83,40.12,10.33,52.4,10.48,12.25,0,36.78-3.82,52.7-10.48,15.92-6.68,29.46-16.23,40.78-28.46,11.29-12.25,20.05-27.04,26.25-44.36,6.22-17.34,9.24-33.22,9.24-54.21,0-20.99-3.34-41.19-10.03-58.36-6.38-17.17-15.14-31.8-26.43-44.03Zm-44.43,163.75c-11.47,15.75-27.56,23.7-48.09,23.7-20.55,0-36.63-7.8-48.1-23.7-11.47-15.75-17.21-34.01-17.21-61.2,0-26.89,5.59-49.14,17.06-64.87,11.45-15.75,27.54-23.52,48.07-23.52,20.55,0,36.63,7.78,48.09,23.52,11.47,15.57,17.36,37.98,17.36,64.87,0,27.19-5.72,45.3-17.19,61.2Z"}),k.createElement("path",{className:"cls-2",d:"M894.42,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),k.createElement("path",{className:"cls-2",d:"M2133.97,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),k.createElement("path",{className:"cls-2",d:"M1314.05,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-11.79,18.34-19.6,39.64-22.11,62.59-.58,5.3-.88,10.68-.88,16.14s.31,11.15,.93,16.59c4.28,38.09,23.14,71.61,50.66,94.52,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47h0c17.99,0,34.61-5.93,48.16-15.97,16.29-11.58,28.88-28.54,34.48-47.75v50.26h-.11v11.08c0,21.84-5.71,38.27-17.34,49.36-11.61,11.08-31.04,16.63-58.25,16.63-11.12,0-28.79-.59-46.6-2.41-2.83-.29-5.46,1.5-6.27,4.22l-12.78,43.11c-1.02,3.46,1.27,7.02,4.83,7.53,21.52,3.08,42.52,4.68,54.65,4.68,48.91,0,85.16-10.75,108.89-32.21,21.48-19.41,33.15-48.89,35.2-88.52V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,64.1s.65,139.13,0,143.36c-12.08,9.77-27.11,13.59-43.49,14.7-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-1.32,0-2.63-.03-3.94-.1-40.41-2.11-74.52-37.26-74.52-79.38,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33Z"}),k.createElement("path",{className:"cls-1",d:"M249.83,0C113.3,0,2,110.09,.03,246.16c-2,138.19,110.12,252.7,248.33,253.5,42.68,.25,83.79-10.19,120.3-30.03,3.56-1.93,4.11-6.83,1.08-9.51l-23.38-20.72c-4.75-4.21-11.51-5.4-17.36-2.92-25.48,10.84-53.17,16.38-81.71,16.03-111.68-1.37-201.91-94.29-200.13-205.96,1.76-110.26,92-199.41,202.67-199.41h202.69V407.41l-115-102.18c-3.72-3.31-9.42-2.66-12.42,1.31-18.46,24.44-48.53,39.64-81.93,37.34-46.33-3.2-83.87-40.5-87.34-86.81-4.15-55.24,39.63-101.52,94-101.52,49.18,0,89.68,37.85,93.91,85.95,.38,4.28,2.31,8.27,5.52,11.12l29.95,26.55c3.4,3.01,8.79,1.17,9.63-3.3,2.16-11.55,2.92-23.58,2.07-35.92-4.82-70.34-61.8-126.93-132.17-131.26-80.68-4.97-148.13,58.14-150.27,137.25-2.09,77.1,61.08,143.56,138.19,145.26,32.19,.71,62.03-9.41,86.14-26.95l150.26,133.2c6.44,5.71,16.61,1.14,16.61-7.47V9.48C499.66,4.25,495.42,0,490.18,0H249.83Z"})))}function Cs(n){return k.createElement("svg",{width:"15",height:"15","aria-label":n.ariaLabel,role:"img"},k.createElement("g",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.2"},n.children))}function X1(n){var t=n.translations,e=t===void 0?{}:t,r=e.selectText,i=r===void 0?"to select":r,o=e.selectKeyAriaLabel,s=o===void 0?"Enter key":o,a=e.navigateText,l=a===void 0?"to navigate":a,c=e.navigateUpKeyAriaLabel,u=c===void 0?"Arrow up":c,f=e.navigateDownKeyAriaLabel,h=f===void 0?"Arrow down":f,d=e.closeText,g=d===void 0?"to close":d,m=e.closeKeyAriaLabel,p=m===void 0?"Escape key":m,v=e.searchByText,b=v===void 0?"Search by":v;return k.createElement(k.Fragment,null,k.createElement("div",{className:"DocSearch-Logo"},k.createElement(G1,{translations:{searchByText:b}})),k.createElement("ul",{className:"DocSearch-Commands"},k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Cs,{ariaLabel:s},k.createElement("path",{d:"M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3"}))),k.createElement("span",{className:"DocSearch-Label"},i)),k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Cs,{ariaLabel:h},k.createElement("path",{d:"M7.5 3.5v8M10.5 8.5l-3 3-3-3"}))),k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Cs,{ariaLabel:u},k.createElement("path",{d:"M7.5 11.5v-8M10.5 6.5l-3-3-3 3"}))),k.createElement("span",{className:"DocSearch-Label"},l)),k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Cs,{ariaLabel:p},k.createElement("path",{d:"M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956"}))),k.createElement("span",{className:"DocSearch-Label"},g))))}function Z1(n){var t=n.hit,e=n.children;return k.createElement("a",{href:t.url},e)}function $1(){return k.createElement("svg",{viewBox:"0 0 38 38",stroke:"currentColor",strokeOpacity:".5"},k.createElement("g",{fill:"none",fillRule:"evenodd"},k.createElement("g",{transform:"translate(1 1)",strokeWidth:"2"},k.createElement("circle",{strokeOpacity:".3",cx:"18",cy:"18",r:"18"}),k.createElement("path",{d:"M36 18c0-9.94-8.06-18-18-18"},k.createElement("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"1s",repeatCount:"indefinite"})))))}function tw(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M3.18 6.6a8.23 8.23 0 1112.93 9.94h0a8.23 8.23 0 01-11.63 0"}),k.createElement("path",{d:"M6.44 7.25H2.55V3.36M10.45 6v5.6M10.45 11.6L13 13"})))}function Gl(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M10 10l5.09-5.09L10 10l5.09 5.09L10 10zm0 0L4.91 4.91 10 10l-5.09 5.09L10 10z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function ew(){return k.createElement("svg",{className:"DocSearch-Hit-Select-Icon",width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M18 3v4c0 2-2 4-4 4H2"}),k.createElement("path",{d:"M8 17l-6-6 6-6"})))}var nw=function(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M17 6v12c0 .52-.2 1-1 1H4c-.7 0-1-.33-1-1V2c0-.55.42-1 1-1h8l5 5zM14 8h-3.13c-.51 0-.87-.34-.87-.87V4",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))};function rw(n){switch(n.type){case"lvl1":return k.createElement(nw,null);case"content":return k.createElement(ow,null);default:return k.createElement(iw,null)}}function iw(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M13 13h4-4V8H7v5h6v4-4H7V8H3h4V3v5h6V3v5h4-4v5zm-6 0v4-4H3h4z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function ow(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M17 5H3h14zm0 5H3h14zm0 5H3h14z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function td(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M10 14.2L5 17l1-5.6-4-4 5.5-.7 2.5-5 2.5 5 5.6.8-4 4 .9 5.5z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function sw(){return k.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M19 4.8a16 16 0 00-2-1.2m-3.3-1.2A16 16 0 001.1 4.7M16.7 8a12 12 0 00-2.8-1.4M10 6a12 12 0 00-6.7 2M12.3 14.7a4 4 0 00-4.5 0M14.5 11.4A8 8 0 0010 10M3 16L18 2M10 18h0"}))}function aw(){return k.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M15.5 4.8c2 3 1.7 7-1 9.7h0l4.3 4.3-4.3-4.3a7.8 7.8 0 01-9.8 1m-2.2-2.2A7.8 7.8 0 0113.2 2.4M2 18L18 2"}))}function lw(n){var t=n.translations,e=t===void 0?{}:t,r=e.titleText,i=r===void 0?"Unable to fetch results":r,o=e.helpText,s=o===void 0?"You might want to check your network connection.":o;return k.createElement("div",{className:"DocSearch-ErrorScreen"},k.createElement("div",{className:"DocSearch-Screen-Icon"},k.createElement(sw,null)),k.createElement("p",{className:"DocSearch-Title"},i),k.createElement("p",{className:"DocSearch-Help"},s))}var cw=["translations"];function uw(n){var t=n.translations,e=t===void 0?{}:t,r=nr(n,cw),i=e.noResultsText,o=i===void 0?"No results for":i,s=e.suggestedQueryText,a=s===void 0?"Try searching for":s,l=e.reportMissingResultsText,c=l===void 0?"Believe this query should return results?":l,u=e.reportMissingResultsLinkText,f=u===void 0?"Let us know.":u,h=r.state.context.searchSuggestions;return k.createElement("div",{className:"DocSearch-NoResults"},k.createElement("div",{className:"DocSearch-Screen-Icon"},k.createElement(aw,null)),k.createElement("p",{className:"DocSearch-Title"},o,' "',k.createElement("strong",null,r.state.query),'"'),h&&h.length>0&&k.createElement("div",{className:"DocSearch-NoResults-Prefill-List"},k.createElement("p",{className:"DocSearch-Help"},a,":"),k.createElement("ul",null,h.slice(0,3).reduce(function(d,g){return[].concat(To(d),[k.createElement("li",{key:g},k.createElement("button",{className:"DocSearch-Prefill",key:g,type:"button",onClick:function(){r.setQuery(g.toLowerCase()+" "),r.refresh(),r.inputRef.current.focus()}},g))])},[]))),r.getMissingResultsUrl&&k.createElement("p",{className:"DocSearch-Help"},"".concat(c," "),k.createElement("a",{href:r.getMissingResultsUrl({query:r.state.query}),target:"_blank",rel:"noopener noreferrer"},f)))}var fw=["hit","attribute","tagName"];function ed(n,t){return t.split(".").reduce(function(e,r){return e!=null&&e[r]?e[r]:null},n)}function mi(n){var t=n.hit,e=n.attribute,r=n.tagName;return tr(r===void 0?"span":r,Vt(Vt({},nr(n,fw)),{},{dangerouslySetInnerHTML:{__html:ed(t,"_snippetResult.".concat(e,".value"))||ed(t,e)}}))}function Xl(n){return n.collection&&n.collection.items.length!==0?k.createElement("section",{className:"DocSearch-Hits"},k.createElement("div",{className:"DocSearch-Hit-source"},n.title),k.createElement("ul",n.getListProps(),n.collection.items.map(function(t,e){return k.createElement(dw,Qe({key:[n.title,t.objectID].join(":"),item:t,index:e},n))}))):null}function dw(n){var t=n.item,e=n.index,r=n.renderIcon,i=n.renderAction,o=n.getItemProps,s=n.onItemClick,a=n.collection,l=n.hitComponent,c=fn(k.useState(!1),2),u=c[0],f=c[1],h=fn(k.useState(!1),2),d=h[0],g=h[1],m=k.useRef(null),p=l;return k.createElement("li",Qe({className:["DocSearch-Hit",t.__docsearch_parent&&"DocSearch-Hit--Child",u&&"DocSearch-Hit--deleting",d&&"DocSearch-Hit--favoriting"].filter(Boolean).join(" "),onTransitionEnd:function(){m.current&&m.current()}},o({item:t,source:a.source,onClick:function(v){s(t,v)}})),k.createElement(p,{hit:t},k.createElement("div",{className:"DocSearch-Hit-Container"},r({item:t,index:e}),t.hierarchy[t.type]&&t.type==="lvl1"&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(mi,{className:"DocSearch-Hit-title",hit:t,attribute:"hierarchy.lvl1"}),t.content&&k.createElement(mi,{className:"DocSearch-Hit-path",hit:t,attribute:"content"})),t.hierarchy[t.type]&&(t.type==="lvl2"||t.type==="lvl3"||t.type==="lvl4"||t.type==="lvl5"||t.type==="lvl6")&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(mi,{className:"DocSearch-Hit-title",hit:t,attribute:"hierarchy.".concat(t.type)}),k.createElement(mi,{className:"DocSearch-Hit-path",hit:t,attribute:"hierarchy.lvl1"})),t.type==="content"&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(mi,{className:"DocSearch-Hit-title",hit:t,attribute:"content"}),k.createElement(mi,{className:"DocSearch-Hit-path",hit:t,attribute:"hierarchy.lvl1"})),i({item:t,runDeleteTransition:function(v){f(!0),m.current=v},runFavoriteTransition:function(v){g(!0),m.current=v}}))))}function nd(n,t,e){return n.reduce(function(r,i){var o=t(i);return r.hasOwnProperty(o)||(r[o]=[]),r[o].length<(e||5)&&r[o].push(i),r},{})}function rd(n){return n}function Fs(n){return n.button===1||n.altKey||n.ctrlKey||n.metaKey||n.shiftKey}function hw(){}var Og=/(<mark>|<\/mark>)/g,mw=RegExp(Og.source);function kg(n){var t,e,r=n;if(!r.__docsearch_parent&&!n._highlightResult)return n.hierarchy.lvl0;var i=r.__docsearch_parent?(t=r.__docsearch_parent)===null||t===void 0||(t=t._highlightResult)===null||t===void 0||(t=t.hierarchy)===null||t===void 0?void 0:t.lvl0:(e=n._highlightResult)===null||e===void 0||(e=e.hierarchy)===null||e===void 0?void 0:e.lvl0;return i?i.value&&mw.test(i.value)?i.value.replace(Og,""):i.value:n.hierarchy.lvl0}function pw(n){return k.createElement("div",{className:"DocSearch-Dropdown-Container"},n.state.collections.map(function(t){if(t.items.length===0)return null;var e=kg(t.items[0]);return k.createElement(Xl,Qe({},n,{key:t.source.sourceId,title:e,collection:t,renderIcon:function(r){var i,o=r.item,s=r.index;return k.createElement(k.Fragment,null,o.__docsearch_parent&&k.createElement("svg",{className:"DocSearch-Hit-Tree",viewBox:"0 0 24 54"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},o.__docsearch_parent!==((i=t.items[s+1])===null||i===void 0?void 0:i.__docsearch_parent)?k.createElement("path",{d:"M8 6v21M20 27H8.3"}):k.createElement("path",{d:"M8 6v42M20 27H8.3"}))),k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(rw,{type:o.type})))},renderAction:function(){return k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement(ew,null))}}))}),n.resultsFooterComponent&&k.createElement("section",{className:"DocSearch-HitsFooter"},k.createElement(n.resultsFooterComponent,{state:n.state})))}var gw=["translations"];function _w(n){var t=n.translations,e=t===void 0?{}:t,r=nr(n,gw),i=e.recentSearchesTitle,o=i===void 0?"Recent":i,s=e.noRecentSearchesText,a=s===void 0?"No recent searches":s,l=e.saveRecentSearchButtonTitle,c=l===void 0?"Save this search":l,u=e.removeRecentSearchButtonTitle,f=u===void 0?"Remove this search from history":u,h=e.favoriteSearchesTitle,d=h===void 0?"Favorite":h,g=e.removeFavoriteSearchButtonTitle,m=g===void 0?"Remove this search from favorites":g;return r.state.status==="idle"&&r.hasCollections===!1?r.disableUserPersonalization?null:k.createElement("div",{className:"DocSearch-StartScreen"},k.createElement("p",{className:"DocSearch-Help"},a)):r.hasCollections===!1?null:k.createElement("div",{className:"DocSearch-Dropdown-Container"},k.createElement(Xl,Qe({},r,{title:o,collection:r.state.collections[0],renderIcon:function(){return k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(tw,null))},renderAction:function(p){var v=p.item,b=p.runFavoriteTransition,_=p.runDeleteTransition;return k.createElement(k.Fragment,null,k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:c,type:"submit",onClick:function(O){O.preventDefault(),O.stopPropagation(),b(function(){r.favoriteSearches.add(v),r.recentSearches.remove(v),r.refresh()})}},k.createElement(td,null))),k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:f,type:"submit",onClick:function(O){O.preventDefault(),O.stopPropagation(),_(function(){r.recentSearches.remove(v),r.refresh()})}},k.createElement(Gl,null))))}})),k.createElement(Xl,Qe({},r,{title:d,collection:r.state.collections[1],renderIcon:function(){return k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(td,null))},renderAction:function(p){var v=p.item,b=p.runDeleteTransition;return k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:m,type:"submit",onClick:function(_){_.preventDefault(),_.stopPropagation(),b(function(){r.favoriteSearches.remove(v),r.refresh()})}},k.createElement(Gl,null)))}})))}var bw=["translations"],yw=k.memo(function(n){var t=n.translations,e=t===void 0?{}:t,r=nr(n,bw);if(r.state.status==="error")return k.createElement(lw,{translations:e==null?void 0:e.errorScreen});var i=r.state.collections.some(function(o){return o.items.length>0});return r.state.query?i===!1?k.createElement(uw,Qe({},r,{translations:e==null?void 0:e.noResultsScreen})):k.createElement(pw,r):k.createElement(_w,Qe({},r,{hasCollections:i,translations:e==null?void 0:e.startScreen}))},function(n,t){return t.state.status==="loading"||t.state.status==="stalled"}),vw=["translations"];function ww(n){var t=n.translations,e=t===void 0?{}:t,r=nr(n,vw),i=e.resetButtonTitle,o=i===void 0?"Clear the query":i,s=e.resetButtonAriaLabel,a=s===void 0?"Clear the query":s,l=e.cancelButtonText,c=l===void 0?"Cancel":l,u=e.cancelButtonAriaLabel,f=u===void 0?"Cancel":u,h=e.searchInputLabel,d=h===void 0?"Search":h,g=r.getFormProps({inputElement:r.inputRef.current}).onReset;return k.useEffect(function(){r.autoFocus&&r.inputRef.current&&r.inputRef.current.focus()},[r.autoFocus,r.inputRef]),k.useEffect(function(){r.isFromSelection&&r.inputRef.current&&r.inputRef.current.select()},[r.isFromSelection,r.inputRef]),k.createElement(k.Fragment,null,k.createElement("form",{className:"DocSearch-Form",onSubmit:function(m){m.preventDefault()},onReset:g},k.createElement("label",Qe({className:"DocSearch-MagnifierLabel"},r.getLabelProps()),k.createElement(gg,null),k.createElement("span",{className:"DocSearch-VisuallyHiddenForAccessibility"},d)),k.createElement("div",{className:"DocSearch-LoadingIndicator"},k.createElement($1,null)),k.createElement("input",Qe({className:"DocSearch-Input",ref:r.inputRef},r.getInputProps({inputElement:r.inputRef.current,autoFocus:r.autoFocus,maxLength:64}))),k.createElement("button",{type:"reset",title:o,className:"DocSearch-Reset","aria-label":a,hidden:!r.state.query},k.createElement(Gl,null))),k.createElement("button",{className:"DocSearch-Cancel",type:"reset","aria-label":f,onClick:r.onClose},c))}var Sw=["_highlightResult","_snippetResult"];function id(n){var t=n.key,e=n.limit,r=e===void 0?5:e,i=function(s){return function(){var a="__TEST_KEY__";try{return localStorage.setItem(a,""),localStorage.removeItem(a),!0}catch{return!1}}()===!1?{setItem:function(){},getItem:function(){return[]}}:{setItem:function(a){return window.localStorage.setItem(s,JSON.stringify(a))},getItem:function(){var a=window.localStorage.getItem(s);return a?JSON.parse(a):[]}}}(t),o=i.getItem().slice(0,r);return{add:function(s){var a=s;a._highlightResult,a._snippetResult;var l=nr(a,Sw),c=o.findIndex(function(u){return u.objectID===l.objectID});c>-1&&o.splice(c,1),o.unshift(l),o=o.slice(0,r),i.setItem(o)},remove:function(s){o=o.filter(function(a){return a.objectID!==s.objectID}),i.setItem(o)},getAll:function(){return o}}}function Iw(n){var t,e="algolia-client-js-".concat(n.key);function r(){return t===void 0&&(t=n.localStorage||window.localStorage),t}function i(){return JSON.parse(r().getItem(e)||"{}")}function o(s){r().setItem(e,JSON.stringify(s))}return{get:function(s,a){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{miss:function(){return Promise.resolve()}};return Promise.resolve().then(function(){var c,u,f;return c=n.timeToLive?1e3*n.timeToLive:null,u=i(),o(f=Object.fromEntries(Object.entries(u).filter(function(h){return fn(h,2)[1].timestamp!==void 0}))),c&&o(Object.fromEntries(Object.entries(f).filter(function(h){var d=fn(h,2)[1],g=new Date().getTime();return!(d.timestamp+c<g)}))),i()[JSON.stringify(s)]}).then(function(c){return Promise.all([c?c.value:a(),c!==void 0])}).then(function(c){var u=fn(c,2),f=u[0],h=u[1];return Promise.all([f,h||l.miss(f)])}).then(function(c){return fn(c,1)[0]})},set:function(s,a){return Promise.resolve().then(function(){var l=i();return l[JSON.stringify(s)]={timestamp:new Date().getTime(),value:a},r().setItem(e,JSON.stringify(l)),a})},delete:function(s){return Promise.resolve().then(function(){var a=i();delete a[JSON.stringify(s)],r().setItem(e,JSON.stringify(a))})},clear:function(){return Promise.resolve().then(function(){r().removeItem(e)})}}}function Oo(n){var t=To(n.caches),e=t.shift();return e===void 0?{get:function(r,i){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{miss:function(){return Promise.resolve()}};return i().then(function(s){return Promise.all([s,o.miss(s)])}).then(function(s){return fn(s,1)[0]})},set:function(r,i){return Promise.resolve(i)},delete:function(r){return Promise.resolve()},clear:function(){return Promise.resolve()}}:{get:function(r,i){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{miss:function(){return Promise.resolve()}};return e.get(r,i,o).catch(function(){return Oo({caches:t}).get(r,i,o)})},set:function(r,i){return e.set(r,i).catch(function(){return Oo({caches:t}).set(r,i)})},delete:function(r){return e.delete(r).catch(function(){return Oo({caches:t}).delete(r)})},clear:function(){return e.clear().catch(function(){return Oo({caches:t}).clear()})}}}function yl(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{serializable:!0},t={};return{get:function(e,r){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{miss:function(){return Promise.resolve()}},o=JSON.stringify(e);if(o in t)return Promise.resolve(n.serializable?JSON.parse(t[o]):t[o]);var s=r();return s.then(function(a){return i.miss(a)}).then(function(){return s})},set:function(e,r){return t[JSON.stringify(e)]=n.serializable?JSON.stringify(r):r,Promise.resolve(r)},delete:function(e){return delete t[JSON.stringify(e)],Promise.resolve()},clear:function(){return t={},Promise.resolve()}}}function Ow(n){var t=n.algoliaAgents,e=n.client,r=n.version,i=function(o){var s={value:"Algolia for JavaScript (".concat(o,")"),add:function(a){var l="; ".concat(a.segment).concat(a.version!==void 0?" (".concat(a.version,")"):"");return s.value.indexOf(l)===-1&&(s.value="".concat(s.value).concat(l)),s}};return s}(r).add({segment:e,version:r});return t.forEach(function(o){return i.add(o)}),i}var od=12e4;function sd(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"up",e=Date.now();return Vt(Vt({},n),{},{status:t,lastUpdate:e,isUp:function(){return t==="up"||Date.now()-e>od},isTimedOut:function(){return t==="timed out"&&Date.now()-e<=od}})}var Dg=function(){function n(t,e){var r;return Gi(this,n),Zi(r=Qi(this,n,[t]),"name","AlgoliaError"),e&&(r.name=e),r}return $i(n,Jl(Error)),Xi(n)}(),Eg=function(){function n(t,e,r){var i;return Gi(this,n),Zi(i=Qi(this,n,[t,r]),"stackTrace",void 0),i.stackTrace=e,i}return $i(n,Dg),Xi(n)}(),kw=function(){function n(t){return Gi(this,n),Qi(this,n,["Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",t,"RetryError"])}return $i(n,Eg),Xi(n)}(),Zl=function(){function n(t,e,r){var i,o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"ApiError";return Gi(this,n),Zi(i=Qi(this,n,[t,r,o]),"status",void 0),i.status=e,i}return $i(n,Eg),Xi(n)}(),Dw=function(){function n(t,e){var r;return Gi(this,n),Zi(r=Qi(this,n,[t,"DeserializationError"]),"response",void 0),r.response=e,r}return $i(n,Dg),Xi(n)}(),Ew=function(){function n(t,e,r,i){var o;return Gi(this,n),Zi(o=Qi(this,n,[t,e,i,"DetailedApiError"]),"error",void 0),o.error=r,o}return $i(n,Zl),Xi(n)}();function Aw(n,t,e){var r,i=(r=e,Object.keys(r).filter(function(s){return r[s]!==void 0}).sort().map(function(s){return"".concat(s,"=").concat(encodeURIComponent(Object.prototype.toString.call(r[s])==="[object Array]"?r[s].join(","):r[s]).replace(/\+/g,"%20"))}).join("&")),o="".concat(n.protocol,"://").concat(n.url).concat(n.port?":".concat(n.port):"","/").concat(t.charAt(0)==="/"?t.substring(1):t);return i.length&&(o+="?".concat(i)),o}function Tw(n,t){if(n.method!=="GET"&&(n.data!==void 0||t.data!==void 0)){var e=Array.isArray(n.data)?n.data:Vt(Vt({},n.data),t.data);return JSON.stringify(e)}}function Bw(n,t,e){var r=Vt(Vt(Vt({Accept:"application/json"},n),t),e),i={};return Object.keys(r).forEach(function(o){var s=r[o];i[o.toLowerCase()]=s}),i}function Pw(n){try{return JSON.parse(n.content)}catch(t){throw new Dw(t.message,n)}}function Mw(n,t){var e=n.content,r=n.status;try{var i=JSON.parse(e);return"error"in i?new Ew(i.message,r,i.error,t):new Zl(i.message,r,t)}catch{}return new Zl(e,r,t)}function Nw(n){return n.map(function(t){return Ag(t)})}function Ag(n){var t=n.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return Vt(Vt({},n),{},{request:Vt(Vt({},n.request),{},{headers:Vt(Vt({},n.request.headers),t)})})}var Cw=["appId","apiKey","authMode","algoliaAgents"],Fw=["params"],ad="5.19.0";function jw(n){return[{url:"".concat(n,"-dsn.algolia.net"),accept:"read",protocol:"https"},{url:"".concat(n,".algolia.net"),accept:"write",protocol:"https"}].concat(function(t){for(var e=t,r=t.length-1;r>0;r--){var i=Math.floor(Math.random()*(r+1)),o=t[r];e[r]=t[i],e[i]=o}return e}([{url:"".concat(n,"-1.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(n,"-2.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(n,"-3.algolianet.com"),accept:"readWrite",protocol:"https"}]))}var $l="3.8.3",Lw=["footer","searchBox"];function Rw(n){var t=n.appId,e=n.apiKey,r=n.indexName,i=n.placeholder,o=i===void 0?"Search docs":i,s=n.searchParameters,a=n.maxResultsPerGroup,l=n.onClose,c=l===void 0?hw:l,u=n.transformItems,f=u===void 0?rd:u,h=n.hitComponent,d=h===void 0?Z1:h,g=n.resultsFooterComponent,m=g===void 0?function(){return null}:g,p=n.navigator,v=n.initialScrollY,b=v===void 0?0:v,_=n.transformSearchClient,O=_===void 0?rd:_,y=n.disableUserPersonalization,T=y!==void 0&&y,S=n.initialQuery,F=S===void 0?"":S,B=n.translations,N=B===void 0?{}:B,R=n.getMissingResultsUrl,P=n.insights,M=P!==void 0&&P,j=N.footer,V=N.searchBox,U=nr(N,Lw),H=fn(k.useState({query:"",collections:[],completion:null,context:{},isOpen:!1,activeItemId:null,status:"idle"}),2),D=H[0],X=H[1],Q=k.useRef(null),Z=k.useRef(null),Dt=k.useRef(null),Bt=k.useRef(null),Ct=k.useRef(null),K=k.useRef(10),Oe=k.useRef(typeof window<"u"?window.getSelection().toString().slice(0,64):"").current,Kt=k.useRef(F||Oe).current,Be=function(dt,Jt,Ce){return k.useMemo(function(){var ze=function(z,$){if(!z||typeof z!="string")throw new Error("`appId` is missing.");if(!$||typeof $!="string")throw new Error("`apiKey` is missing.");return function(x){var tt=x.appId,it=x.apiKey,Qt=x.authMode,ke=x.algoliaAgents,qt=nr(x,Cw),Et=function(G,ct){var me=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"WithinHeaders",Xe={"x-algolia-api-key":ct,"x-algolia-application-id":G};return{headers:function(){return me==="WithinHeaders"?Xe:{}},queryParameters:function(){return me==="WithinQueryParameters"?Xe:{}}}}(tt,it,Qt),At=function(G){var ct=G.hosts,me=G.hostsCache,Xe=G.baseHeaders,bn=G.logger,xn=G.baseQueryParameters,Ft=G.algoliaAgent,Pe=G.timeouts,We=G.requester,Rt=G.requestsCache,ht=G.responsesCache;function ee(je){return ve.apply(this,arguments)}function ve(){return(ve=dl(Zr().mark(function je(Me){var Ne,or,Ye,sn,Dr;return Zr().wrap(function(sr){for(;;)switch(sr.prev=sr.next){case 0:return sr.next=2,Promise.all(Me.map(function(Ze){return me.get(Ze,function(){return Promise.resolve(sd(Ze))})}));case 2:return Ne=sr.sent,or=Ne.filter(function(Ze){return Ze.isUp()}),Ye=Ne.filter(function(Ze){return Ze.isTimedOut()}),sn=[].concat(To(or),To(Ye)),Dr=sn.length>0?sn:Me,sr.abrupt("return",{hosts:Dr,getTimeout:function(Ze,ai){return(Ye.length===0&&Ze===0?1:Ye.length+3+Ze)*ai}});case 8:case"end":return sr.stop()}},je)}))).apply(this,arguments)}function De(je,Me){return qn.apply(this,arguments)}function qn(){return qn=dl(Zr().mark(function je(Me,Ne){var or,Ye,sn,Dr,sr,Ze,ai,Va,Hr,ps,za,jc,Wa,Ha=arguments;return Zr().wrap(function(li){for(;;)switch(li.prev=li.next){case 0:if(or=!(Ha.length>2&&Ha[2]!==void 0)||Ha[2],Ye=[],sn=Tw(Me,Ne),Dr=Bw(Xe,Me.headers,Ne.headers),sr=Me.method==="GET"?Vt(Vt({},Me.data),Ne.data):{},Ze=Vt(Vt(Vt({},xn),Me.queryParameters),sr),Ft.value&&(Ze["x-algolia-agent"]=Ft.value),Ne&&Ne.queryParameters)for(ai=0,Va=Object.keys(Ne.queryParameters);ai<Va.length;ai++)Hr=Va[ai],Ne.queryParameters[Hr]&&Object.prototype.toString.call(Ne.queryParameters[Hr])!=="[object Object]"?Ze[Hr]=Ne.queryParameters[Hr].toString():Ze[Hr]=Ne.queryParameters[Hr];return ps=0,za=function(){var to=dl(Zr().mark(function Lc(gs,xa){var eo,_s,qa,Ya,Er,Rc;return Zr().wrap(function(Cn){for(;;)switch(Cn.prev=Cn.next){case 0:if((eo=gs.pop())!==void 0){Cn.next=3;break}throw new kw(Nw(Ye));case 3:return _s=Vt(Vt({},Pe),Ne.timeouts),qa={data:sn,headers:Dr,method:Me.method,url:Aw(eo,Me.path,Ze),connectTimeout:xa(ps,_s.connect),responseTimeout:xa(ps,or?_s.read:_s.write)},Ya=function(ys){var Vc={request:qa,response:ys,host:eo,triesLeft:gs.length};return Ye.push(Vc),Vc},Cn.next=8,We.send(qa);case 8:if(Ka=(Uc=Er=Cn.sent).isTimedOut,bs=Uc.status,!(Ka||function(ys){return!ys.isTimedOut&&!~~ys.status}({isTimedOut:Ka,status:bs})||~~(bs/100)!=2&&~~(bs/100)!=4)){Cn.next=16;break}return Rc=Ya(Er),Er.isTimedOut&&ps++,bn.info("Retryable failure",Ag(Rc)),Cn.next=15,me.set(eo,sd(eo,Er.isTimedOut?"timed out":"down"));case 15:return Cn.abrupt("return",za(gs,xa));case 16:if(~~(Er.status/100)!=2){Cn.next=18;break}return Cn.abrupt("return",Pw(Er));case 18:throw Ya(Er),Mw(Er,Ye);case 20:case"end":return Cn.stop()}var Uc,Ka,bs},Lc)}));return function(Lc,gs){return to.apply(this,arguments)}}(),jc=ct.filter(function(to){return to.accept==="readWrite"||(or?to.accept==="read":to.accept==="write")}),li.next=13,ee(jc);case 13:return Wa=li.sent,li.abrupt("return",za(To(Wa.hosts).reverse(),Wa.getTimeout));case 15:case"end":return li.stop()}},je)})),qn.apply(this,arguments)}return{hostsCache:me,requester:We,timeouts:Pe,logger:bn,algoliaAgent:Ft,baseHeaders:Xe,baseQueryParameters:xn,hosts:ct,request:function(je){var Me=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Ne=je.useReadTransporter||je.method==="GET";if(!Ne)return De(je,Me,Ne);var or=function(){return De(je,Me)};if((Me.cacheable||je.cacheable)!==!0)return or();var Ye={request:je,requestOptions:Me,transporter:{queryParameters:xn,headers:Xe}};return ht.get(Ye,function(){return Rt.get(Ye,function(){return Rt.set(Ye,or()).then(function(sn){return Promise.all([Rt.delete(Ye),sn])},function(sn){return Promise.all([Rt.delete(Ye),Promise.reject(sn)])}).then(function(sn){var Dr=fn(sn,2);return Dr[0],Dr[1]})})},{miss:function(sn){return ht.set(Ye,sn)}})},requestsCache:Rt,responsesCache:ht}}(Vt(Vt({hosts:jw(tt)},qt),{},{algoliaAgent:Ow({algoliaAgents:ke,client:"Lite",version:ad}),baseHeaders:Vt(Vt({"content-type":"text/plain"},Et.headers()),qt.baseHeaders),baseQueryParameters:Vt(Vt({},Et.queryParameters()),qt.baseQueryParameters)}));return{transporter:At,appId:tt,apiKey:it,clearCache:function(){return Promise.all([At.requestsCache.clear(),At.responsesCache.clear()]).then(function(){})},get _ua(){return At.algoliaAgent.value},addAlgoliaAgent:function(G,ct){At.algoliaAgent.add({segment:G,version:ct})},setClientApiKey:function(G){var ct=G.apiKey;Qt&&Qt!=="WithinHeaders"?At.baseQueryParameters["x-algolia-api-key"]=ct:At.baseHeaders["x-algolia-api-key"]=ct},searchForHits:function(G,ct){return this.search(G,ct)},searchForFacets:function(G,ct){return this.search(G,ct)},customPost:function(G,ct){var me=G.path,Xe=G.parameters,bn=G.body;if(!me)throw new Error("Parameter `path` is required when calling `customPost`.");var xn={method:"POST",path:"/{path}".replace("{path}",me),queryParameters:Xe||{},headers:{},data:bn||{}};return At.request(xn,ct)},getRecommendations:function(G,ct){if(G&&Array.isArray(G)&&(G={requests:G}),!G)throw new Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");if(!G.requests)throw new Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");var me={method:"POST",path:"/1/indexes/*/recommendations",queryParameters:{},headers:{},data:G,useReadTransporter:!0,cacheable:!0};return At.request(me,ct)},search:function(G,ct){if(G&&Array.isArray(G)){var me={requests:G.map(function(bn){var xn=bn.params,Ft=nr(bn,Fw);return Ft.type==="facet"?Vt(Vt(Vt({},Ft),xn),{},{type:"facet"}):Vt(Vt(Vt({},Ft),xn),{},{facet:void 0,maxFacetHits:void 0,facetQuery:void 0})})};G=me}if(!G)throw new Error("Parameter `searchMethodParams` is required when calling `search`.");if(!G.requests)throw new Error("Parameter `searchMethodParams.requests` is required when calling `search`.");var Xe={method:"POST",path:"/1/indexes/*/queries",queryParameters:{},headers:{},data:G,useReadTransporter:!0,cacheable:!0};return At.request(Xe,ct)}}}(Vt({appId:z,apiKey:$,timeouts:{connect:1e3,read:2e3,write:3e4},logger:{debug:function(x,tt){return Promise.resolve()},info:function(x,tt){return Promise.resolve()},error:function(x,tt){return Promise.resolve()}},requester:{send:function(x){return new Promise(function(tt){var it=new XMLHttpRequest;it.open(x.method,x.url,!0),Object.keys(x.headers).forEach(function(Et){return it.setRequestHeader(Et,x.headers[Et])});var Qt,ke=function(Et,At){return setTimeout(function(){it.abort(),tt({status:0,content:At,isTimedOut:!0})},Et)},qt=ke(x.connectTimeout,"Connection timeout");it.onreadystatechange=function(){it.readyState>it.OPENED&&Qt===void 0&&(clearTimeout(qt),Qt=ke(x.responseTimeout,"Socket timeout"))},it.onerror=function(){it.status===0&&(clearTimeout(qt),clearTimeout(Qt),tt({content:it.responseText||"Network request failed",status:it.status,isTimedOut:!1}))},it.onload=function(){clearTimeout(qt),clearTimeout(Qt),tt({content:it.responseText,status:it.status,isTimedOut:!1})},it.send(x.data)})}},algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:yl(),requestsCache:yl({serializable:!1}),hostsCache:Oo({caches:[Iw({key:"".concat(ad,"-").concat(z)}),yl()]})},void 0))}(dt,Jt);return ze.addAlgoliaAgent("docsearch",$l),/docsearch.js \(.*\)/.test(ze.transporter.algoliaAgent.value)===!1&&ze.addAlgoliaAgent("docsearch-react",$l),Ce(ze)},[dt,Jt,Ce])}(t,e,O),qe=k.useRef(id({key:"__DOCSEARCH_FAVORITE_SEARCHES__".concat(r),limit:10})).current,_n=k.useRef(id({key:"__DOCSEARCH_RECENT_SEARCHES__".concat(r),limit:qe.getAll().length===0?7:4})).current,Ve=k.useCallback(function(dt){if(!T){var Jt=dt.type==="content"?dt.__docsearch_parent:dt;Jt&&qe.getAll().findIndex(function(Ce){return Ce.objectID===Jt.objectID})===-1&&_n.add(Jt)}},[qe,_n,T]),Nn=k.useCallback(function(dt){if(D.context.algoliaInsightsPlugin&&dt.__autocomplete_id){var Jt=dt,Ce={eventName:"Item Selected",index:Jt.__autocomplete_indexName,items:[Jt],positions:[dt.__autocomplete_id],queryID:Jt.__autocomplete_queryID};D.context.algoliaInsightsPlugin.insights.clickedObjectIDsAfterSearch(Ce)}},[D.context.algoliaInsightsPlugin]),mn=k.useMemo(function(){return Q1({id:"docsearch",defaultActiveItemId:0,placeholder:o,openOnFocus:!0,initialState:{query:Kt,context:{searchSuggestions:[]}},insights:M,navigator:p,onStateChange:function(dt){X(dt.state)},getSources:function(dt){var Jt=dt.query,Ce=dt.state,ze=dt.setContext,z=dt.setStatus;if(!Jt)return T?[]:[{sourceId:"recentSearches",onSelect:function(x){var tt=x.item,it=x.event;Ve(tt),Fs(it)||c()},getItemUrl:function(x){return x.item.url},getItems:function(){return _n.getAll()}},{sourceId:"favoriteSearches",onSelect:function(x){var tt=x.item,it=x.event;Ve(tt),Fs(it)||c()},getItemUrl:function(x){return x.item.url},getItems:function(){return qe.getAll()}}];var $=!!M;return Be.search({requests:[Vt({query:Jt,indexName:r,attributesToRetrieve:["hierarchy.lvl0","hierarchy.lvl1","hierarchy.lvl2","hierarchy.lvl3","hierarchy.lvl4","hierarchy.lvl5","hierarchy.lvl6","content","type","url"],attributesToSnippet:["hierarchy.lvl1:".concat(K.current),"hierarchy.lvl2:".concat(K.current),"hierarchy.lvl3:".concat(K.current),"hierarchy.lvl4:".concat(K.current),"hierarchy.lvl5:".concat(K.current),"hierarchy.lvl6:".concat(K.current),"content:".concat(K.current)],snippetEllipsisText:"…",highlightPreTag:"<mark>",highlightPostTag:"</mark>",hitsPerPage:20,clickAnalytics:$},s)]}).catch(function(x){throw x.name==="RetryError"&&z("error"),x}).then(function(x){var tt=x.results[0],it=tt.hits,Qt=tt.nbHits,ke=nd(it,function(Et){return kg(Et)},a);Ce.context.searchSuggestions.length<Object.keys(ke).length&&ze({searchSuggestions:Object.keys(ke)}),ze({nbHits:Qt});var qt={};return $&&(qt={__autocomplete_indexName:r,__autocomplete_queryID:tt.queryID,__autocomplete_algoliaCredentials:{appId:t,apiKey:e}}),Object.values(ke).map(function(Et,At){return{sourceId:"hits".concat(At),onSelect:function(G){var ct=G.item,me=G.event;Ve(ct),Fs(me)||c()},getItemUrl:function(G){return G.item.url},getItems:function(){return Object.values(nd(Et,function(G){return G.hierarchy.lvl1},a)).map(f).map(function(G){return G.map(function(ct){var me=null,Xe=G.find(function(bn){return bn.type==="lvl1"&&bn.hierarchy.lvl1===ct.hierarchy.lvl1});return ct.type!=="lvl1"&&Xe&&(me=Xe),Vt(Vt({},ct),{},{__docsearch_parent:me},qt)})}).flat()}}})})}})},[r,s,a,Be,c,_n,qe,Ve,Kt,o,p,f,T,M,t,e]),Hn=mn.getEnvironmentProps,Wr=mn.getRootProps,et=mn.refresh;return function(dt){var Jt=dt.getEnvironmentProps,Ce=dt.panelElement,ze=dt.formElement,z=dt.inputElement;k.useEffect(function(){if(Ce&&ze&&z){var $=Jt({panelElement:Ce,formElement:ze,inputElement:z}),x=$.onTouchStart,tt=$.onTouchMove;return window.addEventListener("touchstart",x),window.addEventListener("touchmove",tt),function(){window.removeEventListener("touchstart",x),window.removeEventListener("touchmove",tt)}}},[Jt,Ce,ze,z])}({getEnvironmentProps:Hn,panelElement:Bt.current,formElement:Dt.current,inputElement:Ct.current}),function(dt){var Jt=dt.container;k.useEffect(function(){if(Jt){var Ce=Jt.querySelectorAll("a[href]:not([disabled]), button:not([disabled]), input:not([disabled])"),ze=Ce[0],z=Ce[Ce.length-1];return Jt.addEventListener("keydown",$),function(){Jt.removeEventListener("keydown",$)}}function $(x){x.key==="Tab"&&(x.shiftKey?document.activeElement===ze&&(x.preventDefault(),z.focus()):document.activeElement===z&&(x.preventDefault(),ze.focus()))}},[Jt])}({container:Q.current}),k.useEffect(function(){return document.body.classList.add("DocSearch--active"),function(){var dt,Jt;document.body.classList.remove("DocSearch--active"),(dt=(Jt=window).scrollTo)===null||dt===void 0||dt.call(Jt,0,b)}},[]),k.useLayoutEffect(function(){var dt=window.innerWidth-document.body.clientWidth;return document.body.style.marginRight="".concat(dt,"px"),function(){document.body.style.marginRight="0px"}},[]),k.useEffect(function(){window.matchMedia("(max-width: 768px)").matches&&(K.current=5)},[]),k.useEffect(function(){Bt.current&&(Bt.current.scrollTop=0)},[D.query]),k.useEffect(function(){Kt.length>0&&(et(),Ct.current&&Ct.current.focus())},[Kt,et]),k.useEffect(function(){function dt(){if(Z.current){var Jt=.01*window.innerHeight;Z.current.style.setProperty("--docsearch-vh","".concat(Jt,"px"))}}return dt(),window.addEventListener("resize",dt),function(){window.removeEventListener("resize",dt)}},[]),k.createElement("div",Qe({ref:Q},Wr({"aria-expanded":!0}),{className:["DocSearch","DocSearch-Container",D.status==="stalled"&&"DocSearch-Container--Stalled",D.status==="error"&&"DocSearch-Container--Errored"].filter(Boolean).join(" "),role:"button",tabIndex:0,onMouseDown:function(dt){dt.target===dt.currentTarget&&c()}}),k.createElement("div",{className:"DocSearch-Modal",ref:Z},k.createElement("header",{className:"DocSearch-SearchBar",ref:Dt},k.createElement(ww,Qe({},mn,{state:D,autoFocus:Kt.length===0,inputRef:Ct,isFromSelection:!!Kt&&Kt===Oe,translations:V,onClose:c}))),k.createElement("div",{className:"DocSearch-Dropdown",ref:Bt},k.createElement(yw,Qe({},mn,{indexName:r,state:D,hitComponent:d,resultsFooterComponent:m,disableUserPersonalization:T,recentSearches:_n,favoriteSearches:qe,inputRef:Ct,translations:U,getMissingResultsUrl:R,onItemClick:function(dt,Jt){Nn(dt),Ve(dt),Fs(Jt)||c()}}))),k.createElement("footer",{className:"DocSearch-Footer"},k.createElement(X1,{translations:j}))))}function Uw(n){var t,e,r=k.useRef(null),i=fn(k.useState(!1),2),o=i[0],s=i[1],a=fn(k.useState((n==null?void 0:n.initialQuery)||void 0),2),l=a[0],c=a[1],u=k.useCallback(function(){s(!0)},[s]),f=k.useCallback(function(){s(!1),c(n==null?void 0:n.initialQuery)},[s,n.initialQuery]);return function(h){var d=h.isOpen,g=h.onOpen,m=h.onClose,p=h.onInput,v=h.searchButtonRef;k.useEffect(function(){function b(_){var O;if(_.code==="Escape"&&d||((O=_.key)===null||O===void 0?void 0:O.toLowerCase())==="k"&&(_.metaKey||_.ctrlKey)||!function(y){var T=y.target,S=T.tagName;return T.isContentEditable||S==="INPUT"||S==="SELECT"||S==="TEXTAREA"}(_)&&_.key==="/"&&!d)return _.preventDefault(),void(d?m():document.body.classList.contains("DocSearch--active")||g());v&&v.current===document.activeElement&&p&&/[a-zA-Z0-9]/.test(String.fromCharCode(_.keyCode))&&p(_)}return window.addEventListener("keydown",b),function(){window.removeEventListener("keydown",b)}},[d,g,m,p,v])}({isOpen:o,onOpen:u,onClose:f,onInput:k.useCallback(function(h){s(!0),c(h.key)},[s,c]),searchButtonRef:r}),k.createElement(k.Fragment,null,k.createElement(p1,{ref:r,translations:n==null||(t=n.translations)===null||t===void 0?void 0:t.button,onClick:u}),o&&dg(k.createElement(Rw,Qe({},n,{initialScrollY:window.scrollY,initialQuery:l,translations:n==null||(e=n.translations)===null||e===void 0?void 0:e.modal,onClose:f})),document.body))}function Vw(n){mg(k.createElement(Uw,Ul({},n,{transformSearchClient:function(t){return t.addAlgoliaAgent("docsearch.js",$l),n.transformSearchClient?n.transformSearchClient(t):t}})),function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window;return typeof t=="string"?e.document.querySelector(t):t}(n.container,n.environment))}function zw(n){let t;return{c(){t=Y("div"),this.h()},l(e){t=q(e,"DIV",{id:!0,class:!0}),J(t).forEach(w),this.h()},h(){C(t,"id","docsearch"),C(t,"class",n[0]+" "+n[1]+" "+n[2]+" "+n[3])},m(e,r){L(e,t,r)},p:Xt,i:Xt,o:Xt,d(e){e&&w(t)}}}function Ww(n,t,e){let{algolia:r}=t;Cr(()=>{Vw({container:"#docsearch",appId:r==null?void 0:r.appId,apiKey:r==null?void 0:r.apiKey,indexName:r==null?void 0:r.indexName})});const i=`
	**:[&.DocSearch-Button]:bg-base-100
	**:[&.DocSearch-Button]:border-base-300
	**:[&.DocSearch-Button]:hover:bg-base-200/40
	**:[&.DocSearch-Button]:transition-colors
	**:[&.DocSearch-Button]:duration-200
	**:[&.DocSearch-Button]:rounded-md
	**:[&.DocSearch-Button]:flex
	**:[&.DocSearch-Button]:gap-16
	**:[&.DocSearch-Button]:cursor-pointer
	**:[&.DocSearch-Button]:py-1
	**:[&.DocSearch-Button]:pl-2
	**:[&.DocSearch-Button]:sm:pr-1
	**:[&.DocSearch-Button]:pr-20
	**:[&.DocSearch-Button]:sm:text-xs
	**:[&.DocSearch-Button]:border
	**:[&.DocSearch-Button]:font-sans
	**:[&.DocSearch-Button]:font-medium
	**:[&.DocSearch-Button]:items-center;
	`,o=`
	**:[&DocSearch-Button-Placeholder]:text-base-content-muted
	`,s=`
	**:[&.DocSearch-Search-Icon]:hidden
	`,a=`
	**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:text-base-content-muted
	**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:*:text-base-content-muted
	`;return n.$$set=l=>{"algolia"in l&&e(4,r=l.algolia)},[i,o,s,a,r]}class Hw extends re{constructor(t){super(),ie(this,t,Ww,zw,Zt,{algolia:4})}}function xw(n){let t;const e=n[3].default,r=ge(e,n,n[11],null);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&2048)&&_e(r,e,i,i[11],t?ye(e,i[11],o,null):be(i[11]),null)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function qw(n){let t,e;const r=[{class:jr("relative flex cursor-pointer select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",n[1]&&"pl-8",n[0])},n[2]];let i={$$slots:{default:[xw]},$$scope:{ctx:n}};for(let o=0;o<r.length;o+=1)i=te(i,r[o]);return t=new Kv({props:i}),t.$on("click",n[4]),t.$on("keydown",n[5]),t.$on("focusin",n[6]),t.$on("focusout",n[7]),t.$on("pointerdown",n[8]),t.$on("pointerleave",n[9]),t.$on("pointermove",n[10]),{c(){lt(t.$$.fragment)},l(o){at(t.$$.fragment,o)},m(o,s){st(t,o,s),e=!0},p(o,[s]){const a=s&7?rn(r,[s&3&&{class:jr("relative flex cursor-pointer select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",o[1]&&"pl-8",o[0])},s&4&&Ma(o[2])]):{};s&2048&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(o){e||(I(t.$$.fragment,o),e=!0)},o(o){E(t.$$.fragment,o),e=!1},d(o){ot(t,o)}}}function Yw(n,t,e){const r=["class","inset"];let i=Ue(t,r),{$$slots:o={},$$scope:s}=t,{class:a=void 0}=t,{inset:l=void 0}=t;function c(p){ne.call(this,n,p)}function u(p){ne.call(this,n,p)}function f(p){ne.call(this,n,p)}function h(p){ne.call(this,n,p)}function d(p){ne.call(this,n,p)}function g(p){ne.call(this,n,p)}function m(p){ne.call(this,n,p)}return n.$$set=p=>{t=te(te({},t),Sr(p)),e(2,i=Ue(t,r)),"class"in p&&e(0,a=p.class),"inset"in p&&e(1,l=p.inset),"$$scope"in p&&e(11,s=p.$$scope)},[a,l,i,o,c,u,f,h,d,g,m,s]}class Fc extends re{constructor(t){super(),ie(this,t,Yw,qw,Zt,{class:0,inset:1})}}function Kw(n){let t;const e=n[5].default,r=ge(e,n,n[7],null);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&128)&&_e(r,e,i,i[7],t?ye(e,i[7],o,null):be(i[7]),null)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function Jw(n){let t,e;const r=[{transition:n[2]},{transitionConfig:n[3]},{sideOffset:n[1]},{class:jr("z-50 min-w-[8rem] rounded-md border border-base-300 bg-popover p-1 shadow-md bg-base-100 focus:outline-none antialiased text-base-conetnt print:hidden",n[0])},n[4]];let i={$$slots:{default:[Kw]},$$scope:{ctx:n}};for(let o=0;o<r.length;o+=1)i=te(i,r[o]);return t=new y0({props:i}),t.$on("keydown",n[6]),{c(){lt(t.$$.fragment)},l(o){at(t.$$.fragment,o)},m(o,s){st(t,o,s),e=!0},p(o,[s]){const a=s&31?rn(r,[s&4&&{transition:o[2]},s&8&&{transitionConfig:o[3]},s&2&&{sideOffset:o[1]},s&1&&{class:jr("z-50 min-w-[8rem] rounded-md border border-base-300 bg-popover p-1 shadow-md bg-base-100 focus:outline-none antialiased text-base-conetnt print:hidden",o[0])},s&16&&Ma(o[4])]):{};s&128&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(o){e||(I(t.$$.fragment,o),e=!0)},o(o){E(t.$$.fragment,o),e=!1},d(o){ot(t,o)}}}function Qw(n,t,e){const r=["class","sideOffset","transition","transitionConfig"];let i=Ue(t,r),{$$slots:o={},$$scope:s}=t,{class:a=void 0}=t,{sideOffset:l=4}=t,{transition:c=u_}=t,{transitionConfig:u=void 0}=t;function f(h){ne.call(this,n,h)}return n.$$set=h=>{t=te(te({},t),Sr(h)),e(4,i=Ue(t,r)),"class"in h&&e(0,a=h.class),"sideOffset"in h&&e(1,l=h.sideOffset),"transition"in h&&e(2,c=h.transition),"transitionConfig"in h&&e(3,u=h.transitionConfig),"$$scope"in h&&e(7,s=h.$$scope)},[a,l,c,u,i,o,f,s]}class Gw extends re{constructor(t){super(),ie(this,t,Qw,Jw,Zt,{class:0,sideOffset:1,transition:2,transitionConfig:3})}}function Xw(n){let t,e,r;const i=n[3].default,o=ge(i,n,n[2],null);let s=[{class:e=jr("ml-auto text-xs tracking-widest opacity-60",n[0])},n[1]],a={};for(let l=0;l<s.length;l+=1)a=te(a,s[l]);return{c(){t=Y("span"),o&&o.c(),this.h()},l(l){t=q(l,"SPAN",{class:!0});var c=J(t);o&&o.l(c),c.forEach(w),this.h()},h(){Ge(t,a)},m(l,c){L(l,t,c),o&&o.m(t,null),r=!0},p(l,[c]){o&&o.p&&(!r||c&4)&&_e(o,i,l,l[2],r?ye(i,l[2],c,null):be(l[2]),null),Ge(t,a=rn(s,[(!r||c&1&&e!==(e=jr("ml-auto text-xs tracking-widest opacity-60",l[0])))&&{class:e},c&2&&l[1]]))},i(l){r||(I(o,l),r=!0)},o(l){E(o,l),r=!1},d(l){l&&w(t),o&&o.d(l)}}}function Zw(n,t,e){const r=["class"];let i=Ue(t,r),{$$slots:o={},$$scope:s}=t,{class:a=void 0}=t;return n.$$set=l=>{t=te(te({},t),Sr(l)),e(1,i=Ue(t,r)),"class"in l&&e(0,a=l.class),"$$scope"in l&&e(2,s=l.$$scope)},[a,i,s,o]}class Tg extends re{constructor(t){super(),ie(this,t,Zw,Xw,Zt,{class:0})}}const $w=i0,t2=D0,e2=t0;function n2(n){let t,e;return t=new Pn({props:{src:h_,class:"h-6 w-6"}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p:Xt,i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function r2(n){let t,e;return t=new M0({props:{builders:[n[15]],variant:"ghost",size:"sm",class:"px-1","aria-label":"Menu",$$slots:{default:[n2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i&32768&&(o.builders=[r[15]]),i&65536&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function i2(n){let t;return{c(){t=wt("⌘P")},l(e){t=vt(e,"⌘P")},m(e,r){L(e,t,r)},d(e){e&&w(t)}}}function o2(n){let t,e,r;return e=new Tg({props:{$$slots:{default:[i2]},$$scope:{ctx:n}}}),{c(){t=wt(`Print PDF
				`),lt(e.$$.fragment)},l(i){t=vt(i,`Print PDF
				`),at(e.$$.fragment,i)},m(i,o){L(i,t,o),st(e,i,o),r=!0},p(i,o){const s={};o&65536&&(s.$$scope={dirty:o,ctx:i}),e.$set(s)},i(i){r||(I(e.$$.fragment,i),r=!0)},o(i){E(e.$$.fragment,i),r=!1},d(i){i&&w(t),ot(e,i)}}}function ld(n){let t,e;return t=new Fc({props:{$$slots:{default:[s2]},$$scope:{ctx:n}}}),t.$on("click",n[11]),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i&65544&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function s2(n){let t=n[3]?"Hide ":"Show ",e,r;return{c(){e=wt(t),r=wt(" Queries")},l(i){e=vt(i,t),r=vt(i," Queries")},m(i,o){L(i,e,o),L(i,r,o)},p(i,o){o&8&&t!==(t=i[3]?"Hide ":"Show ")&&Re(e,t)},d(i){i&&(w(e),w(r))}}}function a2(n){let t,e;return t=new Fc({props:{$$slots:{default:[c2]},$$scope:{ctx:n}}}),t.$on("click",n[12]),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i&65542&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function l2(n){let t,e,r,i,o;return i=new Pn({props:{src:n[1],class:"h-4 w-4 ml-1"}}),{c(){t=Y("span"),e=wt(n[2]),r=rt(),lt(i.$$.fragment),this.h()},l(s){t=q(s,"SPAN",{class:!0});var a=J(t);e=vt(a,n[2]),a.forEach(w),r=nt(s),at(i.$$.fragment,s),this.h()},h(){C(t,"class","text-xs leading-none")},m(s,a){L(s,t,a),W(t,e),L(s,r,a),st(i,s,a),o=!0},p(s,a){(!o||a&4)&&Re(e,s[2]);const l={};a&2&&(l.src=s[1]),i.$set(l)},i(s){o||(I(i.$$.fragment,s),o=!0)},o(s){E(i.$$.fragment,s),o=!1},d(s){s&&(w(t),w(r)),ot(i,s)}}}function c2(n){let t,e,r;return e=new Tg({props:{class:"tracking-normal flex flex-row items-center",$$slots:{default:[l2]},$$scope:{ctx:n}}}),{c(){t=wt(`Appearance
					`),lt(e.$$.fragment)},l(i){t=vt(i,`Appearance
					`),at(e.$$.fragment,i)},m(i,o){L(i,t,o),st(e,i,o),r=!0},p(i,o){const s={};o&65542&&(s.$$scope={dirty:o,ctx:i}),e.$set(s)},i(i){r||(I(e.$$.fragment,i),r=!0)},o(i){E(e.$$.fragment,i),r=!1},d(i){i&&w(t),ot(e,i)}}}function u2(n){let t,e,r,i,o;t=new Fc({props:{$$slots:{default:[o2]},$$scope:{ctx:n}}}),t.$on("click",n[4]);let s=!n[0]&&ld(n),a=n[8].appearance.switcher&&a2(n);return{c(){lt(t.$$.fragment),e=rt(),s&&s.c(),r=rt(),a&&a.c(),i=ut()},l(l){at(t.$$.fragment,l),e=nt(l),s&&s.l(l),r=nt(l),a&&a.l(l),i=ut()},m(l,c){st(t,l,c),L(l,e,c),s&&s.m(l,c),L(l,r,c),a&&a.m(l,c),L(l,i,c),o=!0},p(l,c){const u={};c&65536&&(u.$$scope={dirty:c,ctx:l}),t.$set(u),l[0]?s&&(jt(),E(s,1,1,()=>{s=null}),Lt()):s?(s.p(l,c),c&1&&I(s,1)):(s=ld(l),s.c(),I(s,1),s.m(r.parentNode,r)),l[8].appearance.switcher&&a.p(l,c)},i(l){o||(I(t.$$.fragment,l),I(s),I(a),o=!0)},o(l){E(t.$$.fragment,l),E(s),E(a),o=!1},d(l){l&&(w(e),w(r),w(i)),ot(t,l),s&&s.d(l),a&&a.d(l)}}}function f2(n){let t,e,r,i;t=new e2({props:{$$slots:{default:[u2]},$$scope:{ctx:n}}});let o=Ol;return{c(){lt(t.$$.fragment),e=rt(),r=ut()},l(s){at(t.$$.fragment,s),e=nt(s),r=ut()},m(s,a){st(t,s,a),L(s,e,a),L(s,r,a),i=!0},p(s,a){const l={};a&65551&&(l.$$scope={dirty:a,ctx:s}),t.$set(l)},i(s){i||(I(t.$$.fragment,s),I(o),i=!0)},o(s){E(t.$$.fragment,s),E(o),i=!1},d(s){s&&(w(e),w(r)),ot(t,s)}}}function d2(n){let t,e,r,i;return t=new t2({props:{asChild:!0,$$slots:{default:[r2,({builder:o})=>({15:o}),({builder:o})=>o?32768:0]},$$scope:{ctx:n}}}),r=new Gw({props:{class:"w-52 text-xs",$$slots:{default:[f2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment),e=rt(),lt(r.$$.fragment)},l(o){at(t.$$.fragment,o),e=nt(o),at(r.$$.fragment,o)},m(o,s){st(t,o,s),L(o,e,s),st(r,o,s),i=!0},p(o,s){const a={};s&98304&&(a.$$scope={dirty:s,ctx:o}),t.$set(a);const l={};s&65551&&(l.$$scope={dirty:s,ctx:o}),r.$set(l)},i(o){i||(I(t.$$.fragment,o),I(r.$$.fragment,o),i=!0)},o(o){E(t.$$.fragment,o),E(r.$$.fragment,o),i=!1},d(o){o&&w(e),ot(t,o),ot(r,o)}}}function h2(n){let t,e;return t=new $w({props:{$$slots:{default:[d2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,[i]){const o={};i&65551&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function m2(n,t,e){let r,i,o,s,a;Te(n,qc,b=>e(3,a=b));const l=new Event("export-beforeprint"),c=new Event("export-afterprint");function u(){window.dispatchEvent(l),setTimeout(()=>window.print(),0),setTimeout(()=>window.dispatchEvent(c),0)}const{selectedAppearance:f,activeAppearance:h,cycleAppearance:d,themesConfig:g}=Ah();Te(n,f,b=>e(10,s=b)),Te(n,h,b=>e(9,o=b));let{neverShowQueries:m}=t;const p=b=>{b.preventDefault(),qc.update(_=>!_)},v=b=>{b.preventDefault(),d()};return n.$$set=b=>{"neverShowQueries"in b&&e(0,m=b.neverShowQueries)},n.$$.update=()=>{n.$$.dirty&1024&&e(2,r=s==="system"?"System":s==="light"?"Light":"Dark"),n.$$.dirty&512&&e(1,i=o==="light"?f_:d_)},[m,i,r,a,u,f,h,d,g,o,s,p,v]}class p2 extends re{constructor(t){super(),ie(this,t,m2,h2,Zt,{neverShowQueries:0})}}function g2(n){let t,e,r,i,o,s,a,l,c,u,f;const h=[y2,b2],d=[];function g(m,p){return m[0]?0:1}return r=g(n),i=d[r]=h[r](n),l=new Dc({props:{logo:n[2],lightLogo:n[3],darkLogo:n[4],title:n[1]}}),{c(){t=Y("div"),e=Y("button"),i.c(),s=rt(),a=Y("a"),lt(l.$$.fragment),this.h()},l(m){t=q(m,"DIV",{class:!0});var p=J(t);e=q(p,"BUTTON",{type:!0,class:!0});var v=J(e);i.l(v),v.forEach(w),s=nt(p),a=q(p,"A",{href:!0,class:!0});var b=J(a);at(l.$$.fragment,b),b.forEach(w),p.forEach(w),this.h()},h(){C(e,"type","button"),C(e,"class",o="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 "+(n[9]==="hide"?"block":"md:hidden")),C(a,"href",Wt("/")),C(a,"class","text-sm font-bold text-base-content hidden md:block"),C(t,"class","flex gap-x-4 items-center")},m(m,p){L(m,t,p),W(t,e),d[r].m(e,null),W(t,s),W(t,a),st(l,a,null),c=!0,u||(f=Pt(e,"click",n[15]),u=!0)},p(m,p){let v=r;r=g(m),r!==v&&(jt(),E(d[v],1,1,()=>{d[v]=null}),Lt(),i=d[r],i||(i=d[r]=h[r](m),i.c()),I(i,1),i.m(e,null)),(!c||p&512&&o!==(o="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 "+(m[9]==="hide"?"block":"md:hidden")))&&C(e,"class",o);const b={};p&4&&(b.logo=m[2]),p&8&&(b.lightLogo=m[3]),p&16&&(b.darkLogo=m[4]),p&2&&(b.title=m[1]),l.$set(b)},i(m){c||(I(i),I(l.$$.fragment,m),c=!0)},o(m){E(i),E(l.$$.fragment,m),c=!1},d(m){m&&w(t),d[r].d(),ot(l),u=!1,f()}}}function _2(n){let t,e,r;return e=new Dc({props:{logo:n[2],lightLogo:n[3],darkLogo:n[4],title:n[1]}}),{c(){t=Y("a"),lt(e.$$.fragment),this.h()},l(i){t=q(i,"A",{href:!0,class:!0});var o=J(t);at(e.$$.fragment,o),o.forEach(w),this.h()},h(){C(t,"href",Wt("/")),C(t,"class","block text-sm font-bold text-base-content")},m(i,o){L(i,t,o),st(e,t,null),r=!0},p(i,o){const s={};o&4&&(s.logo=i[2]),o&8&&(s.lightLogo=i[3]),o&16&&(s.darkLogo=i[4]),o&2&&(s.title=i[1]),e.$set(s)},i(i){r||(I(e.$$.fragment,i),r=!0)},o(i){E(e.$$.fragment,i),r=!1},d(i){i&&w(t),ot(e)}}}function b2(n){let t,e="Open sidebar",r,i,o;return i=new Pn({props:{class:"w-5 h-5",src:m_}}),{c(){t=Y("span"),t.textContent=e,r=rt(),lt(i.$$.fragment),this.h()},l(s){t=q(s,"SPAN",{class:!0,"data-svelte-h":!0}),vr(t)!=="svelte-73kebv"&&(t.textContent=e),r=nt(s),at(i.$$.fragment,s),this.h()},h(){C(t,"class","sr-only")},m(s,a){L(s,t,a),L(s,r,a),st(i,s,a),o=!0},i(s){o||(I(i.$$.fragment,s),o=!0)},o(s){E(i.$$.fragment,s),o=!1},d(s){s&&(w(t),w(r)),ot(i,s)}}}function y2(n){let t,e="Close sidebar",r,i,o;return i=new Pn({props:{class:"w-5 h-5",src:Xs}}),{c(){t=Y("span"),t.textContent=e,r=rt(),lt(i.$$.fragment),this.h()},l(s){t=q(s,"SPAN",{class:!0,"data-svelte-h":!0}),vr(t)!=="svelte-13q18xv"&&(t.textContent=e),r=nt(s),at(i.$$.fragment,s),this.h()},h(){C(t,"class","sr-only")},m(s,a){L(s,t,a),L(s,r,a),st(i,s,a),o=!0},i(s){o||(I(i.$$.fragment,s),o=!0)},o(s){E(i.$$.fragment,s),o=!1},d(s){s&&(w(t),w(r)),ot(i,s)}}}function cd(n){let t,e;return t=new Hw({props:{algolia:n[10]}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i&1024&&(o.algolia=r[10]),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function ud(n){let t,e,r,i;return e=new Pn({props:{src:P_,class:"w-4 h-4 text-base-content"}}),{c(){t=Y("a"),lt(e.$$.fragment),this.h()},l(o){t=q(o,"A",{href:!0,class:!0,target:!0,rel:!0});var s=J(t);at(e.$$.fragment,s),s.forEach(w),this.h()},h(){C(t,"href",r=Wt(n[11])),C(t,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),C(t,"target","_blank"),C(t,"rel","noreferrer")},m(o,s){L(o,t,s),st(e,t,null),i=!0},p(o,s){(!i||s&2048&&r!==(r=Wt(o[11])))&&C(t,"href",r)},i(o){i||(I(e.$$.fragment,o),i=!0)},o(o){E(e.$$.fragment,o),i=!1},d(o){o&&w(t),ot(e)}}}function fd(n){let t,e,r,i;return e=new Pn({props:{src:M_,class:"w-4 h-4 text-base-content"}}),{c(){t=Y("a"),lt(e.$$.fragment),this.h()},l(o){t=q(o,"A",{href:!0,class:!0,target:!0,rel:!0});var s=J(t);at(e.$$.fragment,s),s.forEach(w),this.h()},h(){C(t,"href",r=Wt(n[12])),C(t,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),C(t,"target","_blank"),C(t,"rel","noreferrer")},m(o,s){L(o,t,s),st(e,t,null),i=!0},p(o,s){(!i||s&4096&&r!==(r=Wt(o[12])))&&C(t,"href",r)},i(o){i||(I(e.$$.fragment,o),i=!0)},o(o){E(e.$$.fragment,o),i=!1},d(o){o&&w(t),ot(e)}}}function dd(n){let t,e,r;return e=new Pn({props:{src:N_,fill:"currentColor",class:"w-4 h-4 text-base-content "}}),{c(){t=Y("a"),lt(e.$$.fragment),this.h()},l(i){t=q(i,"A",{href:!0,class:!0,target:!0,rel:!0});var o=J(t);at(e.$$.fragment,o),o.forEach(w),this.h()},h(){C(t,"href",n[13]),C(t,"class","hover:bg-gray-50 rounded-lg p-2 transition-all duration-200"),C(t,"target","_blank"),C(t,"rel","noreferrer")},m(i,o){L(i,t,o),st(e,t,null),r=!0},p(i,o){(!r||o&8192)&&C(t,"href",i[13])},i(i){r||(I(e.$$.fragment,i),r=!0)},o(i){E(e.$$.fragment,i),r=!1},d(i){i&&w(t),ot(e)}}}function hd(n){let t,e,r,i;return e=new Pn({props:{src:C_,class:"w-4 h-4 text-base-content "}}),{c(){t=Y("a"),lt(e.$$.fragment),this.h()},l(o){t=q(o,"A",{href:!0,class:!0,target:!0,rel:!0});var s=J(t);at(e.$$.fragment,s),s.forEach(w),this.h()},h(){C(t,"href",r=Wt(n[14])),C(t,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),C(t,"target","_blank"),C(t,"rel","noreferrer")},m(o,s){L(o,t,s),st(e,t,null),i=!0},p(o,s){(!i||s&16384&&r!==(r=Wt(o[14])))&&C(t,"href",r)},i(o){i||(I(e.$$.fragment,o),i=!0)},o(o){E(e.$$.fragment,o),i=!1},d(o){o&&w(t),ot(e)}}}function v2(n){let t,e,r,i,o,s,a,l,c,u,f,h,d,g,m,p;const v=[_2,g2],b=[];function _(B,N){return B[8]||B[9]==="never"?0:1}r=_(n),i=b[r]=v[r](n);let O=n[10]&&cd(n),y=n[11]&&ud(n),T=n[12]&&fd(n),S=n[13]&&dd(n),F=n[14]&&hd(n);return g=new p2({props:{neverShowQueries:n[5]}}),{c(){t=Y("header"),e=Y("div"),i.c(),o=rt(),s=Y("div"),O&&O.c(),a=rt(),l=Y("div"),y&&y.c(),c=rt(),T&&T.c(),u=rt(),S&&S.c(),f=rt(),F&&F.c(),h=rt(),d=Y("div"),lt(g.$$.fragment),this.h()},l(B){t=q(B,"HEADER",{class:!0});var N=J(t);e=q(N,"DIV",{class:!0,style:!0});var R=J(e);i.l(R),o=nt(R),s=q(R,"DIV",{class:!0});var P=J(s);O&&O.l(P),a=nt(P),l=q(P,"DIV",{class:!0});var M=J(l);y&&y.l(M),c=nt(M),T&&T.l(M),u=nt(M),S&&S.l(M),f=nt(M),F&&F.l(M),M.forEach(w),h=nt(P),d=q(P,"DIV",{class:!0});var j=J(d);at(g.$$.fragment,j),j.forEach(w),P.forEach(w),R.forEach(w),N.forEach(w),this.h()},h(){C(l,"class","flex gap-2 items-center"),C(d,"class","relative"),C(s,"class","flex gap-2 text-sm items-center"),C(e,"class",m=(n[6]?"max-w-full ":n[7]?"":" max-w-7xl ")+"mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between"),Gs(e,"max-width",n[7]+"px"),C(t,"class","fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden")},m(B,N){L(B,t,N),W(t,e),b[r].m(e,null),W(e,o),W(e,s),O&&O.m(s,null),W(s,a),W(s,l),y&&y.m(l,null),W(l,c),T&&T.m(l,null),W(l,u),S&&S.m(l,null),W(l,f),F&&F.m(l,null),W(s,h),W(s,d),st(g,d,null),p=!0},p(B,[N]){let R=r;r=_(B),r===R?b[r].p(B,N):(jt(),E(b[R],1,1,()=>{b[R]=null}),Lt(),i=b[r],i?i.p(B,N):(i=b[r]=v[r](B),i.c()),I(i,1),i.m(e,o)),B[10]?O?(O.p(B,N),N&1024&&I(O,1)):(O=cd(B),O.c(),I(O,1),O.m(s,a)):O&&(jt(),E(O,1,1,()=>{O=null}),Lt()),B[11]?y?(y.p(B,N),N&2048&&I(y,1)):(y=ud(B),y.c(),I(y,1),y.m(l,c)):y&&(jt(),E(y,1,1,()=>{y=null}),Lt()),B[12]?T?(T.p(B,N),N&4096&&I(T,1)):(T=fd(B),T.c(),I(T,1),T.m(l,u)):T&&(jt(),E(T,1,1,()=>{T=null}),Lt()),B[13]?S?(S.p(B,N),N&8192&&I(S,1)):(S=dd(B),S.c(),I(S,1),S.m(l,f)):S&&(jt(),E(S,1,1,()=>{S=null}),Lt()),B[14]?F?(F.p(B,N),N&16384&&I(F,1)):(F=hd(B),F.c(),I(F,1),F.m(l,null)):F&&(jt(),E(F,1,1,()=>{F=null}),Lt());const P={};N&32&&(P.neverShowQueries=B[5]),g.$set(P),(!p||N&192&&m!==(m=(B[6]?"max-w-full ":B[7]?"":" max-w-7xl ")+"mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between"))&&C(e,"class",m),(!p||N&128)&&Gs(e,"max-width",B[7]+"px")},i(B){p||(I(i),I(O),I(y),I(T),I(S),I(F),I(g.$$.fragment,B),p=!0)},o(B){E(i),E(O),E(y),E(T),E(S),E(F),E(g.$$.fragment,B),p=!1},d(B){B&&w(t),b[r].d(),O&&O.d(),y&&y.d(),T&&T.d(),S&&S.d(),F&&F.d(),ot(g)}}}function w2(n,t,e){let{mobileSidebarOpen:r=void 0}=t,{title:i=void 0}=t,{logo:o=void 0}=t,{lightLogo:s=void 0}=t,{darkLogo:a=void 0}=t,{neverShowQueries:l=void 0}=t,{fullWidth:c=void 0}=t,{maxWidth:u=void 0}=t,{hideSidebar:f=void 0}=t,{sidebarFrontMatter:h=void 0}=t,{algolia:d=void 0}=t,{githubRepo:g=void 0}=t,{xProfile:m=void 0}=t,{blueskyProfile:p=void 0}=t,{slackCommunity:v=void 0}=t;const b=()=>{e(0,r=!r)};return n.$$set=_=>{"mobileSidebarOpen"in _&&e(0,r=_.mobileSidebarOpen),"title"in _&&e(1,i=_.title),"logo"in _&&e(2,o=_.logo),"lightLogo"in _&&e(3,s=_.lightLogo),"darkLogo"in _&&e(4,a=_.darkLogo),"neverShowQueries"in _&&e(5,l=_.neverShowQueries),"fullWidth"in _&&e(6,c=_.fullWidth),"maxWidth"in _&&e(7,u=_.maxWidth),"hideSidebar"in _&&e(8,f=_.hideSidebar),"sidebarFrontMatter"in _&&e(9,h=_.sidebarFrontMatter),"algolia"in _&&e(10,d=_.algolia),"githubRepo"in _&&e(11,g=_.githubRepo),"xProfile"in _&&e(12,m=_.xProfile),"blueskyProfile"in _&&e(13,p=_.blueskyProfile),"slackCommunity"in _&&e(14,v=_.slackCommunity)},[r,i,o,s,a,l,c,u,f,h,d,g,m,p,v,b]}class S2 extends re{constructor(t){super(),ie(this,t,w2,v2,Zt,{mobileSidebarOpen:0,title:1,logo:2,lightLogo:3,darkLogo:4,neverShowQueries:5,fullWidth:6,maxWidth:7,hideSidebar:8,sidebarFrontMatter:9,algolia:10,githubRepo:11,xProfile:12,blueskyProfile:13,slackCommunity:14})}}function I2(n){return Cr(()=>{}),[]}class O2 extends re{constructor(t){super(),ie(this,t,I2,null,Zt,{})}}function k2(n){let t,e='<span class="sr-only">Loading...</span> <div class="h-8 rounded-full bg-base-200 w-48 mb-8"></div> <div class="flex gap-3"><div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[65%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[100%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[75%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div>',r;return{c(){t=Y("div"),t.innerHTML=e,this.h()},l(i){t=q(i,"DIV",{role:!0,class:!0,"data-svelte-h":!0}),vr(t)!=="svelte-1u7962h"&&(t.innerHTML=e),this.h()},h(){C(t,"role","status"),C(t,"class","animate-pulse")},m(i,o){L(i,t,o)},p:Xt,i(i){i&&(r||En(()=>{r=qi(t,xo,{}),r.start()}))},o:Xt,d(i){i&&w(t)}}}class D2 extends re{constructor(t){super(),ie(this,t,null,k2,Zt,{})}}function E2(n){let t,e;const r=n[1].default,i=ge(r,n,n[0],null);return{c(){t=Y("span"),i&&i.c(),this.h()},l(o){t=q(o,"SPAN",{class:!0});var s=J(t);i&&i.l(s),s.forEach(w),this.h()},h(){C(t,"class","rounded-sm px-0.5 py-[1px] bg-positive/10 border border-positive/20 text-positive text-base sm:text-xs")},m(o,s){L(o,t,s),i&&i.m(t,null),e=!0},p(o,[s]){i&&i.p&&(!e||s&1)&&_e(i,r,o,o[0],e?ye(r,o[0],s,null):be(o[0]),null)},i(o){e||(I(i,o),e=!0)},o(o){E(i,o),e=!1},d(o){o&&w(t),i&&i.d(o)}}}function A2(n,t,e){let{$$slots:r={},$$scope:i}=t;return n.$$set=o=>{"$$scope"in o&&e(0,i=o.$$scope)},[i,r]}class Mn extends re{constructor(t){super(),ie(this,t,A2,E2,Zt,{})}}function md(n,t,e){const r=n.slice();return r[18]=t[e],r}function pd(n,t,e){const r=n.slice();return r[21]=t[e],r}function gd(n,t,e){const r=n.slice();return r[24]=t[e],r}function _d(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[24].href.toUpperCase()+"/";return t[27]=e,t}function T2(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[21].href.toUpperCase()+"/";return t[27]=e,t}function bd(n,t,e){const r=n.slice();return r[18]=t[e],r}function yd(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[18].href.toUpperCase()+"/";return t[27]=e,t}function vd(n,t,e){const r=n.slice();return r[18]=t[e],r}function wd(n,t,e){const r=n.slice();return r[21]=t[e],r}function Sd(n,t,e){const r=n.slice();return r[24]=t[e],r}function Id(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[24].href.toUpperCase()+"/";return t[27]=e,t}function B2(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[21].href.toUpperCase()+"/";return t[27]=e,t}function Od(n,t,e){const r=n.slice();return r[18]=t[e],r}function kd(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[18].href.toUpperCase()+"/";return t[27]=e,t}function Dd(n){let t,e,r,i,o,s,a,l,c,u,f,h,d="Close sidebar",g,m,p,v,b,_,O,y,T,S,F,B,N,R;l=new Dc({props:{logo:n[2],title:n[1]}}),m=new Pn({props:{src:Xs,class:"w-5 h-5"}});let P=he(n[11]),M=[];for(let D=0;D<P.length;D+=1)M[D]=Ed(Od(n,P,D));const j=D=>E(M[D],1,1,()=>{M[D]=null});let V=he(n[11]),U=[];for(let D=0;D<V.length;D+=1)U[D]=Pd(vd(n,V,D));const H=D=>E(U[D],1,1,()=>{U[D]=null});return{c(){t=Y("div"),r=rt(),i=Y("div"),o=Y("div"),s=Y("div"),a=Y("a"),lt(l.$$.fragment),c=rt(),u=Y("span"),f=Y("button"),h=Y("span"),h.textContent=d,g=rt(),lt(m.$$.fragment),p=rt(),v=Y("div"),b=Y("div"),_=Y("a"),O=wt(n[3]),y=rt();for(let D=0;D<M.length;D+=1)M[D].c();T=rt();for(let D=0;D<U.length;D+=1)U[D].c();this.h()},l(D){t=q(D,"DIV",{class:!0,role:!0,tabindex:!0}),J(t).forEach(w),r=nt(D),i=q(D,"DIV",{class:!0});var X=J(i);o=q(X,"DIV",{class:!0});var Q=J(o);s=q(Q,"DIV",{class:!0});var Z=J(s);a=q(Z,"A",{href:!0,class:!0});var Dt=J(a);at(l.$$.fragment,Dt),Dt.forEach(w),c=nt(Z),u=q(Z,"SPAN",{role:!0,tabindex:!0});var Bt=J(u);f=q(Bt,"BUTTON",{type:!0,class:!0});var Ct=J(f);h=q(Ct,"SPAN",{class:!0,"data-svelte-h":!0}),vr(h)!=="svelte-13q18xv"&&(h.textContent=d),g=nt(Ct),at(m.$$.fragment,Ct),Ct.forEach(w),Bt.forEach(w),Z.forEach(w),p=nt(Q),v=q(Q,"DIV",{class:!0,id:!0});var K=J(v);b=q(K,"DIV",{class:!0});var Oe=J(b);_=q(Oe,"A",{class:!0,href:!0});var Kt=J(_);O=vt(Kt,n[3]),Kt.forEach(w),y=nt(Oe);for(let Be=0;Be<M.length;Be+=1)M[Be].l(Oe);Oe.forEach(w),T=nt(K);for(let Be=0;Be<U.length;Be+=1)U[Be].l(K);K.forEach(w),Q.forEach(w),X.forEach(w),this.h()},h(){C(t,"class","fixed inset-0 bg-base-100/80 z-50 backdrop-blur-sm"),C(t,"role","button"),C(t,"tabindex","-1"),C(a,"href",Wt("/")),C(a,"class","block mt-1 text-sm font-bold"),C(h,"class","sr-only"),C(f,"type","button"),C(f,"class","hover:bg-base-200 rounded-lg p-1 transition-all duration-500"),C(u,"role","button"),C(u,"tabindex","-1"),C(s,"class","py-3 px-8 mb-3 flex items-start justify-between"),C(_,"class","sticky top-0 bg-base-100 shadow shadow-base-100 text-base-heading font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100"),C(_,"href",Wt("/")),C(b,"class","flex flex-col pb-6"),C(v,"class","flex-1 px-8 sm:pb-0 pb-4 overflow-auto text-base sm:text-sm pretty-scrollbar"),C(v,"id","mobileScrollable"),C(o,"class","flex flex-col h-full pb-4"),C(i,"class","bg-base-100 border-r border-base-200 shadow-lg fixed inset-0 z-50 flex sm:w-72 h-screen w-screen flex-col overflow-hidden select-none")},m(D,X){L(D,t,X),L(D,r,X),L(D,i,X),W(i,o),W(o,s),W(s,a),st(l,a,null),W(s,c),W(s,u),W(u,f),W(f,h),W(f,g),st(m,f,null),W(o,p),W(o,v),W(v,b),W(b,_),W(_,O),W(b,y);for(let Q=0;Q<M.length;Q+=1)M[Q]&&M[Q].m(b,null);W(v,T);for(let Q=0;Q<U.length;Q+=1)U[Q]&&U[Q].m(v,null);B=!0,N||(R=[Pt(t,"click",n[13]),Pt(t,"keypress",n[14]),Pt(f,"click",n[15]),Pt(u,"click",n[16]),Pt(u,"keypress",n[17])],N=!0)},p(D,X){const Q={};if(X[0]&4&&(Q.logo=D[2]),X[0]&2&&(Q.title=D[1]),l.$set(Q),(!B||X[0]&8)&&Re(O,D[3]),X[0]&2304){P=he(D[11]);let Z;for(Z=0;Z<P.length;Z+=1){const Dt=Od(D,P,Z);M[Z]?(M[Z].p(Dt,X),I(M[Z],1)):(M[Z]=Ed(Dt),M[Z].c(),I(M[Z],1),M[Z].m(b,null))}for(jt(),Z=P.length;Z<M.length;Z+=1)j(Z);Lt()}if(X[0]&2432){V=he(D[11]);let Z;for(Z=0;Z<V.length;Z+=1){const Dt=vd(D,V,Z);U[Z]?(U[Z].p(Dt,X),I(U[Z],1)):(U[Z]=Pd(Dt),U[Z].c(),I(U[Z],1),U[Z].m(v,null))}for(jt(),Z=V.length;Z<U.length;Z+=1)H(Z);Lt()}},i(D){if(!B){D&&En(()=>{B&&(e||(e=Dn(t,xo,{duration:100},!0)),e.run(1))}),I(l.$$.fragment,D),I(m.$$.fragment,D);for(let X=0;X<P.length;X+=1)I(M[X]);for(let X=0;X<V.length;X+=1)I(U[X]);D&&En(()=>{B&&(F&&F.end(1),S=qi(i,ii,{x:-50,duration:300}),S.start())}),B=!0}},o(D){D&&(e||(e=Dn(t,xo,{duration:100},!1)),e.run(0)),E(l.$$.fragment,D),E(m.$$.fragment,D),M=M.filter(Boolean);for(let X=0;X<M.length;X+=1)E(M[X]);U=U.filter(Boolean);for(let X=0;X<U.length;X+=1)E(U[X]);S&&S.invalidate(),D&&(F=is(i,ii,{x:-100,duration:200})),B=!1},d(D){D&&(w(t),w(r),w(i)),D&&e&&e.end(),ot(l),ot(m),An(M,D),An(U,D),D&&F&&F.end(),N=!1,zn(R)}}}function P2(n){var c,u;let t,e=(((c=n[18].frontMatter)==null?void 0:c.title)??n[18].label)+"",r,i,o,s,a,l=((u=n[18].frontMatter)==null?void 0:u.sidebar_badge)&&M2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),l&&l.c(),o=rt(),this.h()},l(f){t=q(f,"A",{class:!0,href:!0});var h=J(t);r=vt(h,e),i=nt(h),l&&l.l(h),o=nt(h),h.forEach(w),this.h()},h(){C(t,"class",s="group inline-block py-1 capitalize transition-colors duration-100 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content")),C(t,"href",Wt(n[18].href))},m(f,h){L(f,t,h),W(t,r),W(t,i),l&&l.m(t,null),W(t,o),a=!0},p(f,h){var d;(d=f[18].frontMatter)!=null&&d.sidebar_badge&&l.p(f,h),(!a||h[0]&256&&s!==(s="group inline-block py-1 capitalize transition-colors duration-100 "+(f[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(t,"class",s)},i(f){a||(I(l),a=!0)},o(f){E(l),a=!1},d(f){f&&w(t),l&&l.d()}}}function M2(n){let t,e;return t=new Mn({props:{$$slots:{default:[N2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function N2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function Ed(n){var i,o;let t,e,r=n[18].children.length===0&&n[18].href&&(((i=n[18].frontMatter)==null?void 0:i.sidebar_link)!==!1||((o=n[18].frontMatter)==null?void 0:o.sidebar_link)===void 0)&&P2(kd(n));return{c(){r&&r.c(),t=ut()},l(s){r&&r.l(s),t=ut()},m(s,a){r&&r.m(s,a),L(s,t,a),e=!0},p(s,a){var l,c;s[18].children.length===0&&s[18].href&&(((l=s[18].frontMatter)==null?void 0:l.sidebar_link)!==!1||((c=s[18].frontMatter)==null?void 0:c.sidebar_link)===void 0)&&r.p(kd(s),a)},i(s){e||(I(r),e=!0)},o(s){E(r),e=!1},d(s){s&&w(t),r&&r.d(s)}}}function C2(n){let t,e,r,i,o,s;const a=[j2,F2],l=[];function c(d,g){var m,p;return d[18].href&&(((m=d[18].frontMatter)==null?void 0:m.sidebar_link)!==!1||((p=d[18].frontMatter)==null?void 0:p.sidebar_link)===void 0)?0:1}e=c(n),r=l[e]=a[e](n);let u=he(n[18].children),f=[];for(let d=0;d<u.length;d+=1)f[d]=Bd(wd(n,u,d));const h=d=>E(f[d],1,1,()=>{f[d]=null});return{c(){t=Y("div"),r.c(),i=rt();for(let d=0;d<f.length;d+=1)f[d].c();o=rt(),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=J(t);r.l(g),i=nt(g);for(let m=0;m<f.length;m+=1)f[m].l(g);o=nt(g),g.forEach(w),this.h()},h(){C(t,"class","flex flex-col pb-6")},m(d,g){L(d,t,g),l[e].m(t,null),W(t,i);for(let m=0;m<f.length;m+=1)f[m]&&f[m].m(t,null);W(t,o),s=!0},p(d,g){if(r.p(d,g),g[0]&2432){u=he(d[18].children);let m;for(m=0;m<u.length;m+=1){const p=wd(d,u,m);f[m]?(f[m].p(p,g),I(f[m],1)):(f[m]=Bd(p),f[m].c(),I(f[m],1),f[m].m(t,o))}for(jt(),m=u.length;m<f.length;m+=1)h(m);Lt()}},i(d){if(!s){I(r);for(let g=0;g<u.length;g+=1)I(f[g]);s=!0}},o(d){E(r),f=f.filter(Boolean);for(let g=0;g<f.length;g+=1)E(f[g]);s=!1},d(d){d&&w(t),l[e].d(),An(f,d)}}}function F2(n){var a,l;let t,e=(((a=n[18].frontMatter)==null?void 0:a.title)??n[18].label)+"",r,i,o,s=((l=n[18].frontMatter)==null?void 0:l.sidebar_badge)&&L2(n);return{c(){t=Y("span"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"SPAN",{class:!0,href:!0});var u=J(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(w),this.h()},h(){C(t,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"),C(t,"href",Wt(n[18].href))},m(c,u){L(c,t,u),W(t,r),W(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[18].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(I(s),o=!0)},o(c){E(s),o=!1},d(c){c&&w(t),s&&s.d()}}}function j2(n){var a,l;let t,e=(((a=n[18].frontMatter)==null?void 0:a.title)??n[18].label)+"",r,i,o,s=((l=n[18].frontMatter)==null?void 0:l.sidebar_badge)&&U2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"A",{class:!0,href:!0});var u=J(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(w),this.h()},h(){C(t,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"),C(t,"href",Wt(n[18].href))},m(c,u){L(c,t,u),W(t,r),W(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[18].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(I(s),o=!0)},o(c){E(s),o=!1},d(c){c&&w(t),s&&s.d()}}}function L2(n){let t,e;return t=new Mn({props:{$$slots:{default:[R2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function R2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function U2(n){let t,e;return t=new Mn({props:{$$slots:{default:[V2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function V2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function z2(n){var a,l;let t,e=(((a=n[21].frontMatter)==null?void 0:a.title)??n[21].label)+"",r,i,o,s=((l=n[21].frontMatter)==null?void 0:l.sidebar_badge)&&H2(n);return{c(){t=Y("span"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"SPAN",{class:!0});var u=J(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(w),this.h()},h(){C(t,"class","group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted")},m(c,u){L(c,t,u),W(t,r),W(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[21].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(I(s),o=!0)},o(c){E(s),o=!1},d(c){c&&w(t),s&&s.d()}}}function W2(n){var l,c;let t,e=(((l=n[21].frontMatter)==null?void 0:l.title)??n[21].label)+"",r,i,o,s,a=((c=n[21].frontMatter)==null?void 0:c.sidebar_badge)&&q2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),a&&a.c(),this.h()},l(u){t=q(u,"A",{class:!0,href:!0});var f=J(t);r=vt(f,e),i=nt(f),a&&a.l(f),f.forEach(w),this.h()},h(){C(t,"class",o="group inline-block py-1 capitalize transition-colors duration-100 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content")),C(t,"href",Wt(n[21].href))},m(u,f){L(u,t,f),W(t,r),W(t,i),a&&a.m(t,null),s=!0},p(u,f){var h;(h=u[21].frontMatter)!=null&&h.sidebar_badge&&a.p(u,f),(!s||f[0]&256&&o!==(o="group inline-block py-1 capitalize transition-colors duration-100 "+(u[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(t,"class",o)},i(u){s||(I(a),s=!0)},o(u){E(a),s=!1},d(u){u&&w(t),a&&a.d()}}}function H2(n){let t,e;return t=new Mn({props:{$$slots:{default:[x2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function x2(n){let t=n[21].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function q2(n){let t,e;return t=new Mn({props:{$$slots:{default:[Y2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function Y2(n){let t=n[21].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function Ad(n){let t,e,r=he(n[21].children),i=[];for(let s=0;s<r.length;s+=1)i[s]=Td(Sd(n,r,s));const o=s=>E(i[s],1,1,()=>{i[s]=null});return{c(){for(let s=0;s<i.length;s+=1)i[s].c();t=ut()},l(s){for(let a=0;a<i.length;a+=1)i[a].l(s);t=ut()},m(s,a){for(let l=0;l<i.length;l+=1)i[l]&&i[l].m(s,a);L(s,t,a),e=!0},p(s,a){if(a[0]&2304){r=he(s[21].children);let l;for(l=0;l<r.length;l+=1){const c=Sd(s,r,l);i[l]?(i[l].p(c,a),I(i[l],1)):(i[l]=Td(c),i[l].c(),I(i[l],1),i[l].m(t.parentNode,t))}for(jt(),l=r.length;l<i.length;l+=1)o(l);Lt()}},i(s){if(!e){for(let a=0;a<r.length;a+=1)I(i[a]);e=!0}},o(s){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)E(i[a]);e=!1},d(s){s&&w(t),An(i,s)}}}function K2(n){var l,c;let t,e=(((l=n[24].frontMatter)==null?void 0:l.title)??n[24].label)+"",r,i,o,s,a=((c=n[24].frontMatter)==null?void 0:c.sidebar_badge)&&J2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),a&&a.c(),this.h()},l(u){t=q(u,"A",{href:!0,class:!0});var f=J(t);r=vt(f,e),i=nt(f),a&&a.l(f),f.forEach(w),this.h()},h(){C(t,"href",Wt(n[24].href)),C(t,"class",o="group inline-block py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 capitalize transition-all duration-1000 border-l ml-[1px] "+(n[27]?"text-primary border-primary":"text-base-content-muted hover:text-base-content hover:border-base-content"))},m(u,f){L(u,t,f),W(t,r),W(t,i),a&&a.m(t,null),s=!0},p(u,f){var h;(h=u[24].frontMatter)!=null&&h.sidebar_badge&&a.p(u,f),(!s||f[0]&256&&o!==(o="group inline-block py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 capitalize transition-all duration-1000 border-l ml-[1px] "+(u[27]?"text-primary border-primary":"text-base-content-muted hover:text-base-content hover:border-base-content")))&&C(t,"class",o)},i(u){s||(I(a),s=!0)},o(u){E(a),s=!1},d(u){u&&w(t),a&&a.d()}}}function J2(n){let t,e;return t=new Mn({props:{$$slots:{default:[Q2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function Q2(n){let t=n[24].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function Td(n){var i,o;let t,e,r=n[24].href&&(((i=n[24].frontMatter)==null?void 0:i.sidebar_link)!==!1||((o=n[24].frontMatter)==null?void 0:o.sidebar_link)===void 0)&&K2(Id(n));return{c(){r&&r.c(),t=ut()},l(s){r&&r.l(s),t=ut()},m(s,a){r&&r.m(s,a),L(s,t,a),e=!0},p(s,a){var l,c;s[24].href&&(((l=s[24].frontMatter)==null?void 0:l.sidebar_link)!==!1||((c=s[24].frontMatter)==null?void 0:c.sidebar_link)===void 0)&&r.p(Id(s),a)},i(s){e||(I(r),e=!0)},o(s){E(r),e=!1},d(s){s&&w(t),r&&r.d(s)}}}function Bd(n){let t,e,r,i,o;const s=[W2,z2],a=[];function l(f,h){var d,g;return f[21].href&&(((d=f[21].frontMatter)==null?void 0:d.sidebar_link)!==!1||((g=f[21].frontMatter)==null?void 0:g.sidebar_link)===void 0)?0:1}function c(f,h){return h===0?B2(f):f}t=l(n),e=a[t]=s[t](c(n,t));let u=n[21].children.length>0&&n[7]>2&&Ad(n);return{c(){e.c(),r=rt(),u&&u.c(),i=ut()},l(f){e.l(f),r=nt(f),u&&u.l(f),i=ut()},m(f,h){a[t].m(f,h),L(f,r,h),u&&u.m(f,h),L(f,i,h),o=!0},p(f,h){e.p(c(f,t),h),f[21].children.length>0&&f[7]>2?u?(u.p(f,h),h[0]&128&&I(u,1)):(u=Ad(f),u.c(),I(u,1),u.m(i.parentNode,i)):u&&(jt(),E(u,1,1,()=>{u=null}),Lt())},i(f){o||(I(e),I(u),o=!0)},o(f){E(e),E(u),o=!1},d(f){f&&(w(r),w(i)),a[t].d(f),u&&u.d(f)}}}function Pd(n){let t,e,r=n[18].children.length>0&&C2(n);return{c(){r&&r.c(),t=ut()},l(i){r&&r.l(i),t=ut()},m(i,o){r&&r.m(i,o),L(i,t,o),e=!0},p(i,o){i[18].children.length>0&&r.p(i,o)},i(i){e||(I(r),e=!0)},o(i){E(r),e=!1},d(i){i&&w(t),r&&r.d(i)}}}function Md(n){let t,e,r,i,o,s,a,l=he(n[11]),c=[];for(let g=0;g<l.length;g+=1)c[g]=Nd(bd(n,l,g));const u=g=>E(c[g],1,1,()=>{c[g]=null});let f=he(n[11]),h=[];for(let g=0;g<f.length;g+=1)h[g]=Rd(md(n,f,g));const d=g=>E(h[g],1,1,()=>{h[g]=null});return{c(){t=Y("div"),e=Y("div"),r=Y("a"),i=wt(n[3]),o=rt();for(let g=0;g<c.length;g+=1)c[g].c();s=rt();for(let g=0;g<h.length;g+=1)h[g].c();this.h()},l(g){t=q(g,"DIV",{class:!0});var m=J(t);e=q(m,"DIV",{class:!0});var p=J(e);r=q(p,"A",{class:!0,href:!0});var v=J(r);i=vt(v,n[3]),v.forEach(w),o=nt(p);for(let b=0;b<c.length;b+=1)c[b].l(p);p.forEach(w),s=nt(m);for(let b=0;b<h.length;b+=1)h[b].l(m);m.forEach(w),this.h()},h(){C(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading"),C(r,"href",Wt("/")),C(e,"class","flex flex-col pb-6"),C(t,"class","hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar"),Fr(t,"top-8",n[5])},m(g,m){L(g,t,m),W(t,e),W(e,r),W(r,i),W(e,o);for(let p=0;p<c.length;p+=1)c[p]&&c[p].m(e,null);W(t,s);for(let p=0;p<h.length;p+=1)h[p]&&h[p].m(t,null);a=!0},p(g,m){if((!a||m[0]&8)&&Re(i,g[3]),m[0]&2304){l=he(g[11]);let p;for(p=0;p<l.length;p+=1){const v=bd(g,l,p);c[p]?(c[p].p(v,m),I(c[p],1)):(c[p]=Nd(v),c[p].c(),I(c[p],1),c[p].m(e,null))}for(jt(),p=l.length;p<c.length;p+=1)u(p);Lt()}if(m[0]&2432){f=he(g[11]);let p;for(p=0;p<f.length;p+=1){const v=md(g,f,p);h[p]?(h[p].p(v,m),I(h[p],1)):(h[p]=Rd(v),h[p].c(),I(h[p],1),h[p].m(t,null))}for(jt(),p=f.length;p<h.length;p+=1)d(p);Lt()}(!a||m[0]&32)&&Fr(t,"top-8",g[5])},i(g){if(!a){for(let m=0;m<l.length;m+=1)I(c[m]);for(let m=0;m<f.length;m+=1)I(h[m]);a=!0}},o(g){c=c.filter(Boolean);for(let m=0;m<c.length;m+=1)E(c[m]);h=h.filter(Boolean);for(let m=0;m<h.length;m+=1)E(h[m]);a=!1},d(g){g&&w(t),An(c,g),An(h,g)}}}function G2(n){var c,u;let t,e=(((c=n[18].frontMatter)==null?void 0:c.title)??n[18].label)+"",r,i,o,s,a,l=((u=n[18].frontMatter)==null?void 0:u.sidebar_badge)&&X2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),l&&l.c(),o=rt(),this.h()},l(f){t=q(f,"A",{class:!0,href:!0});var h=J(t);r=vt(h,e),i=nt(h),l&&l.l(h),o=nt(h),h.forEach(w),this.h()},h(){C(t,"class",s="group inline-block py-1 capitalize transition-all duration-100 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content")),C(t,"href",Wt(n[18].href))},m(f,h){L(f,t,h),W(t,r),W(t,i),l&&l.m(t,null),W(t,o),a=!0},p(f,h){var d;(d=f[18].frontMatter)!=null&&d.sidebar_badge&&l.p(f,h),(!a||h[0]&256&&s!==(s="group inline-block py-1 capitalize transition-all duration-100 "+(f[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(t,"class",s)},i(f){a||(I(l),a=!0)},o(f){E(l),a=!1},d(f){f&&w(t),l&&l.d()}}}function X2(n){let t,e;return t=new Mn({props:{$$slots:{default:[Z2]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function Z2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function Nd(n){var i,o;let t,e,r=n[18].children.length===0&&n[18].href&&(((i=n[18].frontMatter)==null?void 0:i.sidebar_link)!==!1||((o=n[18].frontMatter)==null?void 0:o.sidebar_link)===void 0)&&G2(yd(n));return{c(){r&&r.c(),t=ut()},l(s){r&&r.l(s),t=ut()},m(s,a){r&&r.m(s,a),L(s,t,a),e=!0},p(s,a){var l,c;s[18].children.length===0&&s[18].href&&(((l=s[18].frontMatter)==null?void 0:l.sidebar_link)!==!1||((c=s[18].frontMatter)==null?void 0:c.sidebar_link)===void 0)&&r.p(yd(s),a)},i(s){e||(I(r),e=!0)},o(s){E(r),e=!1},d(s){s&&w(t),r&&r.d(s)}}}function $2(n){let t,e,r,i,o,s;const a=[eS,tS],l=[];function c(d,g){var m,p;return d[18].href&&(((m=d[18].frontMatter)==null?void 0:m.sidebar_link)!==!1||((p=d[18].frontMatter)==null?void 0:p.sidebar_link)===void 0)?0:1}e=c(n),r=l[e]=a[e](n);let u=he(n[18].children),f=[];for(let d=0;d<u.length;d+=1)f[d]=Ld(pd(n,u,d));const h=d=>E(f[d],1,1,()=>{f[d]=null});return{c(){t=Y("div"),r.c(),i=rt();for(let d=0;d<f.length;d+=1)f[d].c();o=rt(),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=J(t);r.l(g),i=nt(g);for(let m=0;m<f.length;m+=1)f[m].l(g);o=nt(g),g.forEach(w),this.h()},h(){C(t,"class","flex flex-col pb-6")},m(d,g){L(d,t,g),l[e].m(t,null),W(t,i);for(let m=0;m<f.length;m+=1)f[m]&&f[m].m(t,null);W(t,o),s=!0},p(d,g){if(r.p(d,g),g[0]&2432){u=he(d[18].children);let m;for(m=0;m<u.length;m+=1){const p=pd(d,u,m);f[m]?(f[m].p(p,g),I(f[m],1)):(f[m]=Ld(p),f[m].c(),I(f[m],1),f[m].m(t,o))}for(jt(),m=u.length;m<f.length;m+=1)h(m);Lt()}},i(d){if(!s){I(r);for(let g=0;g<u.length;g+=1)I(f[g]);s=!0}},o(d){E(r),f=f.filter(Boolean);for(let g=0;g<f.length;g+=1)E(f[g]);s=!1},d(d){d&&w(t),l[e].d(),An(f,d)}}}function tS(n){var a,l;let t,e=(((a=n[18].frontMatter)==null?void 0:a.title)??n[18].label)+"",r,i,o,s=((l=n[18].frontMatter)==null?void 0:l.sidebar_badge)&&nS(n);return{c(){t=Y("span"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"SPAN",{class:!0,href:!0});var u=J(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(w),this.h()},h(){C(t,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize text-base-heading z-10"),C(t,"href",Wt(n[18].href))},m(c,u){L(c,t,u),W(t,r),W(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[18].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(I(s),o=!0)},o(c){E(s),o=!1},d(c){c&&w(t),s&&s.d()}}}function eS(n){var a,l;let t,e=(((a=n[18].frontMatter)==null?void 0:a.title)??n[18].label)+"",r,i,o,s=((l=n[18].frontMatter)==null?void 0:l.sidebar_badge)&&iS(n);return{c(){t=Y("a"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"A",{class:!0,href:!0});var u=J(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(w),this.h()},h(){C(t,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group block capitalize hover:underline text-base-heading z-10"),C(t,"href",Wt(n[18].href))},m(c,u){L(c,t,u),W(t,r),W(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[18].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(I(s),o=!0)},o(c){E(s),o=!1},d(c){c&&w(t),s&&s.d()}}}function nS(n){let t,e;return t=new Mn({props:{$$slots:{default:[rS]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function rS(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function iS(n){let t,e;return t=new Mn({props:{$$slots:{default:[oS]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function oS(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function sS(n){var a,l;let t,e=(((a=n[21].frontMatter)==null?void 0:a.title)??n[21].label)+"",r,i,o,s=((l=n[21].frontMatter)==null?void 0:l.sidebar_badge)&&lS(n);return{c(){t=Y("span"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"SPAN",{class:!0});var u=J(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(w),this.h()},h(){C(t,"class","group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted")},m(c,u){L(c,t,u),W(t,r),W(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[21].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(I(s),o=!0)},o(c){E(s),o=!1},d(c){c&&w(t),s&&s.d()}}}function aS(n){var l,c;let t,e=(((l=n[21].frontMatter)==null?void 0:l.title)??n[21].label)+"",r,i,o,s,a=((c=n[21].frontMatter)==null?void 0:c.sidebar_badge)&&uS(n);return{c(){t=Y("a"),r=wt(e),i=rt(),a&&a.c(),this.h()},l(u){t=q(u,"A",{href:!0,class:!0});var f=J(t);r=vt(f,e),i=nt(f),a&&a.l(f),f.forEach(w),this.h()},h(){C(t,"href",Wt(n[21].href)),C(t,"class",o="group inline-block py-1 capitalize transition-all duration-100 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content"))},m(u,f){L(u,t,f),W(t,r),W(t,i),a&&a.m(t,null),s=!0},p(u,f){var h;(h=u[21].frontMatter)!=null&&h.sidebar_badge&&a.p(u,f),(!s||f[0]&256&&o!==(o="group inline-block py-1 capitalize transition-all duration-100 "+(u[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(t,"class",o)},i(u){s||(I(a),s=!0)},o(u){E(a),s=!1},d(u){u&&w(t),a&&a.d()}}}function lS(n){let t,e;return t=new Mn({props:{$$slots:{default:[cS]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function cS(n){let t=n[21].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function uS(n){let t,e;return t=new Mn({props:{$$slots:{default:[fS]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function fS(n){let t=n[21].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function Cd(n){let t,e,r=he(n[21].children),i=[];for(let s=0;s<r.length;s+=1)i[s]=jd(gd(n,r,s));const o=s=>E(i[s],1,1,()=>{i[s]=null});return{c(){for(let s=0;s<i.length;s+=1)i[s].c();t=ut()},l(s){for(let a=0;a<i.length;a+=1)i[a].l(s);t=ut()},m(s,a){for(let l=0;l<i.length;l+=1)i[l]&&i[l].m(s,a);L(s,t,a),e=!0},p(s,a){if(a[0]&2304){r=he(s[21].children);let l;for(l=0;l<r.length;l+=1){const c=gd(s,r,l);i[l]?(i[l].p(c,a),I(i[l],1)):(i[l]=jd(c),i[l].c(),I(i[l],1),i[l].m(t.parentNode,t))}for(jt(),l=r.length;l<i.length;l+=1)o(l);Lt()}},i(s){if(!e){for(let a=0;a<r.length;a+=1)I(i[a]);e=!0}},o(s){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)E(i[a]);e=!1},d(s){s&&w(t),An(i,s)}}}function dS(n){var f,h;let t,e,r=(((f=n[24].frontMatter)==null?void 0:f.title)??n[24].label)+"",i,o,s,a,l,c=((h=n[24].frontMatter)==null?void 0:h.sidebar_badge)&&hS(n),u=n[27]&&Fd(n);return{c(){t=Y("div"),e=Y("a"),i=wt(r),o=rt(),c&&c.c(),a=rt(),u&&u.c(),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=J(t);e=q(g,"A",{href:!0,class:!0});var m=J(e);i=vt(m,r),o=nt(m),c&&c.l(m),m.forEach(w),a=nt(g),u&&u.l(g),g.forEach(w),this.h()},h(){C(e,"href",Wt(n[24].href)),C(e,"class",s="group inline-block w-full capitalize transition-all duration-200 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content")),C(t,"class","relative py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 border-l ml-[1px] transition-all duration-200 hover:border-base-content")},m(d,g){L(d,t,g),W(t,e),W(e,i),W(e,o),c&&c.m(e,null),W(t,a),u&&u.m(t,null),l=!0},p(d,g){var m;(m=d[24].frontMatter)!=null&&m.sidebar_badge&&c.p(d,g),(!l||g[0]&256&&s!==(s="group inline-block w-full capitalize transition-all duration-200 "+(d[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(e,"class",s),d[27]?u?g[0]&256&&I(u,1):(u=Fd(d),u.c(),I(u,1),u.m(t,null)):u&&(jt(),E(u,1,1,()=>{u=null}),Lt())},i(d){l||(I(c),I(u),l=!0)},o(d){E(c),E(u),l=!1},d(d){d&&w(t),c&&c.d(),u&&u.d()}}}function hS(n){let t,e;return t=new Mn({props:{$$slots:{default:[mS]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function mS(n){let t=n[24].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){L(r,e,i)},p:Xt,d(r){r&&w(e)}}}function Fd(n){let t,e,r,i;return{c(){t=Y("div"),this.h()},l(o){t=q(o,"DIV",{class:!0}),J(t).forEach(w),this.h()},h(){C(t,"class","absolute top-0 -left-[1px] w-[1px] h-full bg-primary")},m(o,s){L(o,t,s),i=!0},i(o){i||(o&&En(()=>{i&&(r&&r.end(1),e=qi(t,n[9],{key:"trigger"}),e.start())}),i=!0)},o(o){e&&e.invalidate(),o&&(r=is(t,n[10],{key:"trigger"})),i=!1},d(o){o&&w(t),o&&r&&r.end()}}}function jd(n){var i,o;let t,e,r=n[24].href&&(((i=n[24].frontMatter)==null?void 0:i.sidebar_link)!==!1||((o=n[24].frontMatter)==null?void 0:o.sidebar_link)===void 0)&&dS(_d(n));return{c(){r&&r.c(),t=ut()},l(s){r&&r.l(s),t=ut()},m(s,a){r&&r.m(s,a),L(s,t,a),e=!0},p(s,a){var l,c;s[24].href&&(((l=s[24].frontMatter)==null?void 0:l.sidebar_link)!==!1||((c=s[24].frontMatter)==null?void 0:c.sidebar_link)===void 0)&&r.p(_d(s),a)},i(s){e||(I(r),e=!0)},o(s){E(r),e=!1},d(s){s&&w(t),r&&r.d(s)}}}function Ld(n){let t,e,r,i,o;const s=[aS,sS],a=[];function l(f,h){var d,g;return f[21].href&&(((d=f[21].frontMatter)==null?void 0:d.sidebar_link)!==!1||((g=f[21].frontMatter)==null?void 0:g.sidebar_link)===void 0)?0:1}function c(f,h){return h===0?T2(f):f}t=l(n),e=a[t]=s[t](c(n,t));let u=n[21].children.length>0&&n[7]>2&&Cd(n);return{c(){e.c(),r=rt(),u&&u.c(),i=ut()},l(f){e.l(f),r=nt(f),u&&u.l(f),i=ut()},m(f,h){a[t].m(f,h),L(f,r,h),u&&u.m(f,h),L(f,i,h),o=!0},p(f,h){e.p(c(f,t),h),f[21].children.length>0&&f[7]>2?u?(u.p(f,h),h[0]&128&&I(u,1)):(u=Cd(f),u.c(),I(u,1),u.m(i.parentNode,i)):u&&(jt(),E(u,1,1,()=>{u=null}),Lt())},i(f){o||(I(e),I(u),o=!0)},o(f){E(e),E(u),o=!1},d(f){f&&(w(r),w(i)),a[t].d(f),u&&u.d(f)}}}function Rd(n){let t,e,r=n[18].children.length>0&&$2(n);return{c(){r&&r.c(),t=ut()},l(i){r&&r.l(i),t=ut()},m(i,o){r&&r.m(i,o),L(i,t,o),e=!0},p(i,o){i[18].children.length>0&&r.p(i,o)},i(i){e||(I(r),e=!0)},o(i){E(r),e=!1},d(i){i&&w(t),r&&r.d(i)}}}function Ud(n){let t,e='<a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a>';return{c(){t=Y("div"),t.innerHTML=e,this.h()},l(r){t=q(r,"DIV",{class:!0,"data-svelte-h":!0}),vr(t)!=="svelte-fworv4"&&(t.innerHTML=e),this.h()},h(){C(t,"class","fixed bottom-0 text-xs py-2")},m(r,i){L(r,t,i)},d(r){r&&w(t)}}}function pS(n){let t,e,r,i,o,s=n[0]&&Dd(n),a=!n[0]&&Md(n),l=n[4]&&Ud();return{c(){s&&s.c(),t=rt(),e=Y("aside"),a&&a.c(),r=rt(),l&&l.c(),this.h()},l(c){s&&s.l(c),t=nt(c),e=q(c,"ASIDE",{class:!0});var u=J(e);a&&a.l(u),r=nt(u),l&&l.l(u),u.forEach(w),this.h()},h(){C(e,"class",i="w-48 flex-none "+(n[6]==="hide"?"hidden":"hidden md:flex"))},m(c,u){s&&s.m(c,u),L(c,t,u),L(c,e,u),a&&a.m(e,null),W(e,r),l&&l.m(e,null),o=!0},p(c,u){c[0]?s?(s.p(c,u),u[0]&1&&I(s,1)):(s=Dd(c),s.c(),I(s,1),s.m(t.parentNode,t)):s&&(jt(),E(s,1,1,()=>{s=null}),Lt()),c[0]?a&&(jt(),E(a,1,1,()=>{a=null}),Lt()):a?(a.p(c,u),u[0]&1&&I(a,1)):(a=Md(c),a.c(),I(a,1),a.m(e,r)),c[4]?l||(l=Ud(),l.c(),l.m(e,null)):l&&(l.d(1),l=null),(!o||u[0]&64&&i!==(i="w-48 flex-none "+(c[6]==="hide"?"hidden":"hidden md:flex")))&&C(e,"class",i)},i(c){o||(I(s),I(a),o=!0)},o(c){E(s),E(a),o=!1},d(c){c&&(w(t),w(e)),s&&s.d(c),a&&a.d(),l&&l.d()}}}function Bg(n){Object.keys(n.children).forEach(function(t){var r;const e=n.children[t];Bg(e),(!e.label&&!e.href||e.children.length===0&&((r=e.frontMatter)==null?void 0:r.sidebar_link)===!1)&&delete n.children[t]})}function Pg(n){return n.children=Object.values(n.children).sort((t,e)=>{var r,i,o,s;return!isNaN((r=t.frontMatter)==null?void 0:r.sidebar_position)&&!isNaN((i=e.frontMatter)==null?void 0:i.sidebar_position)?t.frontMatter.sidebar_position-e.frontMatter.sidebar_position||t.label.localeCompare(e.label):isNaN((o=t.frontMatter)==null?void 0:o.sidebar_position)?isNaN((s=e.frontMatter)==null?void 0:s.sidebar_position)?t.label.localeCompare(e.label):1:-1}),n.children.forEach(Pg),n}function gS(n,t,e){let r;Te(n,Na,y=>e(8,r=y));let{fileTree:i=void 0}=t,{title:o=void 0}=t,{logo:s=void 0}=t,{homePageName:a=void 0}=t,{builtWithEvidence:l=void 0}=t,{hideHeader:c=!1}=t,{sidebarFrontMatter:u=void 0}=t,{sidebarDepth:f=3}=t;const[h,d]=p_({duration:200,easing:g_});i=structuredClone(i),Bg(i),i=Pg(i);let g=i==null?void 0:i.children,{mobileSidebarOpen:m=!1}=t;Gg(()=>{{let y=document.querySelector("#mobileScrollable");m?b_(y):__(y)}});const p=()=>e(0,m=!1),v=()=>e(0,m=!1),b=()=>{e(0,m=!1)},_=()=>e(0,m=!1),O=()=>e(0,m=!1);return n.$$set=y=>{"fileTree"in y&&e(12,i=y.fileTree),"title"in y&&e(1,o=y.title),"logo"in y&&e(2,s=y.logo),"homePageName"in y&&e(3,a=y.homePageName),"builtWithEvidence"in y&&e(4,l=y.builtWithEvidence),"hideHeader"in y&&e(5,c=y.hideHeader),"sidebarFrontMatter"in y&&e(6,u=y.sidebarFrontMatter),"sidebarDepth"in y&&e(7,f=y.sidebarDepth),"mobileSidebarOpen"in y&&e(0,m=y.mobileSidebarOpen)},[m,o,s,a,l,c,u,f,r,h,d,g,i,p,v,b,_,O]}class _S extends re{constructor(t){super(),ie(this,t,gS,pS,Zt,{fileTree:12,title:1,logo:2,homePageName:3,builtWithEvidence:4,hideHeader:5,sidebarFrontMatter:6,sidebarDepth:7,mobileSidebarOpen:0},null,[-1,-1])}}function Vd(n,t,e){const r=n.slice();return r[5]=t[e],r}function zd(n){let t,e="On this page",r,i,o=he(n[0]),s=[];for(let a=0;a<o.length;a+=1)s[a]=Wd(Vd(n,o,a));return{c(){t=Y("span"),t.textContent=e,r=rt();for(let a=0;a<s.length;a+=1)s[a].c();i=ut(),this.h()},l(a){t=q(a,"SPAN",{class:!0,"data-svelte-h":!0}),vr(t)!=="svelte-14mun4z"&&(t.textContent=e),r=nt(a);for(let l=0;l<s.length;l+=1)s[l].l(a);i=ut(),this.h()},h(){C(t,"class","block text-xs sticky top-0 mb-2 bg-base-100 shadow-base-100 font-medium")},m(a,l){L(a,t,l),L(a,r,l);for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(a,l);L(a,i,l)},p(a,l){if(l&3){o=he(a[0]);let c;for(c=0;c<o.length;c+=1){const u=Vd(a,o,c);s[c]?s[c].p(u,l):(s[c]=Wd(u),s[c].c(),s[c].m(i.parentNode,i))}for(;c<s.length;c+=1)s[c].d(1);s.length=o.length}},d(a){a&&(w(t),w(r),w(i)),An(s,a)}}}function Wd(n){let t,e=n[5].innerText+"",r,i,o,s;return{c(){t=Y("a"),r=wt(e),i=rt(),this.h()},l(a){t=q(a,"A",{href:!0,class:!0});var l=J(t);r=vt(l,e),i=nt(l),l.forEach(w),this.h()},h(){C(t,"href",o="#"+n[5].id),C(t,"class",s=n[1][n[5].nodeName.toLowerCase()]+" block text-xs transition-all duration-200 py-1 hover:underline")},m(a,l){L(a,t,l),W(t,r),W(t,i)},p(a,l){l&1&&e!==(e=a[5].innerText+"")&&Re(r,e),l&1&&o!==(o="#"+a[5].id)&&C(t,"href",o),l&1&&s!==(s=a[1][a[5].nodeName.toLowerCase()]+" block text-xs transition-all duration-200 py-1 hover:underline")&&C(t,"class",s)},d(a){a&&w(t)}}}function bS(n){let t,e=n[0]&&n[0].length>1&&zd(n);return{c(){e&&e.c(),t=ut()},l(r){e&&e.l(r),t=ut()},m(r,i){e&&e.m(r,i),L(r,t,i)},p(r,[i]){r[0]&&r[0].length>1?e?e.p(r,i):(e=zd(r),e.c(),e.m(t.parentNode,t)):e&&(e.d(1),e=null)},i:Xt,o:Xt,d(r){r&&w(t),e&&e.d(r)}}}function yS(n,t,e){let r=[],i;function o(){e(0,r=Array.from(document.querySelectorAll("h1.markdown, h2.markdown, h3.markdown")))}function s(){return i=new MutationObserver(()=>{o()}),r.forEach(l=>{i.observe(l,{subtree:!0,characterData:!0,childList:!0})}),i}return Cr(()=>{o(),i=s()}),Xg(()=>{i==null||i.disconnect()}),[r,{h1:"mt-3 font-semibold block bg-base-100 shadow shadow-base-100",h2:"pl-0 text-base-content-muted",h3:"pl-4 text-base-content-muted"}]}class vS extends re{constructor(t){super(),ie(this,t,yS,bS,Zt,{})}}function Hd(n){let t,e,r;return e=new vS({}),{c(){t=Y("div"),lt(e.$$.fragment),this.h()},l(i){t=q(i,"DIV",{class:!0});var o=J(t);at(e.$$.fragment,o),o.forEach(w),this.h()},h(){C(t,"class","fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar"),Fr(t,"top-8",n[0])},m(i,o){L(i,t,o),st(e,t,null),r=!0},p(i,o){(!r||o&1)&&Fr(t,"top-8",i[0])},i(i){r||(I(e.$$.fragment,i),r=!0)},o(i){E(e.$$.fragment,i),r=!1},d(i){i&&w(t),ot(e)}}}function wS(n){let t,e,r=!n[1]&&n[2].data.isUserPage&&Hd(n);return{c(){t=Y("aside"),r&&r.c(),this.h()},l(i){t=q(i,"ASIDE",{class:!0});var o=J(t);r&&r.l(o),o.forEach(w),this.h()},h(){C(t,"class","hidden lg:block w-48")},m(i,o){L(i,t,o),r&&r.m(t,null),e=!0},p(i,[o]){!i[1]&&i[2].data.isUserPage?r?(r.p(i,o),o&6&&I(r,1)):(r=Hd(i),r.c(),I(r,1),r.m(t,null)):r&&(jt(),E(r,1,1,()=>{r=null}),Lt())},i(i){e||(I(r),e=!0)},o(i){E(r),e=!1},d(i){i&&w(t),r&&r.d()}}}function SS(n,t,e){let r,i;Te(n,Th,s=>e(1,r=s)),Te(n,Na,s=>e(2,i=s));let{hideHeader:o=!1}=t;return n.$$set=s=>{"hideHeader"in s&&e(0,o=s.hideHeader)},[o,r,i]}class IS extends re{constructor(t){super(),ie(this,t,SS,wS,Zt,{hideHeader:0})}}function xd(n,t,e){const r=n.slice();return r[3]=t[e],r[5]=e,r}function OS(n){let t,e=n[3].title+"",r,i,o;return{c(){t=Y("a"),r=wt(e),i=rt(),this.h()},l(s){t=q(s,"A",{href:!0,class:!0});var a=J(t);r=vt(a,e),i=nt(a),a.forEach(w),this.h()},h(){C(t,"href",o=Wt(n[3].href)),C(t,"class","hover:underline")},m(s,a){L(s,t,a),W(t,r),W(t,i)},p(s,a){a&1&&e!==(e=s[3].title+"")&&Re(r,e),a&1&&o!==(o=Wt(s[3].href))&&C(t,"href",o)},i:Xt,o:Xt,d(s){s&&w(t)}}}function kS(n){let t,e,r,i;t=new Pn({props:{src:y_,size:"12px",theme:"solid"}});function o(l,c){return l[3].href?ES:DS}let s=o(n),a=s(n);return{c(){lt(t.$$.fragment),e=rt(),a.c(),r=ut()},l(l){at(t.$$.fragment,l),e=nt(l),a.l(l),r=ut()},m(l,c){st(t,l,c),L(l,e,c),a.m(l,c),L(l,r,c),i=!0},p(l,c){s===(s=o(l))&&a?a.p(l,c):(a.d(1),a=s(l),a&&(a.c(),a.m(r.parentNode,r)))},i(l){i||(I(t.$$.fragment,l),i=!0)},o(l){E(t.$$.fragment,l),i=!1},d(l){l&&(w(e),w(r)),ot(t,l),a.d(l)}}}function DS(n){let t,e=n[3].title+"",r;return{c(){t=Y("span"),r=wt(e),this.h()},l(i){t=q(i,"SPAN",{class:!0});var o=J(t);r=vt(o,e),o.forEach(w),this.h()},h(){C(t,"class","cursor-default")},m(i,o){L(i,t,o),W(t,r)},p(i,o){o&1&&e!==(e=i[3].title+"")&&Re(r,e)},d(i){i&&w(t)}}}function ES(n){let t,e=n[3].title+"",r,i;return{c(){t=Y("a"),r=wt(e),this.h()},l(o){t=q(o,"A",{href:!0,class:!0});var s=J(t);r=vt(s,e),s.forEach(w),this.h()},h(){C(t,"href",i=Wt(n[3].href)),C(t,"class","hover:underline")},m(o,s){L(o,t,s),W(t,r)},p(o,s){s&1&&e!==(e=o[3].title+"")&&Re(r,e),s&1&&i!==(i=Wt(o[3].href))&&C(t,"href",i)},d(o){o&&w(t)}}}function qd(n){let t,e,r,i;const o=[kS,OS],s=[];function a(l,c){return l[5]>0?0:1}return t=a(n),e=s[t]=o[t](n),{c(){e.c(),r=ut()},l(l){e.l(l),r=ut()},m(l,c){s[t].m(l,c),L(l,r,c),i=!0},p(l,c){e.p(l,c)},i(l){i||(I(e),i=!0)},o(l){E(e),i=!1},d(l){l&&w(r),s[t].d(l)}}}function AS(n){let t,e,r,i=he(n[0]),o=[];for(let a=0;a<i.length;a+=1)o[a]=qd(xd(n,i,a));const s=a=>E(o[a],1,1,()=>{o[a]=null});return{c(){t=Y("div"),e=Y("div");for(let a=0;a<o.length;a+=1)o[a].c();this.h()},l(a){t=q(a,"DIV",{class:!0});var l=J(t);e=q(l,"DIV",{class:!0});var c=J(e);for(let u=0;u<o.length;u+=1)o[u].l(c);c.forEach(w),l.forEach(w),this.h()},h(){C(e,"class","inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4"),C(t,"class","flex items-start mt-0 whitespace-nowrap overflow-auto")},m(a,l){L(a,t,l),W(t,e);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(e,null);r=!0},p(a,[l]){if(l&1){i=he(a[0]);let c;for(c=0;c<i.length;c+=1){const u=xd(a,i,c);o[c]?(o[c].p(u,l),I(o[c],1)):(o[c]=qd(u),o[c].c(),I(o[c],1),o[c].m(e,null))}for(jt(),c=i.length;c<o.length;c+=1)s(c);Lt()}},i(a){if(!r){for(let l=0;l<i.length;l+=1)I(o[l]);r=!0}},o(a){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)E(o[l]);r=!1},d(a){a&&w(t),An(o,a)}}}function TS(n,t){if(n==="/")return t;const e=n.replace(wh.deployment.basePath,"").split("/").slice(1);let r=t;for(let i of e)if(r.children[i]?r=r.children[i]:r=Object.values(r.children).find(o=>o.isTemplated),!r)return null;return r}function BS(n,t){const e=[{href:"/",title:"Home"}];n.forEach((r,i)=>{r!=""&&`/${r}`!==wh.deployment.basePath&&e.push({href:"/"+n.slice(0,i+1).join("/"),title:decodeURIComponent(r.replace(/_/g," ").replace(/-/g," "))})}),e.length>3&&e.splice(1,e.length-3,{href:e.slice(-3)[0].href,title:"..."});for(const r of e)if(r.href==="/")r.href=Wt("/"),r.title="Home";else{const i=TS(r.href,t);!i||!i.isPage?r.href=null:r.title=i.title??r.title}return e}function PS(n,t,e){let r,i;Te(n,Na,s=>e(2,i=s));let{fileTree:o}=t;return n.$$set=s=>{"fileTree"in s&&e(1,o=s.fileTree)},n.$$.update=()=>{n.$$.dirty&6&&e(0,r=BS(i.url.pathname.split("/").slice(1),o))},[r,o,i]}class MS extends re{constructor(t){super(),ie(this,t,PS,AS,Zt,{fileTree:1})}}function Yd(n){let t,e,r,i="Error",o,s,a,l,c,u='<a href="https://docs.evidence.dev" target="”_blank”" class="hover:text-base-content-muted transition-colors duration-200">docs</a> <a href="https://evidencedev.slack.com/join/shared_invite/zt-uda6wp6a-hP6Qyz0LUOddwpXW5qG03Q#/shared-invite/email" target="”_blank”" class="hover:text-base-content-muted transition-colors duration-200">slack</a> <a href="mailto:<EMAIL>" class="hover:text-base-content-muted transition-colors duration-200">email</a>',f,h,d;return{c(){t=Y("div"),e=Y("div"),r=Y("h1"),r.textContent=i,o=rt(),s=Y("p"),a=wt(n[1]),l=rt(),c=Y("div"),c.innerHTML=u,this.h()},l(g){t=q(g,"DIV",{class:!0});var m=J(t);e=q(m,"DIV",{class:!0});var p=J(e);r=q(p,"H1",{class:!0,"data-svelte-h":!0}),vr(r)!=="svelte-1wczc15"&&(r.textContent=i),o=nt(p),s=q(p,"P",{class:!0});var v=J(s);a=vt(v,n[1]),v.forEach(w),l=nt(p),c=q(p,"DIV",{class:!0,"data-svelte-h":!0}),vr(c)!=="svelte-vfh8n7"&&(c.innerHTML=u),p.forEach(w),m.forEach(w),this.h()},h(){C(r,"class","text-2xl font-bold tracking-wide border-b pb-4 border-base-300"),C(s,"class","text-xl mt-6 leading-relaxed select-text"),C(c,"class","absolute bottom-0 flex items-end gap-4 text-lg mb-6"),C(e,"class","relative min-w-full h-screen bg-gradient-to-b from-base-200 to-base-300 rounded-lg border-t-8 border-negative shadow-xl p-8"),C(t,"class","fixed flex flex-col z-50 h-screen w-screen bg-base-100/50 justify-center items-center py-20 px-10 sm:px-20 select-none backdrop-blur-sm")},m(g,m){L(g,t,m),W(t,e),W(e,r),W(e,o),W(e,s),W(s,a),W(e,l),W(e,c),d=!0},p(g,m){(!d||m&2)&&Re(a,g[1])},i(g){d||(g&&En(()=>{d&&(f||(f=Dn(e,ii,{y:100,duration:300},!0)),f.run(1))}),g&&En(()=>{d&&(h||(h=Dn(t,xo,{duration:100},!0)),h.run(1))}),d=!0)},o(g){g&&(f||(f=Dn(e,ii,{y:100,duration:300},!1)),f.run(0)),g&&(h||(h=Dn(t,xo,{duration:100},!1)),h.run(0)),d=!1},d(g){g&&w(t),g&&f&&f.end(),g&&h&&h.end()}}}function NS(n){let t,e=n[0]&&Yd(n);return{c(){e&&e.c(),t=ut()},l(r){e&&e.l(r),t=ut()},m(r,i){e&&e.m(r,i),L(r,t,i)},p(r,[i]){r[0]?e?(e.p(r,i),i&1&&I(e,1)):(e=Yd(r),e.c(),I(e,1),e.m(t.parentNode,t)):e&&(jt(),E(e,1,1,()=>{e=null}),Lt())},i(r){I(e)},o(r){E(e)},d(r){r&&w(t),e&&e.d(r)}}}function CS(n,t,e){return[!1,void 0]}class FS extends re{constructor(t){super(),ie(this,t,CS,NS,Zt,{})}}function jS(n){let t,e=JSON.stringify(n[0],null,2)+"",r;return{c(){t=Y("pre"),r=wt(e),this.h()},l(i){t=q(i,"PRE",{class:!0});var o=J(t);r=vt(o,e),o.forEach(w),this.h()},h(){C(t,"class","text-xs px-2 py-2 bg-base-200 my-2")},m(i,o){L(i,t,o),W(t,r)},p(i,[o]){o&1&&e!==(e=JSON.stringify(i[0],null,2)+"")&&Re(r,e)},i:Xt,o:Xt,d(i){i&&w(t)}}}function LS(n,t,e){let r;const i=Sh();return Te(n,i,o=>e(0,r=o)),[r,i]}class RS extends re{constructor(t){super(),ie(this,t,LS,jS,Zt,{})}}function US(n){const t=[{type:"unchanged",content:"{"}],e=v_(n.before,n.after);function r(o,s){return s.reduce((a,l)=>a==null?void 0:a[l],o)}function i(o,s){const a=Object.keys(o);a.forEach((l,c)=>{const u=r(n.added,s)??{},f=r(n.deleted,s)??{},h=r(n.updated,s)??{};let d="unchanged";l in u&&(d="added"),l in f&&(d="deleted"),l in h&&(d="updated");const g=(m,p=!1)=>{const v=`"${l}": `;let b=`${"  ".repeat(s.length+1)}${p?"":v}${m}`;return c<a.length-1&&(b+=","),b};if(typeof o[l]=="object"){t.push({type:d==="updated"?"unchanged":d,content:g("{")}),i(o[l],s.concat(l)),t.push({type:d==="updated"?"unchanged":d,content:g("}",!0)});return}else{const m=d==="deleted"?r(n.before,s)[l]:o[l];t.push({type:d,content:g(JSON.stringify(m))});return}})}return i(e,[]),t.push({type:"unchanged",content:"}"}),t}const VS={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},zS=(n,t,e)=>{let r;const i=VS[n];return typeof i=="string"?r=i:t===1?r=i.one:r=i.other.replace("{{count}}",t.toString()),e!=null&&e.addSuffix?e.comparison&&e.comparison>0?"in "+r:r+" ago":r};function vl(n){return(t={})=>{const e=t.width?String(t.width):n.defaultWidth;return n.formats[e]||n.formats[n.defaultWidth]}}const WS={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},HS={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},xS={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},qS={date:vl({formats:WS,defaultWidth:"full"}),time:vl({formats:HS,defaultWidth:"full"}),dateTime:vl({formats:xS,defaultWidth:"full"})},YS={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},KS=(n,t,e,r)=>YS[n];function uo(n){return(t,e)=>{const r=e!=null&&e.context?String(e.context):"standalone";let i;if(r==="formatting"&&n.formattingValues){const s=n.defaultFormattingWidth||n.defaultWidth,a=e!=null&&e.width?String(e.width):s;i=n.formattingValues[a]||n.formattingValues[s]}else{const s=n.defaultWidth,a=e!=null&&e.width?String(e.width):n.defaultWidth;i=n.values[a]||n.values[s]}const o=n.argumentCallback?n.argumentCallback(t):t;return i[o]}}const JS={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},QS={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},GS={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},XS={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},ZS={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},$S={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},tI=(n,t)=>{const e=Number(n),r=e%100;if(r>20||r<10)switch(r%10){case 1:return e+"st";case 2:return e+"nd";case 3:return e+"rd"}return e+"th"},eI={ordinalNumber:tI,era:uo({values:JS,defaultWidth:"wide"}),quarter:uo({values:QS,defaultWidth:"wide",argumentCallback:n=>n-1}),month:uo({values:GS,defaultWidth:"wide"}),day:uo({values:XS,defaultWidth:"wide"}),dayPeriod:uo({values:ZS,defaultWidth:"wide",formattingValues:$S,defaultFormattingWidth:"wide"})};function fo(n){return(t,e={})=>{const r=e.width,i=r&&n.matchPatterns[r]||n.matchPatterns[n.defaultMatchWidth],o=t.match(i);if(!o)return null;const s=o[0],a=r&&n.parsePatterns[r]||n.parsePatterns[n.defaultParseWidth],l=Array.isArray(a)?rI(a,f=>f.test(s)):nI(a,f=>f.test(s));let c;c=n.valueCallback?n.valueCallback(l):l,c=e.valueCallback?e.valueCallback(c):c;const u=t.slice(s.length);return{value:c,rest:u}}}function nI(n,t){for(const e in n)if(Object.prototype.hasOwnProperty.call(n,e)&&t(n[e]))return e}function rI(n,t){for(let e=0;e<n.length;e++)if(t(n[e]))return e}function iI(n){return(t,e={})=>{const r=t.match(n.matchPattern);if(!r)return null;const i=r[0],o=t.match(n.parsePattern);if(!o)return null;let s=n.valueCallback?n.valueCallback(o[0]):o[0];s=e.valueCallback?e.valueCallback(s):s;const a=t.slice(i.length);return{value:s,rest:a}}}const oI=/^(\d+)(th|st|nd|rd)?/i,sI=/\d+/i,aI={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},lI={any:[/^b/i,/^(a|c)/i]},cI={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},uI={any:[/1/i,/2/i,/3/i,/4/i]},fI={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},dI={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},hI={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},mI={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},pI={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},gI={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},_I={ordinalNumber:iI({matchPattern:oI,parsePattern:sI,valueCallback:n=>parseInt(n,10)}),era:fo({matchPatterns:aI,defaultMatchWidth:"wide",parsePatterns:lI,defaultParseWidth:"any"}),quarter:fo({matchPatterns:cI,defaultMatchWidth:"wide",parsePatterns:uI,defaultParseWidth:"any",valueCallback:n=>n+1}),month:fo({matchPatterns:fI,defaultMatchWidth:"wide",parsePatterns:dI,defaultParseWidth:"any"}),day:fo({matchPatterns:hI,defaultMatchWidth:"wide",parsePatterns:mI,defaultParseWidth:"any"}),dayPeriod:fo({matchPatterns:pI,defaultMatchWidth:"any",parsePatterns:gI,defaultParseWidth:"any"})},Mg={code:"en-US",formatDistance:zS,formatLong:qS,formatRelative:KS,localize:eI,match:_I,options:{weekStartsOn:0,firstWeekContainsDate:1}};let bI={};function ms(){return bI}function yI(n){return t=>{const r=(n?Math[n]:Math.trunc)(t);return r===0?0:r}}function xe(n){const t=Object.prototype.toString.call(n);return n instanceof Date||typeof n=="object"&&t==="[object Date]"?new n.constructor(+n):typeof n=="number"||t==="[object Number]"||typeof n=="string"||t==="[object String]"?new Date(n):new Date(NaN)}function Ta(n){const t=xe(n),e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),+n-+e}function vI(n,t){const e=xe(n),r=xe(t),i=e.getTime()-r.getTime();return i<0?-1:i>0?1:i}const Ng=6048e5,wI=864e5,Kd=6e4,Jd=525600,Qd=43200,Gd=1440;function SI(n,t,e){const r=ms(),i=(e==null?void 0:e.locale)??r.locale??Mg,o=vI(n,t);if(isNaN(o))throw new RangeError("Invalid time value");const s=Object.assign({},e,{addSuffix:e==null?void 0:e.addSuffix,comparison:o});let a,l;o>0?(a=xe(t),l=xe(n)):(a=xe(n),l=xe(t));const c=yI((e==null?void 0:e.roundingMethod)??"round"),u=l.getTime()-a.getTime(),f=u/Kd,h=Ta(l)-Ta(a),d=(u-h)/Kd,g=e==null?void 0:e.unit;let m;if(g?m=g:f<1?m="second":f<60?m="minute":f<Gd?m="hour":d<Qd?m="day":d<Jd?m="month":m="year",m==="second"){const p=c(u/1e3);return i.formatDistance("xSeconds",p,s)}else if(m==="minute"){const p=c(f);return i.formatDistance("xMinutes",p,s)}else if(m==="hour"){const p=c(f/60);return i.formatDistance("xHours",p,s)}else if(m==="day"){const p=c(d/Gd);return i.formatDistance("xDays",p,s)}else if(m==="month"){const p=c(d/Qd);return p===12&&g!=="month"?i.formatDistance("xYears",1,s):i.formatDistance("xMonths",p,s)}else{const p=c(d/Jd);return i.formatDistance("xYears",p,s)}}function Vr(n,t){return n instanceof Date?new n.constructor(t):new Date(t)}function II(n){return Vr(n,Date.now())}function Xd(n,t){return SI(n,II(n),t)}function Zd(n){const t=xe(n);return t.setHours(0,0,0,0),t}function OI(n,t){const e=Zd(n),r=Zd(t),i=+e-Ta(e),o=+r-Ta(r);return Math.round((i-o)/wI)}function kI(n){const t=xe(n),e=Vr(n,0);return e.setFullYear(t.getFullYear(),0,1),e.setHours(0,0,0,0),e}function DI(n){const t=xe(n);return OI(t,kI(t))+1}function es(n,t){var a,l,c,u;const e=ms(),r=(t==null?void 0:t.weekStartsOn)??((l=(a=t==null?void 0:t.locale)==null?void 0:a.options)==null?void 0:l.weekStartsOn)??e.weekStartsOn??((u=(c=e.locale)==null?void 0:c.options)==null?void 0:u.weekStartsOn)??0,i=xe(n),o=i.getDay(),s=(o<r?7:0)+o-r;return i.setDate(i.getDate()-s),i.setHours(0,0,0,0),i}function Ba(n){return es(n,{weekStartsOn:1})}function Cg(n){const t=xe(n),e=t.getFullYear(),r=Vr(n,0);r.setFullYear(e+1,0,4),r.setHours(0,0,0,0);const i=Ba(r),o=Vr(n,0);o.setFullYear(e,0,4),o.setHours(0,0,0,0);const s=Ba(o);return t.getTime()>=i.getTime()?e+1:t.getTime()>=s.getTime()?e:e-1}function EI(n){const t=Cg(n),e=Vr(n,0);return e.setFullYear(t,0,4),e.setHours(0,0,0,0),Ba(e)}function AI(n){const t=xe(n),e=+Ba(t)-+EI(t);return Math.round(e/Ng)+1}function Fg(n,t){var u,f,h,d;const e=xe(n),r=e.getFullYear(),i=ms(),o=(t==null?void 0:t.firstWeekContainsDate)??((f=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:f.firstWeekContainsDate)??i.firstWeekContainsDate??((d=(h=i.locale)==null?void 0:h.options)==null?void 0:d.firstWeekContainsDate)??1,s=Vr(n,0);s.setFullYear(r+1,0,o),s.setHours(0,0,0,0);const a=es(s,t),l=Vr(n,0);l.setFullYear(r,0,o),l.setHours(0,0,0,0);const c=es(l,t);return e.getTime()>=a.getTime()?r+1:e.getTime()>=c.getTime()?r:r-1}function TI(n,t){var a,l,c,u;const e=ms(),r=(t==null?void 0:t.firstWeekContainsDate)??((l=(a=t==null?void 0:t.locale)==null?void 0:a.options)==null?void 0:l.firstWeekContainsDate)??e.firstWeekContainsDate??((u=(c=e.locale)==null?void 0:c.options)==null?void 0:u.firstWeekContainsDate)??1,i=Fg(n,t),o=Vr(n,0);return o.setFullYear(i,0,r),o.setHours(0,0,0,0),es(o,t)}function BI(n,t){const e=xe(n),r=+es(e,t)-+TI(e,t);return Math.round(r/Ng)+1}function ue(n,t){const e=n<0?"-":"",r=Math.abs(n).toString().padStart(t,"0");return e+r}const Br={y(n,t){const e=n.getFullYear(),r=e>0?e:1-e;return ue(t==="yy"?r%100:r,t.length)},M(n,t){const e=n.getMonth();return t==="M"?String(e+1):ue(e+1,2)},d(n,t){return ue(n.getDate(),t.length)},a(n,t){const e=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.toUpperCase();case"aaa":return e;case"aaaaa":return e[0];case"aaaa":default:return e==="am"?"a.m.":"p.m."}},h(n,t){return ue(n.getHours()%12||12,t.length)},H(n,t){return ue(n.getHours(),t.length)},m(n,t){return ue(n.getMinutes(),t.length)},s(n,t){return ue(n.getSeconds(),t.length)},S(n,t){const e=t.length,r=n.getMilliseconds(),i=Math.trunc(r*Math.pow(10,e-3));return ue(i,t.length)}},pi={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},$d={G:function(n,t,e){const r=n.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return e.era(r,{width:"abbreviated"});case"GGGGG":return e.era(r,{width:"narrow"});case"GGGG":default:return e.era(r,{width:"wide"})}},y:function(n,t,e){if(t==="yo"){const r=n.getFullYear(),i=r>0?r:1-r;return e.ordinalNumber(i,{unit:"year"})}return Br.y(n,t)},Y:function(n,t,e,r){const i=Fg(n,r),o=i>0?i:1-i;if(t==="YY"){const s=o%100;return ue(s,2)}return t==="Yo"?e.ordinalNumber(o,{unit:"year"}):ue(o,t.length)},R:function(n,t){const e=Cg(n);return ue(e,t.length)},u:function(n,t){const e=n.getFullYear();return ue(e,t.length)},Q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return ue(r,2);case"Qo":return e.ordinalNumber(r,{unit:"quarter"});case"QQQ":return e.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return e.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return e.quarter(r,{width:"wide",context:"formatting"})}},q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return ue(r,2);case"qo":return e.ordinalNumber(r,{unit:"quarter"});case"qqq":return e.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return e.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return e.quarter(r,{width:"wide",context:"standalone"})}},M:function(n,t,e){const r=n.getMonth();switch(t){case"M":case"MM":return Br.M(n,t);case"Mo":return e.ordinalNumber(r+1,{unit:"month"});case"MMM":return e.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return e.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return e.month(r,{width:"wide",context:"formatting"})}},L:function(n,t,e){const r=n.getMonth();switch(t){case"L":return String(r+1);case"LL":return ue(r+1,2);case"Lo":return e.ordinalNumber(r+1,{unit:"month"});case"LLL":return e.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return e.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return e.month(r,{width:"wide",context:"standalone"})}},w:function(n,t,e,r){const i=BI(n,r);return t==="wo"?e.ordinalNumber(i,{unit:"week"}):ue(i,t.length)},I:function(n,t,e){const r=AI(n);return t==="Io"?e.ordinalNumber(r,{unit:"week"}):ue(r,t.length)},d:function(n,t,e){return t==="do"?e.ordinalNumber(n.getDate(),{unit:"date"}):Br.d(n,t)},D:function(n,t,e){const r=DI(n);return t==="Do"?e.ordinalNumber(r,{unit:"dayOfYear"}):ue(r,t.length)},E:function(n,t,e){const r=n.getDay();switch(t){case"E":case"EE":case"EEE":return e.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return e.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return e.day(r,{width:"short",context:"formatting"});case"EEEE":default:return e.day(r,{width:"wide",context:"formatting"})}},e:function(n,t,e,r){const i=n.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return ue(o,2);case"eo":return e.ordinalNumber(o,{unit:"day"});case"eee":return e.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return e.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return e.day(i,{width:"short",context:"formatting"});case"eeee":default:return e.day(i,{width:"wide",context:"formatting"})}},c:function(n,t,e,r){const i=n.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return ue(o,t.length);case"co":return e.ordinalNumber(o,{unit:"day"});case"ccc":return e.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return e.day(i,{width:"narrow",context:"standalone"});case"cccccc":return e.day(i,{width:"short",context:"standalone"});case"cccc":default:return e.day(i,{width:"wide",context:"standalone"})}},i:function(n,t,e){const r=n.getDay(),i=r===0?7:r;switch(t){case"i":return String(i);case"ii":return ue(i,t.length);case"io":return e.ordinalNumber(i,{unit:"day"});case"iii":return e.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return e.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return e.day(r,{width:"short",context:"formatting"});case"iiii":default:return e.day(r,{width:"wide",context:"formatting"})}},a:function(n,t,e){const i=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return e.dayPeriod(i,{width:"narrow",context:"formatting"});case"aaaa":default:return e.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(n,t,e){const r=n.getHours();let i;switch(r===12?i=pi.noon:r===0?i=pi.midnight:i=r/12>=1?"pm":"am",t){case"b":case"bb":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return e.dayPeriod(i,{width:"narrow",context:"formatting"});case"bbbb":default:return e.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(n,t,e){const r=n.getHours();let i;switch(r>=17?i=pi.evening:r>=12?i=pi.afternoon:r>=4?i=pi.morning:i=pi.night,t){case"B":case"BB":case"BBB":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return e.dayPeriod(i,{width:"narrow",context:"formatting"});case"BBBB":default:return e.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(n,t,e){if(t==="ho"){let r=n.getHours()%12;return r===0&&(r=12),e.ordinalNumber(r,{unit:"hour"})}return Br.h(n,t)},H:function(n,t,e){return t==="Ho"?e.ordinalNumber(n.getHours(),{unit:"hour"}):Br.H(n,t)},K:function(n,t,e){const r=n.getHours()%12;return t==="Ko"?e.ordinalNumber(r,{unit:"hour"}):ue(r,t.length)},k:function(n,t,e){let r=n.getHours();return r===0&&(r=24),t==="ko"?e.ordinalNumber(r,{unit:"hour"}):ue(r,t.length)},m:function(n,t,e){return t==="mo"?e.ordinalNumber(n.getMinutes(),{unit:"minute"}):Br.m(n,t)},s:function(n,t,e){return t==="so"?e.ordinalNumber(n.getSeconds(),{unit:"second"}):Br.s(n,t)},S:function(n,t){return Br.S(n,t)},X:function(n,t,e){const r=n.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return eh(r);case"XXXX":case"XX":return $r(r);case"XXXXX":case"XXX":default:return $r(r,":")}},x:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"x":return eh(r);case"xxxx":case"xx":return $r(r);case"xxxxx":case"xxx":default:return $r(r,":")}},O:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+th(r,":");case"OOOO":default:return"GMT"+$r(r,":")}},z:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+th(r,":");case"zzzz":default:return"GMT"+$r(r,":")}},t:function(n,t,e){const r=Math.trunc(n.getTime()/1e3);return ue(r,t.length)},T:function(n,t,e){const r=n.getTime();return ue(r,t.length)}};function th(n,t=""){const e=n>0?"-":"+",r=Math.abs(n),i=Math.trunc(r/60),o=r%60;return o===0?e+String(i):e+String(i)+t+ue(o,2)}function eh(n,t){return n%60===0?(n>0?"-":"+")+ue(Math.abs(n)/60,2):$r(n,t)}function $r(n,t=""){const e=n>0?"-":"+",r=Math.abs(n),i=ue(Math.trunc(r/60),2),o=ue(r%60,2);return e+i+t+o}const nh=(n,t)=>{switch(n){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},jg=(n,t)=>{switch(n){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},PI=(n,t)=>{const e=n.match(/(P+)(p+)?/)||[],r=e[1],i=e[2];if(!i)return nh(n,t);let o;switch(r){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;case"PPPP":default:o=t.dateTime({width:"full"});break}return o.replace("{{date}}",nh(r,t)).replace("{{time}}",jg(i,t))},MI={p:jg,P:PI},NI=/^D+$/,CI=/^Y+$/,FI=["D","DD","YY","YYYY"];function jI(n){return NI.test(n)}function LI(n){return CI.test(n)}function RI(n,t,e){const r=UI(n,t,e);if(console.warn(r),FI.includes(n))throw new RangeError(r)}function UI(n,t,e){const r=n[0]==="Y"?"years":"days of the month";return`Use \`${n.toLowerCase()}\` instead of \`${n}\` (in \`${t}\`) for formatting ${r} to the input \`${e}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}function VI(n){return n instanceof Date||typeof n=="object"&&Object.prototype.toString.call(n)==="[object Date]"}function zI(n){if(!VI(n)&&typeof n!="number")return!1;const t=xe(n);return!isNaN(Number(t))}const WI=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,HI=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,xI=/^'([^]*?)'?$/,qI=/''/g,YI=/[a-zA-Z]/;function rh(n,t,e){var u,f,h,d;const r=ms(),i=r.locale??Mg,o=r.firstWeekContainsDate??((f=(u=r.locale)==null?void 0:u.options)==null?void 0:f.firstWeekContainsDate)??1,s=r.weekStartsOn??((d=(h=r.locale)==null?void 0:h.options)==null?void 0:d.weekStartsOn)??0,a=xe(n);if(!zI(a))throw new RangeError("Invalid time value");let l=t.match(HI).map(g=>{const m=g[0];if(m==="p"||m==="P"){const p=MI[m];return p(g,i.formatLong)}return g}).join("").match(WI).map(g=>{if(g==="''")return{isToken:!1,value:"'"};const m=g[0];if(m==="'")return{isToken:!1,value:KI(g)};if($d[m])return{isToken:!0,value:g};if(m.match(YI))throw new RangeError("Format string contains an unescaped latin alphabet character `"+m+"`");return{isToken:!1,value:g}});i.localize.preprocessor&&(l=i.localize.preprocessor(a,l));const c={firstWeekContainsDate:o,weekStartsOn:s,locale:i};return l.map(g=>{if(!g.isToken)return g.value;const m=g.value;(LI(m)||jI(m))&&RI(m,t,String(n));const p=$d[m[0]];return p(a,m,i.localize,c)}).join("")}function KI(n){const t=n.match(xI);return t?t[1].replace(qI,"'"):n}function ih(n,t,e){const r=n.slice();return r[6]=t[e],r}function oh(n){let t,e,r=n[4](n[6].type)+"",i,o,s,a,l,c=n[6].content+"",u,f,h;return{c(){t=Y("div"),e=Y("span"),i=wt(r),s=rt(),a=Y("div"),l=Y("pre"),u=wt(c),h=rt(),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=J(t);e=q(g,"SPAN",{class:!0});var m=J(e);i=vt(m,r),m.forEach(w),s=nt(g),a=q(g,"DIV",{class:!0});var p=J(a);l=q(p,"PRE",{class:!0});var v=J(l);u=vt(v,c),v.forEach(w),p.forEach(w),h=nt(g),g.forEach(w),this.h()},h(){C(e,"class",o=n[3][n[6].type]+" px-1 select-none"),C(l,"class","whitespace-pre-wrap"),C(a,"class",f=n[3][n[6].type]+" selection:bg-black/15"),C(t,"class","group contents")},m(d,g){L(d,t,g),W(t,e),W(e,i),W(t,s),W(t,a),W(a,l),W(l,u),W(t,h)},p(d,g){g&2&&r!==(r=d[4](d[6].type)+"")&&Re(i,r),g&2&&o!==(o=d[3][d[6].type]+" px-1 select-none")&&C(e,"class",o),g&2&&c!==(c=d[6].content+"")&&Re(u,c),g&2&&f!==(f=d[3][d[6].type]+" selection:bg-black/15")&&C(a,"class",f)},d(d){d&&w(t)}}}function JI(n){let t,e,r,i,o,s=rh(n[0].asof,"HH:mm:ss")+"",a,l,c,u,f=he(n[1]),h=[];for(let d=0;d<f.length;d+=1)h[d]=oh(ih(n,f,d));return{c(){t=Y("section"),e=Y("span"),r=wt("About "),i=wt(n[2]),o=wt(" ("),a=wt(s),l=wt(")"),c=rt(),u=Y("div");for(let d=0;d<h.length;d+=1)h[d].c();this.h()},l(d){t=q(d,"SECTION",{});var g=J(t);e=q(g,"SPAN",{});var m=J(e);r=vt(m,"About "),i=vt(m,n[2]),o=vt(m," ("),a=vt(m,s),l=vt(m,")"),m.forEach(w),c=nt(g),u=q(g,"DIV",{class:!0});var p=J(u);for(let v=0;v<h.length;v+=1)h[v].l(p);p.forEach(w),g.forEach(w),this.h()},h(){C(u,"class","font-mono text-xs grid grid-cols-[auto,1fr] text-[0.7rem] bg-base-200 p-2 select-text")},m(d,g){L(d,t,g),W(t,e),W(e,r),W(e,i),W(e,o),W(e,a),W(e,l),W(t,c),W(t,u);for(let m=0;m<h.length;m+=1)h[m]&&h[m].m(u,null)},p(d,[g]){if(g&4&&Re(i,d[2]),g&1&&s!==(s=rh(d[0].asof,"HH:mm:ss")+"")&&Re(a,s),g&26){f=he(d[1]);let m;for(m=0;m<f.length;m+=1){const p=ih(d,f,m);h[m]?h[m].p(p,g):(h[m]=oh(p),h[m].c(),h[m].m(u,null))}for(;m<h.length;m+=1)h[m].d(1);h.length=f.length}},i:Xt,o:Xt,d(d){d&&w(t),An(h,d)}}}function QI(n,t,e){let r,i,{diffData:o={added:{},deleted:{},updated:{},before:{},after:{},asof:new Date}}=t;const s={added:"bg-positive/25",deleted:"bg-negative/25",updated:"bg-warning/25",unchanged:""},a=c=>{switch(c){case"added":return"+";case"deleted":return"-";case"updated":return"~";case"unchanged":return" ";default:return"?"}},l=O_(null,c=>{c(Xd(o.asof,{addSuffix:!0,includeSeconds:!0}));const u=setInterval(()=>{c(Xd(o.asof,{addSuffix:!0,includeSeconds:!0}))},5e3);return()=>{clearInterval(u)}});return Te(n,l,c=>e(2,i=c)),n.$$set=c=>{"diffData"in c&&e(0,o=c.diffData)},n.$$.update=()=>{n.$$.dirty&1&&e(1,r=US(o))},[o,r,i,s,a,l]}class GI extends re{constructor(t){super(),ie(this,t,QI,JI,Zt,{diffData:0})}}function sh(n,t,e){const r=n.slice();return r[2]=t[e],r}function ah(n){let t,e,r,i;return e=new GI({props:{diffData:n[2]}}),{c(){t=Y("div"),lt(e.$$.fragment),r=rt(),this.h()},l(o){t=q(o,"DIV",{class:!0});var s=J(t);at(e.$$.fragment,s),r=nt(s),s.forEach(w),this.h()},h(){C(t,"class","my-4")},m(o,s){L(o,t,s),st(e,t,null),W(t,r),i=!0},p(o,s){const a={};s&2&&(a.diffData=o[2]),e.$set(a)},i(o){i||(I(e.$$.fragment,o),i=!0)},o(o){E(e.$$.fragment,o),i=!1},d(o){o&&w(t),ot(e)}}}function XI(n){let t,e,r=he(n[1].reverse()),i=[];for(let s=0;s<r.length;s+=1)i[s]=ah(sh(n,r,s));const o=s=>E(i[s],1,1,()=>{i[s]=null});return{c(){for(let s=0;s<i.length;s+=1)i[s].c();t=ut()},l(s){for(let a=0;a<i.length;a+=1)i[a].l(s);t=ut()},m(s,a){for(let l=0;l<i.length;l+=1)i[l]&&i[l].m(s,a);L(s,t,a),e=!0},p(s,[a]){if(a&2){r=he(s[1].reverse());let l;for(l=0;l<r.length;l+=1){const c=sh(s,r,l);i[l]?(i[l].p(c,a),I(i[l],1)):(i[l]=ah(c),i[l].c(),I(i[l],1),i[l].m(t.parentNode,t))}for(jt(),l=r.length;l<i.length;l+=1)o(l);Lt()}},i(s){if(!e){for(let a=0;a<r.length;a+=1)I(i[a]);e=!0}},o(s){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)E(i[a]);e=!1},d(s){s&&w(t),An(i,s)}}}function ZI(n,t,e){let r,i=Xt,o=()=>(i(),i=Zg(s,a=>e(1,r=a)),s);n.$$.on_destroy.push(()=>i());let{history:s}=t;return o(),n.$$set=a=>{"history"in a&&o(e(0,s=a.history))},[s,r]}class $I extends re{constructor(t){super(),ie(this,t,ZI,XI,Zt,{history:0})}}function lh(n,t,e){const r=n.slice();return r[12]=t[e][0],r[13]=t[e][1],r}function ch(n){let t,e,r,i,o,s="Evidence Dev Tools",a,l,c,u,f,h;return r=new Pn({props:{src:n[0]?Xs:Il,class:"w-4 h-4"}}),l=new F_({props:{$$slots:{default:[rO]},$$scope:{ctx:n}}}),{c(){t=Y("div"),e=Y("button"),lt(r.$$.fragment),i=rt(),o=Y("header"),o.textContent=s,a=rt(),lt(l.$$.fragment),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=J(t);e=q(g,"BUTTON",{class:!0});var m=J(e);at(r.$$.fragment,m),m.forEach(w),i=nt(g),o=q(g,"HEADER",{class:!0,"data-svelte-h":!0}),vr(o)!=="svelte-ekyf9x"&&(o.textContent=s),a=nt(g),at(l.$$.fragment,g),g.forEach(w),this.h()},h(){C(e,"class","absolute right-4 top-4 rounded-full bg-info text-info-content w-8 h-8 flex items-center justify-center hover:brightness-110 z-30"),C(o,"class","text-xl font-bold mb-4"),C(t,"class","h-[calc(100vh-3rem)] w-96 bg-base-100 fixed overflow-auto right-0 top-12 px-4 py-4 z-10")},m(d,g){L(d,t,g),W(t,e),st(r,e,null),W(t,i),W(t,o),W(t,a),st(l,t,null),u=!0,f||(h=Pt(e,"click",n[7]),f=!0)},p(d,g){const m={};g&1&&(m.src=d[0]?Xs:Il),r.$set(m);const p={};g&2054&&(p.$$scope={dirty:g,ctx:d}),l.$set(p)},i(d){u||(I(r.$$.fragment,d),I(l.$$.fragment,d),d&&En(()=>{u&&(c||(c=Dn(t,ii,{x:384,duration:250,delay:0},!0)),c.run(1))}),u=!0)},o(d){E(r.$$.fragment,d),E(l.$$.fragment,d),d&&(c||(c=Dn(t,ii,{x:384,duration:250,delay:0},!1)),c.run(0)),u=!1},d(d){d&&w(t),ot(r),ot(l),d&&c&&c.end(),f=!1,h()}}}function uh(n,t){let e,r,i=t[13].id+"",o,s,a,l=t[13].hash+"",c,u,f,h;function d(){return t[8](t[13])}return{key:n,first:null,c(){e=Y("button"),r=Y("p"),o=wt(i),s=rt(),a=Y("p"),c=wt(l),u=rt(),this.h()},l(g){e=q(g,"BUTTON",{class:!0});var m=J(e);r=q(m,"P",{class:!0});var p=J(r);o=vt(p,i),p.forEach(w),s=nt(m),a=q(m,"P",{class:!0});var v=J(a);c=vt(v,l),v.forEach(w),u=nt(m),m.forEach(w),this.h()},h(){var g;C(r,"class","w-full text-left truncate"),C(a,"class","w-full text-right"),C(e,"class","flex justify-between w-full odd:bg-base-200/40 hover:bg-base-200"),Fr(e,"bg-negative",t[13].error),Fr(e,"bg-warning",(g=t[13].opts)==null?void 0:g.noResolve),this.first=e},m(g,m){L(g,e,m),W(e,r),W(r,o),W(e,s),W(e,a),W(a,c),W(e,u),f||(h=Pt(e,"click",d),f=!0)},p(g,m){var p;t=g,m&4&&i!==(i=t[13].id+"")&&Re(o,i),m&4&&l!==(l=t[13].hash+"")&&Re(c,l),m&4&&Fr(e,"bg-negative",t[13].error),m&4&&Fr(e,"bg-warning",(p=t[13].opts)==null?void 0:p.noResolve)},d(g){g&&w(e),f=!1,h()}}}function fh(n){let t,e;return t=new I_({props:{query:n[1]}}),t.$on("close",n[9]),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i&2&&(o.query=r[1]),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function tO(n){let t,e=[],r=new Map,i,o=Yc.isQuery(n[1]),s,a,l=he(n[2].entries());const c=f=>f[12];for(let f=0;f<l.length;f+=1){let h=lh(n,l,f),d=c(h);r.set(d,e[f]=uh(d,h))}let u=o&&fh(n);return{c(){t=Y("section");for(let f=0;f<e.length;f+=1)e[f].c();i=rt(),u&&u.c(),s=ut(),this.h()},l(f){t=q(f,"SECTION",{class:!0});var h=J(t);for(let d=0;d<e.length;d+=1)e[d].l(h);h.forEach(w),i=nt(f),u&&u.l(f),s=ut(),this.h()},h(){C(t,"class","")},m(f,h){L(f,t,h);for(let d=0;d<e.length;d+=1)e[d]&&e[d].m(t,null);L(f,i,h),u&&u.m(f,h),L(f,s,h),a=!0},p(f,h){h&6&&(l=he(f[2].entries()),e=Eh(e,h,c,1,f,l,r,t,S_,uh,null,lh)),h&2&&(o=Yc.isQuery(f[1])),o?u?(u.p(f,h),h&2&&I(u,1)):(u=fh(f),u.c(),I(u,1),u.m(s.parentNode,s)):u&&(jt(),E(u,1,1,()=>{u=null}),Lt())},i(f){a||(I(u),a=!0)},o(f){E(u),a=!1},d(f){f&&(w(t),w(i),w(s));for(let h=0;h<e.length;h+=1)e[h].d();u&&u.d(f)}}}function eO(n){let t,e;return t=new RS({props:{history:n[4]}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p:Xt,i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function nO(n){let t,e;return t=new $I({props:{history:n[4]}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p:Xt,i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function rO(n){let t,e,r,i,o,s;return t=new Xa({props:{title:"Inspect Queries",compact:!0,$$slots:{default:[tO]},$$scope:{ctx:n}}}),r=new Xa({props:{title:"Inspect Inputs",compact:!0,$$slots:{default:[eO]},$$scope:{ctx:n}}}),o=new Xa({props:{title:"View Input History",compact:!0,$$slots:{default:[nO]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment),e=rt(),lt(r.$$.fragment),i=rt(),lt(o.$$.fragment)},l(a){at(t.$$.fragment,a),e=nt(a),at(r.$$.fragment,a),i=nt(a),at(o.$$.fragment,a)},m(a,l){st(t,a,l),L(a,e,l),st(r,a,l),L(a,i,l),st(o,a,l),s=!0},p(a,l){const c={};l&2054&&(c.$$scope={dirty:l,ctx:a}),t.$set(c);const u={};l&2048&&(u.$$scope={dirty:l,ctx:a}),r.$set(u);const f={};l&2048&&(f.$$scope={dirty:l,ctx:a}),o.$set(f)},i(a){s||(I(t.$$.fragment,a),I(r.$$.fragment,a),I(o.$$.fragment,a),s=!0)},o(a){E(t.$$.fragment,a),E(r.$$.fragment,a),E(o.$$.fragment,a),s=!1},d(a){a&&(w(e),w(i)),ot(t,a),ot(r,a),ot(o,a)}}}function iO(n){let t,e,r,i,o,s;return e=new Pn({props:{src:Il,class:"w-4 h-4"}}),{c(){t=Y("button"),lt(e.$$.fragment),this.h()},l(a){t=q(a,"BUTTON",{class:!0});var l=J(t);at(e.$$.fragment,l),l.forEach(w),this.h()},h(){C(t,"class","fixed right-4 top-16 rounded-full bg-info text-info-content w-8 h-8 flex items-center justify-center hover:brightness-110 z-0")},m(a,l){L(a,t,l),st(e,t,null),i=!0,o||(s=Pt(t,"click",n[10]),o=!0)},p:Xt,i(a){i||(I(e.$$.fragment,a),a&&En(()=>{i&&(r||(r=Dn(t,Sl,{axis:"x"},!0)),r.run(1))}),i=!0)},o(a){E(e.$$.fragment,a),a&&(r||(r=Dn(t,Sl,{axis:"x"},!1)),r.run(0)),i=!1},d(a){a&&w(t),ot(e),a&&r&&r.end(),o=!1,s()}}}function oO(n){let t,e=qg(),r,i,o=n[0]&&ch(n),s=e&&iO(n);const a=n[6].default,l=ge(a,n,n[11],null);return{c(){o&&o.c(),t=rt(),s&&s.c(),r=rt(),l&&l.c()},l(c){o&&o.l(c),t=nt(c),s&&s.l(c),r=nt(c),l&&l.l(c)},m(c,u){o&&o.m(c,u),L(c,t,u),s&&s.m(c,u),L(c,r,u),l&&l.m(c,u),i=!0},p(c,[u]){c[0]?o?(o.p(c,u),u&1&&I(o,1)):(o=ch(c),o.c(),I(o,1),o.m(t.parentNode,t)):o&&(jt(),E(o,1,1,()=>{o=null}),Lt()),e&&s.p(c,u),l&&l.p&&(!i||u&2048)&&_e(l,a,c,c[11],i?ye(a,c[11],u,null):be(c[11]),null)},i(c){i||(I(o),I(s),I(l,c),i=!0)},o(c){E(o),E(s),E(l,c),i=!1},d(c){c&&(w(t),w(r)),o&&o.d(c),s&&s.d(c),l&&l.d(c)}}}function sO(n,t,e){let r,i;Te(n,w_,m=>e(2,i=m));let{$$slots:o={},$$scope:s}=t;Yg(Ke({}));let a=!1,l;k_(()=>{}),Cr(()=>{const m=p=>{p.key==="Escape"&&(e(0,a=!1),p.stopPropagation()),p.key.toLowerCase()==="e"&&p.shiftKey&&(p.ctrlKey||p.metaKey)&&(e(0,a=!0),p.stopPropagation())};return window.addEventListener("keydown",m),()=>window.removeEventListener("keydown",m)});const c=Sh();Te(n,c,m=>e(5,r=m));const u=new A0,f=()=>e(0,a=!a),h=m=>e(1,l=m),d=()=>e(1,l=null),g=()=>e(0,a=!a);return n.$$set=m=>{"$$scope"in m&&e(11,s=m.$$scope)},n.$$.update=()=>{n.$$.dirty&32&&u.push(r)},[a,l,i,c,u,r,o,f,h,d,g,s]}class aO extends re{constructor(t){super(),ie(this,t,sO,oO,Zt,{})}}function lO(n){let t;const e=n[0].default,r=ge(e,n,n[1],null);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&2)&&_e(r,e,i,i[1],t?ye(e,i[1],o,null):be(i[1]),null)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function cO(n){let t,e;return t=new aO({props:{$$slots:{default:[uO]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,i){const o={};i&2&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function uO(n){let t;const e=n[0].default,r=ge(e,n,n[1],null);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&2)&&_e(r,e,i,i[1],t?ye(e,i[1],o,null):be(i[1]),null)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function fO(n){let t,e,r,i;const o=[cO,lO],s=[];function a(l,c){return 1}return t=a(),e=s[t]=o[t](n),{c(){e.c(),r=ut()},l(l){e.l(l),r=ut()},m(l,c){s[t].m(l,c),L(l,r,c),i=!0},p(l,[c]){e.p(l,c)},i(l){i||(I(e),i=!0)},o(l){E(e),i=!1},d(l){l&&w(r),s[t].d(l)}}}function dO(n,t,e){let{$$slots:r={},$$scope:i}=t;return n.$$set=o=>{"$$scope"in o&&e(1,i=o.$$scope)},[r,i]}class hO extends re{constructor(t){super(),ie(this,t,dO,fO,Zt,{})}}const mO=n=>({}),dh=n=>({});function hh(n){let t,e,r;function i(s){n[41](s)}let o={title:n[0],logo:n[1],lightLogo:n[2],darkLogo:n[3],neverShowQueries:n[4],fullWidth:n[21],maxWidth:n[20],hideSidebar:n[5],githubRepo:n[8],slackCommunity:n[11],xProfile:n[9],blueskyProfile:n[10],algolia:n[7],sidebarFrontMatter:n[14]};return n[17]!==void 0&&(o.mobileSidebarOpen=n[17]),t=new S2({props:o}),kn.push(()=>kh(t,"mobileSidebarOpen",i)),{c(){lt(t.$$.fragment)},l(s){at(t.$$.fragment,s)},m(s,a){st(t,s,a),r=!0},p(s,a){const l={};a[0]&1&&(l.title=s[0]),a[0]&2&&(l.logo=s[1]),a[0]&4&&(l.lightLogo=s[2]),a[0]&8&&(l.darkLogo=s[3]),a[0]&16&&(l.neverShowQueries=s[4]),a[0]&2097152&&(l.fullWidth=s[21]),a[0]&1048576&&(l.maxWidth=s[20]),a[0]&32&&(l.hideSidebar=s[5]),a[0]&256&&(l.githubRepo=s[8]),a[0]&2048&&(l.slackCommunity=s[11]),a[0]&512&&(l.xProfile=s[9]),a[0]&1024&&(l.blueskyProfile=s[10]),a[0]&128&&(l.algolia=s[7]),a[0]&16384&&(l.sidebarFrontMatter=s[14]),!e&&a[0]&131072&&(e=!0,l.mobileSidebarOpen=s[17],Oh(()=>e=!1)),t.$set(l)},i(s){r||(I(t.$$.fragment,s),r=!0)},o(s){E(t.$$.fragment,s),r=!1},d(s){ot(t,s)}}}function mh(n){let t,e,r,i;function o(a){n[42](a)}let s={fileTree:n[24],title:n[0],logo:n[1],homePageName:n[12],builtWithEvidence:n[6],hideHeader:n[19],sidebarFrontMatter:n[14],sidebarDepth:n[13]};return n[17]!==void 0&&(s.mobileSidebarOpen=n[17]),e=new _S({props:s}),kn.push(()=>kh(e,"mobileSidebarOpen",o)),{c(){t=Y("div"),lt(e.$$.fragment),this.h()},l(a){t=q(a,"DIV",{class:!0});var l=J(t);at(e.$$.fragment,l),l.forEach(w),this.h()},h(){C(t,"class","print:hidden")},m(a,l){L(a,t,l),st(e,t,null),i=!0},p(a,l){const c={};l[0]&1&&(c.title=a[0]),l[0]&2&&(c.logo=a[1]),l[0]&4096&&(c.homePageName=a[12]),l[0]&64&&(c.builtWithEvidence=a[6]),l[0]&524288&&(c.hideHeader=a[19]),l[0]&16384&&(c.sidebarFrontMatter=a[14]),l[0]&8192&&(c.sidebarDepth=a[13]),!r&&l[0]&131072&&(r=!0,c.mobileSidebarOpen=a[17],Oh(()=>r=!1)),e.$set(c)},i(a){i||(I(e.$$.fragment,a),i=!0)},o(a){E(e.$$.fragment,a),i=!1},d(a){a&&w(t),ot(e)}}}function ph(n){let t,e,r=n[15].route.id!=="/settings"&&gh(n);return{c(){t=Y("div"),r&&r.c(),this.h()},l(i){t=q(i,"DIV",{class:!0});var o=J(t);r&&r.l(o),o.forEach(w),this.h()},h(){C(t,"class","print:hidden")},m(i,o){L(i,t,o),r&&r.m(t,null),e=!0},p(i,o){i[15].route.id!=="/settings"?r?(r.p(i,o),o[0]&32768&&I(r,1)):(r=gh(i),r.c(),I(r,1),r.m(t,null)):r&&(jt(),E(r,1,1,()=>{r=null}),Lt())},i(i){e||(I(r),e=!0)},o(i){E(r),e=!1},d(i){i&&w(t),r&&r.d()}}}function gh(n){let t,e;return t=new MS({props:{fileTree:n[24]}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p:Xt,i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function pO(n){let t,e;return t=new D2({}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p:Xt,i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function gO(n){let t,e;const r=n[40].content,i=ge(r,n,n[43],dh);return{c(){t=Y("article"),i&&i.c(),this.h()},l(o){t=q(o,"ARTICLE",{id:!0,class:!0});var s=J(t);i&&i.l(s),s.forEach(w),this.h()},h(){C(t,"id","evidence-main-article"),C(t,"class","select-text markdown pb-10")},m(o,s){L(o,t,s),i&&i.m(t,null),e=!0},p(o,s){i&&i.p&&(!e||s[1]&4096)&&_e(i,r,o,o[43],e?ye(r,o[43],s,mO):be(o[43]),dh)},i(o){e||(I(i,o),e=!0)},o(o){E(i,o),e=!1},d(o){o&&w(t),i&&i.d(o)}}}function _h(n){let t,e,r;return e=new IS({props:{hideHeader:n[19]}}),{c(){t=Y("div"),lt(e.$$.fragment),this.h()},l(i){t=q(i,"DIV",{class:!0});var o=J(t);at(e.$$.fragment,o),o.forEach(w),this.h()},h(){C(t,"class","print:hidden")},m(i,o){L(i,t,o),st(e,t,null),r=!0},p(i,o){const s={};o[0]&524288&&(s.hideHeader=i[19]),e.$set(s)},i(i){r||(I(e.$$.fragment,i),r=!0)},o(i){E(e.$$.fragment,i),r=!1},d(i){i&&w(t),ot(e)}}}function bh(n){let t,e;return t=new O2({}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function _O(n){let t,e,r,i,o,s,a,l,c,u,f,h,d,g,m=!n[16]&&Ol,p,v;e=new FS({});let b=!n[19]&&hh(n),_=!n[5]&&n[14]!=="never"&&n[15].route.id!=="/settings"&&mh(n),O=!n[22]&&n[15].route.id!=="/settings"&&ph(n);const y=[gO,pO],T=[];function S(N,R){return N[16]?1:0}c=S(n),u=T[c]=y[c](n);let F=!n[18]&&n[15].route.id!=="/settings"&&_h(n),B=m&&bh();return{c(){t=Y("div"),lt(e.$$.fragment),r=rt(),b&&b.c(),i=rt(),o=Y("div"),_&&_.c(),s=rt(),a=Y("main"),O&&O.c(),l=rt(),u.c(),h=rt(),F&&F.c(),g=rt(),B&&B.c(),p=ut(),this.h()},l(N){t=q(N,"DIV",{"data-sveltekit-preload-data":!0,class:!0});var R=J(t);at(e.$$.fragment,R),r=nt(R),b&&b.l(R),i=nt(R),o=q(R,"DIV",{class:!0,style:!0});var P=J(o);_&&_.l(P),s=nt(P),a=q(P,"MAIN",{class:!0});var M=J(a);O&&O.l(M),l=nt(M),u.l(M),M.forEach(w),h=nt(P),F&&F.l(P),P.forEach(w),R.forEach(w),g=nt(N),B&&B.l(N),p=ut(),this.h()},h(){C(a,"class",f=(n[15].route.id==="/settings"?"w-full mt-16 sm:mt-20 ":(!n[5]&&!["hide","never"].includes(n[14])?"md:pl-8 ":"")+(n[18]?"":"md:pr-8 ")+(n[19]?n[22]?" mt-4 sm:mt-[26px] ":" mt-4 sm:mt-8 ":n[22]?" mt-16 sm:mt-[74px] ":" mt-16 sm:mt-20 "))+"flex-grow overflow-x-hidden print:px-0 print:mt-8"),C(o,"class",d=(n[21]?"max-w-full ":n[20]?"":" max-w-7xl ")+"print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start"),Gs(o,"max-width",n[20]+"px"),C(t,"data-sveltekit-preload-data",n[23]),C(t,"class","antialiased")},m(N,R){L(N,t,R),st(e,t,null),W(t,r),b&&b.m(t,null),W(t,i),W(t,o),_&&_.m(o,null),W(o,s),W(o,a),O&&O.m(a,null),W(a,l),T[c].m(a,null),W(o,h),F&&F.m(o,null),L(N,g,R),B&&B.m(N,R),L(N,p,R),v=!0},p(N,R){N[19]?b&&(jt(),E(b,1,1,()=>{b=null}),Lt()):b?(b.p(N,R),R[0]&524288&&I(b,1)):(b=hh(N),b.c(),I(b,1),b.m(t,i)),!N[5]&&N[14]!=="never"&&N[15].route.id!=="/settings"?_?(_.p(N,R),R[0]&49184&&I(_,1)):(_=mh(N),_.c(),I(_,1),_.m(o,s)):_&&(jt(),E(_,1,1,()=>{_=null}),Lt()),!N[22]&&N[15].route.id!=="/settings"?O?(O.p(N,R),R[0]&4227072&&I(O,1)):(O=ph(N),O.c(),I(O,1),O.m(a,l)):O&&(jt(),E(O,1,1,()=>{O=null}),Lt());let P=c;c=S(N),c===P?T[c].p(N,R):(jt(),E(T[P],1,1,()=>{T[P]=null}),Lt(),u=T[c],u?u.p(N,R):(u=T[c]=y[c](N),u.c()),I(u,1),u.m(a,null)),(!v||R[0]&5029920&&f!==(f=(N[15].route.id==="/settings"?"w-full mt-16 sm:mt-20 ":(!N[5]&&!["hide","never"].includes(N[14])?"md:pl-8 ":"")+(N[18]?"":"md:pr-8 ")+(N[19]?N[22]?" mt-4 sm:mt-[26px] ":" mt-4 sm:mt-8 ":N[22]?" mt-16 sm:mt-[74px] ":" mt-16 sm:mt-20 "))+"flex-grow overflow-x-hidden print:px-0 print:mt-8"))&&C(a,"class",f),!N[18]&&N[15].route.id!=="/settings"?F?(F.p(N,R),R[0]&294912&&I(F,1)):(F=_h(N),F.c(),I(F,1),F.m(o,null)):F&&(jt(),E(F,1,1,()=>{F=null}),Lt()),(!v||R[0]&3145728&&d!==(d=(N[21]?"max-w-full ":N[20]?"":" max-w-7xl ")+"print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start"))&&C(o,"class",d),(!v||R[0]&1048576)&&Gs(o,"max-width",N[20]+"px"),R[0]&98304&&(m=!N[16]&&Ol),m?B?R[0]&98304&&I(B,1):(B=bh(),B.c(),I(B,1),B.m(p.parentNode,p)):B&&(jt(),E(B,1,1,()=>{B=null}),Lt())},i(N){v||(I(e.$$.fragment,N),I(b),I(_),I(O),I(u),I(F),I(B),v=!0)},o(N){E(e.$$.fragment,N),E(b),E(_),E(O),E(u),E(F),E(B),v=!1},d(N){N&&(w(t),w(g),w(p)),ot(e),b&&b.d(),_&&_.d(),O&&O.d(),T[c].d(),F&&F.d(),B&&B.d(N)}}}function bO(n){let t,e,r,i,o;const s=n[40].default,a=ge(s,n,n[43],null);return e=new V0({}),i=new hO({props:{$$slots:{default:[_O]},$$scope:{ctx:n}}}),{c(){a&&a.c(),t=rt(),lt(e.$$.fragment),r=rt(),lt(i.$$.fragment)},l(l){a&&a.l(l),t=nt(l),at(e.$$.fragment,l),r=nt(l),at(i.$$.fragment,l)},m(l,c){a&&a.m(l,c),L(l,t,c),st(e,l,c),L(l,r,c),st(i,l,c),o=!0},p(l,c){a&&a.p&&(!o||c[1]&4096)&&_e(a,s,l,l[43],o?ye(s,l[43],c,null):be(l[43]),null);const u={};c[0]&8388607|c[1]&4096&&(u.$$scope={dirty:c,ctx:l}),i.$set(u)},i(l){o||(I(a,l),I(e.$$.fragment,l),I(i.$$.fragment,l),o=!0)},o(l){E(a,l),E(e.$$.fragment,l),E(i.$$.fragment,l),o=!1},d(l){l&&(w(t),w(r)),a&&a.d(l),ot(e,l),ot(i,l)}}}function yO(n){const t=new Map;function e(r,i=""){const o=r.href||i;r.isPage&&t.set(decodeURI(o),r),r.children&&Object.entries(r.children).forEach(([s,a])=>{const l=`${o}/${s}`;e(a,l)})}return e(n),t}function vO(n,t,e){let r,i,o,s,a,l,c,u,f,h,d,g,m,p,v,b,_;Te(n,Na,et=>e(15,b=et)),Te(n,Th,et=>e(16,_=et));let{$$slots:O={},$$scope:y}=t;{const et=document.getElementById("__evidence_project_splash");et==null||et.remove()}let{data:T}=t,{title:S=void 0}=t,{logo:F=void 0}=t,{lightLogo:B=void 0}=t,{darkLogo:N=void 0}=t,{neverShowQueries:R=!1}=t,{fullWidth:P=!1}=t,{hideSidebar:M=!1}=t,{builtWithEvidence:j=!0}=t,{algolia:V=void 0}=t,{githubRepo:U=void 0}=t,{xProfile:H=void 0}=t,{blueskyProfile:D=void 0}=t,{slackCommunity:X=void 0}=t,{maxWidth:Q=void 0}=t,{homePageName:Z="Home"}=t,{hideBreadcrumbs:Dt=!1}=t,{hideHeader:Bt=!1}=t,{hideTOC:Ct=!1}=t,{sidebarDepth:K=3}=t;const Oe="hover";let Kt=!1,Be=T==null?void 0:T.pagesManifest;Cr(async()=>{if(!("serviceWorker"in navigator))return;const et=await navigator.serviceWorker.register(Wt("/fix-tprotocol-service-worker.js"),{scope:Wt("/"),type:"classic"});console.debug("[fix-tprotocol-service-worker] Service Worker registered",{registration:et})});const{syncThemeAttribute:qe,cycleAppearance:_n,selectedAppearance:Ve,setAppearance:Nn,activeAppearance:mn}=Ah();Te(n,Ve,et=>e(44,p=et)),Te(n,mn,et=>e(45,v=et)),Cr(()=>{const et=dt=>{dt.key.toLowerCase()==="l"&&dt.shiftKey&&(dt.ctrlKey||dt.metaKey)&&_n()};return window.addEventListener("keydown",et),()=>window.removeEventListener("keydown",et)}),Cr(()=>qe(document.querySelector("html"))),Cr(()=>{let et;const dt=()=>{et=v,p==="dark"&&Nn("light")},Jt=()=>{et==="dark"&&Nn("dark")};return window.addEventListener("beforeprint",dt),window.addEventListener("afterprint",Jt),()=>{window.removeEventListener("beforeprint",dt),window.removeEventListener("afterprint",Jt)}});function Hn(et){Kt=et,e(17,Kt),e(16,_)}function Wr(et){Kt=et,e(17,Kt),e(16,_)}return n.$$set=et=>{"data"in et&&e(27,T=et.data),"title"in et&&e(0,S=et.title),"logo"in et&&e(1,F=et.logo),"lightLogo"in et&&e(2,B=et.lightLogo),"darkLogo"in et&&e(3,N=et.darkLogo),"neverShowQueries"in et&&e(4,R=et.neverShowQueries),"fullWidth"in et&&e(28,P=et.fullWidth),"hideSidebar"in et&&e(5,M=et.hideSidebar),"builtWithEvidence"in et&&e(6,j=et.builtWithEvidence),"algolia"in et&&e(7,V=et.algolia),"githubRepo"in et&&e(8,U=et.githubRepo),"xProfile"in et&&e(9,H=et.xProfile),"blueskyProfile"in et&&e(10,D=et.blueskyProfile),"slackCommunity"in et&&e(11,X=et.slackCommunity),"maxWidth"in et&&e(29,Q=et.maxWidth),"homePageName"in et&&e(12,Z=et.homePageName),"hideBreadcrumbs"in et&&e(30,Dt=et.hideBreadcrumbs),"hideHeader"in et&&e(31,Bt=et.hideHeader),"hideTOC"in et&&e(32,Ct=et.hideTOC),"sidebarDepth"in et&&e(13,K=et.sidebarDepth),"$$scope"in et&&e(43,y=et.$$scope)},n.$$.update=()=>{var et;n.$$.dirty[0]&65536&&_&&e(17,Kt=!1),n.$$.dirty[0]&32768|n.$$.dirty[1]&256&&e(34,i=(et=r.get(b.route.id))==null?void 0:et.frontMatter),n.$$.dirty[1]&8&&e(14,o=i==null?void 0:i.sidebar),n.$$.dirty[0]&16384&&(["show","hide","never"].includes(o)||e(14,o=void 0)),n.$$.dirty[1]&8&&e(38,s=i==null?void 0:i.hide_breadcrumbs),n.$$.dirty[0]&1073741824|n.$$.dirty[1]&128&&e(22,a=s??Dt),n.$$.dirty[1]&8&&e(37,l=i==null?void 0:i.full_width),n.$$.dirty[0]&268435456|n.$$.dirty[1]&64&&e(21,c=l??P),n.$$.dirty[1]&8&&e(36,u=i==null?void 0:i.max_width),n.$$.dirty[0]&536870912|n.$$.dirty[1]&32&&e(20,f=u??Q),n.$$.dirty[1]&8&&e(35,h=i==null?void 0:i.hide_header),n.$$.dirty[1]&17&&e(19,d=h??Bt),n.$$.dirty[1]&8&&e(33,g=i==null?void 0:i.hide_toc),n.$$.dirty[1]&6&&e(18,m=g??Ct)},e(39,r=yO(Be)),[S,F,B,N,R,M,j,V,U,H,D,X,Z,K,o,b,_,Kt,m,d,f,c,a,Oe,Be,Ve,mn,T,P,Q,Dt,Bt,Ct,g,i,h,u,l,s,r,O,Hn,Wr,y]}class wO extends re{constructor(t){super(),ie(this,t,vO,bO,Zt,{data:27,title:0,logo:1,lightLogo:2,darkLogo:3,neverShowQueries:4,fullWidth:28,hideSidebar:5,builtWithEvidence:6,algolia:7,githubRepo:8,xProfile:9,blueskyProfile:10,slackCommunity:11,maxWidth:29,homePageName:12,hideBreadcrumbs:30,hideHeader:31,hideTOC:32,sidebarDepth:13},null,[-1,-1])}}const SO=n=>({}),yh=n=>({slot:"content"});function IO(n){let t;const e=n[1].default,r=ge(e,n,n[2],yh);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&4)&&_e(r,e,i,i[2],t?ye(e,i[2],o,SO):be(i[2]),yh)},i(i){t||(I(r,i),t=!0)},o(i){E(r,i),t=!1},d(i){r&&r.d(i)}}}function OO(n){let t,e;return t=new wO({props:{data:n[0],$$slots:{content:[IO]},$$scope:{ctx:n}}}),{c(){lt(t.$$.fragment)},l(r){at(t.$$.fragment,r)},m(r,i){st(t,r,i),e=!0},p(r,[i]){const o={};i&1&&(o.data=r[0]),i&4&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(I(t.$$.fragment,r),e=!0)},o(r){E(t.$$.fragment,r),e=!1},d(r){ot(t,r)}}}function kO(n,t,e){let{$$slots:r={},$$scope:i}=t,{data:o}=t;return n.$$set=s=>{"data"in s&&e(0,o=s.data),"$$scope"in s&&e(2,i=s.$$scope)},[o,r,i]}class VO extends re{constructor(t){super(),ie(this,t,kO,OO,Zt,{data:0})}}export{VO as component,RO as universal};

import{s as It,v as Q,d as o,i as p,e as v,b as e,z as vt,A as at,k as _,B as At,h as c,r as lt,q as w,j as S,n as h,m as u,t as Lt,C as Ot,D as St,E as Vt,F as qt,x as Ft,y as Bt}from"../chunks/scheduler.D0cbHTIG.js";import{S as Nt,i as Pt}from"../chunks/index.YnsWT1Qn.js";import{e as jt,s as Qt,p as Rt,a as Mt,r as gt,C as Gt}from"../chunks/inferColumnTypes.DiSvbcQ7.js";import{p as Kt}from"../chunks/profile.BW8tN6E9.js";import{p as Ut}from"../chunks/stores.CAjSrnTs.js";import{w as Xt}from"../chunks/entry.DRa3JAkG.js";function Yt(z){let i,d=m.title+"",l;return{c(){i=u("h1"),l=Bt(d),this.h()},l(a){i=c(a,"H1",{class:!0});var n=S(i);l=Ft(n,d),n.forEach(o),this.h()},h(){e(i,"class","title")},m(a,n){p(a,i,n),v(i,l)},p:Q,d(a){a&&o(i)}}}function Jt(z){return{c(){this.h()},l(i){this.h()},h(){document.title="Evidence"},m:Q,p:Q,d:Q}}function Wt(z){let i,d,l,a,n;return document.title=i=m.title,{c(){d=h(),l=u("meta"),a=h(),n=u("meta"),this.h()},l(t){d=_(t),l=c(t,"META",{property:!0,content:!0}),a=_(t),n=c(t,"META",{name:!0,content:!0}),this.h()},h(){var t,s;e(l,"property","og:title"),e(l,"content",((t=m.og)==null?void 0:t.title)??m.title),e(n,"name","twitter:title"),e(n,"content",((s=m.og)==null?void 0:s.title)??m.title)},m(t,s){p(t,d,s),p(t,l,s),p(t,a,s),p(t,n,s)},p(t,s){s&0&&i!==(i=m.title)&&(document.title=i)},d(t){t&&(o(d),o(l),o(a),o(n))}}}function Zt(z){var n,t;let i,d,l=(m.description||((n=m.og)==null?void 0:n.description))&&$t(),a=((t=m.og)==null?void 0:t.image)&&te();return{c(){l&&l.c(),i=h(),a&&a.c(),d=lt()},l(s){l&&l.l(s),i=_(s),a&&a.l(s),d=lt()},m(s,b){l&&l.m(s,b),p(s,i,b),a&&a.m(s,b),p(s,d,b)},p(s,b){var C,R;(m.description||(C=m.og)!=null&&C.description)&&l.p(s,b),(R=m.og)!=null&&R.image&&a.p(s,b)},d(s){s&&(o(i),o(d)),l&&l.d(s),a&&a.d(s)}}}function $t(z){let i,d,l,a,n;return{c(){i=u("meta"),d=h(),l=u("meta"),a=h(),n=u("meta"),this.h()},l(t){i=c(t,"META",{name:!0,content:!0}),d=_(t),l=c(t,"META",{property:!0,content:!0}),a=_(t),n=c(t,"META",{name:!0,content:!0}),this.h()},h(){var t,s,b;e(i,"name","description"),e(i,"content",m.description??((t=m.og)==null?void 0:t.description)),e(l,"property","og:description"),e(l,"content",((s=m.og)==null?void 0:s.description)??m.description),e(n,"name","twitter:description"),e(n,"content",((b=m.og)==null?void 0:b.description)??m.description)},m(t,s){p(t,i,s),p(t,d,s),p(t,l,s),p(t,a,s),p(t,n,s)},p:Q,d(t){t&&(o(i),o(d),o(l),o(a),o(n))}}}function te(z){let i,d,l;return{c(){i=u("meta"),d=h(),l=u("meta"),this.h()},l(a){i=c(a,"META",{property:!0,content:!0}),d=_(a),l=c(a,"META",{name:!0,content:!0}),this.h()},h(){var a,n;e(i,"property","og:image"),e(i,"content",Mt((a=m.og)==null?void 0:a.image)),e(l,"name","twitter:image"),e(l,"content",Mt((n=m.og)==null?void 0:n.image))},m(a,n){p(a,i,n),p(a,d,n),p(a,l,n)},p:Q,d(a){a&&(o(i),o(d),o(l))}}}function ee(z){let i,d,l,a,n,t,s='<a href="#domo-data-loader">Domo Data Loader</a>',b,C,R="Load datasets from Domo into DuckDB for analysis.",X,y,x,V,q,pt="Select Dataset:",it,G,E,ft="Choose a dataset...",st,D,_t='<h4>Dataset Information</h4> <div id="preview-content" class="preview-content"><div id="dataset-info" class="dataset-info svelte-16omzez"></div> <h5>Schema</h5> <div id="schema-table" class="schema-table"></div> <div class="preview-actions svelte-16omzez"><button id="preview-btn" class="btn btn-secondary svelte-16omzez">Preview Data</button></div> <div id="data-preview" class="data-preview svelte-16omzez" style="display: none;"></div></div>',ot,T,U,ht="Loading Configuration",nt,F,K,bt='<label for="table-name" class="svelte-16omzez">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-16omzez"/>',rt,B,N,yt="Refresh Mode:",dt,P,L,xt="Replace existing data",M,zt="Append to existing data",ct,g,Ct='<button id="load-dataset-btn" class="btn btn-primary svelte-16omzez">📊 Load Dataset into DuckDB</button>',ut,H,wt='<div class="loading-spinner svelte-16omzez"></div> <p class="svelte-16omzez">Loading...</p>',J,I,Tt='<a href="#loaded-datasets">Loaded Datasets</a>',W,j,kt="<p>Once you load datasets from Domo, they will appear here and you can query them using SQL.</p>",A=typeof m<"u"&&m.title&&m.hide_title!==!0&&Yt();function Ht(r,f){return typeof m<"u"&&m.title?Wt:Jt}let Y=Ht()(z),O=typeof m=="object"&&Zt();return{c(){A&&A.c(),i=h(),Y.c(),d=u("meta"),l=u("meta"),O&&O.c(),a=lt(),n=h(),t=u("h1"),t.innerHTML=s,b=h(),C=u("p"),C.textContent=R,X=h(),y=u("div"),x=u("div"),V=u("div"),q=u("label"),q.textContent=pt,it=h(),G=u("select"),E=u("option"),E.textContent=ft,st=h(),D=u("div"),D.innerHTML=_t,ot=h(),T=u("div"),U=u("h4"),U.textContent=ht,nt=h(),F=u("div"),K=u("div"),K.innerHTML=bt,rt=h(),B=u("div"),N=u("label"),N.textContent=yt,dt=h(),P=u("select"),L=u("option"),L.textContent=xt,M=u("option"),M.textContent=zt,ct=h(),g=u("div"),g.innerHTML=Ct,ut=h(),H=u("div"),H.innerHTML=wt,J=h(),I=u("h2"),I.innerHTML=Tt,W=h(),j=u("div"),j.innerHTML=kt,this.h()},l(r){A&&A.l(r),i=_(r);const f=At("svelte-2igo1p",document.head);Y.l(f),d=c(f,"META",{name:!0,content:!0}),l=c(f,"META",{name:!0,content:!0}),O&&O.l(f),a=lt(),f.forEach(o),n=_(r),t=c(r,"H1",{class:!0,id:!0,"data-svelte-h":!0}),w(t)!=="svelte-94lco6"&&(t.innerHTML=s),b=_(r),C=c(r,"P",{class:!0,"data-svelte-h":!0}),w(C)!=="svelte-4aovu"&&(C.textContent=R),X=_(r),y=c(r,"DIV",{class:!0});var Et=S(y);x=c(Et,"DIV",{id:!0,class:!0});var k=S(x);V=c(k,"DIV",{class:!0});var Z=S(V);q=c(Z,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),w(q)!=="svelte-1fci9ty"&&(q.textContent=pt),it=_(Z),G=c(Z,"SELECT",{id:!0,class:!0});var Dt=S(G);E=c(Dt,"OPTION",{"data-svelte-h":!0}),w(E)!=="svelte-59d9xk"&&(E.textContent=ft),Dt.forEach(o),Z.forEach(o),st=_(k),D=c(k,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),w(D)!=="svelte-1bdnaam"&&(D.innerHTML=_t),ot=_(k),T=c(k,"DIV",{id:!0,class:!0,style:!0});var $=S(T);U=c($,"H4",{"data-svelte-h":!0}),w(U)!=="svelte-1foy07w"&&(U.textContent=ht),nt=_($),F=c($,"DIV",{class:!0});var tt=S(F);K=c(tt,"DIV",{class:!0,"data-svelte-h":!0}),w(K)!=="svelte-1y7um71"&&(K.innerHTML=bt),rt=_(tt),B=c(tt,"DIV",{class:!0});var et=S(B);N=c(et,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),w(N)!=="svelte-p1qydn"&&(N.textContent=yt),dt=_(et),P=c(et,"SELECT",{id:!0,class:!0});var mt=S(P);L=c(mt,"OPTION",{"data-svelte-h":!0}),w(L)!=="svelte-qvzdub"&&(L.textContent=xt),M=c(mt,"OPTION",{"data-svelte-h":!0}),w(M)!=="svelte-idsvi6"&&(M.textContent=zt),mt.forEach(o),et.forEach(o),tt.forEach(o),$.forEach(o),ct=_(k),g=c(k,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),w(g)!=="svelte-1jj2g91"&&(g.innerHTML=Ct),ut=_(k),H=c(k,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),w(H)!=="svelte-9gnlc9"&&(H.innerHTML=wt),k.forEach(o),Et.forEach(o),J=_(r),I=c(r,"H2",{class:!0,id:!0,"data-svelte-h":!0}),w(I)!=="svelte-1f0ly50"&&(I.innerHTML=Tt),W=_(r),j=c(r,"DIV",{id:!0,"data-svelte-h":!0}),w(j)!=="svelte-187xr4d"&&(j.innerHTML=kt),this.h()},h(){e(d,"name","twitter:card"),e(d,"content","summary_large_image"),e(l,"name","twitter:site"),e(l,"content","@evidence_dev"),e(t,"class","markdown"),e(t,"id","domo-data-loader"),e(C,"class","markdown"),e(q,"for","dataset-selector"),e(q,"class","svelte-16omzez"),E.__value="",vt(E,E.__value),e(G,"id","dataset-selector"),e(G,"class","dataset-dropdown svelte-16omzez"),e(V,"class","workflow-step svelte-16omzez"),e(D,"id","dataset-preview"),e(D,"class","dataset-preview svelte-16omzez"),at(D,"display","none"),e(K,"class","config-item"),e(N,"for","refresh-mode"),e(N,"class","svelte-16omzez"),L.__value="replace",vt(L,L.__value),M.__value="append",vt(M,M.__value),e(P,"id","refresh-mode"),e(P,"class","svelte-16omzez"),e(B,"class","config-item"),e(F,"class","config-grid svelte-16omzez"),e(T,"id","loading-config"),e(T,"class","workflow-step svelte-16omzez"),at(T,"display","none"),e(g,"id","workflow-actions"),e(g,"class","workflow-actions svelte-16omzez"),at(g,"display","none"),e(H,"id","loading-status"),e(H,"class","loading-status svelte-16omzez"),at(H,"display","none"),e(x,"id","domo-workflow-picker"),e(x,"class","workflow-picker svelte-16omzez"),e(y,"class","workflow-picker-section svelte-16omzez"),e(I,"class","markdown"),e(I,"id","loaded-datasets"),e(j,"id","loaded-datasets-section")},m(r,f){A&&A.m(r,f),p(r,i,f),Y.m(document.head,null),v(document.head,d),v(document.head,l),O&&O.m(document.head,null),v(document.head,a),p(r,n,f),p(r,t,f),p(r,b,f),p(r,C,f),p(r,X,f),p(r,y,f),v(y,x),v(x,V),v(V,q),v(V,it),v(V,G),v(G,E),v(x,st),v(x,D),v(x,ot),v(x,T),v(T,U),v(T,nt),v(T,F),v(F,K),v(F,rt),v(F,B),v(B,N),v(B,dt),v(B,P),v(P,L),v(P,M),v(x,ct),v(x,g),v(x,ut),v(x,H),p(r,J,f),p(r,I,f),p(r,W,f),p(r,j,f)},p(r,[f]){typeof m<"u"&&m.title&&m.hide_title!==!0&&A.p(r,f),Y.p(r,f),typeof m=="object"&&O.p(r,f)},i:Q,o:Q,d(r){r&&(o(i),o(n),o(t),o(b),o(C),o(X),o(y),o(J),o(I),o(W),o(j)),A&&A.d(r),Y.d(r),o(d),o(l),O&&O.d(r),o(a)}}}const m={title:"Domo Data Loader"};function ae(z,i,d){let l,a;Lt(z,Ut,y=>d(2,l=y)),Lt(z,gt,y=>d(8,a=y));let{data:n}=i,{data:t={},customFormattingSettings:s,__db:b,inputs:C}=n;Ot(gt,a="6666cd76f96956469e7be39d750cc7d9",a);let R=jt(Xt(C));return St(R.subscribe(y=>C=y)),Vt(Gt,{getCustomFormats:()=>s.customFormats||[]}),Qt((y,x)=>Kt(b.query,y,{query_name:x})),l.params,qt(()=>!0),z.$$set=y=>{"data"in y&&d(0,n=y.data)},z.$$.update=()=>{z.$$.dirty&1&&d(1,{data:t={},customFormattingSettings:s,__db:b}=n,t),z.$$.dirty&2&&Rt.set(Object.keys(t).length>0),z.$$.dirty&4&&l.params},[n,t,l]}class ce extends Nt{constructor(i){super(),Pt(this,i,ae,ee,It,{data:0})}}export{ce as component};

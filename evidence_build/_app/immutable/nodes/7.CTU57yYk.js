import{v as re,R as hl,L as _n,O as pl,a0 as Mn,a1 as Vn,s as _e,c as Oe,u as Le,g as Pe,a as De,t as qe,d as p,i as j,r as fe,a2 as Se,a3 as ae,a4 as Ye,z as Je,p as ke,a5 as Ie,A as Qe,l as ce,h as k,j as M,m as w,o as Ke,D as Ee,w as ye,x as ie,y as le,k as q,n as B,b as E,E as ln,ag as Ue,aa as we,J as tt,e as g,q as oe,ab as ki,al as jn,a9 as at,am as St,ah as wi,an as Ci,ao as ns,a7 as en,a8 as tn,ac as Ti,ad as Ei}from"../chunks/scheduler.DQwIXrE4.js";import{n as gl,l as _l,o as bl,p as vl,S as be,i as ve,t as P,a as T,g as pe,c as ge,f as nt,h as Si,j as Ae,d as J,m as z,b as X,e as Z,k as Ge}from"../chunks/index.BEt_7cXZ.js";import{p as rt,K as yl,aG as Ii,aH as kl,y as et,F as ut,w as ss,x as Nn,t as An,r as Ni,z as Ai,v as wl,aI as Cl,B as qt,J as me,aJ as is,aK as bn,aL as Tl,aM as ls,aN as El,aO as Sl,aP as Il,aQ as Nl,aR as Al,n as vn,A as Ht,aS as Ol,aT as Xt,H as On,G as Ll,aU as Pl,aV as Dl,aW as Ml,aX as jt,aY as Vl,aZ as jl,a_ as Fl,a$ as ql,l as yn,N as Oi,L as Li,M as Pi,b0 as Bl,Q as rn,O as Ve,S as Ut,R as ot,I as je,b1 as Hl,b2 as Rl,b3 as Kl,b4 as Ul,b5 as Gl,Z as Xe,b6 as _t,b7 as Di,b8 as It,b9 as Mi,ba as Vi,bb as Be,bc as Et,bd as Fn,be as qn,bf as Bn,bg as Yl,bh as bt,bi as Wl,bj as Jl,bk as Zt,bl as on,bm as rs,bn as zl,bo as os,bp as as,bq as nn,br as Xl,bs as Zl,bt as Hn,bu as Ql,bv as $l,bw as ft,bx as Rt,by as Qt,bz as Nt,bA as cs,bB as xl,bC as er,bD as Rn,bE as an,bF as tr,bG as nr,bH as ji,bI as Ct,bJ as Fi,bK as sr,bL as ir,bM as sn,bN as qi,bO as lr,bP as rr,bQ as or,bR as ar,bS as cr,bT as ur,e as dt,h as Ne,bU as fr,bV as vt,bW as Kn,j as Gt,bX as dr,u as Un,o as mr,bY as At,bZ as Ot,b_ as hr,b$ as pr,c0 as gr,ay as Ln,c1 as _r,aa as kn,c2 as Yt}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.ebVGE6DV.js";import{w as Bt,d as pt,r as br,i as vr,a as yr,p as kr}from"../chunks/entry.DSMgDpC3.js";import{c as wr,a as Cr,r as Tr,b as Er,s as st}from"../chunks/index.DFESiLto.js";import{B as We}from"../chunks/Button.D4O5R9AT.js";import{b as Sr,A as Gn,a as Tt}from"../chunks/AccordionItem.D3vBGfst.js";function Ir(s,e,t,n){if(!e)return re;const i=s.getBoundingClientRect();if(e.left===i.left&&e.right===i.right&&e.top===i.top&&e.bottom===i.bottom)return re;const{delay:l=0,duration:r=300,easing:a=hl,start:o=gl()+l,end:c=o+r,tick:u=re,css:f}=t(s,{from:e,to:i},n);let d=!0,m=!1,h;function _(){f&&(h=vl(s,0,1,r,l,a,f)),l||(m=!0)}function N(){f&&bl(s,h),d=!1}return _l(S=>{if(!m&&S>=o&&(m=!0),m&&S>=c&&(u(1,0),N()),!d)return!1;if(m){const O=S-o,A=0+1*a(O/r);u(A,1-A)}return!0}),_(),u(0,1),N}function Nr(s){const e=getComputedStyle(s);if(e.position!=="absolute"&&e.position!=="fixed"){const{width:t,height:n}=e,i=s.getBoundingClientRect();s.style.position="absolute",s.style.width=t,s.style.height=n,Ar(s,i)}}function Ar(s,e){const t=s.getBoundingClientRect();if(e.left!==t.left||e.top!==t.top){const n=getComputedStyle(s),i=n.transform==="none"?"":n.transform;s.style.transform=`${i} translate(${e.left-t.left}px, ${e.top-t.top}px)`}}function wn(s){return Array.from(s.querySelectorAll('[role="option"]:not([data-disabled])')).filter(e=>rt(e))}function Or(s){return e=>{const t=e.target,n=yl(s);if(!n||!Ii(t))return!1;const i=n.id;return!!(kl(t)&&i===t.htmlFor||t.closest(`label[for="${i}"]`))}}function Lr(){return{elements:{root:et("label",{action:e=>({destroy:ut(e,"mousedown",n=>{!n.defaultPrevented&&n.detail>1&&n.preventDefault()})})})}}}const Pr=[me.ARROW_LEFT,me.ESCAPE,me.ARROW_RIGHT,me.SHIFT,me.CAPS_LOCK,me.CONTROL,me.ALT,me.META,me.ENTER,me.F1,me.F2,me.F3,me.F4,me.F5,me.F6,me.F7,me.F8,me.F9,me.F10,me.F11,me.F12],Dr={positioning:{placement:"bottom",sameWidth:!0},scrollAlignment:"nearest",loop:!0,defaultOpen:!1,closeOnOutsideClick:!0,preventScroll:!0,closeOnEscape:!0,forceVisible:!1,portal:void 0,builder:"listbox",disabled:!1,required:!1,name:void 0,typeahead:!0,highlightOnHover:!0,onOutsideClick:void 0},Mr=["trigger","menu","label"];function Vr(s){const e={...Dr,...s},t=ss(Bt(null)),n=ss(Bt(null)),i=e.selected??Bt(e.defaultSelected),l=Nn(i,e==null?void 0:e.onSelectedChange),r=pt(n,Y=>Y?K(Y):void 0),a=e.open??Bt(e.defaultOpen),o=Nn(a,e==null?void 0:e.onOpenChange),c=An({...Ni(e,"open","defaultOpen","builder","ids"),multiple:e.multiple??!1}),{scrollAlignment:u,loop:f,closeOnOutsideClick:d,closeOnEscape:m,preventScroll:h,portal:_,forceVisible:N,positioning:S,multiple:O,arrowSize:A,disabled:v,required:I,typeahead:C,name:D,highlightOnHover:L,onOutsideClick:V}=c,{name:b,selector:y}=Ai(e.builder),H=An({...wl(Mr),...e.ids}),{handleTypeaheadSearch:U}=wr({onMatch:Y=>{n.set(Y),Y.scrollIntoView({block:u.get()})},getCurrentItem(){return n.get()}});function K(Y){const Q=Y.getAttribute("data-value"),de=Y.getAttribute("data-label"),ze=Y.hasAttribute("data-disabled");return{value:Q&&JSON.parse(Q),label:de??Y.textContent??void 0,disabled:!!ze}}const G=Y=>{l.update(Q=>{if(O.get()){const ze=Array.isArray(Q)?[...Q]:[];return Ml(Y,ze,(lt,Te)=>jt(lt.value,Te.value))}return Y})};function se(Y){const Q=K(Y);G(Q)}async function F(){o.set(!0);const Y=document.getElementById(H.trigger.get());if(!Y)return;Y!==t.get()&&t.set(Y),await _n();const Q=document.getElementById(H.menu.get());if(!rt(Q))return;const de=Q.querySelector("[aria-selected=true]");rt(de)&&n.set(de)}function R(){o.set(!1),n.set(null)}const te=Cl({open:o,forceVisible:N,activeTrigger:t}),ee=pt([l],([Y])=>Q=>Array.isArray(Y)?Y.some(de=>jt(de.value,Q)):Vl(Q)?jt(Y==null?void 0:Y.value,jl(Q,void 0)):jt(Y==null?void 0:Y.value,Q)),W=pt([r],([Y])=>Q=>jt(Y==null?void 0:Y.value,Q)),ne=et(b("trigger"),{stores:[o,n,v,H.menu,H.trigger,H.label],returned:([Y,Q,de,ze,lt,Te])=>({"aria-activedescendant":Q==null?void 0:Q.id,"aria-autocomplete":"list","aria-controls":ze,"aria-expanded":Y,"aria-labelledby":Te,id:lt,role:"combobox",disabled:Ht(de),type:e.builder==="select"?"button":void 0}),action:Y=>{const Q=Ol(Y),de=qt(ut(Y,"click",()=>{Y.focus(),o.get()?R():F()}),ut(Y,"keydown",Te=>{if(!o.get()){if(Pr.includes(Te.key)||Te.key===me.TAB||Te.key===me.BACKSPACE&&Q&&Y.value===""||Te.key===me.SPACE&&is(Y))return;F(),_n().then(()=>{if(l.get())return;const mt=document.getElementById(H.menu.get());if(!rt(mt))return;const Me=Array.from(mt.querySelectorAll(`${y("item")}:not([data-disabled]):not([data-hidden])`)).filter(ct=>rt(ct));Me.length&&(Te.key===me.ARROW_DOWN?(n.set(Me[0]),Me[0].scrollIntoView({block:u.get()})):Te.key===me.ARROW_UP&&(n.set(bn(Me)),bn(Me).scrollIntoView({block:u.get()})))})}if(Te.key===me.TAB){R();return}if(Te.key===me.ENTER&&!Te.isComposing||Te.key===me.SPACE&&is(Y)){Te.preventDefault();const Re=n.get();Re&&se(Re),O.get()||R()}if(Te.key===me.ARROW_UP&&Te.altKey&&R(),Tl.includes(Te.key)){Te.preventDefault();const Re=document.getElementById(H.menu.get());if(!rt(Re))return;const mt=wn(Re);if(!mt.length)return;const Me=mt.filter(ts=>!ls(ts)&&ts.dataset.hidden===void 0),ct=n.get(),ht=ct?Me.indexOf(ct):-1,zt=f.get(),ml=u.get();let xe;switch(Te.key){case me.ARROW_DOWN:xe=Nl(Me,ht,zt);break;case me.ARROW_UP:xe=Il(Me,ht,zt);break;case me.PAGE_DOWN:xe=Sl(Me,ht,10,zt);break;case me.PAGE_UP:xe=El(Me,ht,10,zt);break;case me.HOME:xe=Me[0];break;case me.END:xe=bn(Me);break;default:return}n.set(xe),xe==null||xe.scrollIntoView({block:ml})}else if(C.get()){const Re=document.getElementById(H.menu.get());if(!rt(Re))return;U(Te.key,wn(Re))}}));let ze=vn;const lt=Al(Y,{handler:R,enabled:pt([o,m],([Te,Jt])=>Te&&Jt)});return lt&&lt.destroy&&(ze=lt.destroy),{destroy(){de(),ze()}}}}),x=et(b("menu"),{stores:[te,H.menu],returned:([Y,Q])=>({hidden:Y?void 0:!0,id:Q,role:"listbox",style:On({display:Y?void 0:"none"})}),action:Y=>{let Q=vn;const de=qt(Xt([te,_,d,S,t],([ze,lt,Te,Jt,Re])=>{Q(),!(!ze||!Re)&&_n().then(()=>{Q();const mt=Or(H.trigger.get());Q=Fl(Y,{anchorElement:Re,open:o,options:{floating:Jt,focusTrap:null,modal:{closeOnInteractOutside:Te,onClose:R,open:ze,shouldCloseOnInteractOutside:Me=>{var ht;if((ht=V.get())==null||ht(Me),Me.defaultPrevented)return!1;const ct=Me.target;return!(!Ii(ct)||ct===Re||Re.contains(ct)||mt(Me))}},escapeKeydown:null,portal:ql(Y,lt)}}).destroy})}));return{destroy:()=>{de(),Q()}}}}),{elements:{root:he}}=Lr(),{action:ue}=pl(he),Ce=et(b("label"),{stores:[H.label,H.trigger],returned:([Y,Q])=>({id:Y,for:Q}),action:ue}),He=et(b("option"),{stores:[ee],returned:([Y])=>Q=>{const de=Y(Q.value);return{"data-value":JSON.stringify(Q.value),"data-label":Q.label,"data-disabled":Ht(Q.disabled),"aria-disabled":Q.disabled?!0:void 0,"aria-selected":de,"data-selected":de?"":void 0,id:Ll(),role:"option"}},action:Y=>({destroy:qt(ut(Y,"click",de=>{if(ls(Y)){de.preventDefault();return}se(Y),O.get()||R()}),Xt(L,de=>de?qt(ut(Y,"mouseover",()=>{n.set(Y)}),ut(Y,"mouseleave",()=>{n.set(null)})):void 0))})}),Ze=et(b("group"),{returned:()=>Y=>({role:"group","aria-labelledby":Y})}),yt=et(b("group-label"),{returned:()=>Y=>({id:Y})}),$=Pl({value:pt([l],([Y])=>{const Q=Array.isArray(Y)?Y.map(de=>de.value):Y==null?void 0:Y.value;return typeof Q=="string"?Q:JSON.stringify(Q)}),name:br(D),required:I,prefix:e.builder}),Fe=et(b("arrow"),{stores:A,returned:Y=>({"data-arrow":!0,style:On({position:"absolute",width:`var(--arrow-size, ${Y}px)`,height:`var(--arrow-size, ${Y}px)`})})});return Dl(()=>{if(!yn)return;const Y=document.getElementById(H.menu.get()),Q=document.getElementById(H.trigger.get());if(Q&&t.set(Q),!Y)return;const de=Y.querySelector("[data-selected]");rt(de)}),Xt([n],([Y])=>{if(!yn)return;const Q=document.getElementById(H.menu.get());rt(Q)&&wn(Q).forEach(de=>{de===Y?Cr(de):Tr(de)})}),Xt([o],([Y])=>{if(!yn)return;let Q=vn;return h.get()&&Y&&(Q=Er()),()=>{Q()}}),{ids:H,elements:{trigger:ne,group:Ze,option:He,menu:x,groupLabel:yt,label:Ce,hiddenInput:$,arrow:Fe},states:{open:o,selected:l,highlighted:r,highlightedItem:n},helpers:{isSelected:ee,isHighlighted:W,closeMenu:R},options:c}}function jr(s){const e=Vr({...s,builder:"select"}),t=pt(e.states.selected,n=>Array.isArray(n)?n.map(i=>i.label).join(", "):(n==null?void 0:n.label)??"");return{...e,elements:{...e.elements},states:{...e.states,selectedLabel:t}}}const Fr={defaultChecked:!1,disabled:!1,required:!1,name:"",value:""},{name:us}=Ai("switch");function qr(s){const e={...Fr,...s},t=An(Ni(e,"checked")),{disabled:n,required:i,name:l,value:r}=t,a=e.checked??Bt(e.defaultChecked),o=Nn(a,e==null?void 0:e.onCheckedChange);function c(){n.get()||o.update(d=>!d)}const u=et(us(),{stores:[o,n,i],returned:([d,m,h])=>({"data-disabled":Ht(m),disabled:Ht(m),"data-state":d?"checked":"unchecked",type:"button",role:"switch","aria-checked":d?"true":"false","aria-required":h?"true":void 0}),action(d){return{destroy:qt(ut(d,"click",()=>{c()}),ut(d,"keydown",h=>{h.key!==me.ENTER&&h.key!==me.SPACE||(h.preventDefault(),c())}))}}}),f=et(us("input"),{stores:[o,l,i,n,r],returned:([d,m,h,_,N])=>({type:"checkbox","aria-hidden":!0,hidden:!0,tabindex:-1,name:m,value:N,checked:d,required:h,disabled:Ht(_),style:On({position:"absolute",opacity:0,"pointer-events":"none",margin:0,transform:"translateX(-100%)"})})});return{elements:{root:u,input:f},states:{checked:o},options:t}}function cn(){return{NAME:"select",GROUP_NAME:"select-group",ITEM_NAME:"select-item",PARTS:["arrow","content","group","item","indicator","input","label","trigger","value"]}}function Pt(){const{NAME:s}=cn();return Vn(s)}function Br(s){const{NAME:e,PARTS:t}=cn(),n=Oi(e,t),i={...jr({...Li(s),forceVisible:!0}),getAttrs:n};return Mn(e,i),{...i,updateOption:Pi(i.options)}}function Hr(s){const{ITEM_NAME:e}=cn(),t=Pt();return Mn(e,s),t}function Rr(){const{ITEM_NAME:s}=cn(),{helpers:{isSelected:e},getAttrs:t}=Pt();return{value:Vn(s),isSelected:e,getAttrs:t}}function Kr(s){const t={...{side:"bottom",align:"center",sameWidth:!0},...s},{options:{positioning:n}}=Pt();Bl(n)(t)}const Ur=s=>({ids:s&1}),fs=s=>({ids:s[0]});function Gr(s){let e;const t=s[19].default,n=Oe(t,s,s[18],fs);return{c(){n&&n.c()},l(i){n&&n.l(i)},m(i,l){n&&n.m(i,l),e=!0},p(i,[l]){n&&n.p&&(!e||l&262145)&&Le(n,t,i,i[18],e?De(t,i[18],l,Ur):Pe(i[18]),fs)},i(i){e||(T(n,i),e=!0)},o(i){P(n,i),e=!1},d(i){n&&n.d(i)}}}function Yr(s,e,t){let n,{$$slots:i={},$$scope:l}=e,{required:r=void 0}=e,{disabled:a=void 0}=e,{preventScroll:o=void 0}=e,{loop:c=void 0}=e,{closeOnEscape:u=void 0}=e,{closeOnOutsideClick:f=void 0}=e,{portal:d=void 0}=e,{name:m=void 0}=e,{multiple:h=!1}=e,{selected:_=void 0}=e,{onSelectedChange:N=void 0}=e,{open:S=void 0}=e,{onOpenChange:O=void 0}=e,{items:A=[]}=e,{onOutsideClick:v=void 0}=e,{typeahead:I=void 0}=e;const{states:{open:C,selected:D},updateOption:L,ids:V}=Br({required:r,disabled:a,preventScroll:o,loop:c,closeOnEscape:u,closeOnOutsideClick:f,portal:d,name:m,onOutsideClick:v,multiple:h,forceVisible:!0,defaultSelected:Array.isArray(_)?[..._]:_,defaultOpen:S,onSelectedChange:({next:y})=>Array.isArray(y)?((!Array.isArray(_)||!Sr(_,y))&&(N==null||N(y),t(2,_=y)),y):(_!==y&&(N==null||N(y),t(2,_=y)),y),onOpenChange:({next:y})=>(S!==y&&(O==null||O(y),t(3,S=y)),y),items:A,typeahead:I}),b=pt([V.menu,V.trigger,V.label],([y,H,U])=>({menu:y,trigger:H,label:U}));return qe(s,b,y=>t(0,n=y)),s.$$set=y=>{"required"in y&&t(4,r=y.required),"disabled"in y&&t(5,a=y.disabled),"preventScroll"in y&&t(6,o=y.preventScroll),"loop"in y&&t(7,c=y.loop),"closeOnEscape"in y&&t(8,u=y.closeOnEscape),"closeOnOutsideClick"in y&&t(9,f=y.closeOnOutsideClick),"portal"in y&&t(10,d=y.portal),"name"in y&&t(11,m=y.name),"multiple"in y&&t(12,h=y.multiple),"selected"in y&&t(2,_=y.selected),"onSelectedChange"in y&&t(13,N=y.onSelectedChange),"open"in y&&t(3,S=y.open),"onOpenChange"in y&&t(14,O=y.onOpenChange),"items"in y&&t(15,A=y.items),"onOutsideClick"in y&&t(16,v=y.onOutsideClick),"typeahead"in y&&t(17,I=y.typeahead),"$$scope"in y&&t(18,l=y.$$scope)},s.$$.update=()=>{s.$$.dirty&8&&S!==void 0&&C.set(S),s.$$.dirty&4&&_!==void 0&&D.set(Array.isArray(_)?[..._]:_),s.$$.dirty&16&&L("required",r),s.$$.dirty&32&&L("disabled",a),s.$$.dirty&64&&L("preventScroll",o),s.$$.dirty&128&&L("loop",c),s.$$.dirty&256&&L("closeOnEscape",u),s.$$.dirty&512&&L("closeOnOutsideClick",f),s.$$.dirty&1024&&L("portal",d),s.$$.dirty&2048&&L("name",m),s.$$.dirty&4096&&L("multiple",h),s.$$.dirty&65536&&L("onOutsideClick",v),s.$$.dirty&131072&&L("typeahead",I)},[n,b,_,S,r,a,o,c,u,f,d,m,h,N,O,A,v,I,l,i]}class Wr extends be{constructor(e){super(),ve(this,e,Yr,Gr,_e,{required:4,disabled:5,preventScroll:6,loop:7,closeOnEscape:8,closeOnOutsideClick:9,portal:10,name:11,multiple:12,selected:2,onSelectedChange:13,open:3,onOpenChange:14,items:15,onOutsideClick:16,typeahead:17})}}const Jr=s=>({builder:s[0]&256}),ds=s=>({builder:s[8]}),zr=s=>({builder:s[0]&256}),ms=s=>({builder:s[8]}),Xr=s=>({builder:s[0]&256}),hs=s=>({builder:s[8]}),Zr=s=>({builder:s[0]&256}),ps=s=>({builder:s[8]}),Qr=s=>({builder:s[0]&256}),gs=s=>({builder:s[8]}),$r=s=>({builder:s[0]&256}),_s=s=>({builder:s[8]});function xr(s){let e,t,n,i;const l=s[28].default,r=Oe(l,s,s[27],ds);let a=[s[8],s[13]],o={};for(let c=0;c<a.length;c+=1)o=ae(o,a[c]);return{c(){e=w("div"),r&&r.c(),this.h()},l(c){e=k(c,"DIV",{});var u=M(e);r&&r.l(u),u.forEach(p),this.h()},h(){Ie(e,o)},m(c,u){j(c,e,u),r&&r.m(e,null),s[38](e),t=!0,n||(i=[Qe(s[8].action(e)),ce(e,"m-pointerleave",s[12]),ce(e,"keydown",s[33])],n=!0)},p(c,u){r&&r.p&&(!t||u[0]&134217984)&&Le(r,l,c,c[27],t?De(l,c[27],u,Jr):Pe(c[27]),ds),Ie(e,o=Ve(a,[u[0]&256&&c[8],u[0]&8192&&c[13]]))},i(c){t||(T(r,c),t=!0)},o(c){P(r,c),t=!1},d(c){c&&p(e),r&&r.d(c),s[38](null),n=!1,Je(i)}}}function eo(s){let e,t,n,i,l;const r=s[28].default,a=Oe(r,s,s[27],ms);let o=[s[8],s[13]],c={};for(let u=0;u<o.length;u+=1)c=ae(c,o[u]);return{c(){e=w("div"),a&&a.c(),this.h()},l(u){e=k(u,"DIV",{});var f=M(e);a&&a.l(f),f.forEach(p),this.h()},h(){Ie(e,c)},m(u,f){j(u,e,f),a&&a.m(e,null),s[37](e),n=!0,i||(l=[Qe(s[8].action(e)),ce(e,"m-pointerleave",s[12]),ce(e,"keydown",s[32])],i=!0)},p(u,f){s=u,a&&a.p&&(!n||f[0]&134217984)&&Le(a,r,s,s[27],n?De(r,s[27],f,zr):Pe(s[27]),ms),Ie(e,c=Ve(o,[f[0]&256&&s[8],f[0]&8192&&s[13]]))},i(u){n||(T(a,u),t&&t.end(1),n=!0)},o(u){P(a,u),u&&(t=Si(e,s[5],s[6])),n=!1},d(u){u&&p(e),a&&a.d(u),s[37](null),u&&t&&t.end(),i=!1,Je(l)}}}function to(s){let e,t,n,i,l;const r=s[28].default,a=Oe(r,s,s[27],hs);let o=[s[8],s[13]],c={};for(let u=0;u<o.length;u+=1)c=ae(c,o[u]);return{c(){e=w("div"),a&&a.c(),this.h()},l(u){e=k(u,"DIV",{});var f=M(e);a&&a.l(f),f.forEach(p),this.h()},h(){Ie(e,c)},m(u,f){j(u,e,f),a&&a.m(e,null),s[36](e),n=!0,i||(l=[Qe(s[8].action(e)),ce(e,"m-pointerleave",s[12]),ce(e,"keydown",s[31])],i=!0)},p(u,f){s=u,a&&a.p&&(!n||f[0]&134217984)&&Le(a,r,s,s[27],n?De(r,s[27],f,Xr):Pe(s[27]),hs),Ie(e,c=Ve(o,[f[0]&256&&s[8],f[0]&8192&&s[13]]))},i(u){n||(T(a,u),u&&(t||ke(()=>{t=Ae(e,s[3],s[4]),t.start()})),n=!0)},o(u){P(a,u),n=!1},d(u){u&&p(e),a&&a.d(u),s[36](null),i=!1,Je(l)}}}function no(s){let e,t,n,i,l,r;const a=s[28].default,o=Oe(a,s,s[27],ps);let c=[s[8],s[13]],u={};for(let f=0;f<c.length;f+=1)u=ae(u,c[f]);return{c(){e=w("div"),o&&o.c(),this.h()},l(f){e=k(f,"DIV",{});var d=M(e);o&&o.l(d),d.forEach(p),this.h()},h(){Ie(e,u)},m(f,d){j(f,e,d),o&&o.m(e,null),s[35](e),i=!0,l||(r=[Qe(s[8].action(e)),ce(e,"m-pointerleave",s[12]),ce(e,"keydown",s[30])],l=!0)},p(f,d){s=f,o&&o.p&&(!i||d[0]&134217984)&&Le(o,a,s,s[27],i?De(a,s[27],d,Zr):Pe(s[27]),ps),Ie(e,u=Ve(c,[d[0]&256&&s[8],d[0]&8192&&s[13]]))},i(f){i||(T(o,f),f&&ke(()=>{i&&(n&&n.end(1),t=Ae(e,s[3],s[4]),t.start())}),i=!0)},o(f){P(o,f),t&&t.invalidate(),f&&(n=Si(e,s[5],s[6])),i=!1},d(f){f&&p(e),o&&o.d(f),s[35](null),f&&n&&n.end(),l=!1,Je(r)}}}function so(s){let e,t,n,i,l;const r=s[28].default,a=Oe(r,s,s[27],gs);let o=[s[8],s[13]],c={};for(let u=0;u<o.length;u+=1)c=ae(c,o[u]);return{c(){e=w("div"),a&&a.c(),this.h()},l(u){e=k(u,"DIV",{});var f=M(e);a&&a.l(f),f.forEach(p),this.h()},h(){Ie(e,c)},m(u,f){j(u,e,f),a&&a.m(e,null),s[34](e),n=!0,i||(l=[Qe(s[8].action(e)),ce(e,"m-pointerleave",s[12]),ce(e,"keydown",s[29])],i=!0)},p(u,f){s=u,a&&a.p&&(!n||f[0]&134217984)&&Le(a,r,s,s[27],n?De(r,s[27],f,Qr):Pe(s[27]),gs),Ie(e,c=Ve(o,[f[0]&256&&s[8],f[0]&8192&&s[13]]))},i(u){n||(T(a,u),u&&ke(()=>{n&&(t||(t=nt(e,s[1],s[2],!0)),t.run(1))}),n=!0)},o(u){P(a,u),u&&(t||(t=nt(e,s[1],s[2],!1)),t.run(0)),n=!1},d(u){u&&p(e),a&&a.d(u),s[34](null),u&&t&&t.end(),i=!1,Je(l)}}}function io(s){let e;const t=s[28].default,n=Oe(t,s,s[27],_s);return{c(){n&&n.c()},l(i){n&&n.l(i)},m(i,l){n&&n.m(i,l),e=!0},p(i,l){n&&n.p&&(!e||l[0]&134217984)&&Le(n,t,i,i[27],e?De(t,i[27],l,$r):Pe(i[27]),_s)},i(i){e||(T(n,i),e=!0)},o(i){P(n,i),e=!1},d(i){n&&n.d(i)}}}function lo(s){let e,t,n,i;const l=[io,so,no,to,eo,xr],r=[];function a(o,c){return o[7]&&o[9]?0:o[1]&&o[9]?1:o[3]&&o[5]&&o[9]?2:o[3]&&o[9]?3:o[5]&&o[9]?4:o[9]?5:-1}return~(e=a(s))&&(t=r[e]=l[e](s)),{c(){t&&t.c(),n=fe()},l(o){t&&t.l(o),n=fe()},m(o,c){~e&&r[e].m(o,c),j(o,n,c),i=!0},p(o,c){let u=e;e=a(o),e===u?~e&&r[e].p(o,c):(t&&(pe(),P(r[u],1,1,()=>{r[u]=null}),ge()),~e?(t=r[e],t?t.p(o,c):(t=r[e]=l[e](o),t.c()),T(t,1),t.m(n.parentNode,n)):t=null)},i(o){i||(T(t),i=!0)},o(o){P(t),i=!1},d(o){o&&p(n),~e&&r[e].d(o)}}}function ro(s,e,t){let n;const i=["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"];let l=Se(e,i),r,a,{$$slots:o={},$$scope:c}=e,{transition:u=void 0}=e,{transitionConfig:f=void 0}=e,{inTransition:d=void 0}=e,{inTransitionConfig:m=void 0}=e,{outTransition:h=void 0}=e,{outTransitionConfig:_=void 0}=e,{asChild:N=!1}=e,{id:S=void 0}=e,{side:O="bottom"}=e,{align:A="center"}=e,{sideOffset:v=0}=e,{alignOffset:I=0}=e,{collisionPadding:C=8}=e,{avoidCollisions:D=!0}=e,{collisionBoundary:L=void 0}=e,{sameWidth:V=!0}=e,{fitViewport:b=!1}=e,{strategy:y="absolute"}=e,{overlap:H=!1}=e,{el:U=void 0}=e;const{elements:{menu:K},states:{open:G},ids:se,getAttrs:F}=Pt();qe(s,K,$=>t(26,a=$)),qe(s,G,$=>t(9,r=$));const R=rn(),te=F("content");function ee($){Ke.call(this,s,$)}function W($){Ke.call(this,s,$)}function ne($){Ke.call(this,s,$)}function x($){Ke.call(this,s,$)}function he($){Ke.call(this,s,$)}function ue($){Ee[$?"unshift":"push"](()=>{U=$,t(0,U)})}function Ce($){Ee[$?"unshift":"push"](()=>{U=$,t(0,U)})}function He($){Ee[$?"unshift":"push"](()=>{U=$,t(0,U)})}function Ze($){Ee[$?"unshift":"push"](()=>{U=$,t(0,U)})}function yt($){Ee[$?"unshift":"push"](()=>{U=$,t(0,U)})}return s.$$set=$=>{e=ae(ae({},e),Ye($)),t(13,l=Se(e,i)),"transition"in $&&t(1,u=$.transition),"transitionConfig"in $&&t(2,f=$.transitionConfig),"inTransition"in $&&t(3,d=$.inTransition),"inTransitionConfig"in $&&t(4,m=$.inTransitionConfig),"outTransition"in $&&t(5,h=$.outTransition),"outTransitionConfig"in $&&t(6,_=$.outTransitionConfig),"asChild"in $&&t(7,N=$.asChild),"id"in $&&t(14,S=$.id),"side"in $&&t(15,O=$.side),"align"in $&&t(16,A=$.align),"sideOffset"in $&&t(17,v=$.sideOffset),"alignOffset"in $&&t(18,I=$.alignOffset),"collisionPadding"in $&&t(19,C=$.collisionPadding),"avoidCollisions"in $&&t(20,D=$.avoidCollisions),"collisionBoundary"in $&&t(21,L=$.collisionBoundary),"sameWidth"in $&&t(22,V=$.sameWidth),"fitViewport"in $&&t(23,b=$.fitViewport),"strategy"in $&&t(24,y=$.strategy),"overlap"in $&&t(25,H=$.overlap),"el"in $&&t(0,U=$.el),"$$scope"in $&&t(27,c=$.$$scope)},s.$$.update=()=>{s.$$.dirty[0]&16384&&S&&se.menu.set(S),s.$$.dirty[0]&67108864&&t(8,n=a),s.$$.dirty[0]&256&&Object.assign(n,te),s.$$.dirty[0]&67076608&&r&&Kr({side:O,align:A,sideOffset:v,alignOffset:I,collisionPadding:C,avoidCollisions:D,collisionBoundary:L,sameWidth:V,fitViewport:b,strategy:y,overlap:H})},[U,u,f,d,m,h,_,N,n,r,K,G,R,l,S,O,A,v,I,C,D,L,V,b,y,H,a,c,o,ee,W,ne,x,he,ue,Ce,He,Ze,yt]}let oo=class extends be{constructor(e){super(),ve(this,e,ro,lo,_e,{transition:1,transitionConfig:2,inTransition:3,inTransitionConfig:4,outTransition:5,outTransitionConfig:6,asChild:7,id:14,side:15,align:16,sideOffset:17,alignOffset:18,collisionPadding:19,avoidCollisions:20,collisionBoundary:21,sameWidth:22,fitViewport:23,strategy:24,overlap:25,el:0},null,[-1,-1])}};const ao=s=>({builder:s&4}),bs=s=>({builder:s[2]});function co(s){let e,t,n,i=[s[2],s[5]],l={};for(let r=0;r<i.length;r+=1)l=ae(l,i[r]);return{c(){e=w("input"),this.h()},l(r){e=k(r,"INPUT",{}),this.h()},h(){Ie(e,l)},m(r,a){j(r,e,a),e.autofocus&&e.focus(),s[11](e),t||(n=Qe(s[2].action(e)),t=!0)},p(r,a){Ie(e,l=Ve(i,[a&4&&r[2],a&32&&r[5]]))},i:re,o:re,d(r){r&&p(e),s[11](null),t=!1,n()}}}function uo(s){let e;const t=s[10].default,n=Oe(t,s,s[9],bs);return{c(){n&&n.c()},l(i){n&&n.l(i)},m(i,l){n&&n.m(i,l),e=!0},p(i,l){n&&n.p&&(!e||l&516)&&Le(n,t,i,i[9],e?De(t,i[9],l,ao):Pe(i[9]),bs)},i(i){e||(T(n,i),e=!0)},o(i){P(n,i),e=!1},d(i){n&&n.d(i)}}}function fo(s){let e,t,n,i;const l=[uo,co],r=[];function a(o,c){return o[1]?0:1}return e=a(s),t=r[e]=l[e](s),{c(){t.c(),n=fe()},l(o){t.l(o),n=fe()},m(o,c){r[e].m(o,c),j(o,n,c),i=!0},p(o,[c]){let u=e;e=a(o),e===u?r[e].p(o,c):(pe(),P(r[u],1,1,()=>{r[u]=null}),ge(),t=r[e],t?t.p(o,c):(t=r[e]=l[e](o),t.c()),T(t,1),t.m(n.parentNode,n))},i(o){i||(T(t),i=!0)},o(o){P(t),i=!1},d(o){o&&p(n),r[e].d(o)}}}function mo(s,e,t){let n,i;const l=["asChild","el"];let r=Se(e,l),a,o,{$$slots:c={},$$scope:u}=e,{asChild:f=!1}=e,{el:d=void 0}=e;const{elements:{hiddenInput:m},options:{disabled:h},getAttrs:_}=Pt();qe(s,m,S=>t(7,a=S)),qe(s,h,S=>t(8,o=S));function N(S){Ee[S?"unshift":"push"](()=>{d=S,t(0,d)})}return s.$$set=S=>{e=ae(ae({},e),Ye(S)),t(5,r=Se(e,l)),"asChild"in S&&t(1,f=S.asChild),"el"in S&&t(0,d=S.el),"$$scope"in S&&t(9,u=S.$$scope)},s.$$.update=()=>{s.$$.dirty&256&&t(6,n={..._("input"),disabled:o?!0:void 0}),s.$$.dirty&128&&t(2,i=a),s.$$.dirty&68&&Object.assign(i,n)},[d,f,i,m,h,r,n,a,o,u,c,N]}class ho extends be{constructor(e){super(),ve(this,e,mo,fo,_e,{asChild:1,el:0})}}const po=s=>({builder:s&16,isSelected:s&32}),vs=s=>({builder:s[4],isSelected:s[5]}),go=s=>({builder:s&16,isSelected:s&32}),ys=s=>({builder:s[4],isSelected:s[5]});function _o(s){let e,t,n,i;const l=s[14].default,r=Oe(l,s,s[13],vs),a=r||vo(s);let o=[s[4],s[9]],c={};for(let u=0;u<o.length;u+=1)c=ae(c,o[u]);return{c(){e=w("div"),a&&a.c(),this.h()},l(u){e=k(u,"DIV",{});var f=M(e);a&&a.l(f),f.forEach(p),this.h()},h(){Ie(e,c)},m(u,f){j(u,e,f),a&&a.m(e,null),s[19](e),t=!0,n||(i=[Qe(s[4].action(e)),ce(e,"m-click",s[8]),ce(e,"m-pointermove",s[8]),ce(e,"focusin",s[15]),ce(e,"keydown",s[16]),ce(e,"focusout",s[17]),ce(e,"pointerleave",s[18])],n=!0)},p(u,f){r?r.p&&(!t||f&8240)&&Le(r,l,u,u[13],t?De(l,u[13],f,po):Pe(u[13]),vs):a&&a.p&&(!t||f&6)&&a.p(u,t?f:-1),Ie(e,c=Ve(o,[f&16&&u[4],f&512&&u[9]]))},i(u){t||(T(a,u),t=!0)},o(u){P(a,u),t=!1},d(u){u&&p(e),a&&a.d(u),s[19](null),n=!1,Je(i)}}}function bo(s){let e;const t=s[14].default,n=Oe(t,s,s[13],ys);return{c(){n&&n.c()},l(i){n&&n.l(i)},m(i,l){n&&n.m(i,l),e=!0},p(i,l){n&&n.p&&(!e||l&8240)&&Le(n,t,i,i[13],e?De(t,i[13],l,go):Pe(i[13]),ys)},i(i){e||(T(n,i),e=!0)},o(i){P(n,i),e=!1},d(i){n&&n.d(i)}}}function vo(s){let e=(s[2]||s[1])+"",t;return{c(){t=le(e)},l(n){t=ie(n,e)},m(n,i){j(n,t,i)},p(n,i){i&6&&e!==(e=(n[2]||n[1])+"")&&ye(t,e)},d(n){n&&p(t)}}}function yo(s){let e,t,n,i;const l=[bo,_o],r=[];function a(o,c){return o[3]?0:1}return e=a(s),t=r[e]=l[e](s),{c(){t.c(),n=fe()},l(o){t.l(o),n=fe()},m(o,c){r[e].m(o,c),j(o,n,c),i=!0},p(o,[c]){let u=e;e=a(o),e===u?r[e].p(o,c):(pe(),P(r[u],1,1,()=>{r[u]=null}),ge(),t=r[e],t?t.p(o,c):(t=r[e]=l[e](o),t.c()),T(t,1),t.m(n.parentNode,n))},i(o){i||(T(t),i=!0)},o(o){P(t),i=!1},d(o){o&&p(n),r[e].d(o)}}}function ko(s,e,t){let n,i;const l=["value","disabled","label","asChild","el"];let r=Se(e,l),a,o,{$$slots:c={},$$scope:u}=e,{value:f}=e,{disabled:d=void 0}=e,{label:m=void 0}=e,{asChild:h=!1}=e,{el:_=void 0}=e;const{elements:{option:N},helpers:{isSelected:S},getAttrs:O}=Hr(f);qe(s,N,b=>t(12,o=b)),qe(s,S,b=>t(11,a=b));const A=rn(),v=O("item");function I(b){Ke.call(this,s,b)}function C(b){Ke.call(this,s,b)}function D(b){Ke.call(this,s,b)}function L(b){Ke.call(this,s,b)}function V(b){Ee[b?"unshift":"push"](()=>{_=b,t(0,_)})}return s.$$set=b=>{e=ae(ae({},e),Ye(b)),t(9,r=Se(e,l)),"value"in b&&t(1,f=b.value),"disabled"in b&&t(10,d=b.disabled),"label"in b&&t(2,m=b.label),"asChild"in b&&t(3,h=b.asChild),"el"in b&&t(0,_=b.el),"$$scope"in b&&t(13,u=b.$$scope)},s.$$.update=()=>{s.$$.dirty&5126&&t(4,n=o({value:f,disabled:d,label:m})),s.$$.dirty&16&&Object.assign(n,v),s.$$.dirty&2050&&t(5,i=a(f))},[_,f,m,h,n,i,N,S,A,r,d,a,o,u,c,I,C,D,L,V]}let wo=class extends be{constructor(e){super(),ve(this,e,ko,yo,_e,{value:1,disabled:10,label:2,asChild:3,el:0})}};const Co=s=>({isSelected:s&4}),ks=s=>({attrs:s[5],isSelected:s[2](s[4])}),To=s=>({isSelected:s&4}),ws=s=>({attrs:s[5],isSelected:s[2](s[4])});function Eo(s){let e,t=s[2](s[4]),n,i=t&&Cs(s),l=[s[6],s[5]],r={};for(let a=0;a<l.length;a+=1)r=ae(r,l[a]);return{c(){e=w("div"),i&&i.c(),this.h()},l(a){e=k(a,"DIV",{});var o=M(e);i&&i.l(o),o.forEach(p),this.h()},h(){Ie(e,r)},m(a,o){j(a,e,o),i&&i.m(e,null),s[9](e),n=!0},p(a,o){o&4&&(t=a[2](a[4])),t?i?(i.p(a,o),o&4&&T(i,1)):(i=Cs(a),i.c(),T(i,1),i.m(e,null)):i&&(pe(),P(i,1,1,()=>{i=null}),ge()),Ie(e,r=Ve(l,[o&64&&a[6],a[5]]))},i(a){n||(T(i),n=!0)},o(a){P(i),n=!1},d(a){a&&p(e),i&&i.d(),s[9](null)}}}function So(s){let e;const t=s[8].default,n=Oe(t,s,s[7],ws);return{c(){n&&n.c()},l(i){n&&n.l(i)},m(i,l){n&&n.m(i,l),e=!0},p(i,l){n&&n.p&&(!e||l&132)&&Le(n,t,i,i[7],e?De(t,i[7],l,To):Pe(i[7]),ws)},i(i){e||(T(n,i),e=!0)},o(i){P(n,i),e=!1},d(i){n&&n.d(i)}}}function Cs(s){let e;const t=s[8].default,n=Oe(t,s,s[7],ks);return{c(){n&&n.c()},l(i){n&&n.l(i)},m(i,l){n&&n.m(i,l),e=!0},p(i,l){n&&n.p&&(!e||l&132)&&Le(n,t,i,i[7],e?De(t,i[7],l,Co):Pe(i[7]),ks)},i(i){e||(T(n,i),e=!0)},o(i){P(n,i),e=!1},d(i){n&&n.d(i)}}}function Io(s){let e,t,n,i;const l=[So,Eo],r=[];function a(o,c){return o[1]?0:1}return e=a(s),t=r[e]=l[e](s),{c(){t.c(),n=fe()},l(o){t.l(o),n=fe()},m(o,c){r[e].m(o,c),j(o,n,c),i=!0},p(o,[c]){let u=e;e=a(o),e===u?r[e].p(o,c):(pe(),P(r[u],1,1,()=>{r[u]=null}),ge(),t=r[e],t?t.p(o,c):(t=r[e]=l[e](o),t.c()),T(t,1),t.m(n.parentNode,n))},i(o){i||(T(t),i=!0)},o(o){P(t),i=!1},d(o){o&&p(n),r[e].d(o)}}}function No(s,e,t){const n=["asChild","el"];let i=Se(e,n),l,{$$slots:r={},$$scope:a}=e,{asChild:o=!1}=e,{el:c=void 0}=e;const{isSelected:u,value:f,getAttrs:d}=Rr();qe(s,u,_=>t(2,l=_));const m=d("indicator");function h(_){Ee[_?"unshift":"push"](()=>{c=_,t(0,c)})}return s.$$set=_=>{e=ae(ae({},e),Ye(_)),t(6,i=Se(e,n)),"asChild"in _&&t(1,o=_.asChild),"el"in _&&t(0,c=_.el),"$$scope"in _&&t(7,a=_.$$scope)},[c,o,l,u,f,m,i,a,r,h]}class Ao extends be{constructor(e){super(),ve(this,e,No,Io,_e,{asChild:1,el:0})}}const Oo=s=>({builder:s&4}),Ts=s=>({builder:s[2]}),Lo=s=>({builder:s&4}),Es=s=>({builder:s[2]});function Po(s){let e,t,n,i;const l=s[9].default,r=Oe(l,s,s[8],Ts);let a=[s[2],{type:"button"},s[5]],o={};for(let c=0;c<a.length;c+=1)o=ae(o,a[c]);return{c(){e=w("button"),r&&r.c(),this.h()},l(c){e=k(c,"BUTTON",{type:!0});var u=M(e);r&&r.l(u),u.forEach(p),this.h()},h(){Ie(e,o)},m(c,u){j(c,e,u),r&&r.m(e,null),e.autofocus&&e.focus(),s[10](e),t=!0,n||(i=[Qe(s[2].action(e)),ce(e,"m-click",s[4]),ce(e,"m-keydown",s[4])],n=!0)},p(c,u){r&&r.p&&(!t||u&260)&&Le(r,l,c,c[8],t?De(l,c[8],u,Oo):Pe(c[8]),Ts),Ie(e,o=Ve(a,[u&4&&c[2],{type:"button"},u&32&&c[5]]))},i(c){t||(T(r,c),t=!0)},o(c){P(r,c),t=!1},d(c){c&&p(e),r&&r.d(c),s[10](null),n=!1,Je(i)}}}function Do(s){let e;const t=s[9].default,n=Oe(t,s,s[8],Es);return{c(){n&&n.c()},l(i){n&&n.l(i)},m(i,l){n&&n.m(i,l),e=!0},p(i,l){n&&n.p&&(!e||l&260)&&Le(n,t,i,i[8],e?De(t,i[8],l,Lo):Pe(i[8]),Es)},i(i){e||(T(n,i),e=!0)},o(i){P(n,i),e=!1},d(i){n&&n.d(i)}}}function Mo(s){let e,t,n,i;const l=[Do,Po],r=[];function a(o,c){return o[1]?0:1}return e=a(s),t=r[e]=l[e](s),{c(){t.c(),n=fe()},l(o){t.l(o),n=fe()},m(o,c){r[e].m(o,c),j(o,n,c),i=!0},p(o,[c]){let u=e;e=a(o),e===u?r[e].p(o,c):(pe(),P(r[u],1,1,()=>{r[u]=null}),ge(),t=r[e],t?t.p(o,c):(t=r[e]=l[e](o),t.c()),T(t,1),t.m(n.parentNode,n))},i(o){i||(T(t),i=!0)},o(o){P(t),i=!1},d(o){o&&p(n),r[e].d(o)}}}function Vo(s,e,t){let n;const i=["asChild","id","el"];let l=Se(e,i),r,{$$slots:a={},$$scope:o}=e,{asChild:c=!1}=e,{id:u=void 0}=e,{el:f=void 0}=e;const{elements:{trigger:d},ids:m,getAttrs:h}=Pt();qe(s,d,O=>t(7,r=O));const _=rn(),N=h("trigger");function S(O){Ee[O?"unshift":"push"](()=>{f=O,t(0,f)})}return s.$$set=O=>{e=ae(ae({},e),Ye(O)),t(5,l=Se(e,i)),"asChild"in O&&t(1,c=O.asChild),"id"in O&&t(6,u=O.id),"el"in O&&t(0,f=O.el),"$$scope"in O&&t(8,o=O.$$scope)},s.$$.update=()=>{s.$$.dirty&64&&u&&m.trigger.set(u),s.$$.dirty&128&&t(2,n=r),s.$$.dirty&4&&Object.assign(n,N)},[f,c,n,d,_,l,u,r,o,a,S]}let jo=class extends be{constructor(e){super(),ve(this,e,Vo,Mo,_e,{asChild:1,id:6,el:0})}};function Bi(){return{NAME:"switch",PARTS:["root","input","thumb"]}}function Fo(s){const{NAME:e,PARTS:t}=Bi(),n=Oi(e,t),i={...qr(Li(s)),getAttrs:n};return Mn(e,i),{...i,updateOption:Pi(i.options)}}function Hi(){const{NAME:s}=Bi();return Vn(s)}function qo(s){let e,t,n,i=[s[2],{name:s[3]},{disabled:s[4]},{required:s[5]},{value:s[1]},s[11]],l={};for(let r=0;r<i.length;r+=1)l=ae(l,i[r]);return{c(){e=w("input"),this.h()},l(r){e=k(r,"INPUT",{name:!0}),this.h()},h(){Ie(e,l)},m(r,a){j(r,e,a),"value"in l&&(e.value=l.value),e.autofocus&&e.focus(),s[13](e),t||(n=Qe(s[2].action(e)),t=!0)},p(r,[a]){Ie(e,l=Ve(i,[a&4&&r[2],a&8&&{name:r[3]},a&16&&{disabled:r[4]},a&32&&{required:r[5]},a&2&&e.value!==r[1]&&{value:r[1]},a&2048&&r[11]])),"value"in l&&(e.value=l.value)},i:re,o:re,d(r){r&&p(e),s[13](null),t=!1,n()}}}function Bo(s,e,t){let n;const i=["el"];let l=Se(e,i),r,a,o,c,u,{el:f=void 0}=e;const{elements:{input:d},options:{value:m,name:h,disabled:_,required:N}}=Hi();qe(s,d,O=>t(2,a=O)),qe(s,m,O=>t(12,r=O)),qe(s,h,O=>t(3,o=O)),qe(s,_,O=>t(4,c=O)),qe(s,N,O=>t(5,u=O));function S(O){Ee[O?"unshift":"push"](()=>{f=O,t(0,f)})}return s.$$set=O=>{e=ae(ae({},e),Ye(O)),t(11,l=Se(e,i)),"el"in O&&t(0,f=O.el)},s.$$.update=()=>{s.$$.dirty&4096&&t(1,n=r===void 0||r===""?"on":r)},[f,n,a,o,c,u,d,m,h,_,N,l,r,S]}class Ho extends be{constructor(e){super(),ve(this,e,Bo,qo,_e,{el:0})}}const Ro=s=>({builder:s&16}),Ss=s=>({builder:s[4]}),Ko=s=>({builder:s&16}),Is=s=>({builder:s[4]});function Uo(s){let e,t,n,i;const l=s[17].default,r=Oe(l,s,s[16],Ss);let a=[s[4],{type:"button"},s[7]],o={};for(let c=0;c<a.length;c+=1)o=ae(o,a[c]);return{c(){e=w("button"),r&&r.c(),this.h()},l(c){e=k(c,"BUTTON",{type:!0});var u=M(e);r&&r.l(u),u.forEach(p),this.h()},h(){Ie(e,o)},m(c,u){j(c,e,u),r&&r.m(e,null),e.autofocus&&e.focus(),s[18](e),t=!0,n||(i=[Qe(s[4].action(e)),ce(e,"m-click",s[6]),ce(e,"m-keydown",s[6])],n=!0)},p(c,u){r&&r.p&&(!t||u&65552)&&Le(r,l,c,c[16],t?De(l,c[16],u,Ro):Pe(c[16]),Ss),Ie(e,o=Ve(a,[u&16&&c[4],{type:"button"},u&128&&c[7]]))},i(c){t||(T(r,c),t=!0)},o(c){P(r,c),t=!1},d(c){c&&p(e),r&&r.d(c),s[18](null),n=!1,Je(i)}}}function Go(s){let e;const t=s[17].default,n=Oe(t,s,s[16],Is);return{c(){n&&n.c()},l(i){n&&n.l(i)},m(i,l){n&&n.m(i,l),e=!0},p(i,l){n&&n.p&&(!e||l&65552)&&Le(n,t,i,i[16],e?De(t,i[16],l,Ko):Pe(i[16]),Is)},i(i){e||(T(n,i),e=!0)},o(i){P(n,i),e=!1},d(i){n&&n.d(i)}}}function Ns(s){let e,t;const n=[s[3]];let i={};for(let l=0;l<n.length;l+=1)i=ae(i,n[l]);return e=new Ho({props:i}),{c(){Z(e.$$.fragment)},l(l){X(e.$$.fragment,l)},m(l,r){z(e,l,r),t=!0},p(l,r){const a=r&8?Ve(n,[Ut(l[3])]):{};e.$set(a)},i(l){t||(T(e.$$.fragment,l),t=!0)},o(l){P(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function Yo(s){let e,t,n,i,l;const r=[Go,Uo],a=[];function o(u,f){return u[2]?0:1}e=o(s),t=a[e]=r[e](s);let c=s[1]&&Ns(s);return{c(){t.c(),n=B(),c&&c.c(),i=fe()},l(u){t.l(u),n=q(u),c&&c.l(u),i=fe()},m(u,f){a[e].m(u,f),j(u,n,f),c&&c.m(u,f),j(u,i,f),l=!0},p(u,[f]){let d=e;e=o(u),e===d?a[e].p(u,f):(pe(),P(a[d],1,1,()=>{a[d]=null}),ge(),t=a[e],t?t.p(u,f):(t=a[e]=r[e](u),t.c()),T(t,1),t.m(n.parentNode,n)),u[1]?c?(c.p(u,f),f&2&&T(c,1)):(c=Ns(u),c.c(),T(c,1),c.m(i.parentNode,i)):c&&(pe(),P(c,1,1,()=>{c=null}),ge())},i(u){l||(T(t),T(c),l=!0)},o(u){P(t),P(c),l=!1},d(u){u&&(p(n),p(i)),a[e].d(u),c&&c.d(u)}}}function Wo(s,e,t){let n,i;const l=["checked","onCheckedChange","disabled","name","value","includeInput","required","asChild","inputAttrs","el"];let r=Se(e,l),a,{$$slots:o={},$$scope:c}=e,{checked:u=void 0}=e,{onCheckedChange:f=void 0}=e,{disabled:d=void 0}=e,{name:m=void 0}=e,{value:h=void 0}=e,{includeInput:_=!0}=e,{required:N=void 0}=e,{asChild:S=!1}=e,{inputAttrs:O=void 0}=e,{el:A=void 0}=e;const{elements:{root:v},states:{checked:I},updateOption:C,getAttrs:D}=Fo({disabled:d,name:m,value:h,required:N,defaultChecked:u,onCheckedChange:({next:b})=>(u!==b&&(f==null||f(b),t(8,u=b)),b)});qe(s,v,b=>t(15,a=b));const L=rn();function V(b){Ee[b?"unshift":"push"](()=>{A=b,t(0,A)})}return s.$$set=b=>{e=ae(ae({},e),Ye(b)),t(7,r=Se(e,l)),"checked"in b&&t(8,u=b.checked),"onCheckedChange"in b&&t(9,f=b.onCheckedChange),"disabled"in b&&t(10,d=b.disabled),"name"in b&&t(11,m=b.name),"value"in b&&t(12,h=b.value),"includeInput"in b&&t(1,_=b.includeInput),"required"in b&&t(13,N=b.required),"asChild"in b&&t(2,S=b.asChild),"inputAttrs"in b&&t(3,O=b.inputAttrs),"el"in b&&t(0,A=b.el),"$$scope"in b&&t(16,c=b.$$scope)},s.$$.update=()=>{s.$$.dirty&256&&u!==void 0&&I.set(u),s.$$.dirty&1024&&C("disabled",d),s.$$.dirty&2048&&C("name",m),s.$$.dirty&4096&&C("value",h),s.$$.dirty&8192&&C("required",N),s.$$.dirty&32768&&t(4,n=a),s.$$.dirty&256&&t(14,i={...D("root"),"data-checked":u?"":void 0}),s.$$.dirty&16400&&Object.assign(n,i)},[A,_,S,O,n,v,L,r,u,f,d,m,h,N,i,a,c,o,V]}class Jo extends be{constructor(e){super(),ve(this,e,Wo,Yo,_e,{checked:8,onCheckedChange:9,disabled:10,name:11,value:12,includeInput:1,required:13,asChild:2,inputAttrs:3,el:0})}}const zo=s=>({attrs:s&8,checked:s&4}),As=s=>({attrs:s[3],checked:s[2]});function Xo(s){let e,t=[s[5],s[3]],n={};for(let i=0;i<t.length;i+=1)n=ae(n,t[i]);return{c(){e=w("span"),this.h()},l(i){e=k(i,"SPAN",{}),M(e).forEach(p),this.h()},h(){Ie(e,n)},m(i,l){j(i,e,l),s[8](e)},p(i,l){Ie(e,n=Ve(t,[l&32&&i[5],l&8&&i[3]]))},i:re,o:re,d(i){i&&p(e),s[8](null)}}}function Zo(s){let e;const t=s[7].default,n=Oe(t,s,s[6],As);return{c(){n&&n.c()},l(i){n&&n.l(i)},m(i,l){n&&n.m(i,l),e=!0},p(i,l){n&&n.p&&(!e||l&76)&&Le(n,t,i,i[6],e?De(t,i[6],l,zo):Pe(i[6]),As)},i(i){e||(T(n,i),e=!0)},o(i){P(n,i),e=!1},d(i){n&&n.d(i)}}}function Qo(s){let e,t,n,i;const l=[Zo,Xo],r=[];function a(o,c){return o[1]?0:1}return e=a(s),t=r[e]=l[e](s),{c(){t.c(),n=fe()},l(o){t.l(o),n=fe()},m(o,c){r[e].m(o,c),j(o,n,c),i=!0},p(o,[c]){let u=e;e=a(o),e===u?r[e].p(o,c):(pe(),P(r[u],1,1,()=>{r[u]=null}),ge(),t=r[e],t?t.p(o,c):(t=r[e]=l[e](o),t.c()),T(t,1),t.m(n.parentNode,n))},i(o){i||(T(t),i=!0)},o(o){P(t),i=!1},d(o){o&&p(n),r[e].d(o)}}}function $o(s,e,t){let n;const i=["asChild","el"];let l=Se(e,i),r,{$$slots:a={},$$scope:o}=e,{asChild:c=!1}=e,{el:u=void 0}=e;const{states:{checked:f},getAttrs:d}=Hi();qe(s,f,h=>t(2,r=h));function m(h){Ee[h?"unshift":"push"](()=>{u=h,t(0,u)})}return s.$$set=h=>{e=ae(ae({},e),Ye(h)),t(5,l=Se(e,i)),"asChild"in h&&t(1,c=h.asChild),"el"in h&&t(0,u=h.el),"$$scope"in h&&t(6,o=h.$$scope)},s.$$.update=()=>{s.$$.dirty&4&&t(3,n={...d("thumb"),"data-state":r?"checked":"unchecked","data-checked":r?"":void 0})},[u,c,r,n,f,l,o,a,m]}class xo extends be{constructor(e){super(),ve(this,e,$o,Qo,_e,{asChild:1,el:0})}}function ea(s){let e,t;return e=new je({props:{src:Hl,class:"h-4 w-4"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function ta(s){let e,t,n,i;t=new Ao({props:{$$slots:{default:[ea]},$$scope:{ctx:s}}});const l=s[5].default,r=Oe(l,s,s[9],null);return{c(){e=w("span"),Z(t.$$.fragment),n=B(),r&&r.c(),this.h()},l(a){e=k(a,"SPAN",{class:!0});var o=M(e);X(t.$$.fragment,o),o.forEach(p),n=q(a),r&&r.l(a),this.h()},h(){E(e,"class","absolute right-2 flex h-3.5 w-3.5 items-center justify-center")},m(a,o){j(a,e,o),z(t,e,null),j(a,n,o),r&&r.m(a,o),i=!0},p(a,o){const c={};o&512&&(c.$$scope={dirty:o,ctx:a}),t.$set(c),r&&r.p&&(!i||o&512)&&Le(r,l,a,a[9],i?De(l,a[9],o,null):Pe(a[9]),null)},i(a){i||(T(t.$$.fragment,a),T(r,a),i=!0)},o(a){P(t.$$.fragment,a),P(r,a),i=!1},d(a){a&&(p(e),p(n)),J(t),r&&r.d(a)}}}function na(s){let e,t;const n=[{value:s[1]},{disabled:s[3]},{label:s[2]},{class:ot("relative flex w-full cursor-default select-none items-center rounded-xs py-1.5 pl-2 pr-8 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",s[0])},s[4]];let i={$$slots:{default:[ta]},$$scope:{ctx:s}};for(let l=0;l<n.length;l+=1)i=ae(i,n[l]);return e=new wo({props:i}),e.$on("click",s[6]),e.$on("pointermove",s[7]),e.$on("focusin",s[8]),{c(){Z(e.$$.fragment)},l(l){X(e.$$.fragment,l)},m(l,r){z(e,l,r),t=!0},p(l,[r]){const a=r&31?Ve(n,[r&2&&{value:l[1]},r&8&&{disabled:l[3]},r&4&&{label:l[2]},r&1&&{class:ot("relative flex w-full cursor-default select-none items-center rounded-xs py-1.5 pl-2 pr-8 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",l[0])},r&16&&Ut(l[4])]):{};r&512&&(a.$$scope={dirty:r,ctx:l}),e.$set(a)},i(l){t||(T(e.$$.fragment,l),t=!0)},o(l){P(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function sa(s,e,t){const n=["class","value","label","disabled"];let i=Se(e,n),{$$slots:l={},$$scope:r}=e,{class:a=void 0}=e,{value:o}=e,{label:c=void 0}=e,{disabled:u=void 0}=e;function f(h){Ke.call(this,s,h)}function d(h){Ke.call(this,s,h)}function m(h){Ke.call(this,s,h)}return s.$$set=h=>{e=ae(ae({},e),Ye(h)),t(4,i=Se(e,n)),"class"in h&&t(0,a=h.class),"value"in h&&t(1,o=h.value),"label"in h&&t(2,c=h.label),"disabled"in h&&t(3,u=h.disabled),"$$scope"in h&&t(9,r=h.$$scope)},[a,o,c,u,i,l,f,d,m,r]}class Ri extends be{constructor(e){super(),ve(this,e,sa,na,_e,{class:0,value:1,label:2,disabled:3})}}function ia(s){let e,t;const n=s[7].default,i=Oe(n,s,s[8],null);return{c(){e=w("div"),i&&i.c(),this.h()},l(l){e=k(l,"DIV",{class:!0});var r=M(e);i&&i.l(r),r.forEach(p),this.h()},h(){E(e,"class","w-full p-1")},m(l,r){j(l,e,r),i&&i.m(e,null),t=!0},p(l,r){i&&i.p&&(!t||r&256)&&Le(i,n,l,l[8],t?De(n,l[8],r,null):Pe(l[8]),null)},i(l){t||(T(i,l),t=!0)},o(l){P(i,l),t=!1},d(l){l&&p(e),i&&i.d(l)}}}function la(s){let e,t;const n=[{inTransition:s[2]},{inTransitionConfig:s[3]},{outTransition:s[4]},{outTransitionConfig:s[5]},{sideOffset:s[1]},{class:ot("relative z-50 min-w-[8rem] overflow-hidden rounded-md border border-base-300 bg-base-100 shadow-md focus:outline-none",s[0])},s[6]];let i={$$slots:{default:[ia]},$$scope:{ctx:s}};for(let l=0;l<n.length;l+=1)i=ae(i,n[l]);return e=new oo({props:i}),{c(){Z(e.$$.fragment)},l(l){X(e.$$.fragment,l)},m(l,r){z(e,l,r),t=!0},p(l,[r]){const a=r&127?Ve(n,[r&4&&{inTransition:l[2]},r&8&&{inTransitionConfig:l[3]},r&16&&{outTransition:l[4]},r&32&&{outTransitionConfig:l[5]},r&2&&{sideOffset:l[1]},r&1&&{class:ot("relative z-50 min-w-[8rem] overflow-hidden rounded-md border border-base-300 bg-base-100 shadow-md focus:outline-none",l[0])},r&64&&Ut(l[6])]):{};r&256&&(a.$$scope={dirty:r,ctx:l}),e.$set(a)},i(l){t||(T(e.$$.fragment,l),t=!0)},o(l){P(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function ra(s,e,t){const n=["class","sideOffset","inTransition","inTransitionConfig","outTransition","outTransitionConfig"];let i=Se(e,n),{$$slots:l={},$$scope:r}=e,{class:a=void 0}=e,{sideOffset:o=4}=e,{inTransition:c=Rl}=e,{inTransitionConfig:u=void 0}=e,{outTransition:f=Kl}=e,{outTransitionConfig:d={start:.95,opacity:0,duration:50}}=e;return s.$$set=m=>{e=ae(ae({},e),Ye(m)),t(6,i=Se(e,n)),"class"in m&&t(0,a=m.class),"sideOffset"in m&&t(1,o=m.sideOffset),"inTransition"in m&&t(2,c=m.inTransition),"inTransitionConfig"in m&&t(3,u=m.inTransitionConfig),"outTransition"in m&&t(4,f=m.outTransition),"outTransitionConfig"in m&&t(5,d=m.outTransitionConfig),"$$scope"in m&&t(8,r=m.$$scope)},[a,o,c,u,f,d,i,l,r]}class oa extends be{constructor(e){super(),ve(this,e,ra,la,_e,{class:0,sideOffset:1,inTransition:2,inTransitionConfig:3,outTransition:4,outTransitionConfig:5})}}function aa(s){let e,t,n,i;const l=s[2].default,r=Oe(l,s,s[3],null);return n=new je({props:{src:Ul,class:"h-4 w-4"}}),{c(){r&&r.c(),e=B(),t=w("div"),Z(n.$$.fragment)},l(a){r&&r.l(a),e=q(a),t=k(a,"DIV",{});var o=M(t);X(n.$$.fragment,o),o.forEach(p)},m(a,o){r&&r.m(a,o),j(a,e,o),j(a,t,o),z(n,t,null),i=!0},p(a,o){r&&r.p&&(!i||o&8)&&Le(r,l,a,a[3],i?De(l,a[3],o,null):Pe(a[3]),null)},i(a){i||(T(r,a),T(n.$$.fragment,a),i=!0)},o(a){P(r,a),P(n.$$.fragment,a),i=!1},d(a){a&&(p(e),p(t)),r&&r.d(a),J(n)}}}function ca(s){let e,t;const n=[{class:ot("border border-base-300 flex h-9 w-full items-center justify-between rounded-md bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-base-content-muted focus:outline-none focus:ring-1 focus:ring-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",s[0])},s[1]];let i={$$slots:{default:[aa]},$$scope:{ctx:s}};for(let l=0;l<n.length;l+=1)i=ae(i,n[l]);return e=new jo({props:i}),{c(){Z(e.$$.fragment)},l(l){X(e.$$.fragment,l)},m(l,r){z(e,l,r),t=!0},p(l,[r]){const a=r&3?Ve(n,[r&1&&{class:ot("border border-base-300 flex h-9 w-full items-center justify-between rounded-md bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-base-content-muted focus:outline-none focus:ring-1 focus:ring-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",l[0])},r&2&&Ut(l[1])]):{};r&8&&(a.$$scope={dirty:r,ctx:l}),e.$set(a)},i(l){t||(T(e.$$.fragment,l),t=!0)},o(l){P(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function ua(s,e,t){const n=["class"];let i=Se(e,n),{$$slots:l={},$$scope:r}=e,{class:a=void 0}=e;return s.$$set=o=>{e=ae(ae({},e),Ye(o)),t(1,i=Se(e,n)),"class"in o&&t(0,a=o.class),"$$scope"in o&&t(3,r=o.$$scope)},[a,i,l,r]}class fa extends be{constructor(e){super(),ve(this,e,ua,ca,_e,{class:0})}}const da=Wr,ma=ho;function ha(s,{from:e,to:t},n={}){const i=getComputedStyle(s),l=i.transform==="none"?"":i.transform,[r,a]=i.transformOrigin.split(" ").map(parseFloat),o=e.left+e.width*r/t.width-(t.left+r),c=e.top+e.height*a/t.height-(t.top+a),{delay:u=0,duration:f=m=>Math.sqrt(m)*120,easing:d=Gl}=n;return{delay:u,duration:ln(f)?f(Math.sqrt(o*o+c*c)):f,easing:d,css:(m,h)=>{const _=h*o,N=h*c,S=m+h*e.width/t.width,O=m+h*e.height/t.height;return`transform: ${l} translate(${_}px, ${N}px) scale(${S}, ${O});`}}}var pa={default:{a:{viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},path:[{stroke:"none",d:"M0 0h24v24H0z",fill:"none"},{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"},{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"},{d:"M9 17v-4"},{d:"M12 17v-1"},{d:"M15 17v-2"},{d:"M12 17v-1"}]}},ga={default:{a:{viewBox:"0 0 200 200",style:"enable-background:new 0 0 791.9 221.6;"},path:[{fill:"currentColor",d:"M 13.53 126.114 C 13.53 126.114 64.492 162.546 67.591 164.363 C 70.582 166.179 74.963 166.606 78.808 165.111 C 82.655 163.615 85.54 160.196 86.395 156.777 C 87.356 153.358 99.002 91.819 99.002 91.819 C 99.108 90.964 99.428 88.507 98.147 86.156 C 96.331 82.951 92.271 81.242 88.638 82.31 C 85.754 83.165 84.258 85.302 83.83 86.049 C 83.19 87.224 82.014 89.148 79.877 90.857 C 78.275 92.139 76.672 92.887 75.924 93.208 C 69.407 95.879 62.142 93.635 57.975 88.293 C 55.411 84.981 50.817 83.699 46.757 85.302 C 42.697 86.904 40.347 91.071 40.667 95.238 C 41.415 101.862 37.676 108.486 31.052 111.156 C 27.526 112.546 23.787 112.546 20.475 111.477 C 19.727 111.263 17.056 110.623 14.385 112.118 C 11.073 113.828 9.363 117.887 10.218 121.52 C 10.859 123.978 12.783 125.58 13.53 126.114 Z",style:"transform-origin: 122.948px 117.753px;"},{fill:"currentColor",d:"M 98.467 46.305 C 98.467 46.305 116.63 106.349 117.912 109.554 C 119.194 112.866 122.399 115.858 126.352 117.032 C 130.306 118.101 134.686 117.247 137.463 115.11 C 140.242 112.973 187.143 71.412 187.143 71.412 C 187.785 70.879 189.494 69.062 189.922 66.391 C 190.455 62.759 188.212 58.912 184.793 57.523 C 182.015 56.455 179.451 57.203 178.703 57.523 C 177.528 58.057 175.391 58.805 172.72 58.912 C 170.583 59.019 168.981 58.592 168.126 58.378 C 161.395 56.455 156.908 50.259 156.908 43.528 C 156.802 39.361 154.023 35.515 149.857 34.339 C 145.583 33.164 141.203 34.98 138.959 38.506 C 135.434 44.275 128.489 47.16 121.651 45.237 C 118.019 44.168 115.027 41.925 113.104 39.04 C 112.57 38.399 110.86 36.263 107.869 35.728 C 104.236 35.087 100.283 37.224 98.788 40.536 C 97.612 43.1 98.254 45.451 98.467 46.305 Z",style:"transform-origin: 122.948px 117.753px;"}]}},_a={default:{a:{role:"img",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},title:[{}],path:[{stroke:"none",d:"M0 0h24v24H0z",fill:"none"},{d:"M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4"},{d:"M13.5 6.5l4 4"}]}};const it=Object.freeze(Object.defineProperty({__proto__:null,Clipboard:pa,Motherduck:ga,Pencil:_a},Symbol.toStringTag,{value:"Module"}));function ba(s){const e=JSON.parse(s);return e.data&&(e.data=kr(e.data)),e}function Cn(s){return HTMLElement.prototype.cloneNode.call(s)}function va(s,e=()=>{}){const t=async({action:i,result:l,reset:r=!0,invalidateAll:a=!0})=>{l.type==="success"&&(r&&HTMLFormElement.prototype.reset.call(s),a&&await vr()),(location.origin+location.pathname===i.origin+i.pathname||l.type==="redirect"||l.type==="error")&&yr(l)};async function n(i){var _,N,S,O,A;if(((_=i.submitter)!=null&&_.hasAttribute("formmethod")?i.submitter.formMethod:Cn(s).method)!=="post")return;i.preventDefault();const r=new URL((N=i.submitter)!=null&&N.hasAttribute("formaction")?i.submitter.formAction:Cn(s).action),a=(S=i.submitter)!=null&&S.hasAttribute("formenctype")?i.submitter.formEnctype:Cn(s).enctype,o=new FormData(s),c=(O=i.submitter)==null?void 0:O.getAttribute("name");c&&o.append(c,((A=i.submitter)==null?void 0:A.getAttribute("value"))??"");const u=new AbortController;let f=!1;const m=await e({action:r,cancel:()=>f=!0,controller:u,formData:o,formElement:s,submitter:i.submitter})??t;if(f)return;let h;try{const v=new Headers({accept:"application/json","x-sveltekit-action":"true"});a!=="multipart/form-data"&&v.set("Content-Type",/^(:?application\/x-www-form-urlencoded|text\/plain)$/.test(a)?a:"application/x-www-form-urlencoded");const I=a==="multipart/form-data"?o:new URLSearchParams(o),C=await fetch(r,{method:"POST",headers:v,cache:"no-store",body:I,signal:u.signal});h=ba(await C.text()),h.type==="error"&&(h.status=C.status)}catch(v){if((v==null?void 0:v.name)==="AbortError")return;h={type:"error",error:v}}m({action:r,formData:o,formElement:s,update:v=>t({action:r,result:h,reset:v==null?void 0:v.reset,invalidateAll:v==null?void 0:v.invalidateAll}),result:h})}return HTMLFormElement.prototype.addEventListener.call(s,"submit",n),{destroy(){HTMLFormElement.prototype.removeEventListener.call(s,"submit",n)}}}function ya(s){let e,t;return e=new xo({props:{class:ot("bg-base-100 pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function ka(s){let e,t,n;const i=[{class:ot("focus-visible:ring-base-300 focus-visible:ring-offset-base-100 data-[state=checked]:bg-base-content data-[state=unchecked]:bg-base-300 peer inline-flex h-[20px] w-[36px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s[1])},s[2]];function l(a){s[3](a)}let r={$$slots:{default:[ya]},$$scope:{ctx:s}};for(let a=0;a<i.length;a+=1)r=ae(r,i[a]);return s[0]!==void 0&&(r.checked=s[0]),e=new Jo({props:r}),Ee.push(()=>Ge(e,"checked",l)),e.$on("click",s[4]),e.$on("keydown",s[5]),{c(){Z(e.$$.fragment)},l(a){X(e.$$.fragment,a)},m(a,o){z(e,a,o),n=!0},p(a,[o]){const c=o&6?Ve(i,[o&2&&{class:ot("focus-visible:ring-base-300 focus-visible:ring-offset-base-100 data-[state=checked]:bg-base-content data-[state=unchecked]:bg-base-300 peer inline-flex h-[20px] w-[36px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a[1])},o&4&&Ut(a[2])]):{};o&64&&(c.$$scope={dirty:o,ctx:a}),!t&&o&1&&(t=!0,c.checked=a[0],Ue(()=>t=!1)),e.$set(c)},i(a){n||(T(e.$$.fragment,a),n=!0)},o(a){P(e.$$.fragment,a),n=!1},d(a){J(e,a)}}}function wa(s,e,t){const n=["class","checked"];let i=Se(e,n),{class:l=void 0}=e,{checked:r=void 0}=e;function a(u){r=u,t(0,r)}function o(u){Ke.call(this,s,u)}function c(u){Ke.call(this,s,u)}return s.$$set=u=>{e=ae(ae({},e),Ye(u)),t(2,i=Se(e,n)),"class"in u&&t(1,l=u.class),"checked"in u&&t(0,r=u.checked)},[r,l,i,a,o,c]}class Yn extends be{constructor(e){super(),ve(this,e,wa,ka,_e,{class:1,checked:0})}}function Os(s){let e,t='<span class="text-base-content-muted text-sm select-none font-mono">sources/</span>';return{c(){e=w("div"),e.innerHTML=t,this.h()},l(n){e=k(n,"DIV",{class:!0,"data-svelte-h":!0}),oe(e)!=="svelte-1vp1rbd"&&(e.innerHTML=t),this.h()},h(){E(e,"class","flex items-center border border-r-0 border-base-300 rounded-l-md px-3 bg-base-100 cursor-text")},m(n,i){j(n,e,i)},d(n){n&&p(e)}}}function Ls(s){let e,t,n;return{c(){e=w("span"),t=le(s[1]),this.h()},l(i){e=k(i,"SPAN",{class:!0});var l=M(e);t=ie(l,s[1]),l.forEach(p),this.h()},h(){E(e,"class","text-negative text-xs break-words max-w-md")},m(i,l){j(i,e,l),g(e,t)},p(i,l){l&2&&ye(t,i[1])},i(i){i&&(n||ke(()=>{n=Ae(e,Xe,{}),n.start()}))},o:re,d(i){i&&p(e)}}}function Ca(s){let e,t='Tables from this source can be queried using <code class="select-all">`&lt;source name&gt;.<wbr/>&lt;tablename&gt;`</code>. Changing the name will change how you reference the source in your queries, but it will not\n			change the source directory.';return{c(){e=w("p"),e.innerHTML=t,this.h()},l(n){e=k(n,"P",{class:!0,"data-svelte-h":!0}),oe(e)!=="svelte-17lv2uo"&&(e.innerHTML=t),this.h()},h(){E(e,"class","text-xs text-base-content-muted")},m(n,i){j(n,e,i)},d(n){n&&p(e)}}}function Ta(s){let e,t='Name of the new directory that will be created for this source, under <code class="select-all">`/sources`</code>.';return{c(){e=w("p"),e.innerHTML=t,this.h()},l(n){e=k(n,"P",{class:!0,"data-svelte-h":!0}),oe(e)!=="svelte-1alkzla"&&(e.innerHTML=t),this.h()},h(){E(e,"class","text-xs text-base-content-muted")},m(n,i){j(n,e,i)},d(n){n&&p(e)}}}function Ea(s){let e,t,n,i=s[2]?"Directory Name":"Name",l,r,a,o,c,u,f,d,m,h=s[2]&&Os(),_=s[1]&&Ls(s);function N(A,v){return A[2]?Ta:Ca}let S=N(s),O=S(s);return{c(){e=w("div"),t=w("label"),n=w("span"),l=le(i),r=B(),a=w("div"),h&&h.c(),o=B(),c=w("input"),u=B(),_&&_.c(),f=B(),O.c(),this.h()},l(A){e=k(A,"DIV",{class:!0});var v=M(e);t=k(v,"LABEL",{for:!0,class:!0});var I=M(t);n=k(I,"SPAN",{class:!0});var C=M(n);l=ie(C,i),C.forEach(p),r=q(I),a=k(I,"DIV",{class:!0});var D=M(a);h&&h.l(D),o=q(D),c=k(D,"INPUT",{name:!0,class:!0}),D.forEach(p),I.forEach(p),u=q(v),_&&_.l(v),f=q(v),O.l(v),v.forEach(p),this.h()},h(){E(n,"class","text-sm font-medium"),c.required=!0,E(c,"name","sourceName"),E(c,"class","flex-1 border border-base-300 bg-base-100 shadow-sm text-sm h-9 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none"),tt(c,"rounded-md",!s[2]),tt(c,"rounded-l-none",s[2]),tt(c,"rounded-r-md",s[2]),E(a,"class","flex w-full group focus-within:ring-1 focus-within:ring-base-300 rounded-md"),E(t,"for","sourceName"),E(t,"class","flex flex-col gap-2"),E(e,"class","flex flex-col gap-2")},m(A,v){j(A,e,v),g(e,t),g(t,n),g(n,l),g(t,r),g(t,a),h&&h.m(a,null),g(a,o),g(a,c),we(c,s[0]),g(e,u),_&&_.m(e,null),g(e,f),O.m(e,null),d||(m=[ce(c,"input",s[3]),ce(c,"change",s[4])],d=!0)},p(A,[v]){v&4&&i!==(i=A[2]?"Directory Name":"Name")&&ye(l,i),A[2]?h||(h=Os(),h.c(),h.m(a,o)):h&&(h.d(1),h=null),v&1&&c.value!==A[0]&&we(c,A[0]),v&4&&tt(c,"rounded-md",!A[2]),v&4&&tt(c,"rounded-l-none",A[2]),v&4&&tt(c,"rounded-r-md",A[2]),A[1]?_?(_.p(A,v),v&2&&T(_,1)):(_=Ls(A),_.c(),T(_,1),_.m(e,f)):_&&(_.d(1),_=null),S!==(S=N(A))&&(O.d(1),O=S(A),O&&(O.c(),O.m(e,null)))},i(A){T(_)},o:re,d(A){A&&p(e),h&&h.d(),_&&_.d(),O.d(),d=!1,Je(m)}}}const Sa=/^[\w_]+$/,Ki=(s,e)=>s.length<1?"Source name must be set.":Sa.test(s)?s&&e.some(t=>t.name===s)?`A source named ${s} already exists.`:"":"Source names can only contain letters, numbers, and underscores.";function Ia(s,e,t){let{sourceName:n}=e,{nameError:i}=e,{showPrefix:l=!1}=e;function r(){n=this.value,t(0,n)}const a=()=>t(1,i="");return s.$$set=o=>{"sourceName"in o&&t(0,n=o.sourceName),"nameError"in o&&t(1,i=o.nameError),"showPrefix"in o&&t(2,l=o.showPrefix)},[n,i,l,r,a]}class Ui extends be{constructor(e){super(),ve(this,e,Ia,Ea,_e,{sourceName:0,nameError:1,showPrefix:2})}}const Dt={collection:"map",default:!0,nodeClass:_t,tag:"tag:yaml.org,2002:map",resolve(s,e){return Di(s)||e("Expected a mapping for this tag"),s},createNode:(s,e,t)=>_t.from(s,e,t)},Mt={collection:"seq",default:!0,nodeClass:It,tag:"tag:yaml.org,2002:seq",resolve(s,e){return Mi(s)||e("Expected a sequence for this tag"),s},createNode:(s,e,t)=>It.from(s,e,t)},un={identify:s=>typeof s=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:s=>s,stringify(s,e,t,n){return e=Object.assign({actualString:!0},e),Vi(s,e,t,n)}},fn={identify:s=>s==null,createNode:()=>new Be(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new Be(null),stringify:({source:s},e)=>typeof s=="string"&&fn.test.test(s)?s:e.options.nullStr},Wn={identify:s=>typeof s=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:s=>new Be(s[0]==="t"||s[0]==="T"),stringify({source:s,value:e},t){if(s&&Wn.test.test(s)){const n=s[0]==="t"||s[0]==="T";if(e===n)return s}return e?t.options.trueStr:t.options.falseStr}};function $e({format:s,minFractionDigits:e,tag:t,value:n}){if(typeof n=="bigint")return String(n);const i=typeof n=="number"?n:Number(n);if(!isFinite(i))return isNaN(i)?".nan":i<0?"-.inf":".inf";let l=JSON.stringify(n);if(!s&&e&&(!t||t==="tag:yaml.org,2002:float")&&/^\d/.test(l)){let r=l.indexOf(".");r<0&&(r=l.length,l+=".");let a=e-(l.length-r-1);for(;a-- >0;)l+="0"}return l}const Gi={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:s=>s.slice(-3).toLowerCase()==="nan"?NaN:s[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:$e},Yi={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:s=>parseFloat(s),stringify(s){const e=Number(s.value);return isFinite(e)?e.toExponential():$e(s)}},Wi={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(s){const e=new Be(parseFloat(s)),t=s.indexOf(".");return t!==-1&&s[s.length-1]==="0"&&(e.minFractionDigits=s.length-t-1),e},stringify:$e},dn=s=>typeof s=="bigint"||Number.isInteger(s),Jn=(s,e,t,{intAsBigInt:n})=>n?BigInt(s):parseInt(s.substring(e),t);function Ji(s,e,t){const{value:n}=s;return dn(n)&&n>=0?t+n.toString(e):$e(s)}const zi={identify:s=>dn(s)&&s>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(s,e,t)=>Jn(s,2,8,t),stringify:s=>Ji(s,8,"0o")},Xi={identify:dn,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(s,e,t)=>Jn(s,0,10,t),stringify:$e},Zi={identify:s=>dn(s)&&s>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(s,e,t)=>Jn(s,2,16,t),stringify:s=>Ji(s,16,"0x")},Na=[Dt,Mt,un,fn,Wn,zi,Xi,Zi,Gi,Yi,Wi];function Ps(s){return typeof s=="bigint"||Number.isInteger(s)}const $t=({value:s})=>JSON.stringify(s),Aa=[{identify:s=>typeof s=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:s=>s,stringify:$t},{identify:s=>s==null,createNode:()=>new Be(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:$t},{identify:s=>typeof s=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^true$|^false$/,resolve:s=>s==="true",stringify:$t},{identify:Ps,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(s,e,{intAsBigInt:t})=>t?BigInt(s):parseInt(s,10),stringify:({value:s})=>Ps(s)?s.toString():JSON.stringify(s)},{identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:s=>parseFloat(s),stringify:$t}],Oa={default:!0,tag:"",test:/^/,resolve(s,e){return e(`Unresolved plain scalar ${JSON.stringify(s)}`),s}},La=[Dt,Mt].concat(Aa,Oa),zn={identify:s=>s instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(s,e){if(typeof atob=="function"){const t=atob(s.replace(/[\n\r]/g,"")),n=new Uint8Array(t.length);for(let i=0;i<t.length;++i)n[i]=t.charCodeAt(i);return n}else return e("This environment does not support reading binary tags; either Buffer or atob is required"),s},stringify({comment:s,type:e,value:t},n,i,l){const r=t;let a;if(typeof btoa=="function"){let o="";for(let c=0;c<r.length;++c)o+=String.fromCharCode(r[c]);a=btoa(o)}else throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");if(e||(e=Be.BLOCK_LITERAL),e!==Be.QUOTE_DOUBLE){const o=Math.max(n.options.lineWidth-n.indent.length,n.options.minContentWidth),c=Math.ceil(a.length/o),u=new Array(c);for(let f=0,d=0;f<c;++f,d+=o)u[f]=a.substr(d,o);a=u.join(e===Be.BLOCK_LITERAL?`
`:" ")}return Vi({comment:s,type:e,value:a},n,i,l)}};function Qi({value:s,source:e},t){return e&&(s?$i:xi).test.test(e)?e:s?t.options.trueStr:t.options.falseStr}const $i={identify:s=>s===!0,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new Be(!0),stringify:Qi},xi={identify:s=>s===!1,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new Be(!1),stringify:Qi},Pa={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:s=>s.slice(-3).toLowerCase()==="nan"?NaN:s[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:$e},Da={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:s=>parseFloat(s.replace(/_/g,"")),stringify(s){const e=Number(s.value);return isFinite(e)?e.toExponential():$e(s)}},Ma={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(s){const e=new Be(parseFloat(s.replace(/_/g,""))),t=s.indexOf(".");if(t!==-1){const n=s.substring(t+1).replace(/_/g,"");n[n.length-1]==="0"&&(e.minFractionDigits=n.length)}return e},stringify:$e},Wt=s=>typeof s=="bigint"||Number.isInteger(s);function mn(s,e,t,{intAsBigInt:n}){const i=s[0];if((i==="-"||i==="+")&&(e+=1),s=s.substring(e).replace(/_/g,""),n){switch(t){case 2:s=`0b${s}`;break;case 8:s=`0o${s}`;break;case 16:s=`0x${s}`;break}const r=BigInt(s);return i==="-"?BigInt(-1)*r:r}const l=parseInt(s,t);return i==="-"?-1*l:l}function Xn(s,e,t){const{value:n}=s;if(Wt(n)){const i=n.toString(e);return n<0?"-"+t+i.substr(1):t+i}return $e(s)}const Va={identify:Wt,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(s,e,t)=>mn(s,2,2,t),stringify:s=>Xn(s,2,"0b")},ja={identify:Wt,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(s,e,t)=>mn(s,1,8,t),stringify:s=>Xn(s,8,"0")},Fa={identify:Wt,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(s,e,t)=>mn(s,0,10,t),stringify:$e},qa={identify:Wt,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(s,e,t)=>mn(s,2,16,t),stringify:s=>Xn(s,16,"0x")};function Zn(s,e){const t=s[0],n=t==="-"||t==="+"?s.substring(1):s,i=r=>e?BigInt(r):Number(r),l=n.replace(/_/g,"").split(":").reduce((r,a)=>r*i(60)+i(a),i(0));return t==="-"?i(-1)*l:l}function el(s){let{value:e}=s,t=r=>r;if(typeof e=="bigint")t=r=>BigInt(r);else if(isNaN(e)||!isFinite(e))return $e(s);let n="";e<0&&(n="-",e*=t(-1));const i=t(60),l=[e%i];return e<60?l.unshift(0):(e=(e-l[0])/i,l.unshift(e%i),e>=60&&(e=(e-l[0])/i,l.unshift(e))),n+l.map(r=>String(r).padStart(2,"0")).join(":").replace(/000000\d*$/,"")}const tl={identify:s=>typeof s=="bigint"||Number.isInteger(s),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(s,e,{intAsBigInt:t})=>Zn(s,t),stringify:el},nl={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:s=>Zn(s,!1),stringify:el},hn={identify:s=>s instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(s){const e=s.match(hn.test);if(!e)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");const[,t,n,i,l,r,a]=e.map(Number),o=e[7]?Number((e[7]+"00").substr(1,3)):0;let c=Date.UTC(t,n-1,i,l||0,r||0,a||0,o);const u=e[8];if(u&&u!=="Z"){let f=Zn(u,!1);Math.abs(f)<30&&(f*=60),c-=6e4*f}return new Date(c)},stringify:({value:s})=>s.toISOString().replace(/(T00:00:00)?\.000Z$/,"")},Ds=[Dt,Mt,un,fn,$i,xi,Va,ja,Fa,qa,Pa,Da,Ma,zn,Et,Fn,qn,Bn,tl,nl,hn],Ms=new Map([["core",Na],["failsafe",[Dt,Mt,un]],["json",La],["yaml11",Ds],["yaml-1.1",Ds]]),Vs={binary:zn,bool:Wn,float:Wi,floatExp:Yi,floatNaN:Gi,floatTime:nl,int:Xi,intHex:Zi,intOct:zi,intTime:tl,map:Dt,merge:Et,null:fn,omap:Fn,pairs:qn,seq:Mt,set:Bn,timestamp:hn},Ba={"tag:yaml.org,2002:binary":zn,"tag:yaml.org,2002:merge":Et,"tag:yaml.org,2002:omap":Fn,"tag:yaml.org,2002:pairs":qn,"tag:yaml.org,2002:set":Bn,"tag:yaml.org,2002:timestamp":hn};function Tn(s,e,t){const n=Ms.get(e);if(n&&!s)return t&&!n.includes(Et)?n.concat(Et):n.slice();let i=n;if(!i)if(Array.isArray(s))i=[];else{const l=Array.from(Ms.keys()).filter(r=>r!=="yaml11").map(r=>JSON.stringify(r)).join(", ");throw new Error(`Unknown schema "${e}"; use one of ${l} or define customTags array`)}if(Array.isArray(s))for(const l of s)i=i.concat(l);else typeof s=="function"&&(i=s(i.slice()));return t&&(i=i.concat(Et)),i.reduce((l,r)=>{const a=typeof r=="string"?Vs[r]:r;if(!a){const o=JSON.stringify(r),c=Object.keys(Vs).map(u=>JSON.stringify(u)).join(", ");throw new Error(`Unknown custom tag ${o}; use one of ${c}`)}return l.includes(a)||l.push(a),l},[])}const Ha=(s,e)=>s.key<e.key?-1:s.key>e.key?1:0;class pn{constructor({compat:e,customTags:t,merge:n,resolveKnownTags:i,schema:l,sortMapEntries:r,toStringDefaults:a}){this.compat=Array.isArray(e)?Tn(e,"compat"):e?Tn(null,e):null,this.name=typeof l=="string"&&l||"core",this.knownTags=i?Ba:{},this.tags=Tn(t,this.name,n),this.toStringOptions=a??null,Object.defineProperty(this,Yl,{value:Dt}),Object.defineProperty(this,bt,{value:un}),Object.defineProperty(this,Wl,{value:Mt}),this.sortMapEntries=typeof r=="function"?r:r===!0?Ha:null}clone(){const e=Object.create(pn.prototype,Object.getOwnPropertyDescriptors(this));return e.tags=this.tags.slice(),e}}function Ra(s,e){var o;const t=[];let n=e.directives===!0;if(e.directives!==!1&&s.directives){const c=s.directives.toString(s);c?(t.push(c),n=!0):s.directives.docStart&&(n=!0)}n&&t.push("---");const i=Jl(s,e),{commentString:l}=i.options;if(s.commentBefore){t.length!==1&&t.unshift("");const c=l(s.commentBefore);t.unshift(Zt(c,""))}let r=!1,a=null;if(s.contents){if(on(s.contents)){if(s.contents.spaceBefore&&n&&t.push(""),s.contents.commentBefore){const f=l(s.contents.commentBefore);t.push(Zt(f,""))}i.forceBlockIndent=!!s.comment,a=s.contents.comment}const c=a?void 0:()=>r=!0;let u=rs(s.contents,i,()=>a=null,c);a&&(u+=zl(u,"",l(a))),(u[0]==="|"||u[0]===">")&&t[t.length-1]==="---"?t[t.length-1]=`--- ${u}`:t.push(u)}else t.push(rs(s.contents,i));if((o=s.directives)!=null&&o.docEnd)if(s.comment){const c=l(s.comment);c.includes(`
`)?(t.push("..."),t.push(Zt(c,""))):t.push(`... ${c}`)}else t.push("...");else{let c=s.comment;c&&r&&(c=c.replace(/^\n+/,"")),c&&((!r||a)&&t[t.length-1]!==""&&t.push(""),t.push(Zt(l(c),"")))}return t.join(`
`)+`
`}class Vt{constructor(e,t,n){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,os,{value:as});let i=null;typeof t=="function"||Array.isArray(t)?i=t:n===void 0&&t&&(n=t,t=void 0);const l=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},n);this.options=l;let{version:r}=l;n!=null&&n._directives?(this.directives=n._directives.atDocument(),this.directives.yaml.explicit&&(r=this.directives.yaml.version)):this.directives=new nn({version:r}),this.setSchema(r,n),this.contents=e===void 0?null:this.createNode(e,i,n)}clone(){const e=Object.create(Vt.prototype,{[os]:{value:as}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=on(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){kt(this.contents)&&this.contents.add(e)}addIn(e,t){kt(this.contents)&&this.contents.addIn(e,t)}createAlias(e,t){if(!e.anchor){const n=Xl(this);e.anchor=!t||n.has(t)?Zl(t||"a",n):t}return new Hn(e.anchor)}createNode(e,t,n){let i;if(typeof t=="function")e=t.call({"":e},"",e),i=t;else if(Array.isArray(t)){const N=O=>typeof O=="number"||O instanceof String||O instanceof Number,S=t.filter(N).map(String);S.length>0&&(t=t.concat(S)),i=t}else n===void 0&&t&&(n=t,t=void 0);const{aliasDuplicateObjects:l,anchorPrefix:r,flow:a,keepUndefined:o,onTagObj:c,tag:u}=n??{},{onAnchor:f,setAnchors:d,sourceObjects:m}=$l(this,r||"a"),h={aliasDuplicateObjects:l??!0,keepUndefined:o??!1,onAnchor:f,onTagObj:c,replacer:i,schema:this.schema,sourceObjects:m},_=Ql(e,u,h);return a&&ft(_)&&(_.flow=!0),d(),_}createPair(e,t,n={}){const i=this.createNode(e,null,n),l=this.createNode(t,null,n);return new Rt(i,l)}delete(e){return kt(this.contents)?this.contents.delete(e):!1}deleteIn(e){return Qt(e)?this.contents==null?!1:(this.contents=null,!0):kt(this.contents)?this.contents.deleteIn(e):!1}get(e,t){return ft(this.contents)?this.contents.get(e,t):void 0}getIn(e,t){return Qt(e)?!t&&Nt(this.contents)?this.contents.value:this.contents:ft(this.contents)?this.contents.getIn(e,t):void 0}has(e){return ft(this.contents)?this.contents.has(e):!1}hasIn(e){return Qt(e)?this.contents!==void 0:ft(this.contents)?this.contents.hasIn(e):!1}set(e,t){this.contents==null?this.contents=cs(this.schema,[e],t):kt(this.contents)&&this.contents.set(e,t)}setIn(e,t){Qt(e)?this.contents=t:this.contents==null?this.contents=cs(this.schema,Array.from(e),t):kt(this.contents)&&this.contents.setIn(e,t)}setSchema(e,t={}){typeof e=="number"&&(e=String(e));let n;switch(e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new nn({version:"1.1"}),n={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new nn({version:e}),n={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,n=null;break;default:{const i=JSON.stringify(e);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${i}`)}}if(t.schema instanceof Object)this.schema=t.schema;else if(n)this.schema=new pn(Object.assign(n,t));else throw new Error("With a null YAML version, the { schema: Schema } option is required")}toJS({json:e,jsonArg:t,mapAsMap:n,maxAliasCount:i,onAnchor:l,reviver:r}={}){const a={anchors:new Map,doc:this,keep:!e,mapAsMap:n===!0,mapKeyWarned:!1,maxAliasCount:typeof i=="number"?i:100},o=xl(this.contents,t??"",a);if(typeof l=="function")for(const{count:c,res:u}of a.anchors.values())l(u,c);return typeof r=="function"?er(r,{"":o},"",o):o}toJSON(e,t){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:t})}toString(e={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||Number(e.indent)<=0)){const t=JSON.stringify(e.indent);throw new Error(`"indent" option must be a positive integer, not ${t}`)}return Ra(this,e)}}function kt(s){if(ft(s))return!0;throw new Error("Expected a YAML collection as document contents")}function Lt(s,{flow:e,indicator:t,next:n,offset:i,onError:l,parentIndent:r,startOnNewline:a}){let o=!1,c=a,u=a,f="",d="",m=!1,h=!1,_=null,N=null,S=null,O=null,A=null,v=null,I=null;for(const L of s)switch(h&&(L.type!=="space"&&L.type!=="newline"&&L.type!=="comma"&&l(L.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),h=!1),_&&(c&&L.type!=="comment"&&L.type!=="newline"&&l(_,"TAB_AS_INDENT","Tabs are not allowed as indentation"),_=null),L.type){case"space":!e&&(t!=="doc-start"||(n==null?void 0:n.type)!=="flow-collection")&&L.source.includes("	")&&(_=L),u=!0;break;case"comment":{u||l(L,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const V=L.source.substring(1)||" ";f?f+=d+V:f=V,d="",c=!1;break}case"newline":c?f?f+=L.source:(!v||t!=="seq-item-ind")&&(o=!0):d+=L.source,c=!0,m=!0,(N||S)&&(O=L),u=!0;break;case"anchor":N&&l(L,"MULTIPLE_ANCHORS","A node can have at most one anchor"),L.source.endsWith(":")&&l(L.offset+L.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),N=L,I===null&&(I=L.offset),c=!1,u=!1,h=!0;break;case"tag":{S&&l(L,"MULTIPLE_TAGS","A node can have at most one tag"),S=L,I===null&&(I=L.offset),c=!1,u=!1,h=!0;break}case t:(N||S)&&l(L,"BAD_PROP_ORDER",`Anchors and tags must be after the ${L.source} indicator`),v&&l(L,"UNEXPECTED_TOKEN",`Unexpected ${L.source} in ${e??"collection"}`),v=L,c=t==="seq-item-ind"||t==="explicit-key-ind",u=!1;break;case"comma":if(e){A&&l(L,"UNEXPECTED_TOKEN",`Unexpected , in ${e}`),A=L,c=!1,u=!1;break}default:l(L,"UNEXPECTED_TOKEN",`Unexpected ${L.type} token`),c=!1,u=!1}const C=s[s.length-1],D=C?C.offset+C.source.length:i;return h&&n&&n.type!=="space"&&n.type!=="newline"&&n.type!=="comma"&&(n.type!=="scalar"||n.source!=="")&&l(n.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),_&&(c&&_.indent<=r||(n==null?void 0:n.type)==="block-map"||(n==null?void 0:n.type)==="block-seq")&&l(_,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:A,found:v,spaceBefore:o,comment:f,hasNewline:m,anchor:N,tag:S,newlineAfterProp:O,end:D,start:I??D}}function Kt(s){if(!s)return null;switch(s.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(s.source.includes(`
`))return!0;if(s.end){for(const e of s.end)if(e.type==="newline")return!0}return!1;case"flow-collection":for(const e of s.items){for(const t of e.start)if(t.type==="newline")return!0;if(e.sep){for(const t of e.sep)if(t.type==="newline")return!0}if(Kt(e.key)||Kt(e.value))return!0}return!1;default:return!0}}function Pn(s,e,t){if((e==null?void 0:e.type)==="flow-collection"){const n=e.end[0];n.indent===s&&(n.source==="]"||n.source==="}")&&Kt(e)&&t(n,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}function sl(s,e,t){const{uniqueKeys:n}=s.options;if(n===!1)return!1;const i=typeof n=="function"?n:(l,r)=>l===r||Nt(l)&&Nt(r)&&l.value===r.value;return e.some(l=>i(l.key,t))}const js="All mapping items must start at the same column";function Ka({composeNode:s,composeEmptyNode:e},t,n,i,l){var u;const r=(l==null?void 0:l.nodeClass)??_t,a=new r(t.schema);t.atRoot&&(t.atRoot=!1);let o=n.offset,c=null;for(const f of n.items){const{start:d,key:m,sep:h,value:_}=f,N=Lt(d,{indicator:"explicit-key-ind",next:m??(h==null?void 0:h[0]),offset:o,onError:i,parentIndent:n.indent,startOnNewline:!0}),S=!N.found;if(S){if(m&&(m.type==="block-seq"?i(o,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in m&&m.indent!==n.indent&&i(o,"BAD_INDENT",js)),!N.anchor&&!N.tag&&!h){c=N.end,N.comment&&(a.comment?a.comment+=`
`+N.comment:a.comment=N.comment);continue}(N.newlineAfterProp||Kt(m))&&i(m??d[d.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else((u=N.found)==null?void 0:u.indent)!==n.indent&&i(o,"BAD_INDENT",js);t.atKey=!0;const O=N.end,A=m?s(t,m,N,i):e(t,O,d,null,N,i);t.schema.compat&&Pn(n.indent,m,i),t.atKey=!1,sl(t,a.items,A)&&i(O,"DUPLICATE_KEY","Map keys must be unique");const v=Lt(h??[],{indicator:"map-value-ind",next:_,offset:A.range[2],onError:i,parentIndent:n.indent,startOnNewline:!m||m.type==="block-scalar"});if(o=v.end,v.found){S&&((_==null?void 0:_.type)==="block-map"&&!v.hasNewline&&i(o,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),t.options.strict&&N.start<v.found.offset-1024&&i(A.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));const I=_?s(t,_,v,i):e(t,o,h,null,v,i);t.schema.compat&&Pn(n.indent,_,i),o=I.range[2];const C=new Rt(A,I);t.options.keepSourceTokens&&(C.srcToken=f),a.items.push(C)}else{S&&i(A.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),v.comment&&(A.comment?A.comment+=`
`+v.comment:A.comment=v.comment);const I=new Rt(A);t.options.keepSourceTokens&&(I.srcToken=f),a.items.push(I)}}return c&&c<o&&i(c,"IMPOSSIBLE","Map comment with trailing content"),a.range=[n.offset,o,c??o],a}function Ua({composeNode:s,composeEmptyNode:e},t,n,i,l){const r=(l==null?void 0:l.nodeClass)??It,a=new r(t.schema);t.atRoot&&(t.atRoot=!1),t.atKey&&(t.atKey=!1);let o=n.offset,c=null;for(const{start:u,value:f}of n.items){const d=Lt(u,{indicator:"seq-item-ind",next:f,offset:o,onError:i,parentIndent:n.indent,startOnNewline:!0});if(!d.found)if(d.anchor||d.tag||f)f&&f.type==="block-seq"?i(d.end,"BAD_INDENT","All sequence items must start at the same column"):i(o,"MISSING_CHAR","Sequence item without - indicator");else{c=d.end,d.comment&&(a.comment=d.comment);continue}const m=f?s(t,f,d,i):e(t,d.end,u,null,d,i);t.schema.compat&&Pn(n.indent,f,i),o=m.range[2],a.items.push(m)}return a.range=[n.offset,o,c??o],a}const En="Block collections are not allowed within flow collections",Sn=s=>s&&(s.type==="block-map"||s.type==="block-seq");function Ga({composeNode:s,composeEmptyNode:e},t,n,i,l){const r=n.start.source==="{",a=r?"flow map":"flow sequence",o=(l==null?void 0:l.nodeClass)??(r?_t:It),c=new o(t.schema);c.flow=!0;const u=t.atRoot;u&&(t.atRoot=!1),t.atKey&&(t.atKey=!1);let f=n.offset+n.start.source.length;for(let N=0;N<n.items.length;++N){const S=n.items[N],{start:O,key:A,sep:v,value:I}=S,C=Lt(O,{flow:a,indicator:"explicit-key-ind",next:A??(v==null?void 0:v[0]),offset:f,onError:i,parentIndent:n.indent,startOnNewline:!1});if(!C.found){if(!C.anchor&&!C.tag&&!v&&!I){N===0&&C.comma?i(C.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`):N<n.items.length-1&&i(C.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${a}`),C.comment&&(c.comment?c.comment+=`
`+C.comment:c.comment=C.comment),f=C.end;continue}!r&&t.options.strict&&Kt(A)&&i(A,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(N===0)C.comma&&i(C.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`);else if(C.comma||i(C.start,"MISSING_CHAR",`Missing , between ${a} items`),C.comment){let D="";e:for(const L of O)switch(L.type){case"comma":case"space":break;case"comment":D=L.source.substring(1);break e;default:break e}if(D){let L=c.items[c.items.length-1];Rn(L)&&(L=L.value??L.key),L.comment?L.comment+=`
`+D:L.comment=D,C.comment=C.comment.substring(D.length+1)}}if(!r&&!v&&!C.found){const D=I?s(t,I,C,i):e(t,C.end,v,null,C,i);c.items.push(D),f=D.range[2],Sn(I)&&i(D.range,"BLOCK_IN_FLOW",En)}else{t.atKey=!0;const D=C.end,L=A?s(t,A,C,i):e(t,D,O,null,C,i);Sn(A)&&i(L.range,"BLOCK_IN_FLOW",En),t.atKey=!1;const V=Lt(v??[],{flow:a,indicator:"map-value-ind",next:I,offset:L.range[2],onError:i,parentIndent:n.indent,startOnNewline:!1});if(V.found){if(!r&&!C.found&&t.options.strict){if(v)for(const H of v){if(H===V.found)break;if(H.type==="newline"){i(H,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}C.start<V.found.offset-1024&&i(V.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else I&&("source"in I&&I.source&&I.source[0]===":"?i(I,"MISSING_CHAR",`Missing space after : in ${a}`):i(V.start,"MISSING_CHAR",`Missing , or : between ${a} items`));const b=I?s(t,I,V,i):V.found?e(t,V.end,v,null,V,i):null;b?Sn(I)&&i(b.range,"BLOCK_IN_FLOW",En):V.comment&&(L.comment?L.comment+=`
`+V.comment:L.comment=V.comment);const y=new Rt(L,b);if(t.options.keepSourceTokens&&(y.srcToken=S),r){const H=c;sl(t,H.items,L)&&i(D,"DUPLICATE_KEY","Map keys must be unique"),H.items.push(y)}else{const H=new _t(t.schema);H.flow=!0,H.items.push(y);const U=(b??L).range;H.range=[L.range[0],U[1],U[2]],c.items.push(H)}f=b?b.range[2]:V.end}}const d=r?"}":"]",[m,...h]=n.end;let _=f;if(m&&m.source===d)_=m.offset+m.source.length;else{const N=a[0].toUpperCase()+a.substring(1),S=u?`${N} must end with a ${d}`:`${N} in block collection must be sufficiently indented and end with a ${d}`;i(f,u?"MISSING_CHAR":"BAD_INDENT",S),m&&m.source.length!==1&&h.unshift(m)}if(h.length>0){const N=an(h,_,t.options.strict,i);N.comment&&(c.comment?c.comment+=`
`+N.comment:c.comment=N.comment),c.range=[n.offset,_,N.offset]}else c.range=[n.offset,_,_];return c}function In(s,e,t,n,i,l){const r=t.type==="block-map"?Ka(s,e,t,n,l):t.type==="block-seq"?Ua(s,e,t,n,l):Ga(s,e,t,n,l),a=r.constructor;return i==="!"||i===a.tagName?(r.tag=a.tagName,r):(i&&(r.tag=i),r)}function Ya(s,e,t,n,i){var d;const l=n.tag,r=l?e.directives.tagName(l.source,m=>i(l,"TAG_RESOLVE_FAILED",m)):null;if(t.type==="block-seq"){const{anchor:m,newlineAfterProp:h}=n,_=m&&l?m.offset>l.offset?m:l:m??l;_&&(!h||h.offset<_.offset)&&i(_,"MISSING_CHAR","Missing newline after block sequence props")}const a=t.type==="block-map"?"map":t.type==="block-seq"?"seq":t.start.source==="{"?"map":"seq";if(!l||!r||r==="!"||r===_t.tagName&&a==="map"||r===It.tagName&&a==="seq")return In(s,e,t,i,r);let o=e.schema.tags.find(m=>m.tag===r&&m.collection===a);if(!o){const m=e.schema.knownTags[r];if(m&&m.collection===a)e.schema.tags.push(Object.assign({},m,{default:!1})),o=m;else return m!=null&&m.collection?i(l,"BAD_COLLECTION_TYPE",`${m.tag} used for ${a} collection, but expects ${m.collection}`,!0):i(l,"TAG_RESOLVE_FAILED",`Unresolved tag: ${r}`,!0),In(s,e,t,i,r)}const c=In(s,e,t,i,r,o),u=((d=o.resolve)==null?void 0:d.call(o,c,m=>i(l,"TAG_RESOLVE_FAILED",m),e.options))??c,f=on(u)?u:new Be(u);return f.range=c.range,f.tag=r,o!=null&&o.format&&(f.format=o.format),f}function il(s,e,t,n){const{value:i,type:l,comment:r,range:a}=e.type==="block-scalar"?tr(s,e,n):nr(e,s.options.strict,n),o=t?s.directives.tagName(t.source,f=>n(t,"TAG_RESOLVE_FAILED",f)):null;let c;s.options.stringKeys&&s.atKey?c=s.schema[bt]:o?c=Wa(s.schema,i,o,t,n):e.type==="scalar"?c=Ja(s,i,e,n):c=s.schema[bt];let u;try{const f=c.resolve(i,d=>n(t??e,"TAG_RESOLVE_FAILED",d),s.options);u=Nt(f)?f:new Be(f)}catch(f){const d=f instanceof Error?f.message:String(f);n(t??e,"TAG_RESOLVE_FAILED",d),u=new Be(i)}return u.range=a,u.source=i,l&&(u.type=l),o&&(u.tag=o),c.format&&(u.format=c.format),r&&(u.comment=r),u}function Wa(s,e,t,n,i){var a;if(t==="!")return s[bt];const l=[];for(const o of s.tags)if(!o.collection&&o.tag===t)if(o.default&&o.test)l.push(o);else return o;for(const o of l)if((a=o.test)!=null&&a.test(e))return o;const r=s.knownTags[t];return r&&!r.collection?(s.tags.push(Object.assign({},r,{default:!1,test:void 0})),r):(i(n,"TAG_RESOLVE_FAILED",`Unresolved tag: ${t}`,t!=="tag:yaml.org,2002:str"),s[bt])}function Ja({atKey:s,directives:e,schema:t},n,i,l){const r=t.tags.find(a=>{var o;return(a.default===!0||s&&a.default==="key")&&((o=a.test)==null?void 0:o.test(n))})||t[bt];if(t.compat){const a=t.compat.find(o=>{var c;return o.default&&((c=o.test)==null?void 0:c.test(n))})??t[bt];if(r.tag!==a.tag){const o=e.tagString(r.tag),c=e.tagString(a.tag),u=`Value may be parsed as either ${o} or ${c}`;l(i,"TAG_RESOLVE_FAILED",u,!0)}}return r}function za(s,e,t){if(e){t===null&&(t=e.length);for(let n=t-1;n>=0;--n){let i=e[n];switch(i.type){case"space":case"comment":case"newline":s-=i.source.length;continue}for(i=e[++n];(i==null?void 0:i.type)==="space";)s+=i.source.length,i=e[++n];break}}return s}const Xa={composeNode:ll,composeEmptyNode:Qn};function ll(s,e,t,n){const i=s.atKey,{spaceBefore:l,comment:r,anchor:a,tag:o}=t;let c,u=!0;switch(e.type){case"alias":c=Za(s,e,n),(a||o)&&n(e,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":c=il(s,e,o,n),a&&(c.anchor=a.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":c=Ya(Xa,s,e,t,n),a&&(c.anchor=a.source.substring(1));break;default:{const f=e.type==="error"?e.message:`Unsupported token (type: ${e.type})`;n(e,"UNEXPECTED_TOKEN",f),c=Qn(s,e.offset,void 0,null,t,n),u=!1}}return a&&c.anchor===""&&n(a,"BAD_ALIAS","Anchor cannot be an empty string"),i&&s.options.stringKeys&&(!Nt(c)||typeof c.value!="string"||c.tag&&c.tag!=="tag:yaml.org,2002:str")&&n(o??e,"NON_STRING_KEY","With stringKeys, all keys must be strings"),l&&(c.spaceBefore=!0),r&&(e.type==="scalar"&&e.source===""?c.comment=r:c.commentBefore=r),s.options.keepSourceTokens&&u&&(c.srcToken=e),c}function Qn(s,e,t,n,{spaceBefore:i,comment:l,anchor:r,tag:a,end:o},c){const u={type:"scalar",offset:za(e,t,n),indent:-1,source:""},f=il(s,u,a,c);return r&&(f.anchor=r.source.substring(1),f.anchor===""&&c(r,"BAD_ALIAS","Anchor cannot be an empty string")),i&&(f.spaceBefore=!0),l&&(f.comment=l,f.range[2]=o),f}function Za({options:s},{offset:e,source:t,end:n},i){const l=new Hn(t.substring(1));l.source===""&&i(e,"BAD_ALIAS","Alias cannot be an empty string"),l.source.endsWith(":")&&i(e+t.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);const r=e+t.length,a=an(n,r,s.strict,i);return l.range=[e,r,a.offset],a.comment&&(l.comment=a.comment),l}function Qa(s,e,{offset:t,start:n,value:i,end:l},r){const a=Object.assign({_directives:e},s),o=new Vt(void 0,a),c={atKey:!1,atRoot:!0,directives:o.directives,options:o.options,schema:o.schema},u=Lt(n,{indicator:"doc-start",next:i??(l==null?void 0:l[0]),offset:t,onError:r,parentIndent:0,startOnNewline:!0});u.found&&(o.directives.docStart=!0,i&&(i.type==="block-map"||i.type==="block-seq")&&!u.hasNewline&&r(u.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),o.contents=i?ll(c,i,u,r):Qn(c,u.end,n,null,u,r);const f=o.contents.range[2],d=an(l,f,!1,r);return d.comment&&(o.comment=d.comment),o.range=[t,f,d.offset],o}function Ft(s){if(typeof s=="number")return[s,s+1];if(Array.isArray(s))return s.length===2?s:[s[0],s[1]];const{offset:e,source:t}=s;return[e,e+(typeof t=="string"?t.length:1)]}function Fs(s){var i;let e="",t=!1,n=!1;for(let l=0;l<s.length;++l){const r=s[l];switch(r[0]){case"#":e+=(e===""?"":n?`

`:`
`)+(r.substring(1)||" "),t=!0,n=!1;break;case"%":((i=s[l+1])==null?void 0:i[0])!=="#"&&(l+=1),t=!1;break;default:t||(n=!0),t=!1}}return{comment:e,afterEmptyLine:n}}class $n{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(t,n,i,l)=>{const r=Ft(t);l?this.warnings.push(new ji(r,n,i)):this.errors.push(new Ct(r,n,i))},this.directives=new nn({version:e.version||"1.2"}),this.options=e}decorate(e,t){const{comment:n,afterEmptyLine:i}=Fs(this.prelude);if(n){const l=e.contents;if(t)e.comment=e.comment?`${e.comment}
${n}`:n;else if(i||e.directives.docStart||!l)e.commentBefore=n;else if(ft(l)&&!l.flow&&l.items.length>0){let r=l.items[0];Rn(r)&&(r=r.key);const a=r.commentBefore;r.commentBefore=a?`${n}
${a}`:n}else{const r=l.commentBefore;l.commentBefore=r?`${n}
${r}`:n}}t?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:Fs(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,t=!1,n=-1){for(const i of e)yield*this.next(i);yield*this.end(t,n)}*next(e){switch(e.type){case"directive":this.directives.add(e.source,(t,n,i)=>{const l=Ft(e);l[0]+=t,this.onError(l,"BAD_DIRECTIVE",n,i)}),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{const t=Qa(this.options,this.directives,e,this.onError);this.atDirectives&&!t.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(t,!1),this.doc&&(yield this.doc),this.doc=t,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{const t=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,n=new Ct(Ft(e),"UNEXPECTED_TOKEN",t);this.atDirectives||!this.doc?this.errors.push(n):this.doc.errors.push(n);break}case"doc-end":{if(!this.doc){const n="Unexpected doc-end without preceding document";this.errors.push(new Ct(Ft(e),"UNEXPECTED_TOKEN",n));break}this.doc.directives.docEnd=!0;const t=an(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),t.comment){const n=this.doc.comment;this.doc.comment=n?`${n}
${t.comment}`:t.comment}this.doc.range[2]=t.offset;break}default:this.errors.push(new Ct(Ft(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,t=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){const n=Object.assign({_directives:this.directives},this.options),i=new Vt(void 0,n);this.atDirectives&&this.onError(t,"MISSING_CHAR","Missing directives-end indicator line"),i.range=[0,t,t],this.decorate(i,!1),yield i}}}class rl{constructor(){this.lineStarts=[],this.addNewLine=e=>this.lineStarts.push(e),this.linePos=e=>{let t=0,n=this.lineStarts.length;for(;t<n;){const l=t+n>>1;this.lineStarts[l]<e?t=l+1:n=l}if(this.lineStarts[t]===e)return{line:t+1,col:1};if(t===0)return{line:0,col:e};const i=this.lineStarts[t-1];return{line:t,col:e-i+1}}}}function gt(s,e){for(let t=0;t<s.length;++t)if(s[t].type===e)return!0;return!1}function qs(s){for(let e=0;e<s.length;++e)switch(s[e].type){case"space":case"comment":case"newline":break;default:return e}return-1}function ol(s){switch(s==null?void 0:s.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function xt(s){switch(s.type){case"document":return s.start;case"block-map":{const e=s.items[s.items.length-1];return e.sep??e.start}case"block-seq":return s.items[s.items.length-1].start;default:return[]}}function wt(s){var t;if(s.length===0)return[];let e=s.length;e:for(;--e>=0;)switch(s[e].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;((t=s[++e])==null?void 0:t.type)==="space";);return s.splice(e,s.length)}function Bs(s){if(s.start.type==="flow-seq-start")for(const e of s.items)e.sep&&!e.value&&!gt(e.start,"explicit-key-ind")&&!gt(e.sep,"map-value-ind")&&(e.key&&(e.value=e.key),delete e.key,ol(e.value)?e.value.end?Array.prototype.push.apply(e.value.end,e.sep):e.value.end=e.sep:Array.prototype.push.apply(e.start,e.sep),delete e.sep)}class xn{constructor(e){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new Fi,this.onNewLine=e}*parse(e,t=!1){this.onNewLine&&this.offset===0&&this.onNewLine(0);for(const n of this.lexer.lex(e,t))yield*this.next(n);t||(yield*this.end())}*next(e){if(this.source=e,this.atScalar){this.atScalar=!1,yield*this.step(),this.offset+=e.length;return}const t=sr(e);if(t)if(t==="scalar")this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=t,yield*this.step(),t){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+e.length);break;case"space":this.atNewLine&&e[0]===" "&&(this.indent+=e.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=e.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=e.length}else{const n=`Not a YAML token: ${e}`;yield*this.pop({type:"error",offset:this.offset,message:n,source:e}),this.offset+=e.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){const e=this.peek(1);if(this.type==="doc-end"&&(!e||e.type!=="doc-end")){for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source});return}if(!e)return yield*this.stream();switch(e.type){case"document":return yield*this.document(e);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(e);case"block-scalar":return yield*this.blockScalar(e);case"block-map":return yield*this.blockMap(e);case"block-seq":return yield*this.blockSequence(e);case"flow-collection":return yield*this.flowCollection(e);case"doc-end":return yield*this.documentEnd(e)}yield*this.pop()}peek(e){return this.stack[this.stack.length-e]}*pop(e){const t=e??this.stack.pop();if(!t)yield{type:"error",offset:this.offset,source:"",message:"Tried to pop an empty stack"};else if(this.stack.length===0)yield t;else{const n=this.peek(1);switch(t.type==="block-scalar"?t.indent="indent"in n?n.indent:0:t.type==="flow-collection"&&n.type==="document"&&(t.indent=0),t.type==="flow-collection"&&Bs(t),n.type){case"document":n.value=t;break;case"block-scalar":n.props.push(t);break;case"block-map":{const i=n.items[n.items.length-1];if(i.value){n.items.push({start:[],key:t,sep:[]}),this.onKeyLine=!0;return}else if(i.sep)i.value=t;else{Object.assign(i,{key:t,sep:[]}),this.onKeyLine=!i.explicitKey;return}break}case"block-seq":{const i=n.items[n.items.length-1];i.value?n.items.push({start:[],value:t}):i.value=t;break}case"flow-collection":{const i=n.items[n.items.length-1];!i||i.value?n.items.push({start:[],key:t,sep:[]}):i.sep?i.value=t:Object.assign(i,{key:t,sep:[]});return}default:yield*this.pop(),yield*this.pop(t)}if((n.type==="document"||n.type==="block-map"||n.type==="block-seq")&&(t.type==="block-map"||t.type==="block-seq")){const i=t.items[t.items.length-1];i&&!i.sep&&!i.value&&i.start.length>0&&qs(i.start)===-1&&(t.indent===0||i.start.every(l=>l.type!=="comment"||l.indent<t.indent))&&(n.type==="document"?n.end=i.start:n.items.push({start:i.start}),t.items.splice(-1,1))}}}*stream(){switch(this.type){case"directive-line":yield{type:"directive",offset:this.offset,source:this.source};return;case"byte-order-mark":case"space":case"comment":case"newline":yield this.sourceToken;return;case"doc-mode":case"doc-start":{const e={type:"document",offset:this.offset,start:[]};this.type==="doc-start"&&e.start.push(this.sourceToken),this.stack.push(e);return}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(e){if(e.value)return yield*this.lineEnd(e);switch(this.type){case"doc-start":{qs(e.start)!==-1?(yield*this.pop(),yield*this.step()):e.start.push(this.sourceToken);return}case"anchor":case"tag":case"space":case"comment":case"newline":e.start.push(this.sourceToken);return}const t=this.startBlockValue(e);t?this.stack.push(t):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(e){if(this.type==="map-value-ind"){const t=xt(this.peek(2)),n=wt(t);let i;e.end?(i=e.end,i.push(this.sourceToken),delete e.end):i=[this.sourceToken];const l={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:n,key:e,sep:i}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=l}else yield*this.lineEnd(e)}*blockScalar(e){switch(this.type){case"space":case"comment":case"newline":e.props.push(this.sourceToken);return;case"scalar":if(e.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let t=this.source.indexOf(`
`)+1;for(;t!==0;)this.onNewLine(this.offset+t),t=this.source.indexOf(`
`,t)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(e){var n;const t=e.items[e.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,t.value){const i="end"in t.value?t.value.end:void 0,l=Array.isArray(i)?i[i.length-1]:void 0;(l==null?void 0:l.type)==="comment"?i==null||i.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else if(t.sep)t.sep.push(this.sourceToken);else{if(this.atIndentedComment(t.start,e.indent)){const i=e.items[e.items.length-2],l=(n=i==null?void 0:i.value)==null?void 0:n.end;if(Array.isArray(l)){Array.prototype.push.apply(l,t.start),l.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return}if(this.indent>=e.indent){const i=!this.onKeyLine&&this.indent===e.indent,l=i&&(t.sep||t.explicitKey)&&this.type!=="seq-item-ind";let r=[];if(l&&t.sep&&!t.value){const a=[];for(let o=0;o<t.sep.length;++o){const c=t.sep[o];switch(c.type){case"newline":a.push(o);break;case"space":break;case"comment":c.indent>e.indent&&(a.length=0);break;default:a.length=0}}a.length>=2&&(r=t.sep.splice(a[1]))}switch(this.type){case"anchor":case"tag":l||t.value?(r.push(this.sourceToken),e.items.push({start:r}),this.onKeyLine=!0):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"explicit-key-ind":!t.sep&&!t.explicitKey?(t.start.push(this.sourceToken),t.explicitKey=!0):l||t.value?(r.push(this.sourceToken),e.items.push({start:r,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}),this.onKeyLine=!0;return;case"map-value-ind":if(t.explicitKey)if(t.sep)if(t.value)e.items.push({start:[],key:null,sep:[this.sourceToken]});else if(gt(t.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,key:null,sep:[this.sourceToken]}]});else if(ol(t.key)&&!gt(t.sep,"newline")){const a=wt(t.start),o=t.key,c=t.sep;c.push(this.sourceToken),delete t.key,delete t.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:a,key:o,sep:c}]})}else r.length>0?t.sep=t.sep.concat(r,this.sourceToken):t.sep.push(this.sourceToken);else if(gt(t.start,"newline"))Object.assign(t,{key:null,sep:[this.sourceToken]});else{const a=wt(t.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:a,key:null,sep:[this.sourceToken]}]})}else t.sep?t.value||l?e.items.push({start:r,key:null,sep:[this.sourceToken]}):gt(t.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});this.onKeyLine=!0;return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const a=this.flowScalar(this.type);l||t.value?(e.items.push({start:r,key:a,sep:[]}),this.onKeyLine=!0):t.sep?this.stack.push(a):(Object.assign(t,{key:a,sep:[]}),this.onKeyLine=!0);return}default:{const a=this.startBlockValue(e);if(a){i&&a.type!=="block-seq"&&e.items.push({start:r}),this.stack.push(a);return}}}}yield*this.pop(),yield*this.step()}*blockSequence(e){var n;const t=e.items[e.items.length-1];switch(this.type){case"newline":if(t.value){const i="end"in t.value?t.value.end:void 0,l=Array.isArray(i)?i[i.length-1]:void 0;(l==null?void 0:l.type)==="comment"?i==null||i.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(t.start,e.indent)){const i=e.items[e.items.length-2],l=(n=i==null?void 0:i.value)==null?void 0:n.end;if(Array.isArray(l)){Array.prototype.push.apply(l,t.start),l.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return;case"anchor":case"tag":if(t.value||this.indent<=e.indent)break;t.start.push(this.sourceToken);return;case"seq-item-ind":if(this.indent!==e.indent)break;t.value||gt(t.start,"seq-item-ind")?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return}if(this.indent>e.indent){const i=this.startBlockValue(e);if(i){this.stack.push(i);return}}yield*this.pop(),yield*this.step()}*flowCollection(e){const t=e.items[e.items.length-1];if(this.type==="flow-error-end"){let n;do yield*this.pop(),n=this.peek(1);while(n&&n.type==="flow-collection")}else if(e.end.length===0){switch(this.type){case"comma":case"explicit-key-ind":!t||t.sep?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return;case"map-value-ind":!t||t.value?e.items.push({start:[],key:null,sep:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});return;case"space":case"comment":case"newline":case"anchor":case"tag":!t||t.value?e.items.push({start:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const i=this.flowScalar(this.type);!t||t.value?e.items.push({start:[],key:i,sep:[]}):t.sep?this.stack.push(i):Object.assign(t,{key:i,sep:[]});return}case"flow-map-end":case"flow-seq-end":e.end.push(this.sourceToken);return}const n=this.startBlockValue(e);n?this.stack.push(n):(yield*this.pop(),yield*this.step())}else{const n=this.peek(2);if(n.type==="block-map"&&(this.type==="map-value-ind"&&n.indent===e.indent||this.type==="newline"&&!n.items[n.items.length-1].sep))yield*this.pop(),yield*this.step();else if(this.type==="map-value-ind"&&n.type!=="flow-collection"){const i=xt(n),l=wt(i);Bs(e);const r=e.end.splice(1,e.end.length);r.push(this.sourceToken);const a={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:l,key:e,sep:r}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=a}else yield*this.lineEnd(e)}}flowScalar(e){if(this.onNewLine){let t=this.source.indexOf(`
`)+1;for(;t!==0;)this.onNewLine(this.offset+t),t=this.source.indexOf(`
`,t)+1}return{type:e,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(e){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;const t=xt(e),n=wt(t);return n.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:n,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;const t=xt(e),n=wt(t);return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:n,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(e,t){return this.type!=="comment"||this.indent<=t?!1:e.every(n=>n.type==="newline"||n.type==="space")}*documentEnd(e){this.type!=="doc-mode"&&(e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop()))}*lineEnd(e){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;case"space":case"comment":default:e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop())}}}function al(s){const e=s.prettyErrors!==!1;return{lineCounter:s.lineCounter||e&&new rl||null,prettyErrors:e}}function $a(s,e={}){const{lineCounter:t,prettyErrors:n}=al(e),i=new xn(t==null?void 0:t.addNewLine),l=new $n(e),r=Array.from(l.compose(i.parse(s)));if(n&&t)for(const a of r)a.errors.forEach(sn(s,t)),a.warnings.forEach(sn(s,t));return r.length>0?r:Object.assign([],{empty:!0},l.streamInfo())}function cl(s,e={}){const{lineCounter:t,prettyErrors:n}=al(e),i=new xn(t==null?void 0:t.addNewLine),l=new $n(e);let r=null;for(const a of l.compose(i.parse(s),!0,s.length))if(!r)r=a;else if(r.options.logLevel!=="silent"){r.errors.push(new Ct(a.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}return n&&t&&(r.errors.forEach(sn(s,t)),r.warnings.forEach(sn(s,t))),r}function xa(s,e,t){let n;typeof e=="function"?n=e:t===void 0&&e&&typeof e=="object"&&(t=e);const i=cl(s,t);if(!i)return null;if(i.warnings.forEach(l=>ir(i.options.logLevel,l)),i.errors.length>0){if(i.options.logLevel!=="silent")throw i.errors[0];i.errors=[]}return i.toJS(Object.assign({reviver:n},t))}function ec(s,e,t){let n=null;if(typeof e=="function"||Array.isArray(e)?n=e:t===void 0&&e&&(t=e),typeof t=="string"&&(t=t.length),typeof t=="number"){const i=Math.round(t);t=i<1?void 0:i>8?{indent:8}:{indent:i}}if(s===void 0){const{keepUndefined:i}=t??e??{};if(!i)return}return qi(s)&&!n?s.toString(t):new Vt(s,n,t).toString(t)}const Hs=Object.freeze(Object.defineProperty({__proto__:null,Alias:Hn,CST:lr,Composer:$n,Document:Vt,Lexer:Fi,LineCounter:rl,Pair:Rt,Parser:xn,Scalar:Be,Schema:pn,YAMLError:rr,YAMLMap:_t,YAMLParseError:Ct,YAMLSeq:It,YAMLWarning:ji,isAlias:or,isCollection:ft,isDocument:qi,isMap:Di,isNode:on,isPair:Rn,isScalar:Nt,isSeq:Mi,parse:xa,parseAllDocuments:$a,parseDocument:cl,stringify:ec,visit:ar,visitAsync:cr},Symbol.toStringTag,{value:"Module"}));function Rs(s,e,t){const n=s.slice();return n[23]=e[t],n}function Ks(s){let e,t,n;function i(r){s[15](r)}let l={disabled:s[6],required:s[0].required};return s[4]!==void 0&&(l.checked=s[4]),e=new Yn({props:l}),Ee.push(()=>Ge(e,"checked",i)),{c(){Z(e.$$.fragment)},l(r){X(e.$$.fragment,r)},m(r,a){z(e,r,a),n=!0},p(r,a){const o={};a&64&&(o.disabled=r[6]),a&1&&(o.required=r[0].required),!t&&a&16&&(t=!0,o.checked=r[4],Ue(()=>t=!1)),e.$set(o)},i(r){n||(T(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){J(e,r)}}}function tc(s){let e,t,n="Choose file",i,l,r,a,o,c;function u(m,h){return m[4]?oc:rc}let f=u(s),d=f(s);return{c(){e=w("label"),t=w("span"),t.textContent=n,i=B(),l=w("span"),d.c(),r=B(),a=w("input"),this.h()},l(m){e=k(m,"LABEL",{class:!0});var h=M(e);t=k(h,"SPAN",{class:!0,"data-svelte-h":!0}),oe(t)!=="svelte-18m35yh"&&(t.textContent=n),i=q(h),l=k(h,"SPAN",{class:!0});var _=M(l);d.l(_),_.forEach(p),r=q(h),a=k(h,"INPUT",{class:!0,type:!0}),h.forEach(p),this.h()},h(){E(t,"class","text-base-content"),E(l,"class","text-base-content-muted"),E(a,"class","hidden"),a.disabled=s[6],E(a,"type","file"),E(e,"class","flex justify-between items-center px-3 py-2 border border-base-300 rounded-md cursor-pointer hover:bg-base-200 transition-colors")},m(m,h){j(m,e,h),g(e,t),g(e,i),g(e,l),d.m(l,null),g(e,r),g(e,a),o||(c=ce(a,"change",s[8]),o=!0)},p(m,h){f===(f=u(m))&&d?d.p(m,h):(d.d(1),d=f(m),d&&(d.c(),d.m(l,null))),h&64&&(a.disabled=m[6])},d(m){m&&p(e),d.d(),o=!1,c()}}}function nc(s){let e,t,n,i,l,r=Ne(s[0].options),a=[];for(let o=0;o<r.length;o+=1)a[o]=Us(Rs(s,r,o));return{c(){e=w("select"),t=w("option");for(let o=0;o<a.length;o+=1)a[o].c();this.h()},l(o){e=k(o,"SELECT",{class:!0});var c=M(e);t=k(c,"OPTION",{}),M(t).forEach(p);for(let u=0;u<a.length;u+=1)a[u].l(c);c.forEach(p),this.h()},h(){t.disabled=n=s[0].required,t.__value=void 0,we(t,t.__value),e.disabled=s[6],E(e,"class",gn),s[4]===void 0&&ke(()=>s[20].call(e))},m(o,c){j(o,e,c),g(e,t);for(let u=0;u<a.length;u+=1)a[u]&&a[u].m(e,null);St(e,s[4],!0),i||(l=ce(e,"change",s[20]),i=!0)},p(o,c){if(c&1&&n!==(n=o[0].required)&&(t.disabled=n),c&1){r=Ne(o[0].options);let u;for(u=0;u<r.length;u+=1){const f=Rs(o,r,u);a[u]?a[u].p(f,c):(a[u]=Us(f),a[u].c(),a[u].m(e,null))}for(;u<a.length;u+=1)a[u].d(1);a.length=r.length}c&64&&(e.disabled=o[6]),c&17&&St(e,o[4])},d(o){o&&p(e),at(a,o),i=!1,l()}}}function sc(s){let e,t,n,i;return{c(){e=w("input"),this.h()},l(l){e=k(l,"INPUT",{class:!0,type:!0}),this.h()},h(){E(e,"class",gn),e.disabled=s[6],e.required=t=s[0].required,E(e,"type","number")},m(l,r){j(l,e,r),we(e,s[4]),n||(i=ce(e,"input",s[19]),n=!0)},p(l,r){r&64&&(e.disabled=l[6]),r&1&&t!==(t=l[0].required)&&(e.required=t),r&17&&ki(e.value)!==l[4]&&we(e,l[4])},d(l){l&&p(e),n=!1,i()}}}function ic(s){let e,t,n,i;return{c(){e=w("textarea"),this.h()},l(l){e=k(l,"TEXTAREA",{rows:!0}),M(e).forEach(p),this.h()},h(){e.disabled=s[6],e.required=t=s[0].required,E(e,"rows","5")},m(l,r){j(l,e,r),we(e,s[4]),n||(i=ce(e,"input",s[18]),n=!0)},p(l,r){r&64&&(e.disabled=l[6]),r&1&&t!==(t=l[0].required)&&(e.required=t),r&17&&we(e,l[4])},d(l){l&&p(e),n=!1,i()}}}function lc(s){let e;function t(l,r){return l[0].secret&&!l[2]&&l[0].shown!==!0?fc:uc}let n=t(s),i=n(s);return{c(){i.c(),e=fe()},l(l){i.l(l),e=fe()},m(l,r){i.m(l,r),j(l,e,r)},p(l,r){n===(n=t(l))&&i?i.p(l,r):(i.d(1),i=n(l),i&&(i.c(),i.m(e.parentNode,e)))},d(l){l&&p(e),i.d(l)}}}function rc(s){let e;return{c(){e=le("No file selected")},l(t){e=ie(t,"No file selected")},m(t,n){j(t,e,n)},p:re,d(t){t&&p(e)}}}function oc(s){let e=s[4].name+"",t;return{c(){t=le(e)},l(n){t=ie(n,e)},m(n,i){j(n,t,i)},p(n,i){i&16&&e!==(e=n[4].name+"")&&ye(t,e)},d(n){n&&p(t)}}}function ac(s){let e,t=s[23].label+"",n,i;return{c(){e=w("option"),n=le(t),this.h()},l(l){e=k(l,"OPTION",{});var r=M(e);n=ie(r,t),r.forEach(p),this.h()},h(){e.__value=i=s[23].value,we(e,e.__value)},m(l,r){j(l,e,r),g(e,n)},p(l,r){r&1&&t!==(t=l[23].label+"")&&ye(n,t),r&1&&i!==(i=l[23].value)&&(e.__value=i,we(e,e.__value))},d(l){l&&p(e)}}}function cc(s){let e,t=s[23]+"",n,i;return{c(){e=w("option"),n=le(t),this.h()},l(l){e=k(l,"OPTION",{});var r=M(e);n=ie(r,t),r.forEach(p),this.h()},h(){e.__value=i=s[23],we(e,e.__value)},m(l,r){j(l,e,r),g(e,n)},p(l,r){r&1&&t!==(t=l[23]+"")&&ye(n,t),r&1&&i!==(i=l[23])&&(e.__value=i,we(e,e.__value))},d(l){l&&p(e)}}}function Us(s){let e;function t(l,r){return typeof l[23]=="string"?cc:ac}let n=t(s),i=n(s);return{c(){i.c(),e=fe()},l(l){i.l(l),e=fe()},m(l,r){i.m(l,r),j(l,e,r)},p(l,r){n===(n=t(l))&&i?i.p(l,r):(i.d(1),i=n(l),i&&(i.c(),i.m(e.parentNode,e)))},d(l){l&&p(e),i.d(l)}}}function uc(s){let e,t,n,i;return{c(){e=w("input"),this.h()},l(l){e=k(l,"INPUT",{class:!0,type:!0}),this.h()},h(){E(e,"class",gn),e.disabled=s[6],e.required=t=s[0].required,E(e,"type","text")},m(l,r){j(l,e,r),we(e,s[4]),n||(i=ce(e,"input",s[17]),n=!0)},p(l,r){r&64&&(e.disabled=l[6]),r&1&&t!==(t=l[0].required)&&(e.required=t),r&17&&e.value!==l[4]&&we(e,l[4])},d(l){l&&p(e),n=!1,i()}}}function fc(s){let e,t,n,i;return{c(){e=w("input"),this.h()},l(l){e=k(l,"INPUT",{class:!0,type:!0}),this.h()},h(){E(e,"class",gn),e.disabled=s[6],e.required=t=s[0].required,E(e,"type","password")},m(l,r){j(l,e,r),we(e,s[4]),n||(i=ce(e,"input",s[16]),n=!0)},p(l,r){r&64&&(e.disabled=l[6]),r&1&&t!==(t=l[0].required)&&(e.required=t),r&17&&e.value!==l[4]&&we(e,l[4])},d(l){l&&p(e),n=!1,i()}}}function Gs(s){let e,t=s[0].description+"",n;return{c(){e=w("p"),n=le(t),this.h()},l(i){e=k(i,"P",{class:!0});var l=M(e);n=ie(l,t),l.forEach(p),this.h()},h(){E(e,"class","text-xs text-base-content-muted break-words max-w-2xl")},m(i,l){j(i,e,l),g(e,n)},p(i,l){l&1&&t!==(t=i[0].description+"")&&ye(n,t)},d(i){i&&p(e)}}}function Ys(s){var o;let e,t,n,i,l;function r(c){s[21](c)}let a={rootOptions:s[1],reveal:s[2],disabled:s[6],optionSpec:(o=s[0].children)==null?void 0:o[s[4]]};return s[3]!==void 0&&(a.options=s[3]),t=new ul({props:a}),Ee.push(()=>Ge(t,"options",r)),{c(){e=w("div"),Z(t.$$.fragment),this.h()},l(c){e=k(c,"DIV",{class:!0});var u=M(e);X(t.$$.fragment,u),u.forEach(p),this.h()},h(){E(e,"class","pl-4 border-l-2 mt-2 border-base-200")},m(c,u){j(c,e,u),z(t,e,null),l=!0},p(c,u){var d;const f={};u&2&&(f.rootOptions=c[1]),u&4&&(f.reveal=c[2]),u&64&&(f.disabled=c[6]),u&17&&(f.optionSpec=(d=c[0].children)==null?void 0:d[c[4]]),!n&&u&8&&(n=!0,f.options=c[3],Ue(()=>n=!1)),t.$set(f)},i(c){l||(T(t.$$.fragment,c),c&&ke(()=>{l&&(i||(i=nt(e,dt,{},!0)),i.run(1))}),l=!0)},o(c){P(t.$$.fragment,c),c&&(i||(i=nt(e,dt,{},!1)),i.run(0)),l=!1},d(c){c&&p(e),J(t),c&&i&&i.end()}}}function dc(s){var I,C;let e,t,n,i,l,r,a,o,c,u,f,d,m=Object.keys(((C=(I=s[0])==null?void 0:I.children)==null?void 0:C[s[4]])??{}).length,h,_=s[0].type==="boolean"&&Ks(s);function N(D,L){if(L&1&&(a=null),D[0].type==="string")return lc;if(D[0].type==="multiline")return ic;if(D[0].type==="number")return sc;if(a==null&&(a=!!(D[0].type==="select"&&Array.isArray(D[0].options))),a)return nc;if(D[0].type==="file")return tc}let S=N(s,-1),O=S&&S(s),A=s[0].description&&Gs(s),v=m&&Ys(s);return{c(){e=w("div"),t=w("label"),n=w("span"),i=le(s[7]),l=B(),_&&_.c(),r=B(),O&&O.c(),o=B(),c=w("p"),u=le(s[5]),f=B(),A&&A.c(),d=B(),v&&v.c(),this.h()},l(D){e=k(D,"DIV",{class:!0});var L=M(e);t=k(L,"LABEL",{class:!0});var V=M(t);n=k(V,"SPAN",{class:!0});var b=M(n);i=ie(b,s[7]),l=q(b),_&&_.l(b),b.forEach(p),r=q(V),O&&O.l(V),o=q(V),c=k(V,"P",{class:!0});var y=M(c);u=ie(y,s[5]),y.forEach(p),V.forEach(p),f=q(L),A&&A.l(L),d=q(L),v&&v.l(L),L.forEach(p),this.h()},h(){E(n,"class","text-sm font-medium flex justify-between items-center"),E(c,"class","text-negative text-xs"),E(t,"class","flex flex-col gap-2"),E(e,"class","w-full mb-2")},m(D,L){j(D,e,L),g(e,t),g(t,n),g(n,i),g(n,l),_&&_.m(n,null),g(t,r),O&&O.m(t,null),g(t,o),g(t,c),g(c,u),g(e,f),A&&A.m(e,null),g(e,d),v&&v.m(e,null),h=!0},p(D,[L]){var V,b;(!h||L&128)&&ye(i,D[7]),D[0].type==="boolean"?_?(_.p(D,L),L&1&&T(_,1)):(_=Ks(D),_.c(),T(_,1),_.m(n,null)):_&&(pe(),P(_,1,1,()=>{_=null}),ge()),S===(S=N(D,L))&&O?O.p(D,L):(O&&O.d(1),O=S&&S(D),O&&(O.c(),O.m(t,o))),(!h||L&32)&&ye(u,D[5]),D[0].description?A?A.p(D,L):(A=Gs(D),A.c(),A.m(e,d)):A&&(A.d(1),A=null),L&17&&(m=Object.keys(((b=(V=D[0])==null?void 0:V.children)==null?void 0:b[D[4]])??{}).length),m?v?(v.p(D,L),L&17&&T(v,1)):(v=Ys(D),v.c(),T(v,1),v.m(e,null)):v&&(pe(),P(v,1,1,()=>{v=null}),ge())},i(D){h||(T(_),T(v),h=!0)},o(D){P(_),P(v),h=!1},d(D){D&&p(e),_&&_.d(),O&&O.d(),A&&A.d(),v&&v.d()}}}const gn="rounded-md border border-base-300 bg-base-100 shadow-sm px-2 py-1 text-sm focus-visible:ring-base-300 flex h-9 w-full transition-colors focus-visible:outline-none focus-visible:ring-1 p-1 ml-auto align-middle";function mc(s,e,t){let n,i,l,{spec:r}=e,{key:a}=e,{options:o}=e,{disabled:c}=e,{rootOptions:u}=e,{reveal:f}=e;const d=`_${a}`;let m,h;r.children?r.nest?(m=d,h=o[a]??{}):(m=a,h=o):(m=a,h={});let _=o[m],N={};async function S(b){if(!b.target)return;const{files:y}=b.target;if(!y)return;const[H]=y;switch(r.fileFormat){case"json":try{t(9,o[m]=await H.text().then(U=>JSON.parse(U)),o)}catch(U){t(5,O="Failed to parse YAML file"),console.warn(U)}break;case"yaml":try{t(9,o[m]=await H.text().then(U=>Hs.parse(U)),o)}catch(U){t(5,O="Failed to parse JSON file"),console.warn(U)}break;default:{const U=await H.text();try{t(9,o[m]=JSON.parse(U),o);break}catch{}try{t(9,o[m]=Hs.parse(U),o);break}catch{}t(9,o[m]=await H.text(),o);break}}}let O="";function A(b){_=b,t(4,_),t(14,i),t(0,r),t(1,u)}function v(){_=this.value,t(4,_),t(14,i),t(0,r),t(1,u),t(0,r)}function I(){_=this.value,t(4,_),t(14,i),t(0,r),t(1,u),t(0,r)}function C(){_=this.value,t(4,_),t(14,i),t(0,r),t(1,u),t(0,r)}function D(){_=ki(this.value),t(4,_),t(14,i),t(0,r),t(1,u),t(0,r)}function L(){_=jn(this),t(4,_),t(14,i),t(0,r),t(1,u),t(0,r)}function V(b){h=b,t(3,h),t(0,r),t(4,_),t(10,a),t(9,o),t(13,N),t(12,m),t(14,i),t(1,u)}return s.$$set=b=>{"spec"in b&&t(0,r=b.spec),"key"in b&&t(10,a=b.key),"options"in b&&t(9,o=b.options),"disabled"in b&&t(11,c=b.disabled),"rootOptions"in b&&t(1,u=b.rootOptions),"reveal"in b&&t(2,f=b.reveal)},s.$$.update=()=>{var b,y,H;if(s.$$.dirty&1025&&t(7,n=r.title??a),s.$$.dirty&3&&t(14,i=r.references?ur.query(u,r.references):null),s.$$.dirty&16384&&i!=null&&i.length&&t(4,_=i[0]),s.$$.dirty&13849)if(r!=null&&r.children&&!Object.keys(r.children[_]??{}).length){if(t(9,o[a]=_,o),delete o[d],typeof h=="object")for(const U of Object.keys(N))delete h[U];else console.warn(`child_value_target was unexpectedly not an object ${h}`,{key:a,options:o,spec:r});t(13,N=((b=r==null?void 0:r.children)==null?void 0:b[_])??{})}else(y=r==null?void 0:r.children)!=null&&y[_]&&(r.nest?(t(12,m=d),typeof o[a]!="object"&&t(9,o[a]={},o),t(3,h=o[a])):(t(12,m=a),t(3,h=o)),t(9,o[m]=_,o),t(13,N=((H=r==null?void 0:r.children)==null?void 0:H[_])??{}));s.$$.dirty&4112&&t(9,o[m]=_,o),s.$$.dirty&18433&&t(6,l=c||r.forceReference||r.references&&i!==null)},[r,u,f,h,_,O,l,n,S,o,a,c,m,N,i,A,v,I,C,D,L,V]}class hc extends be{constructor(e){super(),ve(this,e,mc,dc,_e,{spec:0,key:10,options:9,disabled:11,rootOptions:1,reveal:2})}}function Ws(s,e,t){const n=s.slice();return n[6]=e[t][0],n[7]=e[t][1],n}function Js(s){let e,t,n;function i(r){s[5](r)}let l={reveal:s[4],disabled:s[2],key:s[6],spec:s[7],rootOptions:s[3]};return s[0]!==void 0&&(l.options=s[0]),e=new hc({props:l}),Ee.push(()=>Ge(e,"options",i)),{c(){Z(e.$$.fragment)},l(r){X(e.$$.fragment,r)},m(r,a){z(e,r,a),n=!0},p(r,a){const o={};a&16&&(o.reveal=r[4]),a&4&&(o.disabled=r[2]),a&2&&(o.key=r[6]),a&2&&(o.spec=r[7]),a&8&&(o.rootOptions=r[3]),!t&&a&1&&(t=!0,o.options=r[0],Ue(()=>t=!1)),e.$set(o)},i(r){n||(T(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){J(e,r)}}}function pc(s){let e,t,n=Ne(Object.entries(s[1])),i=[];for(let r=0;r<n.length;r+=1)i[r]=Js(Ws(s,n,r));const l=r=>P(i[r],1,1,()=>{i[r]=null});return{c(){for(let r=0;r<i.length;r+=1)i[r].c();e=fe()},l(r){for(let a=0;a<i.length;a+=1)i[a].l(r);e=fe()},m(r,a){for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(r,a);j(r,e,a),t=!0},p(r,[a]){if(a&31){n=Ne(Object.entries(r[1]));let o;for(o=0;o<n.length;o+=1){const c=Ws(r,n,o);i[o]?(i[o].p(c,a),T(i[o],1)):(i[o]=Js(c),i[o].c(),T(i[o],1),i[o].m(e.parentNode,e))}for(pe(),o=n.length;o<i.length;o+=1)l(o);ge()}},i(r){if(!t){for(let a=0;a<n.length;a+=1)T(i[a]);t=!0}},o(r){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)P(i[a]);t=!1},d(r){r&&p(e),at(i,r)}}}function gc(s,e,t){let{optionSpec:n}=e,{options:i={}}=e,{disabled:l=!1}=e,{rootOptions:r=i}=e,{reveal:a}=e;for(const[c,u]of Object.entries(n))u.default&&!i[c]&&(i[c]=u.default);function o(c){i=c,t(0,i)}return s.$$set=c=>{"optionSpec"in c&&t(1,n=c.optionSpec),"options"in c&&t(0,i=c.options),"disabled"in c&&t(2,l=c.disabled),"rootOptions"in c&&t(3,r=c.rootOptions),"reveal"in c&&t(4,a=c.reveal)},[i,n,l,r,a,o]}class ul extends be{constructor(e){super(),ve(this,e,gc,pc,_e,{optionSpec:1,options:0,disabled:2,rootOptions:3,reveal:4})}}function zs(s){let e,t,n,i;function l(o){s[14](o)}function r(o){s[15](o)}let a={};return s[0].name!==void 0&&(a.sourceName=s[0].name),s[10]!==void 0&&(a.nameError=s[10]),e=new Ui({props:a}),Ee.push(()=>Ge(e,"sourceName",l)),Ee.push(()=>Ge(e,"nameError",r)),{c(){Z(e.$$.fragment)},l(o){X(e.$$.fragment,o)},m(o,c){z(e,o,c),i=!0},p(o,c){const u={};!t&&c&1&&(t=!0,u.sourceName=o[0].name,Ue(()=>t=!1)),!n&&c&1024&&(n=!0,u.nameError=o[10],Ue(()=>n=!1)),e.$set(u)},i(o){i||(T(e.$$.fragment,o),i=!0)},o(o){P(e.$$.fragment,o),i=!1},d(o){J(e,o)}}}function Xs(s){var r;let e,t,n;function i(a){s[16](a)}let l={reveal:s[3],disabled:s[5]||s[9],rootOptions:s[0].options,optionSpec:(r=s[1])==null?void 0:r.options};return s[0].options!==void 0&&(l.options=s[0].options),e=new ul({props:l}),Ee.push(()=>Ge(e,"options",i)),{c(){Z(e.$$.fragment)},l(a){X(e.$$.fragment,a)},m(a,o){z(e,a,o),n=!0},p(a,o){var u;const c={};o&8&&(c.reveal=a[3]),o&544&&(c.disabled=a[5]||a[9]),o&1&&(c.rootOptions=a[0].options),o&2&&(c.optionSpec=(u=a[1])==null?void 0:u.options),!t&&o&1&&(t=!0,c.options=a[0].options,Ue(()=>t=!1)),e.$set(c)},i(a){n||(T(e.$$.fragment,a),n=!0)},o(a){P(e.$$.fragment,a),n=!1},d(a){J(e,a)}}}function Zs(s){var c;let e,t,n,i,l,r;function a(u){s[17](u)}let o={id:"reveal-switch-"+((c=s[0])==null?void 0:c.name)};return s[3]!==void 0&&(o.checked=s[3]),n=new Yn({props:o}),Ee.push(()=>Ge(n,"checked",a)),{c(){e=w("label"),t=le(`Show Hidden Values
				`),Z(n.$$.fragment),this.h()},l(u){e=k(u,"LABEL",{for:!0,class:!0});var f=M(e);t=ie(f,`Show Hidden Values
				`),X(n.$$.fragment,f),f.forEach(p),this.h()},h(){var u;E(e,"for",l="reveal-switch-"+((u=s[0])==null?void 0:u.name)),E(e,"class","flex gap-2 items-center pt-4 border-t border-base-200")},m(u,f){j(u,e,f),g(e,t),z(n,e,null),r=!0},p(u,f){var m,h;const d={};f&1&&(d.id="reveal-switch-"+((m=u[0])==null?void 0:m.name)),!i&&f&8&&(i=!0,d.checked=u[3],Ue(()=>i=!1)),n.$set(d),(!r||f&1&&l!==(l="reveal-switch-"+((h=u[0])==null?void 0:h.name)))&&E(e,"for",l)},i(u){r||(T(n.$$.fragment,u),r=!0)},o(u){P(n.$$.fragment,u),r=!1},d(u){u&&p(e),J(n)}}}function _c(s){let e,t='<div class="h-2 w-2 bg-positive rounded-full inline-flex items-center justify-center animate-pulse"><div class="h-2 w-2 rounded-full bg-positive"></div></div> <p class="text-base-content-muted font-medium text-xs">Connected</p>',n;return{c(){e=w("div"),e.innerHTML=t,this.h()},l(i){e=k(i,"DIV",{class:!0,"data-svelte-h":!0}),oe(e)!=="svelte-rze512"&&(e.innerHTML=t),this.h()},h(){E(e,"class","flex gap-2 items-center")},m(i,l){j(i,e,l)},p:re,i(i){i&&(n||ke(()=>{n=Ae(e,Xe,{}),n.start()}))},o:re,d(i){i&&p(e)}}}function bc(s){let e,t,n;return{c(){e=w("p"),t=le(s[8]),this.h()},l(i){e=k(i,"P",{class:!0});var l=M(e);t=ie(l,s[8]),l.forEach(p),this.h()},h(){E(e,"class","text-negative text-xs max-w-md break-words")},m(i,l){j(i,e,l),g(e,t)},p(i,l){l&256&&ye(t,i[8])},i(i){i&&(n||ke(()=>{n=Ae(e,Xe,{}),n.start()}))},o:re,d(i){i&&p(e)}}}function vc(s){let e,t,n;return{c(){e=w("p"),t=le(s[4]),this.h()},l(i){e=k(i,"P",{class:!0});var l=M(e);t=ie(l,s[4]),l.forEach(p),this.h()},h(){E(e,"class","text-negative text-xs max-w-md break-words")},m(i,l){j(i,e,l),g(e,t)},p(i,l){l&16&&ye(t,i[4])},i(i){i&&(n||ke(()=>{n=Ae(e,Xe,{}),n.start()}))},o:re,d(i){i&&p(e)}}}function Qs(s){let e,t;return e=new We({props:{variant:"ghost",type:"button",$$slots:{default:[yc]},$$scope:{ctx:s}}}),e.$on("click",s[11]),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i&524288&&(l.$$scope={dirty:i,ctx:n}),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function yc(s){let e;return{c(){e=le("Back")},l(t){e=ie(t,"Back")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function kc(s){let e,t,n,i;return t=new We({props:{variant:"primary",disabled:s[5]||s[9],class:s[9]?"animate-pulse":"w-32",type:"submit",$$slots:{default:[Tc]},$$scope:{ctx:s}}}),{c(){e=w("div"),Z(t.$$.fragment)},l(l){e=k(l,"DIV",{});var r=M(e);X(t.$$.fragment,r),r.forEach(p)},m(l,r){j(l,e,r),z(t,e,null),i=!0},p(l,r){const a={};r&544&&(a.disabled=l[5]||l[9]),r&512&&(a.class=l[9]?"animate-pulse":"w-32"),r&524288&&(a.$$scope={dirty:r,ctx:l}),t.$set(a)},i(l){i||(T(t.$$.fragment,l),l&&(n||ke(()=>{n=Ae(e,Xe,{}),n.start()})),i=!0)},o(l){P(t.$$.fragment,l),i=!1},d(l){l&&p(e),J(t)}}}function wc(s){let e,t;return e=new We({props:{variant:"primary",formaction:"?/testSource",disabled:s[9]||s[5],class:s[9]?"animate-pulse w-32":"w-32",$$slots:{default:[Ec]},$$scope:{ctx:s}}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i&544&&(l.disabled=n[9]||n[5]),i&512&&(l.class=n[9]?"animate-pulse w-32":"w-32"),i&524288&&(l.$$scope={dirty:i,ctx:n}),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Cc(s){let e,t;return e=new We({props:{variant:"primary",disabled:s[5]||s[9],class:s[9]?"animate-pulse":"w-full",type:"submit",$$slots:{default:[Sc]},$$scope:{ctx:s}}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i&544&&(l.disabled=n[5]||n[9]),i&512&&(l.class=n[9]?"animate-pulse":"w-full"),i&524288&&(l.$$scope={dirty:i,ctx:n}),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Tc(s){let e;return{c(){e=le("Save")},l(t){e=ie(t,"Save")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function Ec(s){let e;return{c(){e=le("Test Configuration")},l(t){e=ie(t,"Test Configuration")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function Sc(s){let e;return{c(){e=le("Save")},l(t){e=ie(t,"Save")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function Ic(s){var te,ee;let e,t,n,i,l,r,a=Object.keys((te=s[1])==null?void 0:te.options).length,o,c=Dn((ee=s[1])==null?void 0:ee.options),u,f,d,m,h,_,N,S,O,A,v,I,C,D,L,V=!s[2]&&zs(s),b=a&&Xs(s),y=c&&Zs(s);function H(W,ne){if(W[4])return vc;if(W[8])return bc;if(W[6])return _c}let U=H(s),K=U&&U(s),G=s[2]&&Qs(s);const se=[Cc,wc,kc],F=[];function R(W,ne){var x;return ne&193&&(A=null),W[2]&&((x=W[0])==null?void 0:x.type)==="duckdb"?0:(A==null&&(A=!W[6]||JSON.stringify(W[0])!==W[7]),A?1:2)}return v=R(s,-1),I=F[v]=se[v](s),{c(){e=w("form"),t=w("section"),V&&V.c(),n=B(),i=w("input"),r=B(),b&&b.c(),o=B(),y&&y.c(),u=B(),f=w("input"),m=B(),h=w("div"),_=w("div"),K&&K.c(),N=B(),S=w("div"),G&&G.c(),O=B(),I.c(),this.h()},l(W){e=k(W,"FORM",{action:!0,method:!0,class:!0});var ne=M(e);t=k(ne,"SECTION",{class:!0});var x=M(t);V&&V.l(x),n=q(x),i=k(x,"INPUT",{type:!0,class:!0}),r=q(x),b&&b.l(x),o=q(x),y&&y.l(x),x.forEach(p),u=q(ne),f=k(ne,"INPUT",{type:!0,name:!0}),m=q(ne),h=k(ne,"DIV",{class:!0});var he=M(h);_=k(he,"DIV",{});var ue=M(_);K&&K.l(ue),ue.forEach(p),N=q(he),S=k(he,"DIV",{class:!0});var Ce=M(S);G&&G.l(Ce),O=q(Ce),I.l(Ce),Ce.forEach(p),he.forEach(p),ne.forEach(p),this.h()},h(){E(i,"type","hidden"),i.disabled=!0,i.value=l=s[0].type,E(i,"class","rounded border border-base-300 p-1 ml-auto w-2/3 bg-base-100 align-middle text-sm"),E(t,"class","flex flex-col gap-4"),E(f,"type","hidden"),f.value=d=JSON.stringify({...s[0],dir:`sources/${s[0].name}`}),E(f,"name","source"),E(S,"class","flex gap-2 justify-end items-center pt-1"),E(h,"class","flex justify-between items-center pt-4"),E(e,"action","?/updateSource"),E(e,"method","POST"),E(e,"class","w-full flex flex-col gap-8 px-1 pt-8 text-sm")},m(W,ne){j(W,e,ne),g(e,t),V&&V.m(t,null),g(t,n),g(t,i),g(t,r),b&&b.m(t,null),g(t,o),y&&y.m(t,null),g(e,u),g(e,f),g(e,m),g(e,h),g(h,_),K&&K.m(_,null),g(h,N),g(h,S),G&&G.m(S,null),g(S,O),F[v].m(S,null),C=!0,D||(L=Qe(va.call(null,e,s[12])),D=!0)},p(W,[ne]){var he,ue;W[2]?V&&(pe(),P(V,1,1,()=>{V=null}),ge()):V?(V.p(W,ne),ne&4&&T(V,1)):(V=zs(W),V.c(),T(V,1),V.m(t,n)),(!C||ne&1&&l!==(l=W[0].type))&&(i.value=l),ne&2&&(a=Object.keys((he=W[1])==null?void 0:he.options).length),a?b?(b.p(W,ne),ne&2&&T(b,1)):(b=Xs(W),b.c(),T(b,1),b.m(t,o)):b&&(pe(),P(b,1,1,()=>{b=null}),ge()),ne&2&&(c=Dn((ue=W[1])==null?void 0:ue.options)),c?y?(y.p(W,ne),ne&2&&T(y,1)):(y=Zs(W),y.c(),T(y,1),y.m(t,null)):y&&(pe(),P(y,1,1,()=>{y=null}),ge()),(!C||ne&1&&d!==(d=JSON.stringify({...W[0],dir:`sources/${W[0].name}`})))&&(f.value=d),U===(U=H(W))&&K?K.p(W,ne):(K&&K.d(1),K=U&&U(W),K&&(K.c(),T(K,1),K.m(_,null))),W[2]?G?(G.p(W,ne),ne&4&&T(G,1)):(G=Qs(W),G.c(),T(G,1),G.m(S,O)):G&&(pe(),P(G,1,1,()=>{G=null}),ge());let x=v;v=R(W,ne),v===x?F[v].p(W,ne):(pe(),P(F[x],1,1,()=>{F[x]=null}),ge(),I=F[v],I?I.p(W,ne):(I=F[v]=se[v](W),I.c()),T(I,1),I.m(S,null))},i(W){C||(T(V),T(b),T(y),T(K),T(G),T(I),C=!0)},o(W){P(V),P(b),P(y),P(G),P(I),C=!1},d(W){W&&p(e),V&&V.d(),b&&b.d(),y&&y.d(),K&&K.d(),G&&G.d(),F[v].d(),D=!1,L()}}}function Dn(s){return s?Object.values(s).some(e=>e.secret?!0:e.children?Object.values(e.children).some(t=>Dn({[t.title]:t})):!1):!1}function Nc(s,e,t){let{sourcePlugin:n}=e,{isNewSource:i=!1}=e,{source:l}=e,{sources:r}=e;const a=wi();function o(){a("cancel")}let c;l.initialName=l.name;let u="",f=!1,d=!1,m="",h="",_=!1,N="";const S=({action:C,cancel:D})=>{if(t(5,f=!1),t(9,_=!1),!i&&(t(10,N=Ki(l.name,r.filter(L=>L!==l))),N)){D();return}switch(C.search){case"?/updateSource":t(5,f=!0),t(4,u=""),t(6,d=!1);break;case"?/testSource":t(9,_=!0),t(8,h="");break}return({result:L,action:V})=>{var b;if(L.type==="failure"){if(typeof L.data=="string")t(4,u=L.data);else if(typeof L.data=="object"&&"message"in L.data)switch(V.search){case"?/updateSource":t(4,u=L.data.message);break;case"?/testSource":t(8,h=L.data.message);break}else t(4,u="Error saving datasource.");t(5,f=!1),t(6,d=!1),t(9,_=!1);return}switch(V.search){case"?/updateSource":L.type==="success"&&Object.assign(l,(b=L.data)==null?void 0:b.updatedSource),t(5,f=!1),t(6,d=!0),a("sourceUpdated",l);break;case"?/testSource":L.type==="success"&&(t(8,h=""),t(6,d=!0),t(7,m=JSON.stringify(l))),t(9,_=!1);break}}};function O(C){s.$$.not_equal(l.name,C)&&(l.name=C,t(0,l))}function A(C){N=C,t(10,N)}function v(C){s.$$.not_equal(l.options,C)&&(l.options=C,t(0,l))}function I(C){c=C,t(3,c)}return s.$$set=C=>{"sourcePlugin"in C&&t(1,n=C.sourcePlugin),"isNewSource"in C&&t(2,i=C.isNewSource),"source"in C&&t(0,l=C.source),"sources"in C&&t(13,r=C.sources)},[l,n,i,c,u,f,d,m,h,_,N,o,S,r,O,A,v,I]}class fl extends be{constructor(e){super(),ve(this,e,Nc,Ic,_e,{sourcePlugin:1,isNewSource:2,source:0,sources:13})}}function $s(s,e,t){const n=s.slice();n[24]=e[t][0],n[25]=e[t][1];const i=n[25].package.package.evidence.datasources;return n[26]=i,n}function xs(s,e,t){const n=s.slice();return n[29]=e[t],n}function Ac(s){const e=s.slice(),t=e[25].package.package.evidence.icon;return e[32]=t,e}function Oc(s){const e=s.slice(),t=e[25].package.package.evidence.icon;return e[32]=t,e}function Lc(s){let e,t,n,i,l,r,a,o,c,u,f,d=s[6].type+"",m,h,_,N=s[6].name+"",S,O,A,v,I,C;const D=[jc,Vc,Mc],L=[];function V(b,y){return y[0]&1024&&(n=null),y[0]&1024&&(i=null),n==null&&(n=!!b[14](b[10])),n?0:(i==null&&(i=!!b[15](b[10])),i?1:2)}return l=V(s,[-1,-1]),r=L[l]=D[l](s),v=new fl({props:{sources:s[3],sourcePlugin:s[0],source:s[6],isNewSource:!0}}),v.$on("sourceUpdated",s[21]),v.$on("cancel",s[22]),{c(){e=w("div"),t=w("div"),r.c(),a=B(),o=w("div"),c=w("div"),u=w("div"),f=w("p"),m=le(d),h=B(),_=w("h4"),S=le(N),O=B(),A=w("div"),Z(v.$$.fragment),this.h()},l(b){e=k(b,"DIV",{class:!0});var y=M(e);t=k(y,"DIV",{class:!0});var H=M(t);r.l(H),H.forEach(p),a=q(y),o=k(y,"DIV",{class:!0});var U=M(o);c=k(U,"DIV",{class:!0});var K=M(c);u=k(K,"DIV",{class:!0});var G=M(u);f=k(G,"P",{class:!0});var se=M(f);m=ie(se,d),se.forEach(p),h=q(G),_=k(G,"H4",{class:!0});var F=M(_);S=ie(F,N),F.forEach(p),G.forEach(p),K.forEach(p),U.forEach(p),y.forEach(p),O=q(b),A=k(b,"DIV",{});var R=M(A);X(v.$$.fragment,R),R.forEach(p),this.h()},h(){E(t,"class","text-base-content h-full"),E(f,"class","text-base-content-muted font-mono text-xs"),E(_,"class","text-base-content font-medium"),E(u,"class","flex flex-col text-sm"),E(c,"class","flex items-center text-base-content gap-4"),E(o,"class","flex w-full justify-between items-center"),E(e,"class","flex items-center gap-4")},m(b,y){j(b,e,y),g(e,t),L[l].m(t,null),g(e,a),g(e,o),g(o,c),g(c,u),g(u,f),g(f,m),g(u,h),g(u,_),g(_,S),j(b,O,y),j(b,A,y),z(v,A,null),C=!0},p(b,y){let H=l;l=V(b,y),l===H?L[l].p(b,y):(pe(),P(L[H],1,1,()=>{L[H]=null}),ge(),r=L[l],r?r.p(b,y):(r=L[l]=D[l](b),r.c()),T(r,1),r.m(t,null)),(!C||y[0]&64)&&d!==(d=b[6].type+"")&&ye(m,d),(!C||y[0]&64)&&N!==(N=b[6].name+"")&&ye(S,N);const U={};y[0]&8&&(U.sources=b[3]),y[0]&1&&(U.sourcePlugin=b[0]),y[0]&64&&(U.source=b[6]),v.$set(U)},i(b){C||(T(r),T(v.$$.fragment,b),b&&(I||ke(()=>{I=Ae(A,vt,{x:100,duration:300}),I.start()})),C=!0)},o(b){P(r),P(v.$$.fragment,b),C=!1},d(b){b&&(p(e),p(O),p(A)),L[l].d(),J(v)}}}function Pc(s){var se;let e,t,n,i,l="New Source",r,a,o,c,u,f,d,m,h,_,N,S,O,A,v,I,C,D,L,V,b;t=new je({props:{src:Kn,class:"w-4 h-4"}});function y(F){s[17](F)}let H={required:!0,name:"sourceType",$$slots:{default:[Qc]},$$scope:{ctx:s}};s[4]!==void 0&&(H.selected=s[4]),f=new da({props:H}),Ee.push(()=>Ge(f,"selected",y));function U(F){s[18](F)}function K(F){s[19](F)}let G={showPrefix:!0};return s[5]!==void 0&&(G.sourceName=s[5]),s[9]!==void 0&&(G.nameError=s[9]),_=new Ui({props:G}),Ee.push(()=>Ge(_,"sourceName",U)),Ee.push(()=>Ge(_,"nameError",K)),v=new We({props:{variant:"ghost",$$slots:{default:[$c]},$$scope:{ctx:s}}}),v.$on("click",s[20]),C=new We({props:{type:"submit",disabled:!((se=s[4])!=null&&se.value)||!s[5],$$slots:{default:[xc]},$$scope:{ctx:s}}}),{c(){e=w("h3"),Z(t.$$.fragment),n=B(),i=w("span"),i.textContent=l,r=B(),a=w("div"),o=w("form"),c=w("label"),u=le(`Source Type
					`),Z(f.$$.fragment),m=B(),h=w("div"),Z(_.$$.fragment),O=B(),A=w("div"),Z(v.$$.fragment),I=B(),Z(C.$$.fragment),this.h()},l(F){e=k(F,"H3",{class:!0});var R=M(e);X(t.$$.fragment,R),n=q(R),i=k(R,"SPAN",{"data-svelte-h":!0}),oe(i)!=="svelte-13328qh"&&(i.textContent=l),R.forEach(p),r=q(F),a=k(F,"DIV",{});var te=M(a);o=k(te,"FORM",{class:!0});var ee=M(o);c=k(ee,"LABEL",{for:!0,class:!0});var W=M(c);u=ie(W,`Source Type
					`),X(f.$$.fragment,W),W.forEach(p),m=q(ee),h=k(ee,"DIV",{});var ne=M(h);X(_.$$.fragment,ne),ne.forEach(p),O=q(ee),A=k(ee,"DIV",{class:!0});var x=M(A);X(v.$$.fragment,x),I=q(x),X(C.$$.fragment,x),x.forEach(p),ee.forEach(p),te.forEach(p),this.h()},h(){E(e,"class","font-semibold text-base-content flex items-center gap-2 mb-4"),E(c,"for","sourceType"),E(c,"class","font-medium text-sm flex flex-col gap-2"),E(A,"class","flex justify-end gap-2"),E(o,"class","flex flex-col w-full gap-4")},m(F,R){j(F,e,R),z(t,e,null),g(e,n),g(e,i),j(F,r,R),j(F,a,R),g(a,o),g(o,c),g(c,u),z(f,c,null),g(o,m),g(o,h),z(_,h,null),g(o,O),g(o,A),z(v,A,null),g(A,I),z(C,A,null),L=!0,V||(b=ce(o,"submit",Ci(s[11])),V=!0)},p(F,R){var x;const te={};R[0]&20|R[1]&4&&(te.$$scope={dirty:R,ctx:F}),!d&&R[0]&16&&(d=!0,te.selected=F[4],Ue(()=>d=!1)),f.$set(te);const ee={};!N&&R[0]&32&&(N=!0,ee.sourceName=F[5],Ue(()=>N=!1)),!S&&R[0]&512&&(S=!0,ee.nameError=F[9],Ue(()=>S=!1)),_.$set(ee);const W={};R[1]&4&&(W.$$scope={dirty:R,ctx:F}),v.$set(W);const ne={};R[0]&48&&(ne.disabled=!((x=F[4])!=null&&x.value)||!F[5]),R[1]&4&&(ne.$$scope={dirty:R,ctx:F}),C.$set(ne)},i(F){L||(T(t.$$.fragment,F),T(f.$$.fragment,F),T(_.$$.fragment,F),T(v.$$.fragment,F),T(C.$$.fragment,F),F&&(D||ke(()=>{D=Ae(a,vt,{x:-50,duration:300}),D.start()})),L=!0)},o(F){P(t.$$.fragment,F),P(f.$$.fragment,F),P(_.$$.fragment,F),P(v.$$.fragment,F),P(C.$$.fragment,F),L=!1},d(F){F&&(p(e),p(r),p(a)),J(t),J(f),J(_),J(v),J(C),V=!1,b()}}}function Dc(s){let e,t,n,i,l,r,a="Connected",o,c,u,f,d,m=s[6].name+"",h,_,N,S,O,A;return n=new je({props:{src:fr,class:"text-positive/90 h-14 w-14"}}),S=new We({props:{variant:"primary",size:"lg",$$slots:{default:[eu]},$$scope:{ctx:s}}}),S.$on("click",s[13]),{c(){e=w("div"),t=w("div"),Z(n.$$.fragment),i=B(),l=w("div"),r=w("p"),r.textContent=a,o=B(),c=w("p"),u=le("Add files to "),f=w("code"),d=le("sources/"),h=le(m),_=le(" in order to query this source."),N=B(),Z(S.$$.fragment),this.h()},l(v){e=k(v,"DIV",{class:!0});var I=M(e);t=k(I,"DIV",{class:!0});var C=M(t);X(n.$$.fragment,C),i=q(C),l=k(C,"DIV",{class:!0});var D=M(l);r=k(D,"P",{class:!0,"data-svelte-h":!0}),oe(r)!=="svelte-ygcw3j"&&(r.textContent=a),o=q(D),c=k(D,"P",{class:!0});var L=M(c);u=ie(L,"Add files to "),f=k(L,"CODE",{class:!0});var V=M(f);d=ie(V,"sources/"),h=ie(V,m),V.forEach(p),_=ie(L," in order to query this source."),L.forEach(p),D.forEach(p),C.forEach(p),N=q(I),X(S.$$.fragment,I),I.forEach(p),this.h()},h(){E(r,"class","font-semibold text-base-content mb-4 text-lg"),E(f,"class","text-sm bg-base-200 px-1.5 py-0.5 rounded border font-mono"),E(c,"class","text-base-content"),E(l,"class","flex flex-col items-center text-sm"),E(t,"class","flex flex-col items-center gap-2"),E(e,"class","flex flex-col gap-8 items-center p-2")},m(v,I){j(v,e,I),g(e,t),z(n,t,null),g(t,i),g(t,l),g(l,r),g(l,o),g(l,c),g(c,u),g(c,f),g(f,d),g(f,h),g(c,_),g(e,N),z(S,e,null),A=!0},p(v,I){(!A||I[0]&64)&&m!==(m=v[6].name+"")&&ye(h,m);const C={};I[1]&4&&(C.$$scope={dirty:I,ctx:v}),S.$set(C)},i(v){A||(T(n.$$.fragment,v),T(S.$$.fragment,v),v&&(O||ke(()=>{O=Ae(e,vt,{x:50,duration:300}),O.start()})),A=!0)},o(v){P(n.$$.fragment,v),P(S.$$.fragment,v),A=!1},d(v){v&&p(e),J(n),J(S)}}}function Mc(s){let e,t;return e=new je({props:{src:Gt,class:"w-6 h-6"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Vc(s){let e,t;return e=new je({props:{src:it[s[10]],class:"w-6 h-6"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i[0]&1024&&(l.src=it[n[10]]),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function jc(s){let e,t;return e=new je({props:{src:st[s[10]],class:"w-6 h-6"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i[0]&1024&&(l.src=st[n[10]]),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Fc(s){var l;let e,t,n=(((l=s[4])==null?void 0:l.value)??"")+"",i;return{c(){e=w("div"),t=w("span"),i=le(n),this.h()},l(r){e=k(r,"DIV",{});var a=M(e);t=k(a,"SPAN",{});var o=M(t);i=ie(o,n),o.forEach(p),a.forEach(p),this.h()},h(){var r;tt(e,"border-negative",!((r=s[4])!=null&&r.value))},m(r,a){j(r,e,a),g(e,t),g(t,i)},p(r,a){var o,c;a[0]&16&&n!==(n=(((o=r[4])==null?void 0:o.value)??"")+"")&&ye(i,n),a[0]&16&&tt(e,"border-negative",!((c=r[4])!=null&&c.value))},d(r){r&&p(e)}}}function qc(s){let e,t;return e=new Ri({props:{value:s[29],$$slots:{default:[Uc]},$$scope:{ctx:s}}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i[0]&4&&(l.value=n[29]),i[0]&4|i[1]&4&&(l.$$scope={dirty:i,ctx:n}),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Bc(s){let e,t,n,i;const l=[Yc,Gc],r=[];function a(c,u){return c[29].length?0:1}function o(c,u){return u===0?Ac(c):c}return e=a(s),t=r[e]=l[e](o(s,e)),{c(){t.c(),n=fe()},l(c){t.l(c),n=fe()},m(c,u){r[e].m(c,u),j(c,n,u),i=!0},p(c,u){let f=e;e=a(c),e===f?r[e].p(o(c,e),u):(pe(),P(r[f],1,1,()=>{r[f]=null}),ge(),t=r[e],t?t.p(o(c,e),u):(t=r[e]=l[e](o(c,e)),t.c()),T(t,1),t.m(n.parentNode,n))},i(c){i||(T(t),i=!0)},o(c){P(t),i=!1},d(c){c&&p(n),r[e].d(c)}}}function Hc(s){let e,t;return e=new je({props:{src:Gt,class:"w-5 h-5"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Rc(s){let e,t;return e=new je({props:{src:it[s[32]],class:"w-5 h-5"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i[0]&4&&(l.src=it[n[32]]),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Kc(s){let e,t;return e=new je({props:{src:st[s[32]],class:"w-5 h-5"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i[0]&4&&(l.src=st[n[32]]),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Uc(s){let e,t,n,i,l,r,a,o,c,u=s[29]+"",f,d,m,h=s[24]+"",_,N,S;const O=[Kc,Rc,Hc],A=[];function v(I,C){return C[0]&4&&(n=null),C[0]&4&&(i=null),n==null&&(n=!!I[14](I[32])),n?0:(i==null&&(i=!!I[15](I[32])),i?1:2)}return l=v(s,[-1,-1]),r=A[l]=O[l](s),{c(){e=w("div"),t=w("div"),r.c(),a=B(),o=w("div"),c=w("div"),f=le(u),d=B(),m=w("div"),_=le(h),N=B(),this.h()},l(I){e=k(I,"DIV",{class:!0});var C=M(e);t=k(C,"DIV",{class:!0});var D=M(t);r.l(D),D.forEach(p),a=q(C),o=k(C,"DIV",{class:!0});var L=M(o);c=k(L,"DIV",{});var V=M(c);f=ie(V,u),V.forEach(p),d=q(L),m=k(L,"DIV",{class:!0});var b=M(m);_=ie(b,h),b.forEach(p),L.forEach(p),C.forEach(p),N=q(I),this.h()},h(){E(t,"class","text-base-content"),E(m,"class","font-light text-base-content-muted/70 text-xs"),E(o,"class","flex flex-col"),E(e,"class","flex items-center gap-4")},m(I,C){j(I,e,C),g(e,t),A[l].m(t,null),g(e,a),g(e,o),g(o,c),g(c,f),g(o,d),g(o,m),g(m,_),j(I,N,C),S=!0},p(I,C){let D=l;l=v(I,C),l===D?A[l].p(I,C):(pe(),P(A[D],1,1,()=>{A[D]=null}),ge(),r=A[l],r?r.p(I,C):(r=A[l]=O[l](I),r.c()),T(r,1),r.m(t,null)),(!S||C[0]&4)&&u!==(u=I[29]+"")&&ye(f,u),(!S||C[0]&4)&&h!==(h=I[24]+"")&&ye(_,h)},i(I){S||(T(r),S=!0)},o(I){P(r),S=!1},d(I){I&&(p(e),p(N)),A[l].d()}}}function Gc(s){return{c:re,l:re,m:re,p:re,i:re,o:re,d:re}}function Yc(s){let e,t;return e=new Ri({props:{value:s[29][0],$$slots:{default:[Xc]},$$scope:{ctx:s}}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i[0]&4&&(l.value=n[29][0]),i[0]&4|i[1]&4&&(l.$$scope={dirty:i,ctx:n}),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Wc(s){let e,t;return e=new je({props:{src:Gt,class:"w-5 h-5"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Jc(s){let e,t;return e=new je({props:{src:it[s[32]],class:"w-5 h-5"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i[0]&4&&(l.src=it[n[32]]),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function zc(s){let e,t;return e=new je({props:{src:st[s[32]],class:"w-5 h-5"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i[0]&4&&(l.src=st[n[32]]),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Xc(s){let e,t,n,i,l,r,a,o,c,u=s[29][0]+"",f,d,m,h=s[24]+"",_,N,S;const O=[zc,Jc,Wc],A=[];function v(I,C){return C[0]&4&&(n=null),C[0]&4&&(i=null),n==null&&(n=!!I[14](I[32])),n?0:(i==null&&(i=!!I[15](I[32])),i?1:2)}return l=v(s,[-1,-1]),r=A[l]=O[l](s),{c(){e=w("div"),t=w("div"),r.c(),a=B(),o=w("div"),c=w("div"),f=le(u),d=B(),m=w("div"),_=le(h),N=B(),this.h()},l(I){e=k(I,"DIV",{class:!0});var C=M(e);t=k(C,"DIV",{class:!0});var D=M(t);r.l(D),D.forEach(p),a=q(C),o=k(C,"DIV",{class:!0});var L=M(o);c=k(L,"DIV",{});var V=M(c);f=ie(V,u),V.forEach(p),d=q(L),m=k(L,"DIV",{class:!0});var b=M(m);_=ie(b,h),b.forEach(p),L.forEach(p),C.forEach(p),N=q(I),this.h()},h(){E(t,"class","text-base-content"),E(m,"class","font-light text-base-content-muted/70 text-xs"),E(o,"class","flex flex-col"),E(e,"class","flex items-center gap-4")},m(I,C){j(I,e,C),g(e,t),A[l].m(t,null),g(e,a),g(e,o),g(o,c),g(c,f),g(o,d),g(o,m),g(m,_),j(I,N,C),S=!0},p(I,C){let D=l;l=v(I,C),l===D?A[l].p(I,C):(pe(),P(A[D],1,1,()=>{A[D]=null}),ge(),r=A[l],r?r.p(I,C):(r=A[l]=O[l](I),r.c()),T(r,1),r.m(t,null)),(!S||C[0]&4)&&u!==(u=I[29][0]+"")&&ye(f,u),(!S||C[0]&4)&&h!==(h=I[24]+"")&&ye(_,h)},i(I){S||(T(r),S=!0)},o(I){P(r),S=!1},d(I){I&&(p(e),p(N)),A[l].d()}}}function ei(s){let e,t,n,i,l;const r=[Bc,qc],a=[];function o(u,f){return f[0]&4&&(e=null),e==null&&(e=!!Array.isArray(u[29])),e?0:1}function c(u,f){return f===1?Oc(u):u}return t=o(s,[-1,-1]),n=a[t]=r[t](c(s,t)),{c(){n.c(),i=fe()},l(u){n.l(u),i=fe()},m(u,f){a[t].m(u,f),j(u,i,f),l=!0},p(u,f){let d=t;t=o(u,f),t===d?a[t].p(c(u,t),f):(pe(),P(a[d],1,1,()=>{a[d]=null}),ge(),n=a[t],n?n.p(c(u,t),f):(n=a[t]=r[t](c(u,t)),n.c()),T(n,1),n.m(i.parentNode,i))},i(u){l||(T(n),l=!0)},o(u){P(n),l=!1},d(u){u&&p(i),a[t].d(u)}}}function ti(s){let e,t,n=Ne(s[26]),i=[];for(let r=0;r<n.length;r+=1)i[r]=ei(xs(s,n,r));const l=r=>P(i[r],1,1,()=>{i[r]=null});return{c(){for(let r=0;r<i.length;r+=1)i[r].c();e=fe()},l(r){for(let a=0;a<i.length;a+=1)i[a].l(r);e=fe()},m(r,a){for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(r,a);j(r,e,a),t=!0},p(r,a){if(a[0]&49156){n=Ne(r[26]);let o;for(o=0;o<n.length;o+=1){const c=xs(r,n,o);i[o]?(i[o].p(c,a),T(i[o],1)):(i[o]=ei(c),i[o].c(),T(i[o],1),i[o].m(e.parentNode,e))}for(pe(),o=n.length;o<i.length;o+=1)l(o);ge()}},i(r){if(!t){for(let a=0;a<n.length;a+=1)T(i[a]);t=!0}},o(r){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)P(i[a]);t=!1},d(r){r&&p(e),at(i,r)}}}function Zc(s){let e,t,n=Ne(Object.entries(s[2])),i=[];for(let r=0;r<n.length;r+=1)i[r]=ti($s(s,n,r));const l=r=>P(i[r],1,1,()=>{i[r]=null});return{c(){for(let r=0;r<i.length;r+=1)i[r].c();e=fe()},l(r){for(let a=0;a<i.length;a+=1)i[a].l(r);e=fe()},m(r,a){for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(r,a);j(r,e,a),t=!0},p(r,a){if(a[0]&49156){n=Ne(Object.entries(r[2]));let o;for(o=0;o<n.length;o+=1){const c=$s(r,n,o);i[o]?(i[o].p(c,a),T(i[o],1)):(i[o]=ti(c),i[o].c(),T(i[o],1),i[o].m(e.parentNode,e))}for(pe(),o=n.length;o<i.length;o+=1)l(o);ge()}},i(r){if(!t){for(let a=0;a<n.length;a+=1)T(i[a]);t=!0}},o(r){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)P(i[a]);t=!1},d(r){r&&p(e),at(i,r)}}}function Qc(s){let e,t,n,i,l,r;return e=new fa({props:{$$slots:{default:[Fc]},$$scope:{ctx:s}}}),n=new oa({props:{$$slots:{default:[Zc]},$$scope:{ctx:s}}}),l=new ma({}),{c(){Z(e.$$.fragment),t=B(),Z(n.$$.fragment),i=B(),Z(l.$$.fragment)},l(a){X(e.$$.fragment,a),t=q(a),X(n.$$.fragment,a),i=q(a),X(l.$$.fragment,a)},m(a,o){z(e,a,o),j(a,t,o),z(n,a,o),j(a,i,o),z(l,a,o),r=!0},p(a,o){const c={};o[0]&16|o[1]&4&&(c.$$scope={dirty:o,ctx:a}),e.$set(c);const u={};o[0]&4|o[1]&4&&(u.$$scope={dirty:o,ctx:a}),n.$set(u)},i(a){r||(T(e.$$.fragment,a),T(n.$$.fragment,a),T(l.$$.fragment,a),r=!0)},o(a){P(e.$$.fragment,a),P(n.$$.fragment,a),P(l.$$.fragment,a),r=!1},d(a){a&&(p(t),p(i)),J(e,a),J(n,a),J(l,a)}}}function $c(s){let e;return{c(){e=le("Cancel")},l(t){e=ie(t,"Cancel")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function xc(s){let e;return{c(){e=le("Next →")},l(t){e=ie(t,"Next →")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function eu(s){let e;return{c(){e=le("Done")},l(t){e=ie(t,"Done")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function tu(s){let e,t,n,i;const l=[Dc,Pc,Lc],r=[];function a(o,c){return o[8]?0:o[7]?2:1}return t=a(s),n=r[t]=l[t](s),{c(){e=w("div"),n.c()},l(o){e=k(o,"DIV",{});var c=M(e);n.l(c),c.forEach(p)},m(o,c){j(o,e,c),r[t].m(e,null),i=!0},p(o,c){let u=t;t=a(o),t===u?r[t].p(o,c):(pe(),P(r[u],1,1,()=>{r[u]=null}),ge(),n=r[t],n?n.p(o,c):(n=r[t]=l[t](o),n.c()),T(n,1),n.m(e,null))},i(o){i||(T(n),i=!0)},o(o){P(n),i=!1},d(o){o&&p(e),r[t].d()}}}function nu(s,e,t){let n,i,{addingSource:l}=e,{availablePackages:r}=e,{sourcePlugin:a}=e,{availableSourcePlugins:o}=e,{sources:c=[]}=e,u={},f="",d=!1,m=!1,h="";const _=wi();function N(){u!=null&&u.value&&(t(9,h=Ki(f,c)),!h&&t(7,d=!0))}function S(y){t(8,m=!0),_("newSource",y.detail)}function O(){t(1,l=!1)}const A=y=>typeof y<"u"&&y in st,v=y=>typeof y<"u"&&y in it;function I(y){u=y,t(4,u)}function C(y){f=y,t(5,f)}function D(y){h=y,t(9,h)}const L=()=>t(1,l=!1),V=y=>S(y),b=()=>t(7,d=!1);return s.$$set=y=>{"addingSource"in y&&t(1,l=y.addingSource),"availablePackages"in y&&t(2,r=y.availablePackages),"sourcePlugin"in y&&t(0,a=y.sourcePlugin),"availableSourcePlugins"in y&&t(16,o=y.availableSourcePlugins),"sources"in y&&t(3,c=y.sources)},s.$$.update=()=>{s.$$.dirty[0]&48&&t(6,n={name:f,type:u==null?void 0:u.value,options:{},environmentVariables:{}}),s.$$.dirty[0]&65600&&t(0,a=o==null?void 0:o[n==null?void 0:n.type]),s.$$.dirty[0]&1&&t(10,i=a==null?void 0:a.package.package.evidence.icon)},[a,l,r,c,u,f,n,d,m,h,i,N,S,O,A,v,o,I,C,D,L,V,b]}class dl extends be{constructor(e){super(),ve(this,e,nu,tu,_e,{addingSource:1,availablePackages:2,sourcePlugin:0,availableSourcePlugins:16,sources:3},null,[-1,-1])}}function su(s){let e,t;return e=new je({props:{src:Gt,class:"w-6 h-6"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function iu(s){let e,t;return e=new je({props:{src:dr,class:"w-6 h-6 text-negative"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function lu(s){let e,t;return e=new je({props:{src:it[s[4]],class:"w-6 h-6"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i&16&&(l.src=it[n[4]]),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function ru(s){let e,t;return e=new je({props:{src:st[s[4]],class:"w-6 h-6"}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i&16&&(l.src=st[n[4]]),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function ou(s){let e;return{c(){e=le("Edit")},l(t){e=ie(t,"Edit")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function ni(s){let e,t,n,i;return t=new fl({props:{sources:s[1],source:s[0],sourcePlugin:s[2]}}),t.$on("sourceUpdated",s[9]),{c(){e=w("div"),Z(t.$$.fragment),this.h()},l(l){e=k(l,"DIV",{class:!0});var r=M(e);X(t.$$.fragment,r),r.forEach(p),this.h()},h(){E(e,"class","flex")},m(l,r){j(l,e,r),z(t,e,null),i=!0},p(l,r){const a={};r&2&&(a.sources=l[1]),r&1&&(a.source=l[0]),r&4&&(a.sourcePlugin=l[2]),t.$set(a)},i(l){i||(T(t.$$.fragment,l),l&&ke(()=>{i&&(n||(n=nt(e,dt,{},!0)),n.run(1))}),i=!0)},o(l){P(t.$$.fragment,l),l&&(n||(n=nt(e,dt,{},!1)),n.run(0)),i=!1},d(l){l&&p(e),J(t),l&&n&&n.end()}}}function au(s){let e,t,n,i,l,r,a,o,c,u,f,d,m=s[0].type+"",h,_,N,S=s[0].name+"",O,A,v,I,C,D;const L=[ru,lu,iu,su],V=[];function b(H,U){return U&16&&(i=null),U&16&&(l=null),i==null&&(i=!!H[5](H[4])),i?0:(l==null&&(l=!!H[6](H[4])),l?1:H[2]?3:2)}r=b(s,-1),a=V[r]=L[r](s),I=new We({props:{variant:"ghost",disabled:!s[2],$$slots:{default:[ou]},$$scope:{ctx:s}}}),I.$on("click",s[8]);let y=s[3]&&ni(s);return{c(){e=w("div"),t=w("div"),n=w("div"),a.c(),o=B(),c=w("div"),u=w("div"),f=w("div"),d=w("p"),h=le(m),_=B(),N=w("h4"),O=le(S),A=B(),v=w("div"),Z(I.$$.fragment),C=B(),y&&y.c(),this.h()},l(H){e=k(H,"DIV",{class:!0});var U=M(e);t=k(U,"DIV",{class:!0});var K=M(t);n=k(K,"DIV",{class:!0});var G=M(n);a.l(G),G.forEach(p),o=q(K),c=k(K,"DIV",{class:!0});var se=M(c);u=k(se,"DIV",{class:!0});var F=M(u);f=k(F,"DIV",{class:!0});var R=M(f);d=k(R,"P",{class:!0});var te=M(d);h=ie(te,m),te.forEach(p),_=q(R),N=k(R,"H4",{class:!0});var ee=M(N);O=ie(ee,S),ee.forEach(p),R.forEach(p),F.forEach(p),A=q(se),v=k(se,"DIV",{class:!0});var W=M(v);X(I.$$.fragment,W),W.forEach(p),se.forEach(p),K.forEach(p),C=q(U),y&&y.l(U),U.forEach(p),this.h()},h(){E(n,"class","text-base-content h-full"),E(d,"class","text-base-content-muted font-mono text-xs"),E(N,"class","text-base-content font-medium"),E(f,"class","flex flex-col text-sm"),E(u,"class","flex items-center text-base-content gap-4"),E(v,"class","flex justify-end gap-1"),E(c,"class","flex w-full justify-between items-center"),E(t,"class","flex items-center gap-4"),E(e,"class","border-b border-base-300 last:border-b-0 p-4")},m(H,U){j(H,e,U),g(e,t),g(t,n),V[r].m(n,null),g(t,o),g(t,c),g(c,u),g(u,f),g(f,d),g(d,h),g(f,_),g(f,N),g(N,O),g(c,A),g(c,v),z(I,v,null),g(e,C),y&&y.m(e,null),D=!0},p(H,[U]){let K=r;r=b(H,U),r===K?V[r].p(H,U):(pe(),P(V[K],1,1,()=>{V[K]=null}),ge(),a=V[r],a?a.p(H,U):(a=V[r]=L[r](H),a.c()),T(a,1),a.m(n,null)),(!D||U&1)&&m!==(m=H[0].type+"")&&ye(h,m),(!D||U&1)&&S!==(S=H[0].name+"")&&ye(O,S);const G={};U&4&&(G.disabled=!H[2]),U&1024&&(G.$$scope={dirty:U,ctx:H}),I.$set(G),H[3]?y?(y.p(H,U),U&8&&T(y,1)):(y=ni(H),y.c(),T(y,1),y.m(e,null)):y&&(pe(),P(y,1,1,()=>{y=null}),ge())},i(H){D||(T(a),T(I.$$.fragment,H),T(y),D=!0)},o(H){P(a),P(I.$$.fragment,H),P(y),D=!1},d(H){H&&p(e),V[r].d(),J(I),y&&y.d()}}}function cu(s,e,t){let n,i,{source:l}=e,{sources:r}=e,{availableSourcePlugins:a}=e,o=!1;const c=m=>typeof m<"u"&&m in st,u=m=>typeof m<"u"&&m in it,f=()=>t(3,o=!o),d=m=>t(0,l=m.detail);return s.$$set=m=>{"source"in m&&t(0,l=m.source),"sources"in m&&t(1,r=m.sources),"availableSourcePlugins"in m&&t(7,a=m.availableSourcePlugins)},s.$$.update=()=>{s.$$.dirty&129&&t(2,n=a==null?void 0:a[l.type]),s.$$.dirty&4&&t(4,i=n==null?void 0:n.package.package.evidence.icon)},[l,r,n,o,i,c,u,a,f,d]}class uu extends be{constructor(e){super(),ve(this,e,cu,au,_e,{source:0,sources:1,availableSourcePlugins:7})}}function si(s,e,t){const n=s.slice();return n[9]=e[t],n}function fu(s){let e,t,n,i,l;function r(o){s[8](o)}let a={availableSourcePlugins:s[1],availablePackages:s[3],sources:s[0]};return s[2]!==void 0&&(a.addingSource=s[2]),t=new dl({props:a}),Ee.push(()=>Ge(t,"addingSource",r)),t.$on("newSource",s[4]),{c(){e=w("div"),Z(t.$$.fragment),this.h()},l(o){e=k(o,"DIV",{class:!0});var c=M(e);X(t.$$.fragment,c),c.forEach(p),this.h()},h(){E(e,"class","py-4 border rounded-md shadow-sm border-base-300 p-4")},m(o,c){j(o,e,c),z(t,e,null),l=!0},p(o,c){const u={};c&2&&(u.availableSourcePlugins=o[1]),c&8&&(u.availablePackages=o[3]),c&1&&(u.sources=o[0]),!n&&c&4&&(n=!0,u.addingSource=o[2],Ue(()=>n=!1)),t.$set(u)},i(o){l||(T(t.$$.fragment,o),o&&(i||ke(()=>{i=Ae(e,vt,{y:100}),i.start()})),l=!0)},o(o){P(t.$$.fragment,o),l=!1},d(o){o&&p(e),J(t)}}}function du(s){let e,t,n,i,l,r='<p class="font-semibold text-base-content">No Sources</p> <p class="text-base-content-muted">Get started by adding your first source.</p>',a,o,c,u;return n=new je({props:{src:Gt,class:"text-base-300 h-14 w-14"}}),o=new We({props:{variant:"primary",size:"xl",class:"w-full",icon:Kn,iconPosition:"left",$$slots:{default:[hu]},$$scope:{ctx:s}}}),o.$on("click",s[7]),{c(){e=w("div"),t=w("div"),Z(n.$$.fragment),i=B(),l=w("div"),l.innerHTML=r,a=B(),Z(o.$$.fragment),this.h()},l(f){e=k(f,"DIV",{class:!0});var d=M(e);t=k(d,"DIV",{class:!0});var m=M(t);X(n.$$.fragment,m),i=q(m),l=k(m,"DIV",{class:!0,"data-svelte-h":!0}),oe(l)!=="svelte-1n847fv"&&(l.innerHTML=r),m.forEach(p),a=q(d),X(o.$$.fragment,d),d.forEach(p),this.h()},h(){E(l,"class","flex flex-col items-center text-sm"),E(t,"class","flex flex-col items-center gap-2"),E(e,"class","bg-base-200 rounded-xl flex flex-col gap-8 items-center p-8")},m(f,d){j(f,e,d),g(e,t),z(n,t,null),g(t,i),g(t,l),g(e,a),z(o,e,null),u=!0},p(f,d){const m={};d&4096&&(m.$$scope={dirty:d,ctx:f}),o.$set(m)},i(f){u||(T(n.$$.fragment,f),T(o.$$.fragment,f),f&&(c||ke(()=>{c=Ae(e,vt,{y:-100}),c.start()})),u=!0)},o(f){P(n.$$.fragment,f),P(o.$$.fragment,f),u=!1},d(f){f&&p(e),J(n),J(o)}}}function mu(s){let e,t,n,i;const l=[gu,pu],r=[];function a(o,c){return o[2]?1:0}return e=a(s),t=r[e]=l[e](s),{c(){t.c(),n=fe()},l(o){t.l(o),n=fe()},m(o,c){r[e].m(o,c),j(o,n,c),i=!0},p(o,c){let u=e;e=a(o),e===u?r[e].p(o,c):(pe(),P(r[u],1,1,()=>{r[u]=null}),ge(),t=r[e],t?t.p(o,c):(t=r[e]=l[e](o),t.c()),T(t,1),t.m(n.parentNode,n))},i(o){i||(T(t),i=!0)},o(o){P(t),i=!1},d(o){o&&p(n),r[e].d(o)}}}function hu(s){let e;return{c(){e=le("New Source")},l(t){e=ie(t,"New Source")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function pu(s){let e,t,n,i,l;function r(o){s[6](o)}let a={availableSourcePlugins:s[1],availablePackages:s[3],sources:s[0]};return s[2]!==void 0&&(a.addingSource=s[2]),t=new dl({props:a}),Ee.push(()=>Ge(t,"addingSource",r)),t.$on("newSource",s[4]),{c(){e=w("div"),Z(t.$$.fragment),this.h()},l(o){e=k(o,"DIV",{class:!0});var c=M(e);X(t.$$.fragment,c),c.forEach(p),this.h()},h(){E(e,"class","py-4 border rounded-md shadow-sm border-base-300 p-4")},m(o,c){j(o,e,c),z(t,e,null),l=!0},p(o,c){const u={};c&2&&(u.availableSourcePlugins=o[1]),c&8&&(u.availablePackages=o[3]),c&1&&(u.sources=o[0]),!n&&c&4&&(n=!0,u.addingSource=o[2],Ue(()=>n=!1)),t.$set(u)},i(o){l||(T(t.$$.fragment,o),o&&(i||ke(()=>{i=Ae(e,vt,{y:100}),i.start()})),l=!0)},o(o){P(t.$$.fragment,o),l=!1},d(o){o&&p(e),J(t)}}}function gu(s){let e,t=[],n=new Map,i,l,r,a,o=Ne(s[0]);const c=u=>{var f;return(f=u[9])==null?void 0:f.name};for(let u=0;u<o.length;u+=1){let f=si(s,o,u),d=c(f);n.set(d,t[u]=ii(d,f))}return r=new We({props:{size:"xl",icon:Kn,iconPosition:"left",class:"w-full",$$slots:{default:[_u]},$$scope:{ctx:s}}}),r.$on("click",s[5]),{c(){e=w("div");for(let u=0;u<t.length;u+=1)t[u].c();l=B(),Z(r.$$.fragment),this.h()},l(u){e=k(u,"DIV",{class:!0});var f=M(e);for(let d=0;d<t.length;d+=1)t[d].l(f);f.forEach(p),l=q(u),X(r.$$.fragment,u),this.h()},h(){E(e,"class","mb-4 rounded-md shadow-sm bg-gradient-to-br from-base-100 to-base-100/60 border")},m(u,f){j(u,e,f);for(let d=0;d<t.length;d+=1)t[d]&&t[d].m(e,null);j(u,l,f),z(r,u,f),a=!0},p(u,f){f&3&&(o=Ne(u[0]),pe(),t=Un(t,f,c,1,u,o,n,e,mr,ii,null,si),ge());const d={};f&4096&&(d.$$scope={dirty:f,ctx:u}),r.$set(d)},i(u){if(!a){for(let f=0;f<o.length;f+=1)T(t[f]);u&&(i||ke(()=>{i=Ae(e,vt,{y:-100}),i.start()})),T(r.$$.fragment,u),a=!0}},o(u){for(let f=0;f<t.length;f+=1)P(t[f]);P(r.$$.fragment,u),a=!1},d(u){u&&(p(e),p(l));for(let f=0;f<t.length;f+=1)t[f].d();J(r,u)}}}function ii(s,e){let t,n,i;return n=new uu({props:{availableSourcePlugins:e[1],source:e[9],sources:e[0]}}),{key:s,first:null,c(){t=fe(),Z(n.$$.fragment),this.h()},l(l){t=fe(),X(n.$$.fragment,l),this.h()},h(){this.first=t},m(l,r){j(l,t,r),z(n,l,r),i=!0},p(l,r){e=l;const a={};r&2&&(a.availableSourcePlugins=e[1]),r&1&&(a.source=e[9]),r&1&&(a.sources=e[0]),n.$set(a)},i(l){i||(T(n.$$.fragment,l),i=!0)},o(l){P(n.$$.fragment,l),i=!1},d(l){l&&p(t),J(n,l)}}}function _u(s){let e;return{c(){e=le("New Source")},l(t){e=ie(t,"New Source")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function bu(s){let e,t,n,i;const l=[mu,du,fu],r=[];function a(o,c){var u;return((u=o[0])==null?void 0:u.length)>0?0:o[2]?2:1}return t=a(s),n=r[t]=l[t](s),{c(){e=w("div"),n.c()},l(o){e=k(o,"DIV",{});var c=M(e);n.l(c),c.forEach(p)},m(o,c){j(o,e,c),r[t].m(e,null),i=!0},p(o,[c]){let u=t;t=a(o),t===u?r[t].p(o,c):(pe(),P(r[u],1,1,()=>{r[u]=null}),ge(),n=r[t],n?n.p(o,c):(n=r[t]=l[t](o),n.c()),T(n,1),n.m(e,null))},i(o){i||(T(n),i=!0)},o(o){P(n),i=!1},d(o){o&&p(e),r[t].d()}}}function vu(s,e,t){let n,{availableSourcePlugins:i={}}=e,{sources:l=[]}=e;function r(d){l.push(d.detail),t(0,l)}let a=!1;const o=()=>t(2,a=!0);function c(d){a=d,t(2,a)}const u=()=>t(2,a=!0);function f(d){a=d,t(2,a)}return s.$$set=d=>{"availableSourcePlugins"in d&&t(1,i=d.availableSourcePlugins),"sources"in d&&t(0,l=d.sources)},s.$$.update=()=>{s.$$.dirty&2&&t(3,n=Object.values(i).reduce((d,m)=>{const h=m.package.package;return d[h.name]||(d[h.name]=m),d},{}))},[l,i,a,n,r,o,c,u,f]}class yu extends be{constructor(e){super(),ve(this,e,vu,bu,_e,{availableSourcePlugins:1,sources:0})}}function li(s){let e,t;return{c(){e=tn("title"),t=le(s[0])},l(n){e=en(n,"title",{});var i=M(e);t=ie(i,s[0]),i.forEach(p)},m(n,i){j(n,e,i),g(e,t)},p(n,i){i&1&&ye(t,n[0])},d(n){n&&p(e)}}}function ku(s){let e,t,n,i=s[0]&&li(s),l=[{xmlns:"http://www.w3.org/2000/svg"},{viewBox:"0 0 32 32"},{fill:"currentColor"},{width:"100%"},{height:"100%"},{preserveAspectRatio:"xMidYMid meet"},s[1],s[2]],r={};for(let a=0;a<l.length;a+=1)r=ae(r,l[a]);return{c(){e=tn("svg"),i&&i.c(),t=tn("path"),n=tn("path"),this.h()},l(a){e=en(a,"svg",{xmlns:!0,viewBox:!0,fill:!0,width:!0,height:!0,preserveAspectRatio:!0});var o=M(e);i&&i.l(o),t=en(o,"path",{d:!0}),M(t).forEach(p),n=en(o,"path",{d:!0}),M(n).forEach(p),o.forEach(p),this.h()},h(){E(t,"d","M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z"),E(n,"d","M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z"),ns(e,r)},m(a,o){j(a,e,o),i&&i.m(e,null),g(e,t),g(e,n)},p(a,[o]){a[0]?i?i.p(a,o):(i=li(a),i.c(),i.m(e,t)):i&&(i.d(1),i=null),ns(e,r=Ve(l,[{xmlns:"http://www.w3.org/2000/svg"},{viewBox:"0 0 32 32"},{fill:"currentColor"},{width:"100%"},{height:"100%"},{preserveAspectRatio:"xMidYMid meet"},o&2&&a[1],o&4&&a[2]]))},i:re,o:re,d(a){a&&p(e),i&&i.d()}}}function wu(s,e,t){let n,i;const l=["title"];let r=Se(e,l),{title:a=void 0}=e;return s.$$set=o=>{t(4,e=ae(ae({},e),Ye(o))),t(2,r=Se(e,l)),"title"in o&&t(0,a=o.title)},s.$$.update=()=>{t(3,n=e["aria-label"]||e["aria-labelledby"]||a),t(1,i={"aria-hidden":n?void 0:!0,role:n?"img":void 0,focusable:Number(e.tabindex)===0?!0:void 0})},e=Ye(e),[a,i,r,n]}class Cu extends be{constructor(e){super(),ve(this,e,wu,ku,_e,{title:0})}}function Tu(s){let e,t="Copy Project Environment Variables",n;return{c(){e=w("span"),e.textContent=t},l(i){e=k(i,"SPAN",{"data-svelte-h":!0}),oe(e)!=="svelte-z6q5lt"&&(e.textContent=t)},m(i,l){j(i,e,l)},i(i){i&&(n||ke(()=>{n=Ae(e,Xe,{}),n.start()}))},o:re,d(i){i&&p(e)}}}function Eu(s){let e,t="Copied",n;return{c(){e=w("span"),e.textContent=t},l(i){e=k(i,"SPAN",{"data-svelte-h":!0}),oe(e)!=="svelte-v4ra8l"&&(e.textContent=t)},m(i,l){j(i,e,l)},i(i){i&&(n||ke(()=>{n=Ae(e,Xe,{}),n.start()}))},o:re,d(i){i&&p(e)}}}function Su(s){let e;function t(l,r){return l[0]?Eu:Tu}let n=t(s),i=n(s);return{c(){i.c(),e=fe()},l(l){i.l(l),e=fe()},m(l,r){i.m(l,r),j(l,e,r)},p(l,r){n!==(n=t(l))&&(i.d(1),i=n(l),i&&(i.c(),T(i,1),i.m(e.parentNode,e)))},d(l){l&&p(e),i.d(l)}}}function Iu(s){let e,t;return e=new We({props:{type:"button",class:"w-full mt-4",size:"xl",$$slots:{default:[Su]},$$scope:{ctx:s}}}),e.$on("click",s[1]),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,[i]){const l={};i&17&&(l.$$scope={dirty:i,ctx:n}),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Nu(s,e,t){let{sources:n}=e,i=!1;const l=function(){t(0,i=!1)};function r(){const a=n.reduce((o,c)=>[o,Object.entries(c.environmentVariables).map(([u,f])=>`${u}="${f.replace(/\\n/g,`
`)}"`).join(`
`)].join(`
`),"");navigator.clipboard.writeText(a),t(0,i=!0),setTimeout(l,2e3)}return s.$$set=a=>{"sources"in a&&t(2,n=a.sources)},[i,r,n]}class Au extends be{constructor(e){super(),ve(this,e,Nu,Iu,_e,{sources:2})}}function Ou(s){let e,t,n=(s[1]?"&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;":s[0])+"",i;return{c(){e=w("span"),t=new Ei(!1),this.h()},l(l){e=k(l,"SPAN",{});var r=M(e);t=Ti(r,!1),r.forEach(p),this.h()},h(){t.a=null},m(l,r){j(l,e,r),t.m(n,e)},p(l,r){r&3&&n!==(n=(l[1]?"&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;":l[0])+"")&&t.p(n)},i(l){l&&(i||ke(()=>{i=Ae(e,Xe,{}),i.start()}))},o:re,d(l){l&&p(e)}}}function Lu(s){let e,t="Copied",n;return{c(){e=w("span"),e.textContent=t},l(i){e=k(i,"SPAN",{"data-svelte-h":!0}),oe(e)!=="svelte-18eylsc"&&(e.textContent=t)},m(i,l){j(i,e,l)},p:re,i(i){i&&(n||ke(()=>{n=Ae(e,Xe,{}),n.start()}))},o:re,d(i){i&&p(e)}}}function Pu(s){let e,t,n,i,l,r,a,o;function c(d,m){return d[3]?Lu:Ou}let u=c(s),f=u(s);return l=new Cu({}),{c(){e=w("button"),t=w("div"),f.c(),n=B(),i=w("div"),Z(l.$$.fragment),this.h()},l(d){e=k(d,"BUTTON",{type:!0,class:!0});var m=M(e);t=k(m,"DIV",{class:!0});var h=M(t);f.l(h),h.forEach(p),n=q(m),i=k(m,"DIV",{class:!0});var _=M(i);X(l.$$.fragment,_),_.forEach(p),m.forEach(p),this.h()},h(){E(t,"class","flex w-3/4 overflow-hidden"),E(i,"class","w-4 h-4"),E(e,"type","button"),E(e,"class","rounded-md bg-base-200 border border-base-300 font-mono text-xs p-2 flex items-center justify-between hover:bg-base-200/50 gap-4"),tt(e,"copied",s[3])},m(d,m){j(d,e,m),g(e,t),f.m(t,null),g(e,n),g(e,i),z(l,i,null),r=!0,a||(o=ce(e,"click",s[4]),a=!0)},p(d,[m]){u===(u=c(d))&&f?f.p(d,m):(f.d(1),f=u(d),f&&(f.c(),T(f,1),f.m(t,null))),(!r||m&8)&&tt(e,"copied",d[3])},i(d){r||(T(f),T(l.$$.fragment,d),r=!0)},o(d){P(l.$$.fragment,d),r=!1},d(d){d&&p(e),f.d(),J(l),a=!1,o()}}}function Du(s,e,t){let{text:n=void 0}=e,{hideText:i=!1}=e,l=!1;const r=function(){t(3,l=!1)};let{copy:a=async c=>{try{l||(await navigator.clipboard.writeText(c),t(3,l=!0),setTimeout(r,2e3))}catch{}}}=e;const o=()=>{n!==void 0&&a(n)};return s.$$set=c=>{"text"in c&&t(0,n=c.text),"hideText"in c&&t(1,i=c.hideText),"copy"in c&&t(2,a=c.copy)},[n,i,a,l,o]}class ri extends be{constructor(e){super(),ve(this,e,Du,Pu,_e,{text:0,hideText:1,copy:2})}}function oi(s,e,t){const n=s.slice();return n[1]=e[t],n}function ai(s,e,t){const n=s.slice();return n[4]=e[t][0],n[5]=e[t][1],n}function ci(s){let e,t,n,i;return e=new ri({props:{text:s[4]}}),n=new ri({props:{text:s[5],hideText:!0}}),{c(){Z(e.$$.fragment),t=B(),Z(n.$$.fragment)},l(l){X(e.$$.fragment,l),t=q(l),X(n.$$.fragment,l)},m(l,r){z(e,l,r),j(l,t,r),z(n,l,r),i=!0},p(l,r){const a={};r&1&&(a.text=l[4]),e.$set(a);const o={};r&1&&(o.text=l[5]),n.$set(o)},i(l){i||(T(e.$$.fragment,l),T(n.$$.fragment,l),i=!0)},o(l){P(e.$$.fragment,l),P(n.$$.fragment,l),i=!1},d(l){l&&p(t),J(e,l),J(n,l)}}}function ui(s){var r;let e,t,n=Ne(Object.entries((r=s[1])==null?void 0:r.environmentVariables)),i=[];for(let a=0;a<n.length;a+=1)i[a]=ci(ai(s,n,a));const l=a=>P(i[a],1,1,()=>{i[a]=null});return{c(){for(let a=0;a<i.length;a+=1)i[a].c();e=fe()},l(a){for(let o=0;o<i.length;o+=1)i[o].l(a);e=fe()},m(a,o){for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(a,o);j(a,e,o),t=!0},p(a,o){var c;if(o&1){n=Ne(Object.entries((c=a[1])==null?void 0:c.environmentVariables));let u;for(u=0;u<n.length;u+=1){const f=ai(a,n,u);i[u]?(i[u].p(f,o),T(i[u],1)):(i[u]=ci(f),i[u].c(),T(i[u],1),i[u].m(e.parentNode,e))}for(pe(),u=n.length;u<i.length;u+=1)l(u);ge()}},i(a){if(!t){for(let o=0;o<n.length;o+=1)T(i[o]);t=!0}},o(a){i=i.filter(Boolean);for(let o=0;o<i.length;o+=1)P(i[o]);t=!1},d(a){a&&p(e),at(i,a)}}}function Mu(s){let e,t='<span class="font-sm">Key</span> <span class="font-sm">Value</span>',n,i,l,r=Ne(s[0]),a=[];for(let c=0;c<r.length;c+=1)a[c]=ui(oi(s,r,c));const o=c=>P(a[c],1,1,()=>{a[c]=null});return{c(){e=w("div"),e.innerHTML=t,n=B(),i=w("div");for(let c=0;c<a.length;c+=1)a[c].c();this.h()},l(c){e=k(c,"DIV",{class:!0,"data-svelte-h":!0}),oe(e)!=="svelte-lgjfnf"&&(e.innerHTML=t),n=q(c),i=k(c,"DIV",{class:!0});var u=M(i);for(let f=0;f<a.length;f+=1)a[f].l(u);u.forEach(p),this.h()},h(){E(e,"class","flex justify-between mb-2 font-medium text-sm text-base-content"),E(i,"class","grid grid-cols-2 gap-x-6 gap-y-4")},m(c,u){j(c,e,u),j(c,n,u),j(c,i,u);for(let f=0;f<a.length;f+=1)a[f]&&a[f].m(i,null);l=!0},p(c,[u]){if(u&1){r=Ne(c[0]);let f;for(f=0;f<r.length;f+=1){const d=oi(c,r,f);a[f]?(a[f].p(d,u),T(a[f],1)):(a[f]=ui(d),a[f].c(),T(a[f],1),a[f].m(i,null))}for(pe(),f=r.length;f<a.length;f+=1)o(f);ge()}},i(c){if(!l){for(let u=0;u<r.length;u+=1)T(a[u]);l=!0}},o(c){a=a.filter(Boolean);for(let u=0;u<a.length;u+=1)P(a[u]);l=!1},d(c){c&&(p(e),p(n),p(i)),at(a,c)}}}function Vu(s,e,t){let{sources:n}=e;return s.$$set=i=>{"sources"in i&&t(0,n=i.sources)},[n]}class ju extends be{constructor(e){super(),ve(this,e,Vu,Mu,_e,{sources:0})}}function Fu(s){let e,t,n,i="Evidence Cloud",l,r,a='<div class="flex items-center justify-center text-base-content font-bold border border-base-300 w-8 h-8 rounded-full shadow-sm tabular-nums">1</div> <h4 class="font-bold text-base-content">Check your project into version control</h4>',o,c,u=`<p>Evidence Cloud deploys your project from its Github repository. As you make changes to your
				project and commit them to main, Evidence cloud will update your deployed project.</p>`,f,d,m='<div class="flex items-center justify-center text-base-content font-bold border border-base-300 w-8 h-8 rounded-full shadow-sm tabular-nums">2</div> <h4 class="font-bold text-base-content">Sign in to Evidence Cloud</h4>',h,_,N=`<p>Sign into Evidence Cloud using your GitHub account and add a new project. Follow the steps
				to connect to your Github repository.</p> <a href="https://evidence.app" target="_blank" class="inline-flex items-center justify-center rounded-md font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-base-300 disabled:pointer-events-none disabled:opacity-50 mt-4 w-full h-10 px-10 text-sm w-full bg-base-content text-base-100 shadow-sm hover:bg-base-content/90 active:bg-base-content/80">Sign In</a>`,S,O,A='<div class="flex items-center justify-center text-base-content font-bold border border-base-300 w-8 h-8 rounded-full shadow-sm tabular-nums">3</div> <h4 class="font-bold text-base-content">Set your Project Environment Variables</h4>',v,I,C,D=`While you are setting up your cloud project, you'll be prompted for your environment
				variables to connect Evidence cloud to your sources. Copy them here.`,L,V,b,y,H=`<p class="text-base-content-muted">To use different connection settings your other deployment environment,
					<a class="underline underline-offset-2" href="https://docs.evidence.dev/deployment/environments" target="_blank">set different environment variable values in cloud</a>
					.</p>`,U,K,G='<div class="flex items-center justify-center text-base-content font-bold border border-base-300 w-8 h-8 rounded-full shadow-sm tabular-nums">4</div> <h4 class="font-bold text-base-content">Done</h4>',se,F,R=`<p>When you make changes to your project locally, push them to main, and Evidence cloud will
				update your deployed project.</p>`,te,ee,W="Other Environments",ne,x,he=`Documentation on deploying Evidence projects to a variety of cloud environments is available <a href="https://docs.evidence.dev/deployment/overview/" class="markdown" target="_blank">here.</a> For all deployment environments, you will need to set the environment variables using the key
			value pairs below.`,ue,Ce,He,Ze,yt=`<p class="text-base-content-muted">To use different connection settings your deployment environment,
				<a class="underline underline-offset-2" href="https://docs.evidence.dev/deployment/environments" target="_blank">set different environment variable values in your deployment environment</a>
				.</p>`,$;return V=new Au({props:{sources:s[0]}}),Ce=new ju({props:{sources:s[0]}}),{c(){e=w("section"),t=w("div"),n=w("h3"),n.textContent=i,l=B(),r=w("div"),r.innerHTML=a,o=B(),c=w("div"),c.innerHTML=u,f=B(),d=w("div"),d.innerHTML=m,h=B(),_=w("div"),_.innerHTML=N,S=B(),O=w("div"),O.innerHTML=A,v=B(),I=w("div"),C=w("p"),C.textContent=D,L=B(),Z(V.$$.fragment),b=B(),y=w("div"),y.innerHTML=H,U=B(),K=w("div"),K.innerHTML=G,se=B(),F=w("div"),F.innerHTML=R,te=B(),ee=w("h3"),ee.textContent=W,ne=B(),x=w("p"),x.innerHTML=he,ue=B(),Z(Ce.$$.fragment),He=B(),Ze=w("div"),Ze.innerHTML=yt,this.h()},l(Fe){e=k(Fe,"SECTION",{class:!0});var Y=M(e);t=k(Y,"DIV",{class:!0});var Q=M(t);n=k(Q,"H3",{class:!0,"data-svelte-h":!0}),oe(n)!=="svelte-aahs3f"&&(n.textContent=i),l=q(Q),r=k(Q,"DIV",{class:!0,"data-svelte-h":!0}),oe(r)!=="svelte-g44hse"&&(r.innerHTML=a),o=q(Q),c=k(Q,"DIV",{class:!0,"data-svelte-h":!0}),oe(c)!=="svelte-14f4l4r"&&(c.innerHTML=u),f=q(Q),d=k(Q,"DIV",{class:!0,"data-svelte-h":!0}),oe(d)!=="svelte-hjxrdp"&&(d.innerHTML=m),h=q(Q),_=k(Q,"DIV",{class:!0,"data-svelte-h":!0}),oe(_)!=="svelte-1pktmmp"&&(_.innerHTML=N),S=q(Q),O=k(Q,"DIV",{class:!0,"data-svelte-h":!0}),oe(O)!=="svelte-1et324z"&&(O.innerHTML=A),v=q(Q),I=k(Q,"DIV",{class:!0});var de=M(I);C=k(de,"P",{"data-svelte-h":!0}),oe(C)!=="svelte-n96f75"&&(C.textContent=D),L=q(de),X(V.$$.fragment,de),b=q(de),y=k(de,"DIV",{class:!0,"data-svelte-h":!0}),oe(y)!=="svelte-1kow9oa"&&(y.innerHTML=H),de.forEach(p),U=q(Q),K=k(Q,"DIV",{class:!0,"data-svelte-h":!0}),oe(K)!=="svelte-1xhvdlg"&&(K.innerHTML=G),se=q(Q),F=k(Q,"DIV",{class:!0,"data-svelte-h":!0}),oe(F)!=="svelte-1rsdnpk"&&(F.innerHTML=R),te=q(Q),ee=k(Q,"H3",{class:!0,"data-svelte-h":!0}),oe(ee)!=="svelte-oks2a"&&(ee.textContent=W),ne=q(Q),x=k(Q,"P",{class:!0,"data-svelte-h":!0}),oe(x)!=="svelte-1lzqb8o"&&(x.innerHTML=he),ue=q(Q),X(Ce.$$.fragment,Q),He=q(Q),Ze=k(Q,"DIV",{class:!0,"data-svelte-h":!0}),oe(Ze)!=="svelte-17e3b2"&&(Ze.innerHTML=yt),Q.forEach(p),Y.forEach(p),this.h()},h(){E(n,"class","text-base-content text-lg font-semibold mt-0 mb-5"),E(r,"class","flex gap-4 items-center"),E(c,"class","pl-8 ml-[calc(1rem-0.5px)] pt-1 pb-10 border-l border-base-200"),E(d,"class","flex gap-4 items-center"),E(_,"class","pl-8 ml-[calc(1rem-0.5px)] pt-1 pb-10 border-l border-base-200"),E(O,"class","flex gap-4 items-center"),E(y,"class","mt-4"),E(I,"class","pl-8 ml-[calc(1rem-0.5px)] pt-1 pb-10 border-l border-base-200"),E(K,"class","flex gap-4 items-center"),E(F,"class","pl-8 ml-[calc(1rem-0.5px)] pt-1 pb-4 border-l border-base-200"),E(ee,"class","text-base-content text-lg font-semibold mt-0 mb-4 mt-8"),E(x,"class","text-base-content mb-4 text-pretty"),E(Ze,"class","mt-6"),E(t,"class","pb-4"),E(e,"class","w-full pt-2 pb-10")},m(Fe,Y){j(Fe,e,Y),g(e,t),g(t,n),g(t,l),g(t,r),g(t,o),g(t,c),g(t,f),g(t,d),g(t,h),g(t,_),g(t,S),g(t,O),g(t,v),g(t,I),g(I,C),g(I,L),z(V,I,null),g(I,b),g(I,y),g(t,U),g(t,K),g(t,se),g(t,F),g(t,te),g(t,ee),g(t,ne),g(t,x),g(t,ue),z(Ce,t,null),g(t,He),g(t,Ze),$=!0},p(Fe,[Y]){const Q={};Y&1&&(Q.sources=Fe[0]),V.$set(Q);const de={};Y&1&&(de.sources=Fe[0]),Ce.$set(de)},i(Fe){$||(T(V.$$.fragment,Fe),T(Ce.$$.fragment,Fe),$=!0)},o(Fe){P(V.$$.fragment,Fe),P(Ce.$$.fragment,Fe),$=!1},d(Fe){Fe&&p(e),J(V),J(Ce)}}}function qu(s,e,t){let{sources:n}=e;return s.$$set=i=>{"sources"in i&&t(0,n=i.sources)},[n]}class Bu extends be{constructor(e){super(),ve(this,e,qu,Fu,_e,{sources:0})}}function fi(s,e,t){const n=s.slice();return n[2]=e[t],n[3]=e,n[4]=t,n}function di(s){let e,t,n=s[2].formatTag+"",i,l,r,a=s[2].formatCode+"",o,c,u,f,d,m,h,_,N=At(s[2])+"",S,O,A,v;function I(){s[1].call(f,s[3],s[4])}return{c(){e=w("tr"),t=w("td"),i=le(n),l=B(),r=w("td"),o=le(a),c=B(),u=w("td"),f=w("input"),h=B(),_=w("td"),S=le(N),O=B(),this.h()},l(C){e=k(C,"TR",{});var D=M(e);t=k(D,"TD",{});var L=M(t);i=ie(L,n),L.forEach(p),l=q(D),r=k(D,"TD",{});var V=M(r);o=ie(V,a),V.forEach(p),c=q(D),u=k(D,"TD",{});var b=M(u);f=k(b,"INPUT",{id:!0,placeholder:!0,class:!0}),b.forEach(p),h=q(D),_=k(D,"TD",{class:!0});var y=M(_);S=ie(y,N),y.forEach(p),O=q(D),D.forEach(p),this.h()},h(){E(f,"id",d="id_format_row"+s[2].formatTag),E(f,"placeholder",m=s[2].exampleInput||Ot(s[2].valueType)),E(f,"class","rounded shadow-sm border border-base-300 px-2 py-1 text-sm w-full bg-base-100 focus:ring-base-300 focus:border-base-300 focus:outline-none focus:ring-1"),E(_,"class","text-right max-w-0")},m(C,D){j(C,e,D),g(e,t),g(t,i),g(e,l),g(e,r),g(r,o),g(e,c),g(e,u),g(u,f),we(f,s[2].userInput),g(e,h),g(e,_),g(_,S),g(e,O),A||(v=[ce(f,"input",I),ce(f,"blur",function(){ln(s[2].userInput=void 0)&&(s[2].userInput=void 0).apply(this,arguments)})],A=!0)},p(C,D){s=C,D&1&&n!==(n=s[2].formatTag+"")&&ye(i,n),D&1&&a!==(a=s[2].formatCode+"")&&ye(o,a),D&1&&d!==(d="id_format_row"+s[2].formatTag)&&E(f,"id",d),D&1&&m!==(m=s[2].exampleInput||Ot(s[2].valueType))&&E(f,"placeholder",m),D&1&&f.value!==s[2].userInput&&we(f,s[2].userInput),D&1&&N!==(N=At(s[2])+"")&&ye(S,N)},d(C){C&&p(e),A=!1,Je(v)}}}function Hu(s){let e,t,n='<th class="max-w-14 text-left font-medium">Format Name</th> <th class="min-w-20 text-left font-medium">Format Code</th> <th class="min-w-20 text-left font-medium">Example Input</th> <th class="min-w-20 text-right font-medium">Example Output</th>',i,l=Ne(s[0]),r=[];for(let a=0;a<l.length;a+=1)r[a]=di(fi(s,l,a));return{c(){e=w("table"),t=w("thead"),t.innerHTML=n,i=B();for(let a=0;a<r.length;a+=1)r[a].c();this.h()},l(a){e=k(a,"TABLE",{class:!0});var o=M(e);t=k(o,"THEAD",{class:!0,"data-svelte-h":!0}),oe(t)!=="svelte-1byu907"&&(t.innerHTML=n),i=q(o);for(let c=0;c<r.length;c+=1)r[c].l(o);o.forEach(p),this.h()},h(){E(t,"class","text-sm py-2"),E(e,"class","w-full border-separate [border-spacing:0.5rem_0.5rem] -mx-2")},m(a,o){j(a,e,o),g(e,t),g(e,i);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(e,null)},p(a,[o]){if(o&1){l=Ne(a[0]);let c;for(c=0;c<l.length;c+=1){const u=fi(a,l,c);r[c]?r[c].p(u,o):(r[c]=di(u),r[c].c(),r[c].m(e,null))}for(;c<r.length;c+=1)r[c].d(1);r.length=l.length}},i:re,o:re,d(a){a&&p(e),at(r,a)}}}function Ru(s,e,t){let{formats:n}=e;function i(l,r){l[r].userInput=this.value,t(0,n)}return s.$$set=l=>{"formats"in l&&t(0,n=l.formats)},[n,i]}class es extends be{constructor(e){super(),ve(this,e,Ru,Hu,_e,{formats:0})}}function mi(s,e,t){const n=s.slice();return n[5]=e[t],n[6]=e,n[7]=t,n}function Ku(s,e,t){const n=s.slice();return n[8]=e[t],n}function Uu(s){let e,t=s[8].displayName+"",n;return{c(){e=w("option"),n=le(t),this.h()},l(i){e=k(i,"OPTION",{name:!0,id:!0});var l=M(e);n=ie(l,t),l.forEach(p),this.h()},h(){E(e,"name",s[8].primaryCode),E(e,"id",s[8].primaryCode),e.__value=s[8].primaryCode,we(e,e.__value)},m(i,l){j(i,e,l),g(e,n)},p:re,d(i){i&&p(e)}}}function hi(s){let e,t,n,i='<th class="max-w-14 text-left font-medium">Format Name</th> <th class="min-w-20 text-left font-medium">Format Code</th> <th class="min-w-20 text-left font-medium">Example Input</th> <th class="min-w-20 text-right font-medium">Example Output</th>',l,r=[],a=new Map,o,c,u=Ne(s[0].filter(s[3]));const f=d=>d[5].formatTag;for(let d=0;d<u.length;d+=1){let m=mi(s,u,d),h=f(m);a.set(h,r[d]=pi(h,m))}return{c(){e=w("div"),t=w("table"),n=w("thead"),n.innerHTML=i,l=B();for(let d=0;d<r.length;d+=1)r[d].c();this.h()},l(d){e=k(d,"DIV",{});var m=M(e);t=k(m,"TABLE",{class:!0});var h=M(t);n=k(h,"THEAD",{class:!0,"data-svelte-h":!0}),oe(n)!=="svelte-1i2g6gp"&&(n.innerHTML=i),l=q(h);for(let _=0;_<r.length;_+=1)r[_].l(h);h.forEach(p),m.forEach(p),this.h()},h(){E(n,"class","text-sm py-2"),E(t,"class","w-full border-separate [border-spacing:0.5rem_0.5rem] -mx-2")},m(d,m){j(d,e,m),g(e,t),g(t,n),g(t,l);for(let h=0;h<r.length;h+=1)r[h]&&r[h].m(t,null);c=!0},p(d,m){m&3&&(u=Ne(d[0].filter(d[3])),r=Un(r,m,f,1,d,u,a,t,pr,pi,null,mi))},i(d){if(!c){for(let m=0;m<u.length;m+=1)T(r[m]);d&&ke(()=>{c&&(o||(o=nt(e,dt,{},!0)),o.run(1))}),c=!0}},o(d){d&&(o||(o=nt(e,dt,{},!1)),o.run(0)),c=!1},d(d){d&&p(e);for(let m=0;m<r.length;m+=1)r[m].d();d&&o&&o.end()}}}function pi(s,e){let t,n,i=e[5].formatTag+"",l,r,a,o,c=e[5].formatCode+"",u,f,d,m,h,_,N,S,O,A=At(e[5])+"",v,I,C,D,L;function V(){e[4].call(h,e[6],e[7])}return{key:s,first:null,c(){t=w("tr"),n=w("td"),l=le(i),a=B(),o=w("td"),u=le(c),d=B(),m=w("td"),h=w("input"),S=B(),O=w("td"),v=le(A),C=B(),this.h()},l(b){t=k(b,"TR",{});var y=M(t);n=k(y,"TD",{});var H=M(n);l=ie(H,i),H.forEach(p),a=q(y),o=k(y,"TD",{});var U=M(o);u=ie(U,c),U.forEach(p),d=q(y),m=k(y,"TD",{});var K=M(m);h=k(K,"INPUT",{id:!0,placeholder:!0,class:!0}),K.forEach(p),S=q(y),O=k(y,"TD",{class:!0});var G=M(O);v=ie(G,A),G.forEach(p),C=q(y),y.forEach(p),this.h()},h(){E(h,"id",_="id_format_row"+e[5].formatTag),E(h,"placeholder",N=e[5].exampleInput||Ot(e[5].valueType)),E(h,"class","rounded shadow-sm border border-base-300 px-2 py-1 text-sm w-full bg-base-100 focus:ring-base-300 focus:border-base-300 focus:outline-none focus:ring-1"),E(O,"class","text-right max-w-0"),this.first=t},m(b,y){j(b,t,y),g(t,n),g(n,l),g(t,a),g(t,o),g(o,u),g(t,d),g(t,m),g(m,h),we(h,e[5].userInput),g(t,S),g(t,O),g(O,v),g(t,C),D||(L=[ce(h,"input",V),ce(h,"blur",function(){ln(e[5].userInput=void 0)&&(e[5].userInput=void 0).apply(this,arguments)})],D=!0)},p(b,y){e=b,y&3&&i!==(i=e[5].formatTag+"")&&ye(l,i),y&3&&c!==(c=e[5].formatCode+"")&&ye(u,c),y&3&&_!==(_="id_format_row"+e[5].formatTag)&&E(h,"id",_),y&3&&N!==(N=e[5].exampleInput||Ot(e[5].valueType))&&E(h,"placeholder",N),y&3&&h.value!==e[5].userInput&&we(h,e[5].userInput),y&3&&A!==(A=At(e[5])+"")&&ye(v,A)},i(b){b&&(r||ke(()=>{r=Ae(n,Xe,{}),r.start()})),b&&(f||ke(()=>{f=Ae(o,Xe,{}),f.start()})),b&&(I||ke(()=>{I=Ae(O,Xe,{}),I.start()}))},o:re,d(b){b&&p(t),D=!1,Je(L)}}}function Gu(s){let e,t,n,i="Choose a currency",l,r,a,o,c,u=Ne(hr),f=[];for(let m=0;m<u.length;m+=1)f[m]=Uu(Ku(s,u,m));let d=s[1]!="Choose a currency"&&hi(s);return{c(){e=w("div"),t=w("select"),n=w("option"),n.textContent=i,l=le(`
		$`);for(let m=0;m<f.length;m+=1)f[m].c();r=B(),d&&d.c(),a=fe(),this.h()},l(m){e=k(m,"DIV",{class:!0});var h=M(e);t=k(h,"SELECT",{class:!0});var _=M(t);n=k(_,"OPTION",{"data-svelte-h":!0}),oe(n)!=="svelte-877q5p"&&(n.textContent=i),l=ie(_,`
		$`);for(let N=0;N<f.length;N+=1)f[N].l(_);_.forEach(p),h.forEach(p),r=q(m),d&&d.l(m),a=fe(),this.h()},h(){n.__value="Choose a currency",we(n,n.__value),E(t,"class","w-full rounded-md shadow-sm border border-base-300 px-3 h-9 py-2 text-sm bg-base-100 focus:ring-base-300 focus:border-base-300 focus:outline-none focus:ring-1 cursor-pointer mt-1 mb-2"),s[1]===void 0&&ke(()=>s[2].call(t)),E(e,"class","flex justify-center px-1")},m(m,h){j(m,e,h),g(e,t),g(t,n),g(t,l);for(let _=0;_<f.length;_+=1)f[_]&&f[_].m(t,null);St(t,s[1],!0),j(m,r,h),d&&d.m(m,h),j(m,a,h),o||(c=ce(t,"change",s[2]),o=!0)},p(m,[h]){h&2&&St(t,m[1]),m[1]!="Choose a currency"?d?(d.p(m,h),h&2&&T(d,1)):(d=hi(m),d.c(),T(d,1),d.m(a.parentNode,a)):d&&(pe(),P(d,1,1,()=>{d=null}),ge())},i(m){T(d)},o(m){P(d)},d(m){m&&(p(e),p(r),p(a)),at(f,m),d&&d.d(m),o=!1,c()}}}function Yu(s,e,t){let{formats:n}=e,i="Choose a currency";function l(){i=jn(this),t(1,i)}const r=o=>o.parentFormat===i;function a(o,c){o[c].userInput=this.value,t(0,n),t(1,i)}return s.$$set=o=>{"formats"in o&&t(0,n=o.formats)},[n,i,l,r,a]}class Wu extends be{constructor(e){super(),ve(this,e,Yu,Gu,_e,{formats:0})}}function gi(s,e,t){const n=s.slice();return n[4]=e[t],n[5]=e,n[6]=t,n}function Ju(s){let e;return{c(){e=le("Delete")},l(t){e=ie(t,"Delete")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function _i(s,e){let t,n,i=e[4].formatTag+"",l,r,a,o=e[4].formatCode+"",c,u,f,d,m,h,_,N,S=At(e[4])+"",O,A,v,I,C,D,L=re,V,b,y;function H(){e[2].call(d,e[5],e[6])}function U(){return e[3](e[4])}return I=new We({props:{type:"button",variant:"ghost",size:"sm",$$slots:{default:[Ju]},$$scope:{ctx:e}}}),I.$on("click",U),{key:s,first:null,c(){t=w("tr"),n=w("td"),l=le(i),r=B(),a=w("td"),c=le(o),u=B(),f=w("td"),d=w("input"),_=B(),N=w("td"),O=le(S),A=B(),v=w("td"),Z(I.$$.fragment),C=B(),this.h()},l(K){t=k(K,"TR",{});var G=M(t);n=k(G,"TD",{});var se=M(n);l=ie(se,i),se.forEach(p),r=q(G),a=k(G,"TD",{});var F=M(a);c=ie(F,o),F.forEach(p),u=q(G),f=k(G,"TD",{});var R=M(f);d=k(R,"INPUT",{id:!0,placeholder:!0,class:!0}),R.forEach(p),_=q(G),N=k(G,"TD",{class:!0});var te=M(N);O=ie(te,S),te.forEach(p),A=q(G),v=k(G,"TD",{class:!0});var ee=M(v);X(I.$$.fragment,ee),ee.forEach(p),C=q(G),G.forEach(p),this.h()},h(){E(d,"id",m="id_format_row"+e[4].formatTag),E(d,"placeholder",h=e[4].exampleInput||Ot(e[4].valueType)),E(d,"class","rounded shadow-sm border border-base-300 px-2 py-1 text-sm w-full bg-base-100 focus:ring-base-300 focus:border-base-300 focus:outline-none focus:ring-1"),E(N,"class","text-right max-w-0"),E(v,"class","flex justify-end"),this.first=t},m(K,G){j(K,t,G),g(t,n),g(n,l),g(t,r),g(t,a),g(a,c),g(t,u),g(t,f),g(f,d),we(d,e[4].userInput),g(t,_),g(t,N),g(N,O),g(t,A),g(t,v),z(I,v,null),g(t,C),V=!0,b||(y=[ce(d,"input",H),ce(d,"blur",function(){ln(e[4].userInput=void 0)&&(e[4].userInput=void 0).apply(this,arguments)})],b=!0)},p(K,G){e=K,(!V||G&1)&&i!==(i=e[4].formatTag+"")&&ye(l,i),(!V||G&1)&&o!==(o=e[4].formatCode+"")&&ye(c,o),(!V||G&1&&m!==(m="id_format_row"+e[4].formatTag))&&E(d,"id",m),(!V||G&1&&h!==(h=e[4].exampleInput||Ot(e[4].valueType)))&&E(d,"placeholder",h),G&1&&d.value!==e[4].userInput&&we(d,e[4].userInput),(!V||G&1)&&S!==(S=At(e[4])+"")&&ye(O,S);const se={};G&128&&(se.$$scope={dirty:G,ctx:e}),I.$set(se)},r(){D=t.getBoundingClientRect()},f(){Nr(t),L()},a(){L(),L=Ir(t,D,ha,{})},i(K){V||(T(I.$$.fragment,K),V=!0)},o(K){P(I.$$.fragment,K),V=!1},d(K){K&&p(t),J(I),b=!1,Je(y)}}}function zu(s){let e,t,n='<th class="max-w-18 text-left font-medium">Format Name</th> <th class="max-w-18 text-left font-medium">Format Code</th> <th class="min-w-20 text-left font-medium">Example Input</th> <th class="min-w-20 text-right font-medium">Example Output</th> <th class="max-w-8 text-right"></th>',i,l=[],r=new Map,a,o=Ne(s[0]);const c=u=>u[4].formatTag;for(let u=0;u<o.length;u+=1){let f=gi(s,o,u),d=c(f);r.set(d,l[u]=_i(d,f))}return{c(){e=w("table"),t=w("thead"),t.innerHTML=n,i=B();for(let u=0;u<l.length;u+=1)l[u].c();this.h()},l(u){e=k(u,"TABLE",{class:!0});var f=M(e);t=k(f,"THEAD",{class:!0,"data-svelte-h":!0}),oe(t)!=="svelte-1epeemt"&&(t.innerHTML=n),i=q(f);for(let d=0;d<l.length;d+=1)l[d].l(f);f.forEach(p),this.h()},h(){E(t,"class","text-sm py-2"),E(e,"class","w-full border-separate [border-spacing:0.5rem_0.5rem] -mx-2")},m(u,f){j(u,e,f),g(e,t),g(e,i);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(e,null);a=!0},p(u,[f]){if(f&3){o=Ne(u[0]),pe();for(let d=0;d<l.length;d+=1)l[d].r();l=Un(l,f,c,1,u,o,r,e,gr,_i,null,gi);for(let d=0;d<l.length;d+=1)l[d].a();ge()}},i(u){if(!a){for(let f=0;f<o.length;f+=1)T(l[f]);a=!0}},o(u){for(let f=0;f<l.length;f+=1)P(l[f]);a=!1},d(u){u&&p(e);for(let f=0;f<l.length;f+=1)l[f].d()}}}function Xu(s,e,t){let{formats:n}=e,{deleteHandler:i}=e;function l(a,o){a[o].userInput=this.value,t(0,n)}const r=a=>i(a);return s.$$set=a=>{"formats"in a&&t(0,n=a.formats),"deleteHandler"in a&&t(1,i=a.deleteHandler)},[n,i,l,r]}class Zu extends be{constructor(e){super(),ve(this,e,Xu,zu,_e,{formats:0,deleteHandler:1})}}function bi(s,e,t){const n=s.slice();return n[14]=e[t],n}function vi(s){let e,t,n,i;return t=new Gn({props:{$$slots:{default:[$u]},$$scope:{ctx:s}}}),{c(){e=w("div"),Z(t.$$.fragment),this.h()},l(l){e=k(l,"DIV",{class:!0});var r=M(e);X(t.$$.fragment,r),r.forEach(p),this.h()},h(){E(e,"class","my-4")},m(l,r){j(l,e,r),z(t,e,null),i=!0},p(l,r){const a={};r&131073&&(a.$$scope={dirty:r,ctx:l}),t.$set(a)},i(l){i||(T(t.$$.fragment,l),l&&ke(()=>{i&&(n||(n=nt(e,dt,{},!0)),n.run(1))}),i=!0)},o(l){P(t.$$.fragment,l),l&&(n||(n=nt(e,dt,{},!1)),n.run(0)),i=!1},d(l){l&&p(e),J(t),l&&n&&n.end()}}}function Qu(s){let e,t;return e=new Zu({props:{formats:s[0].customFormats,deleteHandler:s[6]}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i&1&&(l.formats=n[0].customFormats),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function $u(s){let e,t;return e=new Tt({props:{title:"Saved Custom Formats",$$slots:{default:[Qu]},$$scope:{ctx:s}}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i&131073&&(l.$$scope={dirty:i,ctx:n}),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function yi(s){let e,t=s[14]+"",n,i;return{c(){e=w("option"),n=le(t),i=B(),this.h()},l(l){e=k(l,"OPTION",{});var r=M(e);n=ie(r,t),i=q(r),r.forEach(p),this.h()},h(){e.__value=s[14],we(e,e.__value)},m(l,r){j(l,e,r),g(e,n),g(e,i)},p:re,d(l){l&&p(e)}}}function xu(s){let e;return{c(){e=le("Add Custom Format")},l(t){e=ie(t,"Add Custom Format")},m(t,n){j(t,e,n)},d(t){t&&p(e)}}}function ef(s){let e,t,n,i,l,r="Value Type",a,o,c,u,f,d="Format Name",m,h,_,N,S,O="Format Code",A,v,I,C,D,L,V,b,y,H,U,K=s[0].customFormats&&s[0].customFormats.length>0&&vi(s),G=Ne(s[5]),se=[];for(let F=0;F<G.length;F+=1)se[F]=yi(bi(s,G,F));return D=new We({props:{type:"submit",size:"lg",disabled:!(s[1]&&s[2]),$$slots:{default:[xu]},$$scope:{ctx:s}}}),{c(){K&&K.c(),e=B(),t=w("form"),n=w("div"),i=w("div"),l=w("label"),l.textContent=r,a=B(),o=w("select");for(let F=0;F<se.length;F+=1)se[F].c();c=B(),u=w("div"),f=w("label"),f.textContent=d,m=B(),h=w("input"),_=B(),N=w("div"),S=w("label"),S.textContent=O,A=B(),v=w("input"),C=B(),Z(D.$$.fragment),L=B(),V=w("div"),b=new Ei(!1),this.h()},l(F){K&&K.l(F),e=q(F),t=k(F,"FORM",{autocomplete:!0,class:!0});var R=M(t);n=k(R,"DIV",{class:!0});var te=M(n);i=k(te,"DIV",{class:!0});var ee=M(i);l=k(ee,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),oe(l)!=="svelte-1ynqoxn"&&(l.textContent=r),a=q(ee),o=k(ee,"SELECT",{id:!0,class:!0});var W=M(o);for(let ue=0;ue<se.length;ue+=1)se[ue].l(W);W.forEach(p),ee.forEach(p),c=q(te),u=k(te,"DIV",{class:!0});var ne=M(u);f=k(ne,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),oe(f)!=="svelte-oanagg"&&(f.textContent=d),m=q(ne),h=k(ne,"INPUT",{id:!0,type:!0,placeholder:!0,class:!0}),ne.forEach(p),_=q(te),N=k(te,"DIV",{class:!0});var x=M(N);S=k(x,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),oe(S)!=="svelte-1vghyh5"&&(S.textContent=O),A=q(x),v=k(x,"INPUT",{id:!0,type:!0,placeholder:!0,class:!0}),x.forEach(p),C=q(te),X(D.$$.fragment,te),L=q(te),V=k(te,"DIV",{class:!0});var he=M(V);b=Ti(he,!1),he.forEach(p),te.forEach(p),R.forEach(p),this.h()},h(){E(l,"for","valueType"),E(l,"class","text-sm font-medium text-base-content"),E(o,"id","valueType"),E(o,"class","flex-1 border border-base-300 bg-base-100 shadow-sm text-sm h-9 bg-transparent px-3 py-2 transition-colors focus-visible:outline-none rounded-md cursor-pointer"),s[3]===void 0&&ke(()=>s[9].call(o)),E(i,"class","flex flex-col gap-2"),E(f,"for","formatTag"),E(f,"class","text-sm font-medium text-base-content"),E(h,"id","formatTag"),E(h,"type","text"),E(h,"placeholder","myformat"),E(h,"class","flex-1 border border-base-300 bg-base-100 shadow-sm text-sm h-9 bg-transparent px-3 py-2 transition-colors focus-visible:outline-none rounded-md"),E(u,"class","flex flex-col gap-2"),E(S,"for","formatCode"),E(S,"class","text-sm font-medium text-base-content"),E(v,"id","formatCode"),E(v,"type","text"),E(v,"placeholder",I=s[3]==="date"?"mm/dd/yyyy":"$#,##0.0"),E(v,"class","flex-1 border border-base-300 bg-base-100 shadow-sm text-sm h-9 bg-transparent px-3 py-2 transition-colors focus-visible:outline-none rounded-md"),E(N,"class","flex flex-col gap-2"),b.a=null,E(V,"class","text-negative text-sm"),E(n,"class","flex flex-col gap-4"),E(t,"autocomplete","off"),E(t,"class","my-6")},m(F,R){K&&K.m(F,R),j(F,e,R),j(F,t,R),g(t,n),g(n,i),g(i,l),g(i,a),g(i,o);for(let te=0;te<se.length;te+=1)se[te]&&se[te].m(o,null);St(o,s[3],!0),g(n,c),g(n,u),g(u,f),g(u,m),g(u,h),we(h,s[1]),g(n,_),g(n,N),g(N,S),g(N,A),g(N,v),we(v,s[2]),g(n,C),z(D,n,null),g(n,L),g(n,V),b.m(s[4],V),y=!0,H||(U=[ce(o,"change",s[9]),ce(h,"input",s[10]),ce(v,"input",s[11]),ce(t,"submit",Ci(s[7]))],H=!0)},p(F,[R]){if(F[0].customFormats&&F[0].customFormats.length>0?K?(K.p(F,R),R&1&&T(K,1)):(K=vi(F),K.c(),T(K,1),K.m(e.parentNode,e)):K&&(pe(),P(K,1,1,()=>{K=null}),ge()),R&32){G=Ne(F[5]);let ee;for(ee=0;ee<G.length;ee+=1){const W=bi(F,G,ee);se[ee]?se[ee].p(W,R):(se[ee]=yi(W),se[ee].c(),se[ee].m(o,null))}for(;ee<se.length;ee+=1)se[ee].d(1);se.length=G.length}R&40&&St(o,F[3]),R&2&&h.value!==F[1]&&we(h,F[1]),(!y||R&40&&I!==(I=F[3]==="date"?"mm/dd/yyyy":"$#,##0.0"))&&E(v,"placeholder",I),R&4&&v.value!==F[2]&&we(v,F[2]);const te={};R&6&&(te.disabled=!(F[1]&&F[2])),R&131072&&(te.$$scope={dirty:R,ctx:F}),D.$set(te),(!y||R&16)&&b.p(F[4])},i(F){y||(T(K),T(D.$$.fragment,F),y=!0)},o(F){P(K),P(D.$$.fragment,F),y=!1},d(F){F&&(p(e),p(t)),K&&K.d(F),at(se,F),J(D),H=!1,Je(U)}}}function tf(s,e,t){let{builtInFormats:n=[]}=e,{customFormattingSettings:i={}}=e;const l=["number","date"];let r="",a="",o="number",c="";async function u(S){let A=await(await fetch(Ln("/api/customFormattingSettings.json"),{method:"DELETE",body:JSON.stringify({formatTag:S.formatTag})})).json();A&&t(0,i=A)}async function f(){let S=m();if(S&&S.length>0)t(4,c=S.join("<br/>"));else{let A=await(await fetch(Ln("/api/customFormattingSettings.json"),{method:"POST",body:JSON.stringify({newCustomFormat:{formatTag:r,formatCode:a,valueType:o}})})).json();A?(t(0,i=A),d()):t(4,c=`Unable to create new custom format ${r}`)}}function d(){t(1,r=""),t(2,a=""),t(3,o="number"),t(4,c="")}function m(){var I;let S=[];/^[a-zA-Z][a-zA-Z0-9]*$/.test(r)||S.push(`"${r}" is not a valid format name. The format name should always start with a letter and only contain letters and numbers.`);let O=10,A,v;o==="date"&&(O=new Date);try{A=_r.format(a,O)}catch(C){v=C}return A||S.push(`Format "${a}" is invalid for type "${o}".`),v&&S.push(v),(n.find(C=>C.formatTag===r)||(I=i.customFormats)!=null&&I.find(C=>C.formatTag===r))&&S.push(`The format name "${r}"" is already assigned to an existing format.`),S}function h(){o=jn(this),t(3,o),t(5,l)}function _(){r=this.value,t(1,r)}function N(){a=this.value,t(2,a)}return s.$$set=S=>{"builtInFormats"in S&&t(8,n=S.builtInFormats),"customFormattingSettings"in S&&t(0,i=S.customFormattingSettings)},[i,r,a,o,c,l,u,f,n,h,_,N]}class nf extends be{constructor(e){super(),ve(this,e,tf,ef,_e,{builtInFormats:8,customFormattingSettings:0})}}function sf(s){let e,t;return e=new es({props:{formats:Yt.filter(uf)}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function lf(s){let e,t;return e=new Wu({props:{formats:Yt.filter(ff)}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function rf(s){let e,t;return e=new es({props:{formats:Yt.filter(df)}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function of(s){let e,t;return e=new es({props:{formats:Yt.filter(mf)}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p:re,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function af(s){let e,t,n,i,l,r,a,o;return e=new Tt({props:{title:"Dates",$$slots:{default:[sf]},$$scope:{ctx:s}}}),n=new Tt({props:{title:"Currencies",$$slots:{default:[lf]},$$scope:{ctx:s}}}),l=new Tt({props:{title:"Numbers",$$slots:{default:[rf]},$$scope:{ctx:s}}}),a=new Tt({props:{title:"Percentages",$$slots:{default:[of]},$$scope:{ctx:s}}}),{c(){Z(e.$$.fragment),t=B(),Z(n.$$.fragment),i=B(),Z(l.$$.fragment),r=B(),Z(a.$$.fragment)},l(c){X(e.$$.fragment,c),t=q(c),X(n.$$.fragment,c),i=q(c),X(l.$$.fragment,c),r=q(c),X(a.$$.fragment,c)},m(c,u){z(e,c,u),j(c,t,u),z(n,c,u),j(c,i,u),z(l,c,u),j(c,r,u),z(a,c,u),o=!0},p(c,u){const f={};u&16&&(f.$$scope={dirty:u,ctx:c}),e.$set(f);const d={};u&16&&(d.$$scope={dirty:u,ctx:c}),n.$set(d);const m={};u&16&&(m.$$scope={dirty:u,ctx:c}),l.$set(m);const h={};u&16&&(h.$$scope={dirty:u,ctx:c}),a.$set(h)},i(c){o||(T(e.$$.fragment,c),T(n.$$.fragment,c),T(l.$$.fragment,c),T(a.$$.fragment,c),o=!0)},o(c){P(e.$$.fragment,c),P(n.$$.fragment,c),P(l.$$.fragment,c),P(a.$$.fragment,c),o=!1},d(c){c&&(p(t),p(i),p(r)),J(e,c),J(n,c),J(l,c),J(a,c)}}}function cf(s){let e,t,n,i="Using Formats",l,r,a="In the Value component, you can use the <code>fmt</code> prop",o,c,u,f,d="In charts, you can use the <code>xFmt</code> and <code>yFmt</code> props",m,h,_,N,S=`You can also set formats within your SQL queries using SQL format tags. Use these by aliasing
			your column names and appending a format. For example:`,O,A,v,I,C,D="Builtin Formats",L,V,b="All built-in formats are listed below for reference.",y,H,U,K,G,se="Custom Formats",F,R,te='Add new formats to your project. Custom formats use <a class="markdown" target="_blank" rel="noreferrer" href="https://support.microsoft.com/en-us/office/number-format-codes-5026bbd6-04bc-48cd-bf33-80f18b4eae68">excel-style format codes</a> and are saved in your project.',ee,W,ne;return c=new kn({props:{source:s[3],language:"svelte"}}),h=new kn({props:{source:s[2],language:"svelte"}}),A=new kn({props:{source:s[1],language:"sql"}}),H=new Gn({props:{single:!0,$$slots:{default:[af]},$$scope:{ctx:s}}}),W=new nf({props:{builtInFormats:Yt,customFormattingSettings:s[0]}}),{c(){e=w("section"),t=w("div"),n=w("h3"),n.textContent=i,l=B(),r=w("p"),r.innerHTML=a,o=B(),Z(c.$$.fragment),u=B(),f=w("p"),f.innerHTML=d,m=B(),Z(h.$$.fragment),_=B(),N=w("p"),N.textContent=S,O=B(),Z(A.$$.fragment),v=B(),I=w("div"),C=w("h3"),C.textContent=D,L=B(),V=w("p"),V.textContent=b,y=B(),Z(H.$$.fragment),U=B(),K=w("div"),G=w("h3"),G.textContent=se,F=B(),R=w("p"),R.innerHTML=te,ee=B(),Z(W.$$.fragment),this.h()},l(x){e=k(x,"SECTION",{class:!0});var he=M(e);t=k(he,"DIV",{});var ue=M(t);n=k(ue,"H3",{class:!0,"data-svelte-h":!0}),oe(n)!=="svelte-1pu7as6"&&(n.textContent=i),l=q(ue),r=k(ue,"P",{class:!0,"data-svelte-h":!0}),oe(r)!=="svelte-122umf8"&&(r.innerHTML=a),o=q(ue),X(c.$$.fragment,ue),u=q(ue),f=k(ue,"P",{class:!0,"data-svelte-h":!0}),oe(f)!=="svelte-si2kxx"&&(f.innerHTML=d),m=q(ue),X(h.$$.fragment,ue),_=q(ue),N=k(ue,"P",{class:!0,"data-svelte-h":!0}),oe(N)!=="svelte-vo6frm"&&(N.textContent=S),O=q(ue),X(A.$$.fragment,ue),ue.forEach(p),v=q(he),I=k(he,"DIV",{});var Ce=M(I);C=k(Ce,"H3",{class:!0,"data-svelte-h":!0}),oe(C)!=="svelte-4px9uh"&&(C.textContent=D),L=q(Ce),V=k(Ce,"P",{"data-svelte-h":!0}),oe(V)!=="svelte-1uuy1j1"&&(V.textContent=b),y=q(Ce),X(H.$$.fragment,Ce),Ce.forEach(p),U=q(he),K=k(he,"DIV",{});var He=M(K);G=k(He,"H3",{class:!0,"data-svelte-h":!0}),oe(G)!=="svelte-dhuomx"&&(G.textContent=se),F=q(He),R=k(He,"P",{"data-svelte-h":!0}),oe(R)!=="svelte-14o116d"&&(R.innerHTML=te),ee=q(He),X(W.$$.fragment,He),He.forEach(p),he.forEach(p),this.h()},h(){E(n,"class","text-base-content text-lg font-semibold mb-2"),E(r,"class","markdown"),E(f,"class","markdown"),E(N,"class","markdown"),E(C,"class","text-base-content text-lg font-semibold mb-2"),E(G,"class","text-base-content text-lg font-semibold mb-2"),E(e,"class","flex flex-col gap-6")},m(x,he){j(x,e,he),g(e,t),g(t,n),g(t,l),g(t,r),g(t,o),z(c,t,null),g(t,u),g(t,f),g(t,m),z(h,t,null),g(t,_),g(t,N),g(t,O),z(A,t,null),g(e,v),g(e,I),g(I,C),g(I,L),g(I,V),g(I,y),z(H,I,null),g(e,U),g(e,K),g(K,G),g(K,F),g(K,R),g(K,ee),z(W,K,null),ne=!0},p(x,[he]){const ue={};he&16&&(ue.$$scope={dirty:he,ctx:x}),H.$set(ue);const Ce={};he&1&&(Ce.customFormattingSettings=x[0]),W.$set(Ce)},i(x){ne||(T(c.$$.fragment,x),T(h.$$.fragment,x),T(A.$$.fragment,x),T(H.$$.fragment,x),T(W.$$.fragment,x),ne=!0)},o(x){P(c.$$.fragment,x),P(h.$$.fragment,x),P(A.$$.fragment,x),P(H.$$.fragment,x),P(W.$$.fragment,x),ne=!1},d(x){x&&p(e),J(c),J(h),J(A),J(H),J(W)}}}const uf=s=>s.formatCategory==="date",ff=s=>s.formatCategory==="currency",df=s=>s.formatCategory==="number",mf=s=>s.formatCategory==="percent";function hf(s,e,t){let{customFormattingSettings:n}=e,i=`select 
  growth as growth_pct, -- formatted as a percentage
  sales as sales_usd    -- formatted as US dollars
from table`,l=`<LineChart
	data={sales_data}
	x=date
	y=sales
	yFmt=euro
/>`,r="<Value data={sales_data} column=sales fmt='$#,##0' />";return s.$$set=a=>{"customFormattingSettings"in a&&t(0,n=a.customFormattingSettings)},[n,i,l,r]}class pf extends be{constructor(e){super(),ve(this,e,hf,cf,_e,{customFormattingSettings:0})}}function gf(s){let e,t="Sharing anonymous CLI usage data is one of the best ways you can support Evidence.",n,i,l,r,a,o,c;function u(d){s[3](d)}let f={id:"telemetry-toggle"};return s[0]!==void 0&&(f.checked=s[0]),a=new Yn({props:f}),Ee.push(()=>Ge(a,"checked",u)),a.$on("change",s[1]),{c(){e=w("p"),e.textContent=t,n=B(),i=w("form"),l=w("label"),r=le(`Share anonymous usage data
					`),Z(a.$$.fragment),this.h()},l(d){e=k(d,"P",{class:!0,"data-svelte-h":!0}),oe(e)!=="svelte-1ysee3z"&&(e.textContent=t),n=q(d),i=k(d,"FORM",{id:!0});var m=M(i);l=k(m,"LABEL",{for:!0,class:!0});var h=M(l);r=ie(h,`Share anonymous usage data
					`),X(a.$$.fragment,h),h.forEach(p),m.forEach(p),this.h()},h(){E(e,"class","markdown mb-1 text-pretty"),E(l,"for","telemetry-toggle"),E(l,"class","flex justify-between gap-2 items-center pt-4 mt-4 font-medium"),E(i,"id","telemetry")},m(d,m){j(d,e,m),j(d,n,m),j(d,i,m),g(i,l),g(l,r),z(a,l,null),c=!0},p(d,m){const h={};!o&&m&1&&(o=!0,h.checked=d[0],Ue(()=>o=!1)),a.$set(h)},i(d){c||(T(a.$$.fragment,d),c=!0)},o(d){P(a.$$.fragment,d),c=!1},d(d){d&&(p(e),p(n),p(i)),J(a)}}}function _f(s){let e,t;return e=new Tt({props:{title:"Options",$$slots:{default:[gf]},$$scope:{ctx:s}}}),{c(){Z(e.$$.fragment)},l(n){X(e.$$.fragment,n)},m(n,i){z(e,n,i),t=!0},p(n,i){const l={};i&17&&(l.$$scope={dirty:i,ctx:n}),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function bf(s){let e,t,n;return t=new Gn({props:{$$slots:{default:[_f]},$$scope:{ctx:s}}}),{c(){e=w("div"),Z(t.$$.fragment)},l(i){e=k(i,"DIV",{});var l=M(e);X(t.$$.fragment,l),l.forEach(p)},m(i,l){j(i,e,l),z(t,e,null),n=!0},p(i,[l]){const r={};l&17&&(r.$$scope={dirty:l,ctx:i}),t.$set(r)},i(i){n||(T(t.$$.fragment,i),n=!0)},o(i){P(t.$$.fragment,i),n=!1},d(i){i&&p(e),J(t)}}}function vf(s,e,t){let{settings:n}=e,i=(n.send_anonymous_usage_stats??"yes")==="yes";async function l(){t(2,n.send_anonymous_usage_stats=i?"yes":"no",n),await fetch(Ln("/api/settings.json"),{method:"POST",body:JSON.stringify({settings:n})})}function r(a){i=a,t(0,i)}return s.$$set=a=>{"settings"in a&&t(2,n=a.settings)},[i,l,n,r]}class yf extends be{constructor(e){super(),ve(this,e,vf,bf,_e,{settings:2})}}const kf=async({fetch:s,data:e})=>({...e,settings:{},gitIgnore:""}),jf=Object.freeze(Object.defineProperty({__proto__:null,load:kf},Symbol.toStringTag,{value:"Module"}));function wf(s){let e,t="Settings are only available in development mode.";return{c(){e=w("p"),e.textContent=t},l(n){e=k(n,"P",{"data-svelte-h":!0}),oe(e)!=="svelte-591hpj"&&(e.textContent=t)},m(n,i){j(n,e,i)},p:re,i:re,o:re,d(n){n&&p(e)}}}function Cf(s){let e,t='<div class="max-w-7xl px-6 sm:px-8 md:px-12 mx-auto"><a href="/" class="block text-sm text-base-content-muted mb-2">← Home</a> <h1 class="text-xl text-base-content font-bold">Project Settings</h1></div>',n,i,l,r,a='<div class="flex flex-col gap-4 text-sm text-base-content-muted"><a href="#sources" class="hover:text-base-content transition-colors">Sources</a> <a href="#deployment" class="hover:text-base-content transition-colors">Deployment</a> <a href="#formatting" class="hover:text-base-content transition-colors">Value Formatting</a> <a href="#telemetry" class="hover:text-base-content transition-colors">Telemetry</a></div>',o,c,u,f,d=`<h2 class="text-2xl text-base-content font-semibold mb-4 text-pretty">Sources</h2> <p class="text-base-content text-base mb-2">Sources connect your Evidence project to databases, local files, and APIs. Each source
							creates a directory in your project under <code class="markdown">/sources</code> where
							you can add queries.
							<a href="https://docs.evidence.dev/core-concepts/data-sources/" target="_blank" class="markdown">Learn more about sources.</a></p>`,m,h,_,N,S,O=`<h2 class="text-2xl text-base-content font-semibold mb-4">Deployment</h2> <p class="text-base-content text-base mb-2">Evidence projects can be deployed to a variety of cloud environments. The easiest way
							to deploy your project with authentication, scheduled updates, and a custom domain is
							with Evidence Cloud.
							<a href="https://docs.evidence.dev/deployment/overview/" target="_blank" class="markdown">Learn more about deployment.</a></p>`,A,v,I,C,D,L=`<h2 class="text-2xl text-base-content font-semibold mb-4">Value Formatting</h2> <p class="text-base-content text-base mb-2">Evidence supports built-in formats and Excel-style formats. You can apply these
							formats using component props or SQL format tags.
							<a href="https://docs.evidence.dev/core-concepts/formatting/" target="_blank" class="markdown">Learn more about formatting.</a></p>`,V,b,y,H,U,K=`<h2 class="text-2xl text-base-content font-semibold mb-4">Telemetry</h2> <p class="text-base-content text-base mb-2">The Evidence CLI collects anonymous usage data to help us understand how often the
							tool is being used. <a href="https://github.com/evidence-dev/evidence/tree/next/packages/lib/telemetry" target="_blank" class="markdown">View telemetry source code.</a></p>`,G,se,F;return h=new yu({props:{availableSourcePlugins:s[3],sources:s[2]}}),v=new Bu({props:{settings:s[0],sources:s[2]}}),b=new pf({props:{customFormattingSettings:s[1]}}),se=new yf({props:{settings:s[0]}}),{c(){e=w("div"),e.innerHTML=t,n=B(),i=w("div"),l=w("div"),r=w("div"),r.innerHTML=a,o=B(),c=w("div"),u=w("section"),f=w("div"),f.innerHTML=d,m=B(),Z(h.$$.fragment),_=B(),N=w("section"),S=w("div"),S.innerHTML=O,A=B(),Z(v.$$.fragment),I=B(),C=w("section"),D=w("div"),D.innerHTML=L,V=B(),Z(b.$$.fragment),y=B(),H=w("section"),U=w("div"),U.innerHTML=K,G=B(),Z(se.$$.fragment),this.h()},l(R){e=k(R,"DIV",{class:!0,"data-svelte-h":!0}),oe(e)!=="svelte-1uonzt0"&&(e.innerHTML=t),n=q(R),i=k(R,"DIV",{class:!0});var te=M(i);l=k(te,"DIV",{class:!0});var ee=M(l);r=k(ee,"DIV",{class:!0,"data-svelte-h":!0}),oe(r)!=="svelte-joiizt"&&(r.innerHTML=a),o=q(ee),c=k(ee,"DIV",{class:!0});var W=M(c);u=k(W,"SECTION",{id:!0,class:!0});var ne=M(u);f=k(ne,"DIV",{class:!0,"data-svelte-h":!0}),oe(f)!=="svelte-ofdx38"&&(f.innerHTML=d),m=q(ne),X(h.$$.fragment,ne),ne.forEach(p),_=q(W),N=k(W,"SECTION",{id:!0,class:!0});var x=M(N);S=k(x,"DIV",{class:!0,"data-svelte-h":!0}),oe(S)!=="svelte-axgnvm"&&(S.innerHTML=O),A=q(x),X(v.$$.fragment,x),x.forEach(p),I=q(W),C=k(W,"SECTION",{id:!0,class:!0});var he=M(C);D=k(he,"DIV",{class:!0,"data-svelte-h":!0}),oe(D)!=="svelte-ehtw6i"&&(D.innerHTML=L),V=q(he),X(b.$$.fragment,he),he.forEach(p),y=q(W),H=k(W,"SECTION",{id:!0,class:!0});var ue=M(H);U=k(ue,"DIV",{class:!0,"data-svelte-h":!0}),oe(U)!=="svelte-16wghy7"&&(U.innerHTML=K),G=q(ue),X(se.$$.fragment,ue),ue.forEach(p),W.forEach(p),ee.forEach(p),te.forEach(p),this.h()},h(){E(e,"class","fixed top-12 left-0 right-0 bg-base-100 z-40 py-6 border-b border-base-200 bg-base-100/90 backdrop-blur"),E(r,"class","fixed w-60 top-48 hidden lg:block"),E(f,"class","mb-6"),E(u,"id","sources"),E(u,"class","scroll-mt-48"),E(S,"class","mb-6"),E(N,"id","deployment"),E(N,"class","scroll-mt-[9.5rem] border-t border-base-300 pt-8"),E(D,"class","mb-6"),E(C,"id","formatting"),E(C,"class","scroll-mt-[9.5rem] border-t border-base-300 pt-8"),E(U,"class","mb-6"),E(H,"id","telemetry"),E(H,"class","scroll-mt-[9.5rem] border-t border-base-300 pt-8"),E(c,"class","flex flex-col lg:ml-60 lg:px-8 gap-12 w-full overflow-x-auto"),E(l,"class","w-full relative flex overflow-x-hidden"),E(i,"class","flex pt-28")},m(R,te){j(R,e,te),j(R,n,te),j(R,i,te),g(i,l),g(l,r),g(l,o),g(l,c),g(c,u),g(u,f),g(u,m),z(h,u,null),g(c,_),g(c,N),g(N,S),g(N,A),z(v,N,null),g(c,I),g(c,C),g(C,D),g(C,V),z(b,C,null),g(c,y),g(c,H),g(H,U),g(H,G),z(se,H,null),F=!0},p(R,te){const ee={};te&8&&(ee.availableSourcePlugins=R[3]),te&4&&(ee.sources=R[2]),h.$set(ee);const W={};te&1&&(W.settings=R[0]),te&4&&(W.sources=R[2]),v.$set(W);const ne={};te&2&&(ne.customFormattingSettings=R[1]),b.$set(ne);const x={};te&1&&(x.settings=R[0]),se.$set(x)},i(R){F||(T(h.$$.fragment,R),T(v.$$.fragment,R),T(b.$$.fragment,R),T(se.$$.fragment,R),F=!0)},o(R){P(h.$$.fragment,R),P(v.$$.fragment,R),P(b.$$.fragment,R),P(se.$$.fragment,R),F=!1},d(R){R&&(p(e),p(n),p(i)),J(h),J(v),J(b),J(se)}}}function Tf(s){let e,t,n,i;const l=[Cf,wf],r=[];function a(o,c){return 1}return e=a(),t=r[e]=l[e](s),{c(){t.c(),n=fe()},l(o){t.l(o),n=fe()},m(o,c){r[e].m(o,c),j(o,n,c),i=!0},p(o,[c]){t.p(o,c)},i(o){i||(T(t),i=!0)},o(o){P(t),i=!1},d(o){o&&p(n),r[e].d(o)}}}function Ef(s,e,t){let{data:n}=e,{settings:i,customFormattingSettings:l,sources:r,plugins:a}=n;return s.$$set=o=>{"data"in o&&t(4,n=o.data)},s.$$.update=()=>{s.$$.dirty&16&&t(0,{settings:i,customFormattingSettings:l,sources:r,plugins:a}=n,i,(t(1,l),t(4,n)),(t(2,r),t(4,n)),(t(3,a),t(4,n)))},[i,l,r,a,n]}class Ff extends be{constructor(e){super(),ve(this,e,Ef,Tf,_e,{data:4})}}export{Ff as component,jf as universal};

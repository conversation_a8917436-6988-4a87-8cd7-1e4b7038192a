import{s as nt,v as ce,d,b as h,i as E,e as I,h as M,j as Q,a7 as yl,m as D,a8 as gl,C as St,a9 as Xl,p as xt,l as gt,N as v,k as J,n as x,w as Ze,x as <PERSON>,y as <PERSON>,z as Gl,aa as Dl,ab as Un,r as $e,ac as Hn,ad as Vn,q as Oe,t as Dt,J as Ht,ae as Wn,af as Ti,D as qn,ag as vn,a1 as Pl,A as pt,E as $t,a2 as vi,ah as jn,a3 as Et,a4 as Gt,I as Yn,a0 as Li,c as el,u as tl,g as ll,a as il,ai as In,aj as Xn,ak as Qn,B as Kn}from"../chunks/scheduler.DQwIXrE4.js";import{S as st,i as rt,d as ae,t as U,f as qt,j as Ai,a as O,m as fe,b as oe,e as ue,g as Je,c as xe,k as Zn}from"../chunks/index.BEt_7cXZ.js";import{g as Bt,h as Vt,Y as wi,e as vt,Z as Mn,_ as _l,$ as Jn,a0 as ct,P as ji,a1 as xn,a2 as Yi,a3 as Rl,a4 as pn,a5 as $n,a6 as Oi,a7 as Ii,a8 as Dn,a9 as es,f as ts,aa as ls,ab as Zt,ac as is,ad as ns,ae as Ci,af as ki,ag as Si,V as Ve,ah as Cl,ai as ss,aj as It,ak as Wt,al as Yl,am as Mi,an as Di,ao as Ni,ap as Fi,aq as Ql,ar as Mt,O as Kl,S as Zl,as as Jl,at as rs,au as as,av as fs,aw as os,X as Nn,ax as us,W as cs,R as bl,ay as Ll,az as ms,aA as ds,aB as hs,aC as ys,aD as gs,aE as Xi,aF as bs}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CQPVJDjj.js";import{w as Ei}from"../chunks/entry.ZUgAwmTc.js";import{h as Qi,p as _s}from"../chunks/setTrackProxy.DjIbdjlZ.js";import{p as Fn}from"../chunks/stores.BZJ6BR5k.js";function Ki(i){return t=>t.map(l=>{var s;const n={},r=Object.keys(l);for(const a of r){const f=(s=i[a])!=null?s:a;n[f]=l[a]}return n})}function Cs(i,e){if(i.length===0||e.length===0)return{};const t=Object.keys(i[0]),l=Object.keys(e[0]),s={};for(const n of t)l.includes(n)&&(s[n]=n);return s}function Ts(i,e,t){for(const l in t){const s=t[l];if(i[s]!==e[l])return!1}return!0}function Ls(i,e){return l=>{if(!i.length)return l;const s=Cs(l,i),n=Object.keys(i[0]);return l.flatMap(a=>{const f=i.filter(u=>Ts(a,u,s));if(f.length)return f.map(u=>({...a,...u}));const o=Object.fromEntries(n.filter(u=>a[u]==null).map(u=>[u,void 0]));return{...a,...o}})}}function Zi(i){return t=>{const l=t.map(s=>({...s}));for(const s in i){const n=i[s],r=typeof n=="function"?n(l):n,a=r!=null&&r[Symbol.iterator]&&typeof r!="string"?r:t.map(()=>r);let f=-1;for(const o of l)o[s]=a[++f]}return l}}function ks(i){return t=>{const l=Es(i),s=[];for(const n in l){const r=l[n];let a;typeof r=="function"?a=r(t):Array.isArray(r)?a=r:a=Array.from(new Set(t.map(f=>f[n]))),s.push(a.map(f=>({[n]:f})))}return Ss(s)}}function Ss(i){function e(l,s,n){if(!n.length&&s!=null){l.push(s);return}const r=n[0],a=n.slice(1);for(const f of r)e(l,{...s,...f},a)}const t=[];return e(t,null,i),t}function Es(i){if(Array.isArray(i)){const e={};for(const t of i)e[t]=t;return e}else if(typeof i=="object")return i;return{[i]:i}}function As(i){return t=>{const l=[];for(const s of t){const n={...s};for(const r in i)n[r]==null&&(n[r]=i[r]);l.push(n)}return l}}function Ji(i,e){return l=>{const s=ks(i)(l),n=Ls(l)(s);return e?As(e)(n):n}}function xi(i,e,t){return i==null||e==null?void 0:e===0&&i===0?0:!t&&e===0?void 0:i/e}function pi(i,e,t){const l=typeof i=="function"?i:a=>a[i],s=a=>a[e],{predicate:n,allowDivideByZero:r}={};return n==null?(a,f,o)=>{const u=s(a),c=l(a,f,o);return xi(c,u,r)}:(a,f,o)=>{if(!n(a,f,o))return;const u=s(a),c=l(a,f,o);return xi(c,u,r)}}function ws(i){let e,t,l;return{c(){e=D("span"),t=gl("svg"),l=gl("path"),this.h()},l(s){e=M(s,"SPAN",{"aria-expanded":!0,class:!0});var n=Q(e);t=yl(n,"svg",{viewBox:!0,width:!0,height:!0,class:!0});var r=Q(t);l=yl(r,"path",{fill:!0,"fill-rule":!0,d:!0}),Q(l).forEach(d),r.forEach(d),n.forEach(d),this.h()},h(){h(l,"fill",i[3]),h(l,"fill-rule","evenodd"),h(l,"d","M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"),h(t,"viewBox","0 0 16 16"),h(t,"width",i[1]),h(t,"height",i[1]),h(t,"class","svelte-lqleyo"),h(e,"aria-expanded",i[0]),h(e,"class","svelte-lqleyo")},m(s,n){E(s,e,n),I(e,t),I(t,l)},p(s,[n]){n&8&&h(l,"fill",s[3]),n&2&&h(t,"width",s[1]),n&2&&h(t,"height",s[1]),n&1&&h(e,"aria-expanded",s[0])},i:ce,o:ce,d(s){s&&d(e)}}}function Os(i,e,t){let l,s,n=ce,r=()=>(n(),n=St(l,c=>t(3,s=c)),l);i.$$.on_destroy.push(()=>n());const{resolveColor:a}=Bt();let{toggled:f=!1}=e,{color:o="base-content"}=e,{size:u=10}=e;return i.$$set=c=>{"toggled"in c&&t(0,f=c.toggled),"color"in c&&t(4,o=c.color),"size"in c&&t(1,u=c.size)},i.$$.update=()=>{i.$$.dirty&16&&r(t(2,l=a(o)))},[f,u,l,s,o]}class Pn extends st{constructor(e){super(),rt(this,e,Os,ws,nt,{toggled:0,color:4,size:1})}}function $i(i,e,t){const l=i.slice();return l[12]=e[t],l[14]=t,l}function en(i,e,t){const l=i.slice();return l[15]=e[t],l[17]=t,l}function tn(i,e,t){const l=i.slice();return l[15]=e[t],l}function ln(i,e,t){const l=i.slice();return l[15]=e[t],l}function nn(i){let e,t=i[15].id+"",l,s,n,r;return{c(){e=D("th"),l=Le(t),this.h()},l(a){e=M(a,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var f=Q(e);l=Te(f,t),f.forEach(d),this.h()},h(){var a,f;h(e,"class",s="py-0 px-2 font-medium "+i[15].type+" svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"evidencetype",n=((a=i[15].evidenceColumnType)==null?void 0:a.evidenceType)||"unavailable"),h(e,"evidencetypefidelity",r=((f=i[15].evidenceColumnType)==null?void 0:f.typeFidelity)||"unavailable")},m(a,f){E(a,e,f),I(e,l)},p(a,f){var o,u;f&8&&t!==(t=a[15].id+"")&&Ze(l,t),f&8&&s!==(s="py-0 px-2 font-medium "+a[15].type+" svelte-ghf30y")&&h(e,"class",s),f&64&&v(e,"width",a[6]+"%"),f&8&&n!==(n=((o=a[15].evidenceColumnType)==null?void 0:o.evidenceType)||"unavailable")&&h(e,"evidencetype",n),f&8&&r!==(r=((u=a[15].evidenceColumnType)==null?void 0:u.typeFidelity)||"unavailable")&&h(e,"evidencetypefidelity",r)},d(a){a&&d(e)}}}function sn(i){let e,t=i[15].type+"",l,s,n,r;return{c(){e=D("th"),l=Le(t),this.h()},l(a){e=M(a,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var f=Q(e);l=Te(f,t),f.forEach(d),this.h()},h(){var a,f;h(e,"class",s=i[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"evidencetype",n=((a=i[15].evidenceColumnType)==null?void 0:a.evidenceType)||"unavailable"),h(e,"evidencetypefidelity",r=((f=i[15].evidenceColumnType)==null?void 0:f.typeFidelity)||"unavailable")},m(a,f){E(a,e,f),I(e,l)},p(a,f){var o,u;f&8&&t!==(t=a[15].type+"")&&Ze(l,t),f&8&&s!==(s=a[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y")&&h(e,"class",s),f&64&&v(e,"width",a[6]+"%"),f&8&&n!==(n=((o=a[15].evidenceColumnType)==null?void 0:o.evidenceType)||"unavailable")&&h(e,"evidencetype",n),f&8&&r!==(r=((u=a[15].evidenceColumnType)==null?void 0:u.typeFidelity)||"unavailable")&&h(e,"evidencetypefidelity",r)},d(a){a&&d(e)}}}function Is(i){let e=(i[2]+i[14]+1).toLocaleString()+"",t;return{c(){t=Le(e)},l(l){t=Te(l,e)},m(l,s){E(l,t,s)},p(l,s){s&4&&e!==(e=(l[2]+l[14]+1).toLocaleString()+"")&&Ze(t,e)},d(l){l&&d(t)}}}function Ms(i){let e=(i[2]+i[14]+1).toLocaleString()+"",t;return{c(){t=Le(e)},l(l){t=Te(l,e)},m(l,s){E(l,t,s)},p(l,s){s&4&&e!==(e=(l[2]+l[14]+1).toLocaleString()+"")&&Ze(t,e)},d(l){l&&d(t)}}}function Ds(i){let e,t=(i[12][i[15].id]||"Ø")+"",l;return{c(){e=D("td"),l=Le(t),this.h()},l(s){e=M(s,"TD",{class:!0,style:!0});var n=Q(e);l=Te(n,t),n.forEach(d),this.h()},h(){h(e,"class","other svelte-ghf30y"),v(e,"width",i[6]+"%")},m(s,n){E(s,e,n),I(e,l)},p(s,n){n&40&&t!==(t=(s[12][s[15].id]||"Ø")+"")&&Ze(l,t),n&64&&v(e,"width",s[6]+"%")},d(s){s&&d(e)}}}function Ns(i){let e,t,l=(i[12][i[15].id]??"Ø")+"",s,n;return{c(){e=D("td"),t=D("div"),s=Le(l),this.h()},l(r){e=M(r,"TD",{class:!0,style:!0,title:!0});var a=Q(e);t=M(a,"DIV",{class:!0});var f=Q(t);s=Te(f,l),f.forEach(d),a.forEach(d),this.h()},h(){h(t,"class","svelte-ghf30y"),h(e,"class","boolean svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"title",n=i[12][i[15].id])},m(r,a){E(r,e,a),I(e,t),I(t,s)},p(r,a){a&40&&l!==(l=(r[12][r[15].id]??"Ø")+"")&&Ze(s,l),a&64&&v(e,"width",r[6]+"%"),a&40&&n!==(n=r[12][r[15].id])&&h(e,"title",n)},d(r){r&&d(e)}}}function Fs(i){let e,t,l=(i[12][i[15].id]||"Ø")+"",s,n;return{c(){e=D("td"),t=D("div"),s=Le(l),this.h()},l(r){e=M(r,"TD",{class:!0,style:!0,title:!0});var a=Q(e);t=M(a,"DIV",{class:!0});var f=Q(t);s=Te(f,l),f.forEach(d),a.forEach(d),this.h()},h(){h(t,"class","svelte-ghf30y"),h(e,"class","string svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"title",n=i[12][i[15].id])},m(r,a){E(r,e,a),I(e,t),I(t,s)},p(r,a){a&40&&l!==(l=(r[12][r[15].id]||"Ø")+"")&&Ze(s,l),a&64&&v(e,"width",r[6]+"%"),a&40&&n!==(n=r[12][r[15].id])&&h(e,"title",n)},d(r){r&&d(e)}}}function Ps(i){let e,t,l=ct(i[12][i[15].id],i[3][i[17]].format,i[3][i[17]].columnUnitSummary)+"",s,n;return{c(){e=D("td"),t=D("div"),s=Le(l),this.h()},l(r){e=M(r,"TD",{class:!0,style:!0,title:!0});var a=Q(e);t=M(a,"DIV",{class:!0});var f=Q(t);s=Te(f,l),f.forEach(d),a.forEach(d),this.h()},h(){h(t,"class","svelte-ghf30y"),h(e,"class","string svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"title",n=ct(i[12][i[15].id],i[3][i[17]].format,i[3][i[17]].columnUnitSummary))},m(r,a){E(r,e,a),I(e,t),I(t,s)},p(r,a){a&40&&l!==(l=ct(r[12][r[15].id],r[3][r[17]].format,r[3][r[17]].columnUnitSummary)+"")&&Ze(s,l),a&64&&v(e,"width",r[6]+"%"),a&40&&n!==(n=ct(r[12][r[15].id],r[3][r[17]].format,r[3][r[17]].columnUnitSummary))&&h(e,"title",n)},d(r){r&&d(e)}}}function Rs(i){let e,t=ct(i[12][i[15].id],i[3][i[17]].format,i[3][i[17]].columnUnitSummary)+"",l;return{c(){e=D("td"),l=Le(t),this.h()},l(s){e=M(s,"TD",{class:!0,style:!0});var n=Q(e);l=Te(n,t),n.forEach(d),this.h()},h(){h(e,"class","number svelte-ghf30y"),v(e,"width",i[6]+"%")},m(s,n){E(s,e,n),I(e,l)},p(s,n){n&40&&t!==(t=ct(s[12][s[15].id],s[3][s[17]].format,s[3][s[17]].columnUnitSummary)+"")&&Ze(l,t),n&64&&v(e,"width",s[6]+"%")},d(s){s&&d(e)}}}function Bs(i){let e,t="Ø",l,s;return{c(){e=D("td"),l=Le(t),this.h()},l(n){e=M(n,"TD",{class:!0,style:!0});var r=Q(e);l=Te(r,t),r.forEach(d),this.h()},h(){h(e,"class",s="text-base-content-muted "+i[3][i[17]].type+" svelte-ghf30y"),v(e,"width",i[6]+"%")},m(n,r){E(n,e,r),I(e,l)},p(n,r){r&8&&s!==(s="text-base-content-muted "+n[3][n[17]].type+" svelte-ghf30y")&&h(e,"class",s),r&64&&v(e,"width",n[6]+"%")},d(n){n&&d(e)}}}function rn(i){let e;function t(n,r){return n[12][n[15].id]==null?Bs:n[3][n[17]].type==="number"?Rs:n[3][n[17]].type==="date"?Ps:n[3][n[17]].type==="string"?Fs:n[3][n[17]].type==="boolean"?Ns:Ds}let l=t(i),s=l(i);return{c(){s.c(),e=$e()},l(n){s.l(n),e=$e()},m(n,r){s.m(n,r),E(n,e,r)},p(n,r){l===(l=t(n))&&s?s.p(n,r):(s.d(1),s=l(n),s&&(s.c(),s.m(e.parentNode,e)))},d(n){n&&d(e),s.d(n)}}}function an(i){let e,t,l,s;function n(u,c){return u[14]===0?Ms:Is}let a=n(i)(i),f=Vt(i[3]),o=[];for(let u=0;u<f.length;u+=1)o[u]=rn(en(i,f,u));return{c(){e=D("tr"),t=D("td"),a.c(),l=x();for(let u=0;u<o.length;u+=1)o[u].c();s=x(),this.h()},l(u){e=M(u,"TR",{});var c=Q(e);t=M(c,"TD",{class:!0,style:!0});var y=Q(t);a.l(y),y.forEach(d),l=J(c);for(let T=0;T<o.length;T+=1)o[T].l(c);s=J(c),c.forEach(d),this.h()},h(){h(t,"class","index text-base-content-muted svelte-ghf30y"),v(t,"width","10%")},m(u,c){E(u,e,c),I(e,t),a.m(t,null),I(e,l);for(let y=0;y<o.length;y+=1)o[y]&&o[y].m(e,null);I(e,s)},p(u,c){if(a.p(u,c),c&104){f=Vt(u[3]);let y;for(y=0;y<f.length;y+=1){const T=en(u,f,y);o[y]?o[y].p(T,c):(o[y]=rn(T),o[y].c(),o[y].m(e,s))}for(;y<o.length;y+=1)o[y].d(1);o.length=f.length}},d(u){u&&d(e),a.d(),Xl(o,u)}}}function fn(i){let e,t,l,s,n=(i[2]+Jt).toLocaleString()+"",r,a,f=(i[4]+Jt).toLocaleString()+"",o,u,c;return{c(){e=D("div"),t=D("input"),l=x(),s=D("span"),r=Le(n),a=Le(" of "),o=Le(f),this.h()},l(y){e=M(y,"DIV",{class:!0});var T=Q(e);t=M(T,"INPUT",{type:!0,max:!0,step:!0,class:!0}),l=J(T),s=M(T,"SPAN",{class:!0});var g=Q(s);r=Te(g,n),a=Te(g," of "),o=Te(g,f),g.forEach(d),T.forEach(d),this.h()},h(){h(t,"type","range"),h(t,"max",i[4]),h(t,"step","1"),h(t,"class","slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"),h(s,"class","text-xs svelte-ghf30y"),h(e,"class","pagination svelte-ghf30y")},m(y,T){E(y,e,T),I(e,t),Dl(t,i[2]),I(e,l),I(e,s),I(s,r),I(s,a),I(s,o),u||(c=[gt(t,"change",i[9]),gt(t,"input",i[9]),gt(t,"input",i[7])],u=!0)},p(y,T){T&16&&h(t,"max",y[4]),T&4&&Dl(t,y[2]),T&4&&n!==(n=(y[2]+Jt).toLocaleString()+"")&&Ze(r,n),T&16&&f!==(f=(y[4]+Jt).toLocaleString()+"")&&Ze(o,f)},d(y){y&&d(e),u=!1,Gl(c)}}}function zs(i){let e,t,l,s,n,r,a,f,o,u,c,y,T,g,m,L,w,S,A,B,H,V,K,z,W,b,X=Vt(i[3]),G=[];for(let j=0;j<X.length;j+=1)G[j]=nn(ln(i,X,j));let q=Vt(i[3]),Y=[];for(let j=0;j<q.length;j+=1)Y[j]=sn(tn(i,q,j));let N=Vt(i[5]),p=[];for(let j=0;j<N.length;j+=1)p[j]=an($i(i,N,j));let ne=i[4]>0&&fn(i);return V=new wi({props:{class:"download-button",data:i[1],queryID:i[0],display:!0}}),{c(){e=D("div"),t=D("div"),l=D("table"),s=D("thead"),n=D("tr"),r=D("th"),a=x();for(let j=0;j<G.length;j+=1)G[j].c();f=x(),o=D("tr"),u=x(),c=D("tr"),y=D("th"),T=x();for(let j=0;j<Y.length;j+=1)Y[j].c();g=x(),m=D("tr"),L=x(),w=D("tbody");for(let j=0;j<p.length;j+=1)p[j].c();A=x(),ne&&ne.c(),B=x(),H=D("div"),ue(V.$$.fragment),this.h()},l(j){e=M(j,"DIV",{class:!0});var ee=Q(e);t=M(ee,"DIV",{class:!0});var se=Q(t);l=M(se,"TABLE",{class:!0});var Z=Q(l);s=M(Z,"THEAD",{});var te=Q(s);n=M(te,"TR",{});var me=Q(n);r=M(me,"TH",{class:!0,style:!0}),Q(r).forEach(d),a=J(me);for(let re=0;re<G.length;re+=1)G[re].l(me);f=J(me),me.forEach(d),o=M(te,"TR",{}),Q(o).forEach(d),u=J(te),c=M(te,"TR",{class:!0});var he=Q(c);y=M(he,"TH",{class:!0,style:!0}),Q(y).forEach(d),T=J(he);for(let re=0;re<Y.length;re+=1)Y[re].l(he);g=J(he),he.forEach(d),m=M(te,"TR",{}),Q(m).forEach(d),te.forEach(d),L=J(Z),w=M(Z,"TBODY",{});var ge=Q(w);for(let re=0;re<p.length;re+=1)p[re].l(ge);ge.forEach(d),Z.forEach(d),se.forEach(d),A=J(ee),ne&&ne.l(ee),B=J(ee),H=M(ee,"DIV",{class:!0});var $=Q(H);oe(V.$$.fragment,$),$.forEach(d),ee.forEach(d),this.h()},h(){h(r,"class","py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y"),v(r,"width","10%"),h(y,"class","py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y"),v(y,"width","10%"),h(c,"class","type-indicator svelte-ghf30y"),h(l,"class","text-xs svelte-ghf30y"),h(t,"class","scrollbox pretty-scrollbar svelte-ghf30y"),h(H,"class","footer svelte-ghf30y"),h(e,"class","results-pane py-1 svelte-ghf30y")},m(j,ee){E(j,e,ee),I(e,t),I(t,l),I(l,s),I(s,n),I(n,r),I(n,a);for(let se=0;se<G.length;se+=1)G[se]&&G[se].m(n,null);I(n,f),I(s,o),I(s,u),I(s,c),I(c,y),I(c,T);for(let se=0;se<Y.length;se+=1)Y[se]&&Y[se].m(c,null);I(c,g),I(s,m),I(l,L),I(l,w);for(let se=0;se<p.length;se+=1)p[se]&&p[se].m(w,null);I(e,A),ne&&ne.m(e,null),I(e,B),I(e,H),fe(V,H,null),z=!0,W||(b=gt(w,"wheel",i[8]),W=!0)},p(j,[ee]){if(ee&72){X=Vt(j[3]);let Z;for(Z=0;Z<X.length;Z+=1){const te=ln(j,X,Z);G[Z]?G[Z].p(te,ee):(G[Z]=nn(te),G[Z].c(),G[Z].m(n,f))}for(;Z<G.length;Z+=1)G[Z].d(1);G.length=X.length}if(ee&72){q=Vt(j[3]);let Z;for(Z=0;Z<q.length;Z+=1){const te=tn(j,q,Z);Y[Z]?Y[Z].p(te,ee):(Y[Z]=sn(te),Y[Z].c(),Y[Z].m(c,g))}for(;Z<Y.length;Z+=1)Y[Z].d(1);Y.length=q.length}if(ee&108){N=Vt(j[5]);let Z;for(Z=0;Z<N.length;Z+=1){const te=$i(j,N,Z);p[Z]?p[Z].p(te,ee):(p[Z]=an(te),p[Z].c(),p[Z].m(w,null))}for(;Z<p.length;Z+=1)p[Z].d(1);p.length=N.length}j[4]>0?ne?ne.p(j,ee):(ne=fn(j),ne.c(),ne.m(e,B)):ne&&(ne.d(1),ne=null);const se={};ee&2&&(se.data=j[1]),ee&1&&(se.queryID=j[0]),V.$set(se)},i(j){z||(j&&(S||xt(()=>{S=Ai(l,Mn,{}),S.start()})),O(V.$$.fragment,j),j&&xt(()=>{z&&(K||(K=qt(e,vt,{},!0)),K.run(1))}),z=!0)},o(j){U(V.$$.fragment,j),j&&(K||(K=qt(e,vt,{},!1)),K.run(0)),z=!1},d(j){j&&d(e),Xl(G,j),Xl(Y,j),Xl(p,j),ne&&ne.d(),ae(V),j&&K&&K.end(),W=!1,b()}}}let Jt=5;function Gs(i,e,t){let l,s,n,r,{queryID:a}=e,{data:f}=e,o=0,u;function c(){u=f.slice(o,o+Jt),t(5,r=u)}const y=Jn(m=>{t(2,o=Math.min(Math.max(0,o+Math.floor(m.deltaY/Math.abs(m.deltaY))),n)),c()},60);function T(m){if(Math.abs(m.deltaX)>=Math.abs(m.deltaY))return;const L=m.deltaY<0&&o===0,w=m.deltaY>0&&o===n;L||w||(m.preventDefault(),y(m))}function g(){o=Un(this.value),t(2,o)}return i.$$set=m=>{"queryID"in m&&t(0,a=m.queryID),"data"in m&&t(1,f=m.data)},i.$$.update=()=>{i.$$.dirty&2&&t(3,l=_l(f,"array")),i.$$.dirty&8&&t(6,s=90/(l.length+1)),i.$$.dirty&2&&t(4,n=Math.max(f.length-Jt,0)),i.$$.dirty&6&&t(5,r=f.slice(o,o+Jt))},[a,f,o,l,n,r,s,c,T,g]}class Us extends st{constructor(e){super(),rt(this,e,Gs,zs,nt,{queryID:0,data:1})}}const on={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/};function Hs(i){let e,t,l,s,n=ji.highlight(i[0],on)+"",r;return{c(){e=D("pre"),t=Le("  "),l=D("code"),s=new Vn(!1),r=Le(`
`),this.h()},l(a){e=M(a,"PRE",{class:!0});var f=Q(e);t=Te(f,"  "),l=M(f,"CODE",{class:!0});var o=Q(l);s=Hn(o,!1),o.forEach(d),r=Te(f,`
`),f.forEach(d),this.h()},h(){s.a=null,h(l,"class","language-sql svelte-re3fhx"),h(e,"class","text-xs max-h-56 overflow-auto pretty-scrollbar")},m(a,f){E(a,e,f),I(e,t),I(e,l),s.m(n,l),I(e,r)},p(a,[f]){f&1&&n!==(n=ji.highlight(a[0],on)+"")&&s.p(n)},i:ce,o:ce,d(a){a&&d(e)}}}function Vs(i,e,t){let{code:l=""}=e;return i.$$set=s=>{"code"in s&&t(0,l=s.code)},[l]}class Rn extends st{constructor(e){super(),rt(this,e,Vs,Hs,nt,{code:0})}}function Ws(i){let e,t="Compiled",l,s,n="Written",r,a;return{c(){e=D("button"),e.textContent=t,l=x(),s=D("button"),s.textContent=n,this.h()},l(f){e=M(f,"BUTTON",{class:!0,"data-svelte-h":!0}),Oe(e)!=="svelte-1vzm9jy"&&(e.textContent=t),l=J(f),s=M(f,"BUTTON",{class:!0,"data-svelte-h":!0}),Oe(s)!=="svelte-qu81ez"&&(s.textContent=n),this.h()},h(){h(e,"class","off svelte-ska6l4"),h(s,"class","text-info bg-info/10 border border-info svelte-ska6l4")},m(f,o){E(f,e,o),E(f,l,o),E(f,s,o),r||(a=gt(e,"click",i[1]),r=!0)},p:ce,d(f){f&&(d(e),d(l),d(s)),r=!1,a()}}}function qs(i){let e,t="Compiled",l,s,n="Written",r,a;return{c(){e=D("button"),e.textContent=t,l=x(),s=D("button"),s.textContent=n,this.h()},l(f){e=M(f,"BUTTON",{class:!0,"data-svelte-h":!0}),Oe(e)!=="svelte-wrfleh"&&(e.textContent=t),l=J(f),s=M(f,"BUTTON",{class:!0,"data-svelte-h":!0}),Oe(s)!=="svelte-v36xno"&&(s.textContent=n),this.h()},h(){h(e,"class","text-info bg-info/10 border border-info svelte-ska6l4"),h(s,"class","off svelte-ska6l4")},m(f,o){E(f,e,o),E(f,l,o),E(f,s,o),r||(a=gt(s,"click",i[1]),r=!0)},p:ce,d(f){f&&(d(e),d(l),d(s)),r=!1,a()}}}function vs(i){let e,t,l;function s(a,f){return a[0]?qs:Ws}let n=s(i),r=n(i);return{c(){e=D("div"),r.c(),this.h()},l(a){e=M(a,"DIV",{class:!0});var f=Q(e);r.l(f),f.forEach(d),this.h()},h(){h(e,"class","toggle svelte-ska6l4")},m(a,f){E(a,e,f),r.m(e,null),l=!0},p(a,[f]){n===(n=s(a))&&r?r.p(a,f):(r.d(1),r=n(a),r&&(r.c(),r.m(e,null)))},i(a){l||(a&&xt(()=>{l&&(t||(t=qt(e,vt,{},!0)),t.run(1))}),l=!0)},o(a){a&&(t||(t=qt(e,vt,{},!1)),t.run(0)),l=!1},d(a){a&&d(e),r.d(),a&&t&&t.end()}}}function js(i,e,t){let{showCompiled:l}=e;const s=function(){t(0,l=!l)};return i.$$set=n=>{"showCompiled"in n&&t(0,l=n.showCompiled)},[l,s]}class Ys extends st{constructor(e){super(),rt(this,e,js,vs,nt,{showCompiled:0})}}function un(i){let e,t,l,s,n,r,a,f,o,u,c,y,T,g,m,L,w;s=new Pn({props:{toggled:i[10]}});let S=i[10]&&i[4]&&cn(i),A=i[10]&&mn(i);const B=[xs,Js,Zs,Ks],H=[];function V(z,W){return z[6]?0:z[8]?1:z[2].loading?2:3}c=V(i),y=H[c]=B[c](i);let K=i[8]>0&&!i[6]&&i[9]&&dn(i);return{c(){e=D("div"),t=D("div"),l=D("button"),ue(s.$$.fragment),n=x(),r=Le(i[0]),a=x(),S&&S.c(),f=x(),A&&A.c(),o=x(),u=D("button"),y.c(),T=x(),K&&K.c(),this.h()},l(z){e=M(z,"DIV",{class:!0});var W=Q(e);t=M(W,"DIV",{class:!0});var b=Q(t);l=M(b,"BUTTON",{type:!0,"aria-label":!0,class:!0});var X=Q(l);oe(s.$$.fragment,X),n=J(X),r=Te(X,i[0]),X.forEach(d),a=J(b),S&&S.l(b),f=J(b),A&&A.l(b),b.forEach(d),o=J(W),u=M(W,"BUTTON",{type:!0,"aria-label":!0,class:!0});var G=Q(u);y.l(G),G.forEach(d),T=J(W),K&&K.l(W),W.forEach(d),this.h()},h(){h(l,"type","button"),h(l,"aria-label","show-sql"),h(l,"class","title svelte-1ursthx"),h(t,"class","container-a svelte-1ursthx"),h(u,"type","button"),h(u,"aria-label","view-query"),h(u,"class",Wn("status-bar")+" svelte-1ursthx"),Ht(u,"error",i[6]),Ht(u,"success",!i[6]),Ht(u,"open",i[9]),Ht(u,"closed",!i[9]),h(e,"class","scrollbox my-3 svelte-1ursthx")},m(z,W){E(z,e,W),I(e,t),I(t,l),fe(s,l,null),I(l,n),I(l,r),I(t,a),S&&S.m(t,null),I(t,f),A&&A.m(t,null),I(e,o),I(e,u),H[c].m(u,null),I(e,T),K&&K.m(e,null),m=!0,L||(w=[gt(l,"click",i[15]),gt(u,"click",i[16])],L=!0)},p(z,W){const b={};W&1024&&(b.toggled=z[10]),s.$set(b),(!m||W&1)&&Ze(r,z[0]),z[10]&&z[4]?S?(S.p(z,W),W&1040&&O(S,1)):(S=cn(z),S.c(),O(S,1),S.m(t,f)):S&&(Je(),U(S,1,1,()=>{S=null}),xe()),z[10]?A?(A.p(z,W),W&1024&&O(A,1)):(A=mn(z),A.c(),O(A,1),A.m(t,null)):A&&(Je(),U(A,1,1,()=>{A=null}),xe());let X=c;c=V(z),c===X?H[c].p(z,W):(Je(),U(H[X],1,1,()=>{H[X]=null}),xe(),y=H[c],y?y.p(z,W):(y=H[c]=B[c](z),y.c()),O(y,1),y.m(u,null)),(!m||W&64)&&Ht(u,"error",z[6]),(!m||W&64)&&Ht(u,"success",!z[6]),(!m||W&512)&&Ht(u,"open",z[9]),(!m||W&512)&&Ht(u,"closed",!z[9]),z[8]>0&&!z[6]&&z[9]?K?(K.p(z,W),W&832&&O(K,1)):(K=dn(z),K.c(),O(K,1),K.m(e,null)):K&&(Je(),U(K,1,1,()=>{K=null}),xe())},i(z){m||(O(s.$$.fragment,z),O(S),O(A),O(y),O(K),z&&xt(()=>{m&&(g||(g=qt(e,vt,{},!0)),g.run(1))}),m=!0)},o(z){U(s.$$.fragment,z),U(S),U(A),U(y),U(K),z&&(g||(g=qt(e,vt,{},!1)),g.run(0)),m=!1},d(z){z&&d(e),ae(s),S&&S.d(),A&&A.d(),H[c].d(),K&&K.d(),z&&g&&g.end(),L=!1,Gl(w)}}}function cn(i){let e,t,l;function s(r){i[20](r)}let n={};return i[5]!==void 0&&(n.showCompiled=i[5]),e=new Ys({props:n}),qn.push(()=>Zn(e,"showCompiled",s)),{c(){ue(e.$$.fragment)},l(r){oe(e.$$.fragment,r)},m(r,a){fe(e,r,a),l=!0},p(r,a){const f={};!t&&a&32&&(t=!0,f.showCompiled=r[5],vn(()=>t=!1)),e.$set(f)},i(r){l||(O(e.$$.fragment,r),l=!0)},o(r){U(e.$$.fragment,r),l=!1},d(r){ae(e,r)}}}function mn(i){let e,t,l,s,n;const r=[Qs,Xs],a=[];function f(o,u){return o[5]?0:1}return t=f(i),l=a[t]=r[t](i),{c(){e=D("div"),l.c(),this.h()},l(o){e=M(o,"DIV",{class:!0});var u=Q(e);l.l(u),u.forEach(d),this.h()},h(){h(e,"class","code-container svelte-1ursthx")},m(o,u){E(o,e,u),a[t].m(e,null),n=!0},p(o,u){let c=t;t=f(o),t===c?a[t].p(o,u):(Je(),U(a[c],1,1,()=>{a[c]=null}),xe(),l=a[t],l?l.p(o,u):(l=a[t]=r[t](o),l.c()),O(l,1),l.m(e,null))},i(o){n||(O(l),o&&xt(()=>{n&&(s||(s=qt(e,vt,{},!0)),s.run(1))}),n=!0)},o(o){U(l),o&&(s||(s=qt(e,vt,{},!1)),s.run(0)),n=!1},d(o){o&&d(e),a[t].d(),o&&s&&s.end()}}}function Xs(i){let e,t;return e=new Rn({props:{code:i[3]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&8&&(n.code=l[3]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Qs(i){let e,t;return e=new Rn({props:{code:i[1].originalText}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&2&&(n.code=l[1].originalText),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Ks(i){let e;return{c(){e=Le("ran successfully but no data was returned")},l(t){e=Te(t,"ran successfully but no data was returned")},m(t,l){E(t,e,l)},p:ce,i:ce,o:ce,d(t){t&&d(e)}}}function Zs(i){let e;return{c(){e=Le("loading...")},l(t){e=Te(t,"loading...")},m(t,l){E(t,e,l)},p:ce,i:ce,o:ce,d(t){t&&d(e)}}}function Js(i){let e,t,l=i[8].toLocaleString()+"",s,n,r=i[8]>1?"records":"record",a,f,o=i[7].toLocaleString()+"",u,c,y=i[7]>1?"properties":"property",T,g;return e=new Pn({props:{toggled:i[9],color:i[12].colors.info}}),{c(){ue(e.$$.fragment),t=x(),s=Le(l),n=x(),a=Le(r),f=Le(" with "),u=Le(o),c=x(),T=Le(y)},l(m){oe(e.$$.fragment,m),t=J(m),s=Te(m,l),n=J(m),a=Te(m,r),f=Te(m," with "),u=Te(m,o),c=J(m),T=Te(m,y)},m(m,L){fe(e,m,L),E(m,t,L),E(m,s,L),E(m,n,L),E(m,a,L),E(m,f,L),E(m,u,L),E(m,c,L),E(m,T,L),g=!0},p(m,L){const w={};L&512&&(w.toggled=m[9]),L&4096&&(w.color=m[12].colors.info),e.$set(w),(!g||L&256)&&l!==(l=m[8].toLocaleString()+"")&&Ze(s,l),(!g||L&256)&&r!==(r=m[8]>1?"records":"record")&&Ze(a,r),(!g||L&128)&&o!==(o=m[7].toLocaleString()+"")&&Ze(u,o),(!g||L&128)&&y!==(y=m[7]>1?"properties":"property")&&Ze(T,y)},i(m){g||(O(e.$$.fragment,m),g=!0)},o(m){U(e.$$.fragment,m),g=!1},d(m){m&&(d(t),d(s),d(n),d(a),d(f),d(u),d(c),d(T)),ae(e,m)}}}function xs(i){let e=i[6].message+"",t;return{c(){t=Le(e)},l(l){t=Te(l,e)},m(l,s){E(l,t,s)},p(l,s){s&64&&e!==(e=l[6].message+"")&&Ze(t,e)},i:ce,o:ce,d(l){l&&d(t)}}}function dn(i){let e,t;return e=new Us({props:{data:i[1],queryID:i[0]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&2&&(n.data=l[1]),s&1&&(n.queryID=l[0]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function ps(i){let e,t,l,s=i[11]&&un(i);return{c(){e=D("div"),s&&s.c(),this.h()},l(n){e=M(n,"DIV",{class:!0});var r=Q(e);s&&s.l(r),r.forEach(d),this.h()},h(){h(e,"class","over-container svelte-1ursthx")},m(n,r){E(n,e,r),s&&s.m(e,null),l=!0},p(n,[r]){n[11]?s?(s.p(n,r),r&2048&&O(s,1)):(s=un(n),s.c(),O(s,1),s.m(e,null)):s&&(Je(),U(s,1,1,()=>{s=null}),xe())},i(n){l||(O(s),n&&(t||xt(()=>{t=Ai(e,Mn,{}),t.start()})),l=!0)},o(n){U(s),l=!1},d(n){n&&d(e),s&&s.d()}}}function $s(i,e,t){let l,s,n,r,a=ce,f=()=>(a(),a=St(m,b=>t(2,r=b)),m),o,u,c,y,T;Dt(i,Fn,b=>t(19,c=b)),Dt(i,xn,b=>t(11,y=b)),i.$$.on_destroy.push(()=>a());let{queryID:g}=e,{queryResult:m}=e;f();let L=Yi("showSQL_".concat(g),!1);Dt(i,L,b=>t(10,u=b));let w=Yi(`showResults_${g}`);Dt(i,w,b=>t(9,o=b));const S=function(){Ti(L,u=!u,u)},A=function(){!K&&r.length>0&&Ti(w,o=!o,o)};let B,H,V=!0,K;const{theme:z}=Bt();Dt(i,z,b=>t(12,T=b));function W(b){V=b,t(5,V)}return i.$$set=b=>{"queryID"in b&&t(0,g=b.queryID),"queryResult"in b&&f(t(1,m=b.queryResult))},i.$$.update=()=>{if(i.$$.dirty&524288&&t(18,l=c.data.evidencemeta.queries),i.$$.dirty&4&&(r?t(6,K=r.error):t(6,K=new Error("queryResult is undefined"))),i.$$.dirty&4&&t(8,s=(r==null?void 0:r.length)??0),i.$$.dirty&4&&t(7,n=r.columns.length??(r==null?void 0:r._evidenceColumnTypes.length)??0),i.$$.dirty&262145){let b=l==null?void 0:l.find(X=>X.id===g);b&&(t(3,B=b.inputQueryString),t(4,H=b.compiled&&b.compileError===void 0))}},[g,m,r,B,H,V,K,n,s,o,u,y,T,L,w,S,A,z,l,c,W]}class er extends st{constructor(e){super(),rt(this,e,$s,ps,nt,{queryID:0,queryResult:1})}}const Tl=Symbol.for("__evidence-chart-window-debug__"),tr=(i,e)=>{window[Tl]||(window[Tl]={}),window[Tl][i]=e},lr=i=>{window[Tl]||(window[Tl]={}),delete window[Tl][i]},hl=500,ir=(i,e)=>{var g;const t=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)&&i.clientWidth*3*i.clientHeight*3>16777215;Rl("light",Ii),Rl("dark",Dn);let l;const s=()=>{l=Oi(i,e.theme,{renderer:t?"svg":e.renderer??"canvas"})};s(),tr(l.id,l),e.connectGroup&&(l.group=e.connectGroup,pn(e.connectGroup));const n=()=>{if(e.seriesColors){const m=l.getOption();if(!m)return;const L={...m};for(const w of Object.keys(e.seriesColors)){const S=m.series.findIndex(A=>A.name===w);S!==-1&&(L.series[S]={...L.series[S],itemStyle:{...L.series[S].itemStyle,color:e.seriesColors[w]}})}l.setOption(L)}},r=()=>{e.echartsOptions&&l.setOption({...e.echartsOptions})},a=()=>{let m=[];if(e.seriesOptions){const L=e.config.series.reduce((w,{evidenceSeriesType:S},A)=>((S==="reference_line"||S==="reference_area"||S==="reference_point")&&w.push(A),w),[]);for(let w=0;w<e.config.series.length;w++)L.includes(w)?m.push({}):m.push({...e.seriesOptions});l.setOption({series:m})}};l.setOption({...e.config,animationDuration:hl,animationDurationUpdate:hl}),n(),r(),a();const f=e.dispatch;l.on("click",function(m){f("click",m)});const o=i.parentElement,u=$n(()=>{l.resize({animation:{duration:hl}}),y()},100);let c;window.ResizeObserver&&o?(c=new ResizeObserver(u),c.observe(o)):window.addEventListener("resize",u);const y=()=>{if(e.showAllXAxisLabels){const m=l.getOption();if(!m)return;const L=new Set(m.series.flatMap(A=>{var B;return(B=A.data)==null?void 0:B.map(H=>H[0])})),w=4/5,S=(i==null?void 0:i.clientWidth)??0;if(!e.swapXY){const A={xAxis:{axisLabel:{interval:0,overflow:e.xAxisLabelOverflow,width:S*w/L.size}}};l.setOption(A)}}},T=m=>{m.theme!==e.theme&&(l.dispose(),e=m,s()),e=m,l.setOption({...e.config,animationDuration:hl,animationDurationUpdate:hl},!0),n(),r(),a(),l.resize({animation:{duration:hl}}),y()};return u(),window[g=Symbol.for("chart renders")]??(window[g]=0),window[Symbol.for("chart renders")]++,{update(m){window[Symbol.for("chart renders")]++,T(m)},destroy(){c?c.unobserve(o):window.removeEventListener("resize",u),l.dispose(),lr(l.id)}}},nr=(i,e)=>{Rl("light",Ii),Rl("dark",Dn),console.log("echartsCanvasDownloadAction",e.theme);const t=Oi(i,e.theme,{renderer:"canvas"});e.config.animation=!1,t.setOption(e.config);const l=()=>{if(e.seriesColors){const o=t.getOption();if(!o)return;const u={...o};for(const c of Object.keys(e.seriesColors)){const y=o.series.findIndex(T=>T.name===c);y!==-1&&(u.series[y]={...u.series[y],itemStyle:{...u.series[y].itemStyle,color:e.seriesColors[c]}})}t.setOption(u)}},s=()=>{e.echartsOptions&&t.setOption({...e.echartsOptions})},n=()=>{let o=[];if(e.seriesOptions){const u=e.config.series.reduce((c,{evidenceSeriesType:y},T)=>((y==="reference_line"||y==="reference_area"||y==="reference_point")&&c.push(T),c),[]);for(let c=0;c<e.config.series.length;c++)u.includes(c)?o.push({}):o.push({...e.seriesOptions});t.setOption({series:o})}};s(),l(),n();let r=t.getConnectedDataURL({type:"png",pixelRatio:3,backgroundColor:e.backgroundColor,excludeComponents:["toolbox"]});const a=new Date,f=new Date(a.getTime()-a.getTimezoneOffset()*6e4).toISOString().slice(0,19).replaceAll(":","-");return es(r,(e.evidenceChartTitle??e.queryID??"evidence-chart")+`_${f}.png`),t.dispose(),{destroy(){t.dispose()}}},Bl=(i,e)=>{Rl("evidence-light",Ii);const{config:t,ratio:l,echartsOptions:s,seriesOptions:n,seriesColors:r,isMap:a,extraHeight:f,width:o}=e;let u={renderer:"canvas"};a&&(u.height=o*.5+f,i&&i.parentNode&&(i.style.height=u.height+"px",i.parentNode.style.height=u.height+"px"));const c=Oi(i,"evidence-light",u);t.animation=!1,c.setOption(t),s&&c.setOption(s);const y=()=>{if(r){const L=c.getOption();if(!L)return;const w={...L};for(const S of Object.keys(r)){const A=L.series.findIndex(B=>B.name===S);A!==-1&&(w.series[A]={...w.series[A],itemStyle:{...w.series[A].itemStyle,color:r[S]}})}c.setOption(w)}},T=()=>{s&&c.setOption({...s})},g=()=>{let L=[];if(n){const w=t.series.reduce((S,{evidenceSeriesType:A},B)=>((A==="reference_line"||A==="reference_area"||A==="reference_point")&&S.push(B),S),[]);for(let S=0;S<t.series.length;S++)w.includes(S)?L.push({}):L.push({...n});c.setOption({series:L})}};T(),y(),g();let m=c.getConnectedDataURL({type:"jpeg",pixelRatio:l,backgroundColor:"#fff",excludeComponents:["toolbox"]});i.innerHTML=`<img src=${m} width="100%" style="
        position: absolute; 
        top: 0;
        user-select: all;
        -webkit-user-select: all;
        -moz-user-select: all;
        -ms-user-select: all;
    " />`,e.config.animation=!0};function sr(i){let e;function t(n,r){return n[9]?fr:ar}let l=t(i),s=l(i);return{c(){s.c(),e=$e()},l(n){s.l(n),e=$e()},m(n,r){s.m(n,r),E(n,e,r)},p(n,r){l===(l=t(n))&&s?s.p(n,r):(s.d(1),s=l(n),s&&(s.c(),s.m(e.parentNode,e)))},d(n){n&&d(e),s.d(n)}}}function rr(i){let e,t,l,s;return{c(){e=D("div"),this.h()},l(n){e=M(n,"DIV",{class:!0,style:!0}),Q(e).forEach(d),this.h()},h(){h(e,"class","chart"),v(e,"height",i[1]),v(e,"width",i[2]),v(e,"margin-left","0"),v(e,"margin-top","15px"),v(e,"margin-bottom","10px"),v(e,"overflow","visible"),v(e,"break-inside","avoid")},m(n,r){E(n,e,r),l||(s=pt(t=Bl.call(null,e,{config:i[0],ratio:2,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13]})),l=!0)},p(n,r){r&2&&v(e,"height",n[1]),r&4&&v(e,"width",n[2]),t&&$t(t.update)&&r&8289&&t.update.call(null,{config:n[0],ratio:2,echartsOptions:n[5],seriesOptions:n[6],seriesColors:n[13]})},d(n){n&&d(e),l=!1,s()}}}function ar(i){let e,t,l,s,n,r,a;return{c(){e=D("div"),l=x(),s=D("div"),this.h()},l(f){e=M(f,"DIV",{class:!0,style:!0}),Q(e).forEach(d),l=J(f),s=M(f,"DIV",{class:!0,style:!0}),Q(s).forEach(d),this.h()},h(){h(e,"class","chart md:hidden"),v(e,"height",i[1]),v(e,"width","650px"),v(e,"margin-left","0"),v(e,"margin-top","15px"),v(e,"margin-bottom","10px"),v(e,"overflow","visible"),v(e,"break-inside","avoid"),h(s,"class","chart hidden md:block"),v(s,"height",i[1]),v(s,"width","841px"),v(s,"margin-left","0"),v(s,"margin-top","15px"),v(s,"margin-bottom","10px"),v(s,"overflow","visible"),v(s,"break-inside","avoid")},m(f,o){E(f,e,o),E(f,l,o),E(f,s,o),r||(a=[pt(t=Bl.call(null,e,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:650})),pt(n=Bl.call(null,s,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:841}))],r=!0)},p(f,o){o&2&&v(e,"height",f[1]),t&&$t(t.update)&&o&8673&&t.update.call(null,{config:f[0],ratio:4,echartsOptions:f[5],seriesOptions:f[6],seriesColors:f[13],isMap:f[7],extraHeight:f[8],width:650}),o&2&&v(s,"height",f[1]),n&&$t(n.update)&&o&8673&&n.update.call(null,{config:f[0],ratio:4,echartsOptions:f[5],seriesOptions:f[6],seriesColors:f[13],isMap:f[7],extraHeight:f[8],width:841})},d(f){f&&(d(e),d(l),d(s)),r=!1,Gl(a)}}}function fr(i){let e,t,l,s,n,r,a;return{c(){e=D("div"),l=x(),s=D("div"),this.h()},l(f){e=M(f,"DIV",{class:!0,style:!0}),Q(e).forEach(d),l=J(f),s=M(f,"DIV",{class:!0,style:!0}),Q(s).forEach(d),this.h()},h(){h(e,"class","chart md:hidden"),v(e,"height",i[1]),v(e,"width",i[11]+"px"),v(e,"margin-left","0"),v(e,"margin-top","15px"),v(e,"margin-bottom","10px"),v(e,"overflow","visible"),v(e,"break-inside","avoid"),h(s,"class","chart hidden md:block"),v(s,"height",i[1]),v(s,"width",i[10]+"px"),v(s,"margin-left","0"),v(s,"margin-top","15px"),v(s,"margin-bottom","10px"),v(s,"overflow","visible"),v(s,"break-inside","avoid")},m(f,o){E(f,e,o),E(f,l,o),E(f,s,o),r||(a=[pt(t=Bl.call(null,e,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:i[11]})),pt(n=Bl.call(null,s,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:i[10]}))],r=!0)},p(f,o){o&2&&v(e,"height",f[1]),o&2048&&v(e,"width",f[11]+"px"),t&&$t(t.update)&&o&10721&&t.update.call(null,{config:f[0],ratio:4,echartsOptions:f[5],seriesOptions:f[6],seriesColors:f[13],isMap:f[7],extraHeight:f[8],width:f[11]}),o&2&&v(s,"height",f[1]),o&1024&&v(s,"width",f[10]+"px"),n&&$t(n.update)&&o&9697&&n.update.call(null,{config:f[0],ratio:4,echartsOptions:f[5],seriesOptions:f[6],seriesColors:f[13],isMap:f[7],extraHeight:f[8],width:f[10]})},d(f){f&&(d(e),d(l),d(s)),r=!1,Gl(a)}}}function or(i){let e;function t(n,r){if(n[3])return rr;if(n[4])return sr}let l=t(i),s=l&&l(i);return{c(){s&&s.c(),e=$e()},l(n){s&&s.l(n),e=$e()},m(n,r){s&&s.m(n,r),E(n,e,r)},p(n,[r]){l===(l=t(n))&&s?s.p(n,r):(s&&s.d(1),s=l&&l(n),s&&(s.c(),s.m(e.parentNode,e)))},i:ce,o:ce,d(n){n&&d(e),s&&s.d(n)}}}function ur(i,e,t){let l,s,n,r,a,f,o=ce,u=()=>(o(),o=St(l,b=>t(13,f=b)),l);i.$$.on_destroy.push(()=>o());const{resolveColorsObject:c}=Bt();let{config:y=void 0}=e,{height:T="291px"}=e,{width:g="100%"}=e,{copying:m=!1}=e,{printing:L=!1}=e,{echartsOptions:w=void 0}=e,{seriesOptions:S=void 0}=e,{seriesColors:A=void 0}=e,{isMap:B=!1}=e,{extraHeight:H=void 0}=e,V=!1,K,z;const W=Pl("gridConfig");return W&&(V=!0,{cols:K,gapWidth:z}=W),i.$$set=b=>{"config"in b&&t(0,y=b.config),"height"in b&&t(1,T=b.height),"width"in b&&t(2,g=b.width),"copying"in b&&t(3,m=b.copying),"printing"in b&&t(4,L=b.printing),"echartsOptions"in b&&t(5,w=b.echartsOptions),"seriesOptions"in b&&t(6,S=b.seriesOptions),"seriesColors"in b&&t(14,A=b.seriesColors),"isMap"in b&&t(7,B=b.isMap),"extraHeight"in b&&t(8,H=b.extraHeight)},i.$$.update=()=>{i.$$.dirty&16384&&u(t(12,l=c(A))),i.$$.dirty&32768&&t(18,s=Math.min(Number(K),2)),i.$$.dirty&327680&&t(11,n=(650-Number(z)*(s-1))/s),i.$$.dirty&32768&&t(17,r=Math.min(Number(K),3)),i.$$.dirty&196608&&t(10,a=(841-Number(z)*(r-1))/r)},[y,T,g,m,L,w,S,B,H,V,a,n,l,f,A,K,z,r,s]}class cr extends st{constructor(e){super(),rt(this,e,ur,or,nt,{config:0,height:1,width:2,copying:3,printing:4,echartsOptions:5,seriesOptions:6,seriesColors:14,isMap:7,extraHeight:8})}}function mr(i){let e,t,l="Loading...",s,n,r;return{c(){e=D("div"),t=D("span"),t.textContent=l,s=x(),n=D("div"),this.h()},l(a){e=M(a,"DIV",{role:!0,class:!0});var f=Q(e);t=M(f,"SPAN",{class:!0,"data-svelte-h":!0}),Oe(t)!=="svelte-1wtojot"&&(t.textContent=l),s=J(f),n=M(f,"DIV",{class:!0,style:!0}),Q(n).forEach(d),f.forEach(d),this.h()},h(){h(t,"class","sr-only"),h(n,"class","bg-base-100 rounded-md max-w-[100%]"),v(n,"height",i[0]),v(n,"margin-top","15px"),v(n,"margin-bottom","31px"),h(e,"role","status"),h(e,"class","animate-pulse")},m(a,f){E(a,e,f),I(e,t),I(e,s),I(e,n)},p(a,[f]){f&1&&v(n,"height",a[0])},i(a){a&&(r||xt(()=>{r=Ai(e,ts,{}),r.start()}))},o:ce,d(a){a&&d(e)}}}function dr(i,e,t){let{height:l="231px"}=e;return i.$$set=s=>{"height"in s&&t(0,l=s.height)},[l]}class hr extends st{constructor(e){super(),rt(this,e,dr,mr,nt,{height:0})}}function hn(i){let e,t,l,s;const n=[gr,yr],r=[];function a(f,o){return 1}return e=a(),t=r[e]=n[e](i),{c(){t.c(),l=$e()},l(f){t.l(f),l=$e()},m(f,o){r[e].m(f,o),E(f,l,o),s=!0},p(f,o){t.p(f,o)},i(f){s||(O(t),s=!0)},o(f){U(t),s=!1},d(f){f&&d(l),r[e].d(f)}}}function yr(i){let e,t,l,s;return{c(){e=D("div"),this.h()},l(n){e=M(n,"DIV",{class:!0,style:!0}),Q(e).forEach(d),this.h()},h(){h(e,"class","chart svelte-db4qxn"),v(e,"height",i[3]),v(e,"width",i[4]),v(e,"overflow","visible"),v(e,"display",i[15]?"none":"inherit")},m(n,r){E(n,e,r),l||(s=pt(t=ir.call(null,e,{config:i[0],...i[25],echartsOptions:i[9],seriesOptions:i[10],dispatch:i[24],renderer:i[6],connectGroup:i[12],xAxisLabelOverflow:i[13],seriesColors:i[19],theme:i[20]})),l=!0)},p(n,r){r[0]&8&&v(e,"height",n[3]),r[0]&16&&v(e,"width",n[4]),r[0]&32768&&v(e,"display",n[15]?"none":"inherit"),t&&$t(t.update)&&r[0]&35141185&&t.update.call(null,{config:n[0],...n[25],echartsOptions:n[9],seriesOptions:n[10],dispatch:n[24],renderer:n[6],connectGroup:n[12],xAxisLabelOverflow:n[13],seriesColors:n[19],theme:n[20]})},i:ce,o:ce,d(n){n&&d(e),l=!1,s()}}}function gr(i){let e,t;return e=new hr({props:{height:i[3]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s[0]&8&&(n.height=l[3]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function yn(i){let e,t,l,s=i[8]&&gn(i),n=i[5]&&i[7]&&bn(i);return{c(){e=D("div"),s&&s.c(),t=x(),n&&n.c(),this.h()},l(r){e=M(r,"DIV",{class:!0});var a=Q(e);s&&s.l(a),t=J(a),n&&n.l(a),a.forEach(d),this.h()},h(){h(e,"class","chart-footer svelte-db4qxn")},m(r,a){E(r,e,a),s&&s.m(e,null),I(e,t),n&&n.m(e,null),l=!0},p(r,a){r[8]?s?(s.p(r,a),a[0]&256&&O(s,1)):(s=gn(r),s.c(),O(s,1),s.m(e,t)):s&&(Je(),U(s,1,1,()=>{s=null}),xe()),r[5]&&r[7]?n?(n.p(r,a),a[0]&160&&O(n,1)):(n=bn(r),n.c(),O(n,1),n.m(e,null)):n&&(Je(),U(n,1,1,()=>{n=null}),xe())},i(r){l||(O(s),O(n),l=!0)},o(r){U(s),U(n),l=!1},d(r){r&&d(e),s&&s.d(),n&&n.d()}}}function gn(i){let e,t;return e=new wi({props:{text:"Save Image",class:"download-button",downloadData:i[32],display:i[17],queryID:i[1],$$slots:{default:[br]},$$scope:{ctx:i}}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s[0]&16384&&(n.downloadData=l[32]),s[0]&131072&&(n.display=l[17]),s[0]&2&&(n.queryID=l[1]),s[1]&32&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function br(i){let e,t,l,s;return{c(){e=gl("svg"),t=gl("rect"),l=gl("circle"),s=gl("path"),this.h()},l(n){e=yl(n,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var r=Q(e);t=yl(r,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0}),Q(t).forEach(d),l=yl(r,"circle",{cx:!0,cy:!0,r:!0}),Q(l).forEach(d),s=yl(r,"path",{d:!0}),Q(s).forEach(d),r.forEach(d),this.h()},h(){h(t,"x","3"),h(t,"y","3"),h(t,"width","18"),h(t,"height","18"),h(t,"rx","2"),h(l,"cx","8.5"),h(l,"cy","8.5"),h(l,"r","1.5"),h(s,"d","M20.4 14.5L16 10 4 20"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"width","12"),h(e,"height","12"),h(e,"viewBox","0 0 24 24"),h(e,"fill","none"),h(e,"stroke","#000"),h(e,"stroke-width","2"),h(e,"stroke-linecap","round"),h(e,"stroke-linejoin","round")},m(n,r){E(n,e,r),I(e,t),I(e,l),I(e,s)},p:ce,d(n){n&&d(e)}}}function bn(i){let e,t;return e=new wi({props:{text:"Download Data",data:i[5],queryID:i[1],class:"download-button",display:i[17]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s[0]&32&&(n.data=l[5]),s[0]&2&&(n.queryID=l[1]),s[0]&131072&&(n.display=l[17]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function _n(i){let e,t;return e=new ls({props:{source:JSON.stringify(i[0],void 0,3),copyToClipboard:!0,$$slots:{default:[_r]},$$scope:{ctx:i}}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s[0]&1&&(n.source=JSON.stringify(l[0],void 0,3)),s[0]&1|s[1]&32&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function _r(i){let e=JSON.stringify(i[0],void 0,3)+"",t;return{c(){t=Le(e)},l(l){t=Te(l,e)},m(l,s){E(l,t,s)},p(l,s){s[0]&1&&e!==(e=JSON.stringify(l[0],void 0,3)+"")&&Ze(t,e)},d(l){l&&d(t)}}}function Cn(i){let e,t,l,s;return{c(){e=D("div"),this.h()},l(n){e=M(n,"DIV",{class:!0,style:!0}),Q(e).forEach(d),this.h()},h(){h(e,"class","chart svelte-db4qxn"),v(e,"display","none"),v(e,"visibility","visible"),v(e,"height",i[3]),v(e,"width","666px"),v(e,"margin-left","0"),v(e,"margin-top","15px"),v(e,"margin-bottom","15px"),v(e,"overflow","visible")},m(n,r){E(n,e,r),l||(s=pt(t=nr.call(null,e,{config:i[0],...i[25],echartsOptions:i[9],seriesOptions:i[10],seriesColors:i[19],queryID:i[1],evidenceChartTitle:i[2],theme:i[20],backgroundColor:i[21].colors["base-100"]})),l=!0)},p(n,r){r[0]&8&&v(e,"height",n[3]),t&&$t(t.update)&&r[0]&37225991&&t.update.call(null,{config:n[0],...n[25],echartsOptions:n[9],seriesOptions:n[10],seriesColors:n[19],queryID:n[1],evidenceChartTitle:n[2],theme:n[20],backgroundColor:n[21].colors["base-100"]})},d(n){n&&d(e),l=!1,s()}}}function Cr(i){let e,t,l,s,n,r,a,f,o,u,c=!i[16]&&hn(i);l=new cr({props:{config:i[0],height:i[3],width:i[4],copying:i[15],printing:i[16],echartsOptions:i[9],seriesOptions:i[10],seriesColors:i[18]}});let y=(i[7]||i[8])&&yn(i),T=i[11]&&!i[16]&&_n(i),g=i[14]&&Cn(i);return{c(){e=D("div"),c&&c.c(),t=x(),ue(l.$$.fragment),s=x(),y&&y.c(),n=x(),T&&T.c(),r=x(),g&&g.c(),a=$e(),this.h()},l(m){e=M(m,"DIV",{role:!0,class:!0});var L=Q(e);c&&c.l(L),t=J(L),oe(l.$$.fragment,L),s=J(L),y&&y.l(L),n=J(L),T&&T.l(L),L.forEach(d),r=J(m),g&&g.l(m),a=$e(),this.h()},h(){h(e,"role","none"),h(e,"class","chart-container mt-2 mb-3 svelte-db4qxn")},m(m,L){E(m,e,L),c&&c.m(e,null),I(e,t),fe(l,e,null),I(e,s),y&&y.m(e,null),I(e,n),T&&T.m(e,null),E(m,r,L),g&&g.m(m,L),E(m,a,L),f=!0,o||(u=[gt(window,"copy",i[27]),gt(window,"beforeprint",i[28]),gt(window,"afterprint",i[29]),gt(window,"export-beforeprint",i[30]),gt(window,"export-afterprint",i[31]),gt(e,"mouseenter",i[33]),gt(e,"mouseleave",i[34])],o=!0)},p(m,L){m[16]?c&&(Je(),U(c,1,1,()=>{c=null}),xe()):c?(c.p(m,L),L[0]&65536&&O(c,1)):(c=hn(m),c.c(),O(c,1),c.m(e,t));const w={};L[0]&1&&(w.config=m[0]),L[0]&8&&(w.height=m[3]),L[0]&16&&(w.width=m[4]),L[0]&32768&&(w.copying=m[15]),L[0]&65536&&(w.printing=m[16]),L[0]&512&&(w.echartsOptions=m[9]),L[0]&1024&&(w.seriesOptions=m[10]),L[0]&262144&&(w.seriesColors=m[18]),l.$set(w),m[7]||m[8]?y?(y.p(m,L),L[0]&384&&O(y,1)):(y=yn(m),y.c(),O(y,1),y.m(e,n)):y&&(Je(),U(y,1,1,()=>{y=null}),xe()),m[11]&&!m[16]?T?(T.p(m,L),L[0]&67584&&O(T,1)):(T=_n(m),T.c(),O(T,1),T.m(e,null)):T&&(Je(),U(T,1,1,()=>{T=null}),xe()),m[14]?g?g.p(m,L):(g=Cn(m),g.c(),g.m(a.parentNode,a)):g&&(g.d(1),g=null)},i(m){f||(O(c),O(l.$$.fragment,m),O(y),O(T),f=!0)},o(m){U(c),U(l.$$.fragment,m),U(y),U(T),f=!1},d(m){m&&(d(e),d(r),d(a)),c&&c.d(),ae(l),y&&y.d(),T&&T.d(),g&&g.d(m),o=!1,Gl(u)}}}function Tr(i,e,t){let l;const s=["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"];let n=vi(e,s),r,a=ce,f=()=>(a(),a=St(l,$=>t(19,r=$)),l),o,u;i.$$.on_destroy.push(()=>a());const{activeAppearance:c,theme:y,resolveColorsObject:T}=Bt();Dt(i,c,$=>t(20,o=$)),Dt(i,y,$=>t(21,u=$));let{config:g=void 0}=e,{queryID:m=void 0}=e,{evidenceChartTitle:L=void 0}=e,{height:w="291px"}=e,{width:S="100%"}=e,{data:A}=e,{renderer:B=void 0}=e,{downloadableData:H=void 0}=e,{downloadableImage:V=void 0}=e,{echartsOptions:K=void 0}=e,{seriesOptions:z=void 0}=e,{printEchartsConfig:W}=e,{seriesColors:b=void 0}=e,{connectGroup:X=void 0}=e,{xAxisLabelOverflow:G=void 0}=e;const q=jn();let Y=!1,N=!1,p=!1,ne=!1;const j=()=>{t(15,N=!0),Yn(),setTimeout(()=>{t(15,N=!1)},0)},ee=()=>t(16,p=!0),se=()=>t(16,p=!1),Z=()=>t(16,p=!0),te=()=>t(16,p=!1),me=()=>{t(14,Y=!0),setTimeout(()=>{t(14,Y=!1)},0)},he=()=>t(17,ne=!0),ge=()=>t(17,ne=!1);return i.$$set=$=>{e=Et(Et({},e),Gt($)),t(25,n=vi(e,s)),"config"in $&&t(0,g=$.config),"queryID"in $&&t(1,m=$.queryID),"evidenceChartTitle"in $&&t(2,L=$.evidenceChartTitle),"height"in $&&t(3,w=$.height),"width"in $&&t(4,S=$.width),"data"in $&&t(5,A=$.data),"renderer"in $&&t(6,B=$.renderer),"downloadableData"in $&&t(7,H=$.downloadableData),"downloadableImage"in $&&t(8,V=$.downloadableImage),"echartsOptions"in $&&t(9,K=$.echartsOptions),"seriesOptions"in $&&t(10,z=$.seriesOptions),"printEchartsConfig"in $&&t(11,W=$.printEchartsConfig),"seriesColors"in $&&t(26,b=$.seriesColors),"connectGroup"in $&&t(12,X=$.connectGroup),"xAxisLabelOverflow"in $&&t(13,G=$.xAxisLabelOverflow)},i.$$.update=()=>{i.$$.dirty[0]&67108864&&f(t(18,l=T(b)))},[g,m,L,w,S,A,B,H,V,K,z,W,X,G,Y,N,p,ne,l,r,o,u,c,y,q,n,b,j,ee,se,Z,te,me,he,ge]}class Lr extends st{constructor(e){super(),rt(this,e,Tr,Cr,nt,{config:0,queryID:1,evidenceChartTitle:2,height:3,width:4,data:5,renderer:6,downloadableData:7,downloadableImage:8,echartsOptions:9,seriesOptions:10,printEchartsConfig:11,seriesColors:26,connectGroup:12,xAxisLabelOverflow:13},null,[-1,-1])}}function zl(i,e){const t=new Set(i.map(l=>l[e]));return Array.from(t)}function kr(i,e){return Zt(i,is({count:ns(e)}))[0].count}function Sr(i,e,t){let l;if(typeof t!="object")l=Zt(i,ki(e,Zi({xTotal:Si(t)})),Ci({percentOfX:pi(t,"xTotal")}),Ki({percentOfX:t+"_pct"}));else{l=Zt(i,Ci({valueSum:0}));for(let s=0;s<l.length;s++){l[s].valueSum=0;for(let n=0;n<t.length;n++)l[s].valueSum=l[s].valueSum+l[s][t[n]]}l=Zt(l,ki(e,Zi({xTotal:Si("valueSum")})));for(let s=0;s<t.length;s++)l=Zt(l,Ci({percentOfX:pi(t[s],"xTotal")}),Ki({percentOfX:t[s]+"_pct"}))}return l}function Nl(i,e,t){return[...i].sort((l,s)=>(l[e]<s[e]?-1:1)*(t?1:-1))}function Pi(i,e,t){const l=e+t;return i%l<e?0:1}function Er(i){let e,t;return e=new Fi({props:{error:i[14],title:i[8]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s[0]&16384&&(n.error=l[14]),s[0]&256&&(n.title=l[8]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Ar(i){let e,t,l;const s=i[136].default,n=el(s,i,i[135],null);return t=new Lr({props:{config:i[20],height:i[15],width:i[13],data:i[0],queryID:i[6],evidenceChartTitle:i[7],showAllXAxisLabels:i[1],swapXY:i[3],echartsOptions:i[9],seriesOptions:i[10],printEchartsConfig:i[2],renderer:i[11],downloadableData:i[4],downloadableImage:i[5],connectGroup:i[12],xAxisLabelOverflow:i[23],seriesColors:i[16]}}),{c(){n&&n.c(),e=x(),ue(t.$$.fragment)},l(r){n&&n.l(r),e=J(r),oe(t.$$.fragment,r)},m(r,a){n&&n.m(r,a),E(r,e,a),fe(t,r,a),l=!0},p(r,a){n&&n.p&&(!l||a[4]&2048)&&tl(n,s,r,r[135],l?il(s,r[135],a,null):ll(r[135]),null);const f={};a[0]&1048576&&(f.config=r[20]),a[0]&32768&&(f.height=r[15]),a[0]&8192&&(f.width=r[13]),a[0]&1&&(f.data=r[0]),a[0]&64&&(f.queryID=r[6]),a[0]&128&&(f.evidenceChartTitle=r[7]),a[0]&2&&(f.showAllXAxisLabels=r[1]),a[0]&8&&(f.swapXY=r[3]),a[0]&512&&(f.echartsOptions=r[9]),a[0]&1024&&(f.seriesOptions=r[10]),a[0]&4&&(f.printEchartsConfig=r[2]),a[0]&2048&&(f.renderer=r[11]),a[0]&16&&(f.downloadableData=r[4]),a[0]&32&&(f.downloadableImage=r[5]),a[0]&4096&&(f.connectGroup=r[12]),a[0]&65536&&(f.seriesColors=r[16]),t.$set(f)},i(r){l||(O(n,r),O(t.$$.fragment,r),l=!0)},o(r){U(n,r),U(t.$$.fragment,r),l=!1},d(r){r&&d(e),n&&n.d(r),ae(t,r)}}}function wr(i){let e,t,l,s;const n=[Ar,Er],r=[];function a(f,o){return f[14]?1:0}return e=a(i),t=r[e]=n[e](i),{c(){t.c(),l=$e()},l(f){t.l(f),l=$e()},m(f,o){r[e].m(f,o),E(f,l,o),s=!0},p(f,o){let u=e;e=a(f),e===u?r[e].p(f,o):(Je(),U(r[u],1,1,()=>{r[u]=null}),xe(),t=r[e],t?t.p(f,o):(t=r[e]=n[e](f),t.c()),O(t,1),t.m(l.parentNode,l))},i(f){s||(O(t),s=!0)},o(f){U(t),s=!1},d(f){f&&d(l),r[e].d(f)}}}function Or(i,e,t){let l,s,n,r,a,f=ce,o=()=>(f(),f=St(n,k=>t(131,a=k)),n),u,c,y=ce,T=()=>(y(),y=St(s,k=>t(133,c=k)),s),g,m=ce,L=()=>(m(),m=St(l,k=>t(134,g=k)),l),w;i.$$.on_destroy.push(()=>f()),i.$$.on_destroy.push(()=>y()),i.$$.on_destroy.push(()=>m());let{$$slots:S={},$$scope:A}=e,B=Ei({}),H=Ei({});Dt(i,H,k=>t(20,w=k));const{theme:V,resolveColor:K,resolveColorsObject:z,resolveColorPalette:W}=Bt();Dt(i,V,k=>t(132,u=k));let{data:b=void 0}=e,{queryID:X=void 0}=e,{x:G=void 0}=e,{y:q=void 0}=e,{y2:Y=void 0}=e,{series:N=void 0}=e,{size:p=void 0}=e,{tooltipTitle:ne=void 0}=e,{showAllXAxisLabels:j=void 0}=e,{printEchartsConfig:ee=!1}=e,se=!!q,Z=!!G,{swapXY:te=!1}=e,{title:me=void 0}=e,{subtitle:he=void 0}=e,{chartType:ge="Chart"}=e,{bubble:$=!1}=e,{hist:re=!1}=e,{boxplot:Be=!1}=e,Me,{xType:de=void 0}=e,{xAxisTitle:Ne="false"}=e,{xBaseline:ze=!0}=e,{xTickMarks:Ce=!1}=e,{xGridlines:Fe=!1}=e,{xAxisLabels:we=!0}=e,{sort:Ee=!0}=e,{xFmt:Xe=void 0}=e,{xMin:We=void 0}=e,{xMax:ke=void 0}=e,{yLog:De=!1}=e,{yType:Qe=De===!0?"log":"value"}=e,{yLogBase:Ie=10}=e,{yAxisTitle:Pe="false"}=e,{yBaseline:qe=!1}=e,{yTickMarks:Se=!1}=e,{yGridlines:pe=!0}=e,{yAxisLabels:R=!0}=e,{yMin:ye=void 0}=e,{yMax:at=void 0}=e,{yScale:ve=!1}=e,{yFmt:je=void 0}=e,{yAxisColor:bt="true"}=e,{y2AxisTitle:et="false"}=e,{y2Baseline:Ge=!1}=e,{y2TickMarks:P=!1}=e,{y2Gridlines:be=!0}=e,{y2AxisLabels:Ke=!0}=e,{y2Min:tt=void 0}=e,{y2Max:_t=void 0}=e,{y2Scale:lt=!1}=e,{y2Fmt:mt=void 0}=e,{y2AxisColor:ft="true"}=e,{sizeFmt:At=void 0}=e,{colorPalette:Ct="default"}=e,{legend:Re=void 0}=e,{echartsOptions:Tt=void 0}=e,{seriesOptions:ut=void 0}=e,{seriesColors:Lt=void 0}=e,{stackType:yt=void 0}=e,{stacked100:dt=!1}=e,{chartAreaHeight:Ue}=e,{renderer:_=void 0}=e,{downloadableData:C=!0}=e,{downloadableImage:wt=!0}=e,{connectGroup:kl=void 0}=e,{leftPadding:Ut=void 0}=e,{rightPadding:Ot=void 0}=e,{xLabelWrap:jt=!1}=e;const Ri=jt?"break":"truncate";let _e,kt,He=[],F=[],ie,Nt,Ye,Ft,ht,it,ot,Pt,Yt,Sl,Hl,xl,Vl,nl,pl,$l,El,sl,ei,ti,li,ii,ni,si,ri,ai,fi,oi,ui,rl,Al,wl,Wl,ql,ci,mi,al,di,hi,vl,Bi,yi,Xt=[],fl=!0,zt=[],Ol=[],Rt,Il,gi,Qt;return i.$$set=k=>{"data"in k&&t(0,b=k.data),"queryID"in k&&t(6,X=k.queryID),"x"in k&&t(24,G=k.x),"y"in k&&t(25,q=k.y),"y2"in k&&t(49,Y=k.y2),"series"in k&&t(50,N=k.series),"size"in k&&t(51,p=k.size),"tooltipTitle"in k&&t(52,ne=k.tooltipTitle),"showAllXAxisLabels"in k&&t(1,j=k.showAllXAxisLabels),"printEchartsConfig"in k&&t(2,ee=k.printEchartsConfig),"swapXY"in k&&t(3,te=k.swapXY),"title"in k&&t(7,me=k.title),"subtitle"in k&&t(53,he=k.subtitle),"chartType"in k&&t(8,ge=k.chartType),"bubble"in k&&t(54,$=k.bubble),"hist"in k&&t(55,re=k.hist),"boxplot"in k&&t(56,Be=k.boxplot),"xType"in k&&t(26,de=k.xType),"xAxisTitle"in k&&t(27,Ne=k.xAxisTitle),"xBaseline"in k&&t(28,ze=k.xBaseline),"xTickMarks"in k&&t(29,Ce=k.xTickMarks),"xGridlines"in k&&t(30,Fe=k.xGridlines),"xAxisLabels"in k&&t(31,we=k.xAxisLabels),"sort"in k&&t(32,Ee=k.sort),"xFmt"in k&&t(57,Xe=k.xFmt),"xMin"in k&&t(58,We=k.xMin),"xMax"in k&&t(59,ke=k.xMax),"yLog"in k&&t(33,De=k.yLog),"yType"in k&&t(60,Qe=k.yType),"yLogBase"in k&&t(61,Ie=k.yLogBase),"yAxisTitle"in k&&t(34,Pe=k.yAxisTitle),"yBaseline"in k&&t(35,qe=k.yBaseline),"yTickMarks"in k&&t(36,Se=k.yTickMarks),"yGridlines"in k&&t(37,pe=k.yGridlines),"yAxisLabels"in k&&t(38,R=k.yAxisLabels),"yMin"in k&&t(62,ye=k.yMin),"yMax"in k&&t(63,at=k.yMax),"yScale"in k&&t(39,ve=k.yScale),"yFmt"in k&&t(64,je=k.yFmt),"yAxisColor"in k&&t(65,bt=k.yAxisColor),"y2AxisTitle"in k&&t(40,et=k.y2AxisTitle),"y2Baseline"in k&&t(41,Ge=k.y2Baseline),"y2TickMarks"in k&&t(42,P=k.y2TickMarks),"y2Gridlines"in k&&t(43,be=k.y2Gridlines),"y2AxisLabels"in k&&t(44,Ke=k.y2AxisLabels),"y2Min"in k&&t(66,tt=k.y2Min),"y2Max"in k&&t(67,_t=k.y2Max),"y2Scale"in k&&t(45,lt=k.y2Scale),"y2Fmt"in k&&t(68,mt=k.y2Fmt),"y2AxisColor"in k&&t(69,ft=k.y2AxisColor),"sizeFmt"in k&&t(70,At=k.sizeFmt),"colorPalette"in k&&t(71,Ct=k.colorPalette),"legend"in k&&t(46,Re=k.legend),"echartsOptions"in k&&t(9,Tt=k.echartsOptions),"seriesOptions"in k&&t(10,ut=k.seriesOptions),"seriesColors"in k&&t(72,Lt=k.seriesColors),"stackType"in k&&t(73,yt=k.stackType),"stacked100"in k&&t(74,dt=k.stacked100),"chartAreaHeight"in k&&t(47,Ue=k.chartAreaHeight),"renderer"in k&&t(11,_=k.renderer),"downloadableData"in k&&t(4,C=k.downloadableData),"downloadableImage"in k&&t(5,wt=k.downloadableImage),"connectGroup"in k&&t(12,kl=k.connectGroup),"leftPadding"in k&&t(75,Ut=k.leftPadding),"rightPadding"in k&&t(76,Ot=k.rightPadding),"xLabelWrap"in k&&t(48,jt=k.xLabelWrap),"$$scope"in k&&t(135,A=k.$$scope)},i.$$.update=()=>{var k,zi,Gi,Ui,Hi,Vi;if(i.$$.dirty[0]&4&&t(2,ee=Ve(ee)),i.$$.dirty[0]&8&&t(3,te=Ve(te)),i.$$.dirty[0]&268435456&&t(28,ze=Ve(ze)),i.$$.dirty[0]&536870912&&t(29,Ce=Ve(Ce)),i.$$.dirty[0]&1073741824&&t(30,Fe=Ve(Fe)),i.$$.dirty[1]&1&&t(31,we=Ve(we)),i.$$.dirty[1]&2&&t(32,Ee=Ve(Ee)),i.$$.dirty[1]&4&&t(33,De=Ve(De)),i.$$.dirty[1]&16&&t(35,qe=Ve(qe)),i.$$.dirty[1]&32&&t(36,Se=Ve(Se)),i.$$.dirty[1]&64&&t(37,pe=Ve(pe)),i.$$.dirty[1]&128&&t(38,R=Ve(R)),i.$$.dirty[1]&256&&t(39,ve=Ve(ve)),i.$$.dirty[2]&8&&L(t(19,l=K(bt))),i.$$.dirty[1]&1024&&t(41,Ge=Ve(Ge)),i.$$.dirty[1]&2048&&t(42,P=Ve(P)),i.$$.dirty[1]&4096&&t(43,be=Ve(be)),i.$$.dirty[1]&8192&&t(44,Ke=Ve(Ke)),i.$$.dirty[1]&16384&&t(45,lt=Ve(lt)),i.$$.dirty[2]&128&&T(t(18,s=K(ft))),i.$$.dirty[2]&512&&o(t(17,n=W(Ct))),i.$$.dirty[2]&1024&&t(16,r=z(Lt)),i.$$.dirty[0]&16&&t(4,C=Ve(C)),i.$$.dirty[0]&32&&t(5,wt=Ve(wt)),i.$$.dirty[1]&131072&&t(48,jt=Ve(jt)),i.$$.dirty[0]&2130731403|i.$$.dirty[1]&2147352575|i.$$.dirty[2]&2147481975|i.$$.dirty[3]&2147483647|i.$$.dirty[4]&2047)try{if(t(14,Il=void 0),t(124,Xt=[]),t(83,F=[]),t(126,zt=[]),t(127,Ol=[]),t(85,Nt=[]),t(77,se=!!q),t(78,Z=!!G),Cl(b),t(80,_e=_l(b)),t(81,kt=Object.keys(_e)),Z||t(24,G=kt[0]),!se){t(82,He=kt.filter(function(le){return![G,N,p].includes(le)}));for(let le=0;le<He.length;le++)t(85,Nt=He[le]),t(84,ie=_e[Nt].type),ie==="number"&&F.push(Nt);t(25,q=F.length>1?F:F[0])}$?t(79,Me={x:G,y:q,size:p}):re?t(79,Me={x:G}):Be?t(79,Me={}):t(79,Me={x:G,y:q});for(let le in Me)Me[le]==null&&Xt.push(le);if(Xt.length===1)throw Error(new Intl.ListFormat().format(Xt)+" is required");if(Xt.length>1)throw Error(new Intl.ListFormat().format(Xt)+" are required");if(dt===!0&&q.includes("_pct")&&fl===!1)if(typeof q=="object"){for(let le=0;le<q.length;le++)t(25,q[le]=q[le].replace("_pct",""),q);t(125,fl=!1)}else t(25,q=q.replace("_pct","")),t(125,fl=!1);if(G&&zt.push(G),q)if(typeof q=="object")for(t(128,Rt=0);Rt<q.length;t(128,Rt++,Rt))zt.push(q[Rt]);else zt.push(q);if(Y)if(typeof Y=="object")for(t(128,Rt=0);Rt<Y.length;t(128,Rt++,Rt))zt.push(Y[Rt]);else zt.push(Y);if(p&&zt.push(p),N&&Ol.push(N),ne&&Ol.push(ne),Cl(b,zt,Ol),dt===!0){if(t(0,b=Sr(b,G,q)),typeof q=="object"){for(let le=0;le<q.length;le++)t(25,q[le]=q[le]+"_pct",q);t(125,fl=!1)}else t(25,q=q+"_pct"),t(125,fl=!1);t(80,_e=_l(b))}switch(t(86,Ye=_e[G].type),Ye){case"number":t(86,Ye="value");break;case"string":t(86,Ye="category");break;case"date":t(86,Ye="time");break;default:break}if(t(26,de=de==="category"?"category":Ye),j?t(1,j=j==="true"||j===!0):t(1,j=de==="category"),te&&de!=="category")throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(te&&Y)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(te&&t(26,de="category"),t(87,Ft=Ye==="value"&&de==="category"),t(0,b=Ee?Ye==="category"?Nl(b,q,!1):Nl(b,G,!0):b),Ye==="time"&&t(0,b=Nl(b,G,!0)),t(129,gi=_l(b,"array")),t(130,Qt=gi.filter(le=>le.type==="date")),t(130,Qt=Qt.map(le=>le.id)),Qt.length>0)for(let le=0;le<Qt.length;le++)t(0,b=ss(b,Qt[le]));Xe?t(88,ht=It(Xe,(k=_e[G].format)==null?void 0:k.valueType)):t(88,ht=_e[G].format),q?je?typeof q=="object"?t(89,it=It(je,(zi=_e[q[0]].format)==null?void 0:zi.valueType)):t(89,it=It(je,(Gi=_e[q].format)==null?void 0:Gi.valueType)):typeof q=="object"?t(89,it=_e[q[0]].format):t(89,it=_e[q].format):t(89,it="str"),Y&&(mt?typeof Y=="object"?t(90,ot=It(mt,(Ui=_e[Y[0]].format)==null?void 0:Ui.valueType)):t(90,ot=It(mt,(Hi=_e[Y].format)==null?void 0:Hi.valueType)):typeof Y=="object"?t(90,ot=_e[Y[0]].format):t(90,ot=_e[Y].format)),p&&(At?t(91,Pt=It(At,(Vi=_e[p].format)==null?void 0:Vi.valueType)):t(91,Pt=_e[p].format)),t(92,Yt=_e[G].columnUnitSummary),q&&(typeof q=="object"?t(93,Sl=_e[q[0]].columnUnitSummary):t(93,Sl=_e[q].columnUnitSummary)),Y&&(typeof Y=="object"?t(94,Hl=_e[Y[0]].columnUnitSummary):t(94,Hl=_e[Y].columnUnitSummary)),t(27,Ne=Ne==="true"?Wt(G,ht):Ne==="false"?"":Ne),t(34,Pe=Pe==="true"?typeof q=="object"?"":Wt(q,it):Pe==="false"?"":Pe),t(40,et=et==="true"?typeof Y=="object"?"":Wt(Y,ot):et==="false"?"":et);let ol=typeof q=="object"?q.length:1,Wi=N?kr(b,N):1,Ml=ol*Wi,bi=typeof Y=="object"?Y.length:Y?1:0,_i=Ml+bi;if(Re!==void 0&&t(46,Re=Re==="true"||Re===!0),t(46,Re=Re??_i>1),dt===!0&&De===!0)throw Error("Log axis cannot be used in a 100% stacked chart");if(yt==="stacked"&&_i>1&&De===!0)throw Error("Log axis cannot be used in a stacked chart");let ul;if(typeof q=="object"){ul=_e[q[0]].columnUnitSummary.min;for(let le=0;le<q.length;le++)_e[q[le]].columnUnitSummary.min<ul&&(ul=_e[q[le]].columnUnitSummary.min)}else q&&(ul=_e[q].columnUnitSummary.min);if(De===!0&&ul<=0&&ul!==null)throw Error("Log axis cannot display values less than or equal to zero");B.update(le=>({...le,data:b,x:G,y:q,y2:Y,series:N,swapXY:te,sort:Ee,xType:de,xFormat:ht,yFormat:it,y2Format:ot,sizeFormat:Pt,xMismatch:Ft,size:p,yMin:ye,y2Min:tt,columnSummary:_e,xAxisTitle:Ne,yAxisTitle:Pe,y2AxisTitle:et,tooltipTitle:ne,chartAreaHeight:Ue,chartType:ge,yCount:ol,y2Count:bi})),t(95,xl=zl(b,G));let qi;if(te?t(96,Vl={type:Qe,logBase:Ie,position:"top",axisLabel:{show:R,hideOverlap:!0,showMaxLabel:!0,formatter(le){return Yl(le,it,Sl)},margin:4},min:ye,max:at,scale:ve,splitLine:{show:pe},axisLine:{show:qe,onZero:!1},axisTick:{show:Se},boundaryGap:!1,z:2}):t(96,Vl={type:de,min:We,max:ke,tooltip:{show:!0,position:"inside",formatter(le){if(le.isTruncated())return le.name}},splitLine:{show:Fe},axisLine:{show:ze},axisTick:{show:Ce},axisLabel:{show:we,hideOverlap:!0,showMaxLabel:de==="category"||de==="value",formatter:de==="time"||de==="category"?!1:function(le){return Yl(le,ht,Yt)},margin:6},scale:!0,z:2}),te?t(97,nl={type:de,inverse:"true",splitLine:{show:Fe},axisLine:{show:ze},axisTick:{show:Ce},axisLabel:{show:we,hideOverlap:!0},scale:!0,min:We,max:ke,z:2}):(t(97,nl={type:Qe,logBase:Ie,splitLine:{show:pe},axisLine:{show:qe,onZero:!1},axisTick:{show:Se},axisLabel:{show:R,hideOverlap:!0,margin:4,formatter(le){return Yl(le,it,Sl)},color:Y?g==="true"?a[0]:g!=="false"?g:void 0:void 0},name:Pe,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:Y?g==="true"?a[0]:g!=="false"?g:void 0:void 0},nameGap:6,min:ye,max:at,scale:ve,boundaryGap:["0%","1%"],z:2}),qi={type:"value",show:!1,alignTicks:!0,splitLine:{show:be},axisLine:{show:Ge,onZero:!1},axisTick:{show:P},axisLabel:{show:Ke,hideOverlap:!0,margin:4,formatter(le){return Yl(le,ot,Hl)},color:c==="true"?a[Ml]:c!=="false"?c:void 0},name:et,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:c==="true"?a[Ml]:c!=="false"?c:void 0},nameGap:6,min:tt,max:_t,scale:lt,boundaryGap:["0%","1%"],z:2},t(97,nl=[nl,qi])),Ue){if(t(47,Ue=Number(Ue)),isNaN(Ue))throw Error("chartAreaHeight must be a number");if(Ue<=0)throw Error("chartAreaHeight must be a positive number")}else t(47,Ue=180);t(100,El=!!me),t(101,sl=!!he),t(102,ei=Re*(N!==null||typeof q=="object"&&q.length>1)),t(103,ti=Pe!==""&&te),t(104,li=Ne!==""&&!te),t(105,ii=15),t(106,ni=13),t(107,si=6*sl),t(108,ri=El*ii+sl*ni+si*Math.max(El,sl)),t(109,ai=10),t(110,fi=10),t(111,oi=14),t(112,ui=14),t(113,rl=15),t(113,rl=rl*ei),t(114,Al=7),t(114,Al=Al*Math.max(El,sl)),t(115,wl=ri+Al),t(116,Wl=wl+rl+ui*ti+ai),t(117,ql=li*oi+fi),t(121,di=8),t(123,vl=1),te&&(t(122,hi=xl.length),t(123,vl=Math.max(1,hi/di))),t(118,ci=Ue*vl+Wl+ql),t(119,mi=wl+rl+7),t(15,Bi=ci+"px"),t(13,yi="100%"),t(120,al=te?Pe:Ne),al!==""&&t(120,al=al+" →"),t(98,pl={id:"horiz-axis-title",type:"text",style:{text:al,textAlign:"right",fill:u.colors["base-content-muted"]},cursor:"auto",right:te?"2%":"3%",top:te?mi:null,bottom:te?null:"2%"}),t(99,$l={title:{text:me,subtext:he,subtextStyle:{width:yi}},tooltip:{trigger:"axis",show:!0,formatter(le){let cl,ml,dl,jl;if(_i>1){ml=le[0].value[te?1:0],cl=`<span id="tooltip" style='font-weight: 600;'>${ct(ml,ht)}</span>`;for(let Kt=le.length-1;Kt>=0;Kt--)le[Kt].seriesName!=="stackTotal"&&(dl=le[Kt].value[te?0:1],cl=cl+`<br> <span style='font-size: 11px;'>${le[Kt].marker} ${le[Kt].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${ct(dl,Pi(le[Kt].componentIndex,ol,bi)===0?it:ot)}</span>`)}else de==="value"?(ml=le[0].value[te?1:0],dl=le[0].value[te?0:1],jl=le[0].seriesName,cl=`<span id="tooltip" style='font-weight: 600;'>${Wt(G,ht)}: </span><span style='float:right; margin-left: 10px;'>${ct(ml,ht)}</span><br/><span style='font-weight: 600;'>${Wt(jl,it)}: </span><span style='float:right; margin-left: 10px;'>${ct(dl,it)}</span>`):(ml=le[0].value[te?1:0],dl=le[0].value[te?0:1],jl=le[0].seriesName,cl=`<span id="tooltip" style='font-weight: 600;'>${ct(ml,ht)}</span><br/><span>${Wt(jl,it)}: </span><span style='float:right; margin-left: 10px;'>${ct(dl,it)}</span>`);return cl},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:Re,type:"scroll",top:wl,padding:[0,0,0,0],data:[]},grid:{left:Ut??(te?"1%":"0.8%"),right:Ot??(te?"4%":"3%"),bottom:ql,top:Wl,containLabel:!0},xAxis:Vl,yAxis:nl,series:[],animation:!0,graphic:pl,color:a}),H.update(()=>$l)}catch(ol){if(t(14,Il=ol.message),console.error("\x1B[31m%s\x1B[0m",`Error in ${ge}: ${ol.message}`),Mi)throw Il;B.update(Ml=>({...Ml,error:Il}))}i.$$.dirty[0]&1},Li(Di,B),Li(Ni,H),[b,j,ee,te,C,wt,X,me,ge,Tt,ut,_,kl,yi,Il,Bi,r,n,s,l,w,H,V,Ri,G,q,de,Ne,ze,Ce,Fe,we,Ee,De,Pe,qe,Se,pe,R,ve,et,Ge,P,be,Ke,lt,Re,Ue,jt,Y,N,p,ne,he,$,re,Be,Xe,We,ke,Qe,Ie,ye,at,je,bt,tt,_t,mt,ft,At,Ct,Lt,yt,dt,Ut,Ot,se,Z,Me,_e,kt,He,F,ie,Nt,Ye,Ft,ht,it,ot,Pt,Yt,Sl,Hl,xl,Vl,nl,pl,$l,El,sl,ei,ti,li,ii,ni,si,ri,ai,fi,oi,ui,rl,Al,wl,Wl,ql,ci,mi,al,di,hi,vl,Xt,fl,zt,Ol,Rt,gi,Qt,a,u,c,g,A,S]}class Ir extends st{constructor(e){super(),rt(this,e,Or,wr,nt,{data:0,queryID:6,x:24,y:25,y2:49,series:50,size:51,tooltipTitle:52,showAllXAxisLabels:1,printEchartsConfig:2,swapXY:3,title:7,subtitle:53,chartType:8,bubble:54,hist:55,boxplot:56,xType:26,xAxisTitle:27,xBaseline:28,xTickMarks:29,xGridlines:30,xAxisLabels:31,sort:32,xFmt:57,xMin:58,xMax:59,yLog:33,yType:60,yLogBase:61,yAxisTitle:34,yBaseline:35,yTickMarks:36,yGridlines:37,yAxisLabels:38,yMin:62,yMax:63,yScale:39,yFmt:64,yAxisColor:65,y2AxisTitle:40,y2Baseline:41,y2TickMarks:42,y2Gridlines:43,y2AxisLabels:44,y2Min:66,y2Max:67,y2Scale:45,y2Fmt:68,y2AxisColor:69,sizeFmt:70,colorPalette:71,legend:46,echartsOptions:9,seriesOptions:10,seriesColors:72,stackType:73,stacked100:74,chartAreaHeight:47,renderer:11,downloadableData:4,downloadableImage:5,connectGroup:12,leftPadding:75,rightPadding:76,xLabelWrap:48},null,[-1,-1,-1,-1,-1])}}function Mr(i){let e;const t=i[7].default,l=el(t,i,i[8],null);return{c(){l&&l.c()},l(s){l&&l.l(s)},m(s,n){l&&l.m(s,n),e=!0},p(s,n){l&&l.p&&(!e||n&256)&&tl(l,t,s,s[8],e?il(t,s[8],n,null):ll(s[8]),null)},i(s){e||(O(l,s),e=!0)},o(s){U(l,s),e=!1},d(s){l&&l.d(s)}}}function Dr(i){let e,t;const l=[i[5],{data:Mt.isQuery(i[11])?Array.from(i[11]):i[11]},{queryID:i[6]}];let s={$$slots:{default:[Mr]},$$scope:{ctx:i}};for(let n=0;n<l.length;n+=1)s=Et(s,l[n]);return e=new Ir({props:s}),{c(){ue(e.$$.fragment)},l(n){oe(e.$$.fragment,n)},m(n,r){fe(e,n,r),t=!0},p(n,r){const a=r&2144?Kl(l,[r&32&&Zl(n[5]),r&2048&&{data:Mt.isQuery(n[11])?Array.from(n[11]):n[11]},r&64&&{queryID:n[6]}]):{};r&256&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(O(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){ae(e,n)}}}function Nr(i){let e,t;return e=new Jl({props:{slot:"empty",emptyMessage:i[2],emptySet:i[1],chartType:i[5].chartType,isInitial:i[4]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&4&&(n.emptyMessage=l[2]),s&2&&(n.emptySet=l[1]),s&32&&(n.chartType=l[5].chartType),s&16&&(n.isInitial=l[4]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Fr(i){let e,t;return e=new Fi({props:{slot:"error",title:i[5].chartType,error:i[11].error.message}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&32&&(n.title=l[5].chartType),s&2048&&(n.error=l[11].error.message),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Pr(i){let e,t;return e=new Ql({props:{data:i[0],height:i[3],$$slots:{error:[Fr,({loaded:l})=>({11:l}),({loaded:l})=>l?2048:0],empty:[Nr],default:[Dr,({loaded:l})=>({11:l}),({loaded:l})=>l?2048:0]},$$scope:{ctx:i}}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,[s]){const n={};s&1&&(n.data=l[0]),s&8&&(n.height=l[3]),s&2358&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Rr(i,e,t){let l,{$$slots:s={},$$scope:n}=e,{data:r}=e;const a=Mt.isQuery(r)?r.hash:void 0;let f=(r==null?void 0:r.hash)===a,{emptySet:o=void 0}=e,{emptyMessage:u=void 0}=e,{height:c=200}=e,y=r==null?void 0:r.id;return i.$$set=T=>{t(10,e=Et(Et({},e),Gt(T))),"data"in T&&t(0,r=T.data),"emptySet"in T&&t(1,o=T.emptySet),"emptyMessage"in T&&t(2,u=T.emptyMessage),"height"in T&&t(3,c=T.height),"$$scope"in T&&t(8,n=T.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&t(4,f=(r==null?void 0:r.hash)===a),t(5,l={...Object.fromEntries(Object.entries(e).filter(([,T])=>T!==void 0))})},e=Gt(e),[r,o,u,c,f,l,y,s,n]}class Bn extends st{constructor(e){super(),rt(this,e,Rr,Pr,nt,{data:0,emptySet:1,emptyMessage:2,height:3})}}function zn(i,e,t,l,s,n,r,a,f,o,u=void 0,c=void 0,y=void 0,T=void 0){function g(b,X,G,q){let Y={name:X,data:b,yAxisIndex:G};return Y={...q,...Y},Y}let m,L,w,S=[],A,B,H,V,K;function z(b,X){const G=[];function q(N){return typeof N>"u"}function Y(N,p){q(N)||(Array.isArray(N)?N.forEach(ne=>G.push([ne,p])):G.push([N,p]))}return Y(b,0),Y(X,1),G}let W=z(t,y);if(l!=null&&W.length===1)for(V=zl(i,l),m=0;m<V.length;m++){if(B=i.filter(b=>b[l]===V[m]),s?A=B.map(b=>[b[W[0][0]],a?b[e].toString():b[e]]):A=B.map(b=>[a?b[e].toString():b[e],b[W[0][0]]]),u){let b=B.map(X=>X[u]);A.forEach((X,G)=>X.push(b[G]))}if(c){let b=B.map(X=>X[c]);A.forEach((X,G)=>X.push(b[G]))}H=V[m]??"null",K=W[0][1],w=g(A,H,K,n),S.push(w)}if(l!=null&&W.length>1)for(V=zl(i,l),m=0;m<V.length;m++)for(B=i.filter(b=>b[l]===V[m]),L=0;L<W.length;L++){if(s?A=B.map(b=>[b[W[L][0]],a?b[e].toString():b[e]]):A=B.map(b=>[a?b[e].toString():b[e],b[W[L][0]]]),u){let b=B.map(X=>X[u]);A.forEach((X,G)=>X.push(b[G]))}if(c){let b=B.map(X=>X[c]);A.forEach((X,G)=>X.push(b[G]))}H=(V[m]??"null")+" - "+f[W[L][0]].title,K=W[L][1],w=g(A,H,K,n),S.push(w)}if(l==null&&W.length>1)for(m=0;m<W.length;m++){if(s?A=i.map(b=>[b[W[m][0]],a?b[e].toString():b[e]]):A=i.map(b=>[a?b[e].toString():b[e],b[W[m][0]]]),u){let b=i.map(X=>X[u]);A.forEach((X,G)=>X.push(b[G]))}if(c){let b=i.map(X=>X[c]);A.forEach((X,G)=>X.push(b[G]))}H=f[W[m][0]].title,K=W[m][1],w=g(A,H,K,n),S.push(w)}if(l==null&&W.length===1){if(s?A=i.map(b=>[b[W[0][0]],a?b[e].toString():b[e]]):A=i.map(b=>[a?b[e].toString():b[e],b[W[0][0]]]),u){let b=i.map(X=>X[u]);A.forEach((X,G)=>X.push(b[G]))}if(c){let b=i.map(X=>X[c]);A.forEach((X,G)=>X.push(b[G]))}H=f[W[0][0]].title,K=W[0][1],w=g(A,H,K,n),S.push(w)}return o&&S.sort((b,X)=>o.indexOf(b.name)-o.indexOf(X.name)),T&&S.forEach(b=>{b.name=rs(b.name,T)}),S}function Br(i){let e=[];for(let t=1;t<i.length;t++)e.push(i[t]-i[t-1]);return e}function Gn(i,e){return(typeof i!="number"||isNaN(i))&&(i=0),(typeof e!="number"||isNaN(e))&&(e=0),i=Math.abs(i),e=Math.abs(e),e<=.01?i:Gn(e,i%e)}function zr(i,e){if(!Array.isArray(i))throw new TypeError("Cannot calculate extent of non-array value.");let t,l;for(const s of i)typeof s=="number"&&(t===void 0?s>=s&&(t=l=s):(t>s&&(t=s),l<s&&(l=s)));return[t,l]}function Gr(i,e){let[t,l]=zr(i);const s=[];let n=t;for(;n<=l;)s.push(Math.round((n+Number.EPSILON)*1e8)/1e8),n+=e;return s}function Ur(i){if(i.length<=1)return;i.sort(function(t,l){return t-l}),i=i.map(function(t){return t*1e8}),i=Br(i);let e=i.reduce((t,l)=>Gn(t,l))/1e8;return e=Math.round((e+Number.EPSILON)*1e8)/1e8,e}function Fl(i,e,t,l,s=!1,n=!1){var T;let r=!1;const a=i.map(g=>Object.assign({},g,{[e]:g[e]instanceof Date?(r=!0,g[e].toISOString()):g[e]})).filter(g=>g[e]!==void 0&&g[e]!==null),f=Array.from(a).reduce((g,m)=>(m[e]instanceof Date&&(m[e]=m[e].toISOString(),r=!0),l?(g[m[l]??"null"]||(g[m[l]??"null"]=[]),g[m[l]??"null"].push(m)):(g.default||(g.default=[]),g.default.push(m)),g),{}),o={};let u;const c=((T=a.find(g=>g&&g[e]!==null&&g[e]!==void 0))==null?void 0:T[e])??null;switch(typeof c){case"object":throw c===null?new Error(`Column '${e}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(u=zl(a,e),n){const g=Ur(u);o[e]=Gr(u,g)}break;case"string":u=zl(a,e),o[e]=u;break}const y=[];for(const g of Object.values(f)){const m=l?{[l]:null}:{};if(s)if(t instanceof Array)for(let w=0;w<t.length;w++)m[t[w]]=0;else m[t]=0;else if(t instanceof Array)for(let w=0;w<t.length;w++)m[t[w]]=null;else m[t]=null;l&&(o[l]=l);const L=[];Object.keys(o).length===0?L.push(Ji([e],m)):L.push(Ji(o,m)),y.push(Zt(g,...L))}return r?y.flat().map(g=>({...g,[e]:new Date(g[e])})):y.flat()}function Tn(i,e,t){let l=Zt(i,ki(e,[as(t,Si)]));if(typeof t=="object")for(let s=0;s<l.length;s++){l[s].stackTotal=0;for(let n=0;n<t.length;n++)l[s].stackTotal=l[s].stackTotal+l[s][t[n]]}return l}let Hr=60;function Vr(i,e,t){let l,s,n,r,a,f,o,u,c,y,T,g,m,L,w,S,A,B,H,V,K=ce,z=()=>(K(),K=St(r,P=>t(49,V=P)),r),W,b=ce,X=()=>(b(),b=St(n,P=>t(50,W=P)),n),G,q=ce,Y=()=>(q(),q=St(a,P=>t(51,G=P)),a),N,p=ce,ne=()=>(p(),p=St(l,P=>t(52,N=P)),l);i.$$.on_destroy.push(()=>K()),i.$$.on_destroy.push(()=>b()),i.$$.on_destroy.push(()=>q()),i.$$.on_destroy.push(()=>p());const{resolveColor:j}=Bt();let{y:ee=void 0}=e;const se=!!ee;let{y2:Z=void 0}=e;const te=!!Z;let{series:me=void 0}=e;const he=!!me;let{options:ge=void 0}=e,{name:$=void 0}=e,{type:re="stacked"}=e,{stackName:Be=void 0}=e,{fillColor:Me=void 0}=e,{fillOpacity:de=void 0}=e,{outlineColor:Ne=void 0}=e,{outlineWidth:ze=void 0}=e,{labels:Ce=!1}=e,{seriesLabels:Fe=!0}=e,{labelSize:we=11}=e,{labelPosition:Ee=void 0}=e,{labelColor:Xe=void 0}=e,{labelFmt:We=void 0}=e,ke;We&&(ke=It(We));let{yLabelFmt:De=void 0}=e,Qe;De&&(Qe=It(De));let{y2LabelFmt:Ie=void 0}=e,Pe;Ie&&(Pe=It(Ie));let{y2SeriesType:qe="bar"}=e,{stackTotalLabel:Se=!0}=e,{showAllLabels:pe=!1}=e,{seriesOrder:R=void 0}=e,ye,at,ve,je;const bt={outside:"top",inside:"inside"},et={outside:"right",inside:"inside"};let{seriesLabelFmt:Ge=void 0}=e;return In(()=>{ge&&s.update(P=>({...P,...ge})),H&&s.update(P=>{if(re.includes("stacked")?P.tooltip={...P.tooltip,order:"seriesDesc"}:P.tooltip={...P.tooltip,order:"seriesAsc"},re==="stacked100"&&(g?P.xAxis={...P.xAxis,max:1}:P.yAxis[0]={...P.yAxis[0],max:1}),g)P.yAxis={...P.yAxis,...H.xAxis},P.xAxis={...P.xAxis,...H.yAxis};else if(P.yAxis[0]={...P.yAxis[0],...H.yAxis},P.xAxis={...P.xAxis,...H.xAxis},Z&&(P.yAxis[1]={...P.yAxis[1],show:!0},["line","bar","scatter"].includes(qe)))for(let be=0;be<T;be++)P.series[y+be].type=qe,P.series[y+be].stack=void 0;return P})}),i.$$set=P=>{"y"in P&&t(4,ee=P.y),"y2"in P&&t(5,Z=P.y2),"series"in P&&t(6,me=P.series),"options"in P&&t(13,ge=P.options),"name"in P&&t(7,$=P.name),"type"in P&&t(14,re=P.type),"stackName"in P&&t(8,Be=P.stackName),"fillColor"in P&&t(15,Me=P.fillColor),"fillOpacity"in P&&t(16,de=P.fillOpacity),"outlineColor"in P&&t(17,Ne=P.outlineColor),"outlineWidth"in P&&t(18,ze=P.outlineWidth),"labels"in P&&t(9,Ce=P.labels),"seriesLabels"in P&&t(10,Fe=P.seriesLabels),"labelSize"in P&&t(19,we=P.labelSize),"labelPosition"in P&&t(11,Ee=P.labelPosition),"labelColor"in P&&t(20,Xe=P.labelColor),"labelFmt"in P&&t(21,We=P.labelFmt),"yLabelFmt"in P&&t(22,De=P.yLabelFmt),"y2LabelFmt"in P&&t(23,Ie=P.y2LabelFmt),"y2SeriesType"in P&&t(24,qe=P.y2SeriesType),"stackTotalLabel"in P&&t(12,Se=P.stackTotalLabel),"showAllLabels"in P&&t(25,pe=P.showAllLabels),"seriesOrder"in P&&t(26,R=P.seriesOrder),"seriesLabelFmt"in P&&t(27,Ge=P.seriesLabelFmt)},i.$$.update=()=>{i.$$.dirty[0]&32768&&X(t(2,n=j(Me))),i.$$.dirty[0]&131072&&z(t(1,r=j(Ne))),i.$$.dirty[0]&512&&t(9,Ce=Ce==="true"||Ce===!0),i.$$.dirty[0]&1024&&t(10,Fe=Fe==="true"||Fe===!0),i.$$.dirty[0]&1048576&&Y(t(0,a=j(Xe))),i.$$.dirty[0]&4096&&t(12,Se=Se==="true"||Se===!0),i.$$.dirty[1]&2097152&&t(46,f=N.data),i.$$.dirty[1]&2097152&&t(42,o=N.x),i.$$.dirty[0]&16|i.$$.dirty[1]&2097152&&t(4,ee=se?ee:N.y),i.$$.dirty[0]&32|i.$$.dirty[1]&2097152&&t(5,Z=te?Z:N.y2),i.$$.dirty[1]&2097152&&t(40,u=N.yFormat),i.$$.dirty[1]&2097152&&t(47,c=N.y2Format),i.$$.dirty[1]&2097152&&t(35,y=N.yCount),i.$$.dirty[1]&2097152&&t(36,T=N.y2Count),i.$$.dirty[1]&2097152&&t(37,g=N.swapXY),i.$$.dirty[1]&2097152&&t(39,m=N.xType),i.$$.dirty[1]&2097152&&t(43,L=N.xMismatch),i.$$.dirty[1]&2097152&&t(44,w=N.columnSummary),i.$$.dirty[1]&2097152&&t(48,S=N.sort),i.$$.dirty[0]&64|i.$$.dirty[1]&2097152&&t(6,me=he?me:N.series),i.$$.dirty[0]&16848|i.$$.dirty[1]&174403&&(!me&&typeof ee!="object"?(t(7,$=$??Wt(ee,w[ee].title)),g&&m!=="category"&&(t(46,f=Fl(f,o,ee,me,!0,m!=="time")),t(39,m="category")),t(8,Be="stack1"),t(33,ve=g?"right":"top")):(S===!0&&m==="category"&&(t(31,ye=Tn(f,o,ee)),typeof ee=="object"?t(31,ye=Nl(ye,"stackTotal",!1)):t(31,ye=Nl(ye,ee,!1)),t(32,at=ye.map(P=>P[o])),t(46,f=[...f].sort(function(P,be){return at.indexOf(P[o])-at.indexOf(be[o])}))),g||(m==="value"||m==="category")&&re.includes("stacked")?(t(46,f=Fl(f,o,ee,me,!0,m==="value")),t(39,m="category")):m==="time"&&re.includes("stacked")&&t(46,f=Fl(f,o,ee,me,!0,!0)),re.includes("stacked")?(t(8,Be=Be??"stack1"),t(33,ve="inside")):(t(8,Be=void 0),t(33,ve=g?"right":"top")))),i.$$.dirty[0]&16400|i.$$.dirty[1]&34816&&re==="stacked"&&t(34,je=Tn(f,o,ee)),i.$$.dirty[0]&2048|i.$$.dirty[1]&68&&t(11,Ee=(g?et[Ee]:bt[Ee])??ve),i.$$.dirty[0]&1913458432|i.$$.dirty[1]&1901168&&t(45,A={type:"bar",stack:Be,label:{show:Ce&&Fe,formatter(P){return P.value[g?0:1]===0?"":ct(P.value[g?0:1],[Qe??ke??u,Pe??ke??c][Pi(P.componentIndex,y,T)])},position:Ee,fontSize:we,color:G},labelLayout:{hideOverlap:!pe},emphasis:{focus:"series"},barMaxWidth:Hr,itemStyle:{color:W,opacity:de,borderColor:V,borderWidth:ze}}),i.$$.dirty[0]&201326832|i.$$.dirty[1]&63552&&t(41,B=zn(f,o,ee,me,g,A,$,L,w,R,void 0,void 0,Z,Ge)),i.$$.dirty[0]&268981072|i.$$.dirty[1]&7880&&s.update(P=>(P.series.push(...B),P.legend.data.push(...B.map(be=>be.name.toString())),Ce===!0&&re==="stacked"&&typeof ee=="object"|me!==void 0&&Se===!0&&me!==o&&(P.series.push({type:"bar",stack:Be,name:"stackTotal",color:"none",data:je.map(be=>[g?0:L?be[o].toString():be[o],g?L?be[o].toString():be[o]:0]),label:{show:!0,position:g?"right":"top",formatter(be){let Ke=0;return B.forEach(tt=>{Ke+=tt.data[be.dataIndex][g?0:1]}),Ke===0?"":ct(Ke,ke??u)},fontWeight:"bold",fontSize:we,padding:g?[0,0,0,5]:void 0}}),P.legend.selectedMode=!1),P)),i.$$.dirty[1]&256&&(H={xAxis:{boundaryGap:["1%","2%"],type:m}})},ne(t(3,l=Pl(Di))),t(38,s=Pl(Ni)),[a,r,n,l,ee,Z,me,$,Be,Ce,Fe,Ee,Se,ge,re,Me,de,Ne,ze,we,Xe,We,De,Ie,qe,pe,R,Ge,ke,Qe,Pe,ye,at,ve,je,y,T,g,s,m,u,B,o,L,w,A,f,c,S,V,W,G,N]}class Wr extends st{constructor(e){super(),rt(this,e,Vr,null,nt,{y:4,y2:5,series:6,options:13,name:7,type:14,stackName:8,fillColor:15,fillOpacity:16,outlineColor:17,outlineWidth:18,labels:9,seriesLabels:10,labelSize:19,labelPosition:11,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,stackTotalLabel:12,showAllLabels:25,seriesOrder:26,seriesLabelFmt:27},null,[-1,-1])}}function qr(i){let e,t,l;e=new Wr({props:{type:i[38],fillColor:i[72],fillOpacity:i[39],outlineColor:i[71],outlineWidth:i[40],labels:i[43],labelSize:i[44],labelPosition:i[45],labelColor:i[69],labelFmt:i[46],yLabelFmt:i[47],y2LabelFmt:i[48],stackTotalLabel:i[49],seriesLabels:i[50],showAllLabels:i[51],y2SeriesType:i[9],seriesOrder:i[60],seriesLabelFmt:i[62]}});const s=i[81].default,n=el(s,i,i[82],null);return{c(){ue(e.$$.fragment),t=x(),n&&n.c()},l(r){oe(e.$$.fragment,r),t=J(r),n&&n.l(r)},m(r,a){fe(e,r,a),E(r,t,a),n&&n.m(r,a),l=!0},p(r,a){const f={};a[1]&128&&(f.type=r[38]),a[2]&1024&&(f.fillColor=r[72]),a[1]&256&&(f.fillOpacity=r[39]),a[2]&512&&(f.outlineColor=r[71]),a[1]&512&&(f.outlineWidth=r[40]),a[1]&4096&&(f.labels=r[43]),a[1]&8192&&(f.labelSize=r[44]),a[1]&16384&&(f.labelPosition=r[45]),a[2]&128&&(f.labelColor=r[69]),a[1]&32768&&(f.labelFmt=r[46]),a[1]&65536&&(f.yLabelFmt=r[47]),a[1]&131072&&(f.y2LabelFmt=r[48]),a[1]&262144&&(f.stackTotalLabel=r[49]),a[1]&524288&&(f.seriesLabels=r[50]),a[1]&1048576&&(f.showAllLabels=r[51]),a[0]&512&&(f.y2SeriesType=r[9]),a[1]&536870912&&(f.seriesOrder=r[60]),a[2]&1&&(f.seriesLabelFmt=r[62]),e.$set(f),n&&n.p&&(!l||a[2]&1048576)&&tl(n,s,r,r[82],l?il(s,r[82],a,null):ll(r[82]),null)},i(r){l||(O(e.$$.fragment,r),O(n,r),l=!0)},o(r){U(e.$$.fragment,r),U(n,r),l=!1},d(r){r&&d(t),ae(e,r),n&&n.d(r)}}}function vr(i){let e,t;return e=new Bn({props:{data:i[1],x:i[2],y:i[3],y2:i[4],xFmt:i[12],yFmt:i[10],y2Fmt:i[11],series:i[5],xType:i[6],yLog:i[7],yLogBase:i[8],legend:i[15],xAxisTitle:i[16],yAxisTitle:i[17],y2AxisTitle:i[18],xGridlines:i[19],yGridlines:i[20],y2Gridlines:i[21],xAxisLabels:i[22],yAxisLabels:i[23],y2AxisLabels:i[24],xBaseline:i[25],yBaseline:i[26],y2Baseline:i[27],xTickMarks:i[28],yTickMarks:i[29],y2TickMarks:i[30],yAxisColor:i[68],y2AxisColor:i[67],yMin:i[31],yMax:i[32],yScale:i[33],y2Min:i[34],y2Max:i[35],y2Scale:i[36],swapXY:i[0],title:i[13],subtitle:i[14],chartType:"Bar Chart",stackType:i[38],sort:i[42],stacked100:i[73],chartAreaHeight:i[41],showAllXAxisLabels:i[37],colorPalette:i[70],echartsOptions:i[52],seriesOptions:i[53],printEchartsConfig:i[54],emptySet:i[55],emptyMessage:i[56],renderer:i[57],downloadableData:i[58],downloadableImage:i[59],connectGroup:i[61],xLabelWrap:i[65],seriesColors:i[66],leftPadding:i[63],rightPadding:i[64],$$slots:{default:[qr]},$$scope:{ctx:i}}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s[0]&2&&(n.data=l[1]),s[0]&4&&(n.x=l[2]),s[0]&8&&(n.y=l[3]),s[0]&16&&(n.y2=l[4]),s[0]&4096&&(n.xFmt=l[12]),s[0]&1024&&(n.yFmt=l[10]),s[0]&2048&&(n.y2Fmt=l[11]),s[0]&32&&(n.series=l[5]),s[0]&64&&(n.xType=l[6]),s[0]&128&&(n.yLog=l[7]),s[0]&256&&(n.yLogBase=l[8]),s[0]&32768&&(n.legend=l[15]),s[0]&65536&&(n.xAxisTitle=l[16]),s[0]&131072&&(n.yAxisTitle=l[17]),s[0]&262144&&(n.y2AxisTitle=l[18]),s[0]&524288&&(n.xGridlines=l[19]),s[0]&1048576&&(n.yGridlines=l[20]),s[0]&2097152&&(n.y2Gridlines=l[21]),s[0]&4194304&&(n.xAxisLabels=l[22]),s[0]&8388608&&(n.yAxisLabels=l[23]),s[0]&16777216&&(n.y2AxisLabels=l[24]),s[0]&33554432&&(n.xBaseline=l[25]),s[0]&67108864&&(n.yBaseline=l[26]),s[0]&134217728&&(n.y2Baseline=l[27]),s[0]&268435456&&(n.xTickMarks=l[28]),s[0]&536870912&&(n.yTickMarks=l[29]),s[0]&1073741824&&(n.y2TickMarks=l[30]),s[2]&64&&(n.yAxisColor=l[68]),s[2]&32&&(n.y2AxisColor=l[67]),s[1]&1&&(n.yMin=l[31]),s[1]&2&&(n.yMax=l[32]),s[1]&4&&(n.yScale=l[33]),s[1]&8&&(n.y2Min=l[34]),s[1]&16&&(n.y2Max=l[35]),s[1]&32&&(n.y2Scale=l[36]),s[0]&1&&(n.swapXY=l[0]),s[0]&8192&&(n.title=l[13]),s[0]&16384&&(n.subtitle=l[14]),s[1]&128&&(n.stackType=l[38]),s[1]&2048&&(n.sort=l[42]),s[1]&1024&&(n.chartAreaHeight=l[41]),s[1]&64&&(n.showAllXAxisLabels=l[37]),s[2]&256&&(n.colorPalette=l[70]),s[1]&2097152&&(n.echartsOptions=l[52]),s[1]&4194304&&(n.seriesOptions=l[53]),s[1]&8388608&&(n.printEchartsConfig=l[54]),s[1]&16777216&&(n.emptySet=l[55]),s[1]&33554432&&(n.emptyMessage=l[56]),s[1]&67108864&&(n.renderer=l[57]),s[1]&134217728&&(n.downloadableData=l[58]),s[1]&268435456&&(n.downloadableImage=l[59]),s[1]&1073741824&&(n.connectGroup=l[61]),s[2]&8&&(n.xLabelWrap=l[65]),s[2]&16&&(n.seriesColors=l[66]),s[2]&2&&(n.leftPadding=l[63]),s[2]&4&&(n.rightPadding=l[64]),s[0]&512|s[1]&538964864|s[2]&1050241&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function jr(i,e,t){let l,s,n,r,a,f,o,{$$slots:u={},$$scope:c}=e;const{resolveColor:y,resolveColorsObject:T,resolveColorPalette:g}=Bt();let{data:m=void 0}=e,{x:L=void 0}=e,{y:w=void 0}=e,{y2:S=void 0}=e,{series:A=void 0}=e,{xType:B=void 0}=e,{yLog:H=void 0}=e,{yLogBase:V=void 0}=e,{y2SeriesType:K=void 0}=e,{yFmt:z=void 0}=e,{y2Fmt:W=void 0}=e,{xFmt:b=void 0}=e,{title:X=void 0}=e,{subtitle:G=void 0}=e,{legend:q=void 0}=e,{xAxisTitle:Y=void 0}=e,{yAxisTitle:N=S?"true":void 0}=e,{y2AxisTitle:p=S?"true":void 0}=e,{xGridlines:ne=void 0}=e,{yGridlines:j=void 0}=e,{y2Gridlines:ee=void 0}=e,{xAxisLabels:se=void 0}=e,{yAxisLabels:Z=void 0}=e,{y2AxisLabels:te=void 0}=e,{xBaseline:me=void 0}=e,{yBaseline:he=void 0}=e,{y2Baseline:ge=void 0}=e,{xTickMarks:$=void 0}=e,{yTickMarks:re=void 0}=e,{y2TickMarks:Be=void 0}=e,{yMin:Me=void 0}=e,{yMax:de=void 0}=e,{yScale:Ne=void 0}=e,{y2Min:ze=void 0}=e,{y2Max:Ce=void 0}=e,{y2Scale:Fe=void 0}=e,{swapXY:we=!1}=e,{showAllXAxisLabels:Ee}=e,{type:Xe="stacked"}=e,We=Xe==="stacked100",{fillColor:ke=void 0}=e,{fillOpacity:De=void 0}=e,{outlineColor:Qe=void 0}=e,{outlineWidth:Ie=void 0}=e,{chartAreaHeight:Pe=void 0}=e,{sort:qe=void 0}=e,{colorPalette:Se="default"}=e,{labels:pe=void 0}=e,{labelSize:R=void 0}=e,{labelPosition:ye=void 0}=e,{labelColor:at=void 0}=e,{labelFmt:ve=void 0}=e,{yLabelFmt:je=void 0}=e,{y2LabelFmt:bt=void 0}=e,{stackTotalLabel:et=void 0}=e,{seriesLabels:Ge=void 0}=e,{showAllLabels:P=void 0}=e,{yAxisColor:be=void 0}=e,{y2AxisColor:Ke=void 0}=e,{echartsOptions:tt=void 0}=e,{seriesOptions:_t=void 0}=e,{printEchartsConfig:lt=!1}=e,{emptySet:mt=void 0}=e,{emptyMessage:ft=void 0}=e,{renderer:At=void 0}=e,{downloadableData:Ct=void 0}=e,{downloadableImage:Re=void 0}=e,{seriesColors:Tt=void 0}=e,{seriesOrder:ut=void 0}=e,{connectGroup:Lt=void 0}=e,{seriesLabelFmt:yt=void 0}=e,{leftPadding:dt=void 0}=e,{rightPadding:Ue=void 0}=e,{xLabelWrap:_=void 0}=e;return i.$$set=C=>{"data"in C&&t(1,m=C.data),"x"in C&&t(2,L=C.x),"y"in C&&t(3,w=C.y),"y2"in C&&t(4,S=C.y2),"series"in C&&t(5,A=C.series),"xType"in C&&t(6,B=C.xType),"yLog"in C&&t(7,H=C.yLog),"yLogBase"in C&&t(8,V=C.yLogBase),"y2SeriesType"in C&&t(9,K=C.y2SeriesType),"yFmt"in C&&t(10,z=C.yFmt),"y2Fmt"in C&&t(11,W=C.y2Fmt),"xFmt"in C&&t(12,b=C.xFmt),"title"in C&&t(13,X=C.title),"subtitle"in C&&t(14,G=C.subtitle),"legend"in C&&t(15,q=C.legend),"xAxisTitle"in C&&t(16,Y=C.xAxisTitle),"yAxisTitle"in C&&t(17,N=C.yAxisTitle),"y2AxisTitle"in C&&t(18,p=C.y2AxisTitle),"xGridlines"in C&&t(19,ne=C.xGridlines),"yGridlines"in C&&t(20,j=C.yGridlines),"y2Gridlines"in C&&t(21,ee=C.y2Gridlines),"xAxisLabels"in C&&t(22,se=C.xAxisLabels),"yAxisLabels"in C&&t(23,Z=C.yAxisLabels),"y2AxisLabels"in C&&t(24,te=C.y2AxisLabels),"xBaseline"in C&&t(25,me=C.xBaseline),"yBaseline"in C&&t(26,he=C.yBaseline),"y2Baseline"in C&&t(27,ge=C.y2Baseline),"xTickMarks"in C&&t(28,$=C.xTickMarks),"yTickMarks"in C&&t(29,re=C.yTickMarks),"y2TickMarks"in C&&t(30,Be=C.y2TickMarks),"yMin"in C&&t(31,Me=C.yMin),"yMax"in C&&t(32,de=C.yMax),"yScale"in C&&t(33,Ne=C.yScale),"y2Min"in C&&t(34,ze=C.y2Min),"y2Max"in C&&t(35,Ce=C.y2Max),"y2Scale"in C&&t(36,Fe=C.y2Scale),"swapXY"in C&&t(0,we=C.swapXY),"showAllXAxisLabels"in C&&t(37,Ee=C.showAllXAxisLabels),"type"in C&&t(38,Xe=C.type),"fillColor"in C&&t(74,ke=C.fillColor),"fillOpacity"in C&&t(39,De=C.fillOpacity),"outlineColor"in C&&t(75,Qe=C.outlineColor),"outlineWidth"in C&&t(40,Ie=C.outlineWidth),"chartAreaHeight"in C&&t(41,Pe=C.chartAreaHeight),"sort"in C&&t(42,qe=C.sort),"colorPalette"in C&&t(76,Se=C.colorPalette),"labels"in C&&t(43,pe=C.labels),"labelSize"in C&&t(44,R=C.labelSize),"labelPosition"in C&&t(45,ye=C.labelPosition),"labelColor"in C&&t(77,at=C.labelColor),"labelFmt"in C&&t(46,ve=C.labelFmt),"yLabelFmt"in C&&t(47,je=C.yLabelFmt),"y2LabelFmt"in C&&t(48,bt=C.y2LabelFmt),"stackTotalLabel"in C&&t(49,et=C.stackTotalLabel),"seriesLabels"in C&&t(50,Ge=C.seriesLabels),"showAllLabels"in C&&t(51,P=C.showAllLabels),"yAxisColor"in C&&t(78,be=C.yAxisColor),"y2AxisColor"in C&&t(79,Ke=C.y2AxisColor),"echartsOptions"in C&&t(52,tt=C.echartsOptions),"seriesOptions"in C&&t(53,_t=C.seriesOptions),"printEchartsConfig"in C&&t(54,lt=C.printEchartsConfig),"emptySet"in C&&t(55,mt=C.emptySet),"emptyMessage"in C&&t(56,ft=C.emptyMessage),"renderer"in C&&t(57,At=C.renderer),"downloadableData"in C&&t(58,Ct=C.downloadableData),"downloadableImage"in C&&t(59,Re=C.downloadableImage),"seriesColors"in C&&t(80,Tt=C.seriesColors),"seriesOrder"in C&&t(60,ut=C.seriesOrder),"connectGroup"in C&&t(61,Lt=C.connectGroup),"seriesLabelFmt"in C&&t(62,yt=C.seriesLabelFmt),"leftPadding"in C&&t(63,dt=C.leftPadding),"rightPadding"in C&&t(64,Ue=C.rightPadding),"xLabelWrap"in C&&t(65,_=C.xLabelWrap),"$$scope"in C&&t(82,c=C.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&1&&(we==="true"||we===!0?t(0,we=!0):t(0,we=!1)),i.$$.dirty[2]&4096&&t(72,l=y(ke)),i.$$.dirty[2]&8192&&t(71,s=y(Qe)),i.$$.dirty[2]&16384&&t(70,n=g(Se)),i.$$.dirty[2]&32768&&t(69,r=y(at)),i.$$.dirty[2]&65536&&t(68,a=y(be)),i.$$.dirty[2]&131072&&t(67,f=y(Ke)),i.$$.dirty[2]&262144&&t(66,o=T(Tt))},[we,m,L,w,S,A,B,H,V,K,z,W,b,X,G,q,Y,N,p,ne,j,ee,se,Z,te,me,he,ge,$,re,Be,Me,de,Ne,ze,Ce,Fe,Ee,Xe,De,Ie,Pe,qe,pe,R,ye,ve,je,bt,et,Ge,P,tt,_t,lt,mt,ft,At,Ct,Re,ut,Lt,yt,dt,Ue,_,o,f,a,r,n,s,l,We,ke,Qe,Se,at,be,Ke,Tt,u,c]}class Yr extends st{constructor(e){super(),rt(this,e,jr,vr,nt,{data:1,x:2,y:3,y2:4,series:5,xType:6,yLog:7,yLogBase:8,y2SeriesType:9,yFmt:10,y2Fmt:11,xFmt:12,title:13,subtitle:14,legend:15,xAxisTitle:16,yAxisTitle:17,y2AxisTitle:18,xGridlines:19,yGridlines:20,y2Gridlines:21,xAxisLabels:22,yAxisLabels:23,y2AxisLabels:24,xBaseline:25,yBaseline:26,y2Baseline:27,xTickMarks:28,yTickMarks:29,y2TickMarks:30,yMin:31,yMax:32,yScale:33,y2Min:34,y2Max:35,y2Scale:36,swapXY:0,showAllXAxisLabels:37,type:38,fillColor:74,fillOpacity:39,outlineColor:75,outlineWidth:40,chartAreaHeight:41,sort:42,colorPalette:76,labels:43,labelSize:44,labelPosition:45,labelColor:77,labelFmt:46,yLabelFmt:47,y2LabelFmt:48,stackTotalLabel:49,seriesLabels:50,showAllLabels:51,yAxisColor:78,y2AxisColor:79,echartsOptions:52,seriesOptions:53,printEchartsConfig:54,emptySet:55,emptyMessage:56,renderer:57,downloadableData:58,downloadableImage:59,seriesColors:80,seriesOrder:60,connectGroup:61,seriesLabelFmt:62,leftPadding:63,rightPadding:64,xLabelWrap:65},null,[-1,-1,-1])}}function Xr(i){let e,t;return e=new os({props:{error:i[3]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&8&&(n.error=l[3]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Qr(i){let e,t=ct(i[2],i[4])+"",l,s,n,r=i[1]&&Ln(i);return{c(){e=D("span"),l=Le(t),s=x(),r&&r.c(),this.h()},l(a){e=M(a,"SPAN",{style:!0});var f=Q(e);l=Te(f,t),s=J(f),r&&r.l(f),f.forEach(d),this.h()},h(){v(e,"color",i[5])},m(a,f){E(a,e,f),I(e,l),I(e,s),r&&r.m(e,null),n=!0},p(a,f){(!n||f&20)&&t!==(t=ct(a[2],a[4])+"")&&Ze(l,t),a[1]?r?(r.p(a,f),f&2&&O(r,1)):(r=Ln(a),r.c(),O(r,1),r.m(e,null)):r&&(Je(),U(r,1,1,()=>{r=null}),xe()),(!n||f&32)&&v(e,"color",a[5])},i(a){n||(O(r),n=!0)},o(a){U(r),n=!1},d(a){a&&d(e),r&&r.d()}}}function Kr(i){let e,t,l,s,n,r="Placeholder: no data currently referenced.";return{c(){e=D("span"),t=Le("["),l=Le(i[0]),s=Le("]"),n=D("span"),n.textContent=r,this.h()},l(a){e=M(a,"SPAN",{class:!0});var f=Q(e);t=Te(f,"["),l=Te(f,i[0]),s=Te(f,"]"),n=M(f,"SPAN",{class:!0,"data-svelte-h":!0}),Oe(n)!=="svelte-ddarzq"&&(n.textContent=r),f.forEach(d),this.h()},h(){h(n,"class","error-msg svelte-1mb9o01"),h(e,"class","placeholder svelte-1mb9o01")},m(a,f){E(a,e,f),I(e,t),I(e,l),I(e,s),I(e,n)},p(a,f){f&1&&Ze(l,a[0])},i:ce,o:ce,d(a){a&&d(e)}}}function Ln(i){let e,t;return e=new Nn({props:{description:i[1]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&2&&(n.description=l[1]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Zr(i){let e,t,l,s;const n=[Kr,Qr,Xr],r=[];function a(f,o){return f[0]?0:f[3]?2:1}return e=a(i),t=r[e]=n[e](i),{c(){t.c(),l=$e()},l(f){t.l(f),l=$e()},m(f,o){r[e].m(f,o),E(f,l,o),s=!0},p(f,[o]){let u=e;e=a(f),e===u?r[e].p(f,o):(Je(),U(r[u],1,1,()=>{r[u]=null}),xe(),t=r[e],t?t.p(f,o):(t=r[e]=n[e](f),t.c()),O(t,1),t.m(l.parentNode,l))},i(f){s||(O(t),s=!0)},o(f){U(t),s=!1},d(f){f&&d(l),r[e].d(f)}}}function Jr(i,e,t){let l,s,n=ce,r=()=>(n(),n=St(l,V=>t(15,s=V)),l);i.$$.on_destroy.push(()=>n());const{resolveColor:a}=Bt();let{data:f=null}=e,{row:o=0}=e,{column:u=null}=e,{value:c=null}=e,{placeholder:y=null}=e,{description:T=void 0}=e,{fmt:g=void 0}=e,m,L,w,{color:S=void 0}=e,A="",{redNegatives:B=!1}=e,H;return i.$$set=V=>{"data"in V&&t(7,f=V.data),"row"in V&&t(10,o=V.row),"column"in V&&t(8,u=V.column),"value"in V&&t(11,c=V.value),"placeholder"in V&&t(0,y=V.placeholder),"description"in V&&t(1,T=V.description),"fmt"in V&&t(12,g=V.fmt),"color"in V&&t(13,S=V.color),"redNegatives"in V&&t(9,B=V.redNegatives)},i.$$.update=()=>{var V;if(i.$$.dirty&2304&&t(8,u=u??c),i.$$.dirty&21897)try{if(t(3,w=void 0),!y)if(f){if(typeof f=="string")throw Error(`Received: data=${f}, expected: data={${f}}`);if(Array.isArray(f)||t(7,f=[f]),isNaN(o))throw Error("row must be a number (row="+o+")");try{Object.keys(f[o])[0]}catch{throw Error("Row "+o+" does not exist in the dataset")}t(8,u=u??Object.keys(f[o])[0]),Cl(f,[u]),t(14,H=_l(f,"array"));const K=H.filter(z=>{var W;return z.type==="date"&&!(((W=f[0])==null?void 0:W[z.id])instanceof Date)}).map(z=>z.id);for(let z=0;z<K.length;z++)t(7,f=fs(f,K[z]));t(2,L=f[o][u]),t(14,H=H.filter(z=>z.id===u)),g?t(4,m=It(g,(V=H[0].format)==null?void 0:V.valueType)):t(4,m=H[0].format)}else throw Error("No data provided. If you referenced a query result, check that the name is correct.")}catch(K){if(t(3,w=K.message),console.error("\x1B[31m%s\x1B[0m",`Error in Value: ${w}`),Mi)throw w}i.$$.dirty&2304&&c&&u&&console.warn('Both "value" and "column" were supplied as props to Value. "value" will be ignored.'),i.$$.dirty&8192&&r(t(6,l=a(S))),i.$$.dirty&512&&t(9,B=B==="true"||B===!0),i.$$.dirty&33284&&(B||s)&&(B&&L<0?t(5,A="rgb(220 38 38)"):s&&t(5,A=s))},[y,T,L,w,m,A,l,f,u,B,o,c,g,S,H,s]}class xr extends st{constructor(e){super(),rt(this,e,Jr,Zr,nt,{data:7,row:10,column:8,value:11,placeholder:0,description:1,fmt:12,color:13,redNegatives:9})}}function pr(i){let e;const t=i[7].default,l=el(t,i,i[8],null);return{c(){l&&l.c()},l(s){l&&l.l(s)},m(s,n){l&&l.m(s,n),e=!0},p(s,n){l&&l.p&&(!e||n&256)&&tl(l,t,s,s[8],e?il(t,s[8],n,null):ll(s[8]),null)},i(s){e||(O(l,s),e=!0)},o(s){U(l,s),e=!1},d(s){l&&l.d(s)}}}function $r(i){let e,t;const l=[i[4],{data:Mt.isQuery(i[11])?Array.from(i[11]):i[11]}];let s={$$slots:{default:[pr]},$$scope:{ctx:i}};for(let n=0;n<l.length;n+=1)s=Et(s,l[n]);return e=new xr({props:s}),{c(){ue(e.$$.fragment)},l(n){oe(e.$$.fragment,n)},m(n,r){fe(e,n,r),t=!0},p(n,r){const a=r&2064?Kl(l,[r&16&&Zl(n[4]),r&2048&&{data:Mt.isQuery(n[11])?Array.from(n[11]):n[11]}]):{};r&256&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(O(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){ae(e,n)}}}function kn(i){let e,t;return e=new Jl({props:{emptyMessage:i[2],emptySet:i[1],chartType:ia,isInitial:i[3]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&4&&(n.emptyMessage=l[2]),s&2&&(n.emptySet=l[1]),s&8&&(n.isInitial=l[3]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function ea(i){let e,t,l=!i[4].placeholder&&kn(i);return{c(){e=D("span"),l&&l.c(),this.h()},l(s){e=M(s,"SPAN",{slot:!0});var n=Q(e);l&&l.l(n),n.forEach(d),this.h()},h(){h(e,"slot","empty")},m(s,n){E(s,e,n),l&&l.m(e,null),t=!0},p(s,n){s[4].placeholder?l&&(Je(),U(l,1,1,()=>{l=null}),xe()):l?(l.p(s,n),n&16&&O(l,1)):(l=kn(s),l.c(),O(l,1),l.m(e,null))},i(s){t||(O(l),t=!0)},o(s){U(l),t=!1},d(s){s&&d(e),l&&l.d()}}}function ta(i){let e,t="Loading...";return{c(){e=D("span"),e.textContent=t,this.h()},l(l){e=M(l,"SPAN",{slot:!0,class:!0,"data-svelte-h":!0}),Oe(e)!=="svelte-89gxhc"&&(e.textContent=t),this.h()},h(){h(e,"slot","skeleton"),h(e,"class","text-base-content-muted")},m(l,s){E(l,e,s)},p:ce,d(l){l&&d(e)}}}function la(i){let e,t;return e=new Ql({props:{data:i[0],$$slots:{skeleton:[ta],empty:[ea],default:[$r,({loaded:l})=>({11:l}),({loaded:l})=>l?2048:0]},$$scope:{ctx:i}}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,[s]){const n={};s&1&&(n.data=l[0]),s&2334&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}let ia="Value";function na(i,e,t){let l,{$$slots:s={},$$scope:n}=e,{data:r}=e,{column:a}=e,{agg:f}=e;const o=Mt.isQuery(r)?r.hash:void 0;let u=(r==null?void 0:r.hash)===o,{emptySet:c=void 0}=e,{emptyMessage:y=void 0}=e;return i.$$set=T=>{t(10,e=Et(Et({},e),Gt(T))),"data"in T&&t(0,r=T.data),"column"in T&&t(5,a=T.column),"agg"in T&&t(6,f=T.agg),"emptySet"in T&&t(1,c=T.emptySet),"emptyMessage"in T&&t(2,y=T.emptyMessage),"$$scope"in T&&t(8,n=T.$$scope)},i.$$.update=()=>{i.$$.dirty&97&&f&&t(0,r=r.groupBy(void 0).agg({[f]:{col:a,as:a}})),i.$$.dirty&1&&t(3,u=(r==null?void 0:r.hash)===o),t(4,l=Object.fromEntries(Object.entries(e).filter(([,T])=>T!==void 0)))},e=Gt(e),[r,c,y,u,l,a,f,s,n]}class Ul extends st{constructor(e){super(),rt(this,e,na,la,nt,{data:0,column:5,agg:6,emptySet:1,emptyMessage:2})}}function sa(i){let e;const t=i[6].default,l=el(t,i,i[7],null);return{c(){l&&l.c()},l(s){l&&l.l(s)},m(s,n){l&&l.m(s,n),e=!0},p(s,n){l&&l.p&&(!e||n&128)&&tl(l,t,s,s[7],e?il(t,s[7],n,null):ll(s[7]),null)},i(s){e||(O(l,s),e=!0)},o(s){U(l,s),e=!1},d(s){l&&l.d(s)}}}function ra(i){let e,t;const l=[i[4],{data:Mt.isQuery(i[10])?Array.from(i[10]):i[10]},{queryID:i[5]}];let s={$$slots:{default:[sa]},$$scope:{ctx:i}};for(let n=0;n<l.length;n+=1)s=Et(s,l[n]);return e=new us({props:s}),{c(){ue(e.$$.fragment)},l(n){oe(e.$$.fragment,n)},m(n,r){fe(e,n,r),t=!0},p(n,r){const a=r&1072?Kl(l,[r&16&&Zl(n[4]),r&1024&&{data:Mt.isQuery(n[10])?Array.from(n[10]):n[10]},r&32&&{queryID:n[5]}]):{};r&128&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(O(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){ae(e,n)}}}function aa(i){let e,t;return e=new Jl({props:{slot:"empty",emptyMessage:i[2],emptySet:i[1],chartType:i[4].chartType,isInitial:i[3]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&4&&(n.emptyMessage=l[2]),s&2&&(n.emptySet=l[1]),s&16&&(n.chartType=l[4].chartType),s&8&&(n.isInitial=l[3]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function fa(i){let e,t;return e=new Fi({props:{slot:"error",title:ua,error:i[10].error.message}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&1024&&(n.error=l[10].error.message),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function oa(i){let e,t;return e=new Ql({props:{data:i[0],$$slots:{error:[fa,({loaded:l})=>({10:l}),({loaded:l})=>l?1024:0],empty:[aa],default:[ra,({loaded:l})=>({10:l}),({loaded:l})=>l?1024:0]},$$scope:{ctx:i}}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,[s]){const n={};s&1&&(n.data=l[0]),s&1182&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}let ua="Sparkline";function ca(i,e,t){let l,{$$slots:s={},$$scope:n}=e,{data:r}=e;const a=Mt.isQuery(r)?r.hash:void 0;let f=(r==null?void 0:r.hash)===a,{emptySet:o=void 0}=e,{emptyMessage:u=void 0}=e,c=r==null?void 0:r.id;return i.$$set=y=>{t(9,e=Et(Et({},e),Gt(y))),"data"in y&&t(0,r=y.data),"emptySet"in y&&t(1,o=y.emptySet),"emptyMessage"in y&&t(2,u=y.emptyMessage),"$$scope"in y&&t(7,n=y.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&t(3,f=(r==null?void 0:r.hash)===a),t(4,l={...Object.fromEntries(Object.entries(e).filter(([,y])=>y!==void 0))})},e=Gt(e),[r,o,u,f,l,c,s,n]}class ma extends st{constructor(e){super(),rt(this,e,ca,oa,nt,{data:0,emptySet:1,emptyMessage:2})}}function da(i){let e,t,l,s,n,r,a,f,o,u,c,y,T,g=i[23]&&Sn(i);const m=[ga,ya],L=[];function w(B,H){return B[22]?0:1}a=w(i),f=L[a]=m[a](i);let S=i[8]&&En(i),A=i[7]&&An(i);return{c(){e=D("p"),t=Le(i[3]),l=x(),g&&g.c(),n=x(),r=D("div"),f.c(),o=x(),S&&S.c(),c=x(),A&&A.c(),y=$e(),this.h()},l(B){e=M(B,"P",{class:!0});var H=Q(e);t=Te(H,i[3]),l=J(H),g&&g.l(H),H.forEach(d),n=J(B),r=M(B,"DIV",{class:!0});var V=Q(r);f.l(V),o=J(V),S&&S.l(V),V.forEach(d),c=J(B),A&&A.l(B),y=$e(),this.h()},h(){h(e,"class",s=bl("text-sm align-top leading-none",i[19])),h(r,"class",u=bl("relative text-xl font-medium mt-1.5",i[20]))},m(B,H){E(B,e,H),I(e,t),I(e,l),g&&g.m(e,null),E(B,n,H),E(B,r,H),L[a].m(r,null),I(r,o),S&&S.m(r,null),E(B,c,H),A&&A.m(B,H),E(B,y,H),T=!0},p(B,H){(!T||H&8)&&Ze(t,B[3]),B[23]?g?(g.p(B,H),H&8388608&&O(g,1)):(g=Sn(B),g.c(),O(g,1),g.m(e,null)):g&&(Je(),U(g,1,1,()=>{g=null}),xe()),(!T||H&524288&&s!==(s=bl("text-sm align-top leading-none",B[19])))&&h(e,"class",s);let V=a;a=w(B),a===V?L[a].p(B,H):(Je(),U(L[V],1,1,()=>{L[V]=null}),xe(),f=L[a],f?f.p(B,H):(f=L[a]=m[a](B),f.c()),O(f,1),f.m(r,o)),B[8]?S?(S.p(B,H),H&256&&O(S,1)):(S=En(B),S.c(),O(S,1),S.m(r,null)):S&&(Je(),U(S,1,1,()=>{S=null}),xe()),(!T||H&1048576&&u!==(u=bl("relative text-xl font-medium mt-1.5",B[20])))&&h(r,"class",u),B[7]?A?(A.p(B,H),H&128&&O(A,1)):(A=An(B),A.c(),O(A,1),A.m(y.parentNode,y)):A&&(Je(),U(A,1,1,()=>{A=null}),xe())},i(B){T||(O(g),O(f),O(S),O(A),T=!0)},o(B){U(g),U(f),U(S),U(A),T=!1},d(B){B&&(d(e),d(n),d(r),d(c),d(y)),g&&g.d(),L[a].d(),S&&S.d(),A&&A.d(B)}}}function ha(i){let e,t;return e=new cs({props:{inputType:"BigValue",error:i[24],width:"148",height:"28"}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&16777216&&(n.error=l[24]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Sn(i){let e,t;return e=new Nn({props:{description:i[23],size:"3"}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&8388608&&(n.description=l[23]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function ya(i){let e,t;return e=new Ul({props:{data:i[0],column:i[6],fmt:i[13]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&1&&(n.data=l[0]),s&64&&(n.column=l[6]),s&8192&&(n.fmt=l[13]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function ga(i){let e,t,l,s;return t=new Ul({props:{data:i[0],column:i[6],fmt:i[13]}}),{c(){e=D("a"),ue(t.$$.fragment),this.h()},l(n){e=M(n,"A",{class:!0,href:!0});var r=Q(e);oe(t.$$.fragment,r),r.forEach(d),this.h()},h(){h(e,"class","hover:bg-base-200"),h(e,"href",l=Ll(i[22]))},m(n,r){E(n,e,r),fe(t,e,null),s=!0},p(n,r){const a={};r&1&&(a.data=n[0]),r&64&&(a.column=n[6]),r&8192&&(a.fmt=n[13]),t.$set(a),(!s||r&4194304&&l!==(l=Ll(n[22])))&&h(e,"href",l)},i(n){s||(O(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&d(e),ae(t)}}}function En(i){let e,t;return e=new ma({props:{height:"15",data:i[0],dateCol:i[8],valueCol:i[6],type:i[9],interactive:"true",color:i[25],valueFmt:i[13]??i[10],dateFmt:i[11],yScale:i[2],connectGroup:i[12]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&1&&(n.data=l[0]),s&256&&(n.dateCol=l[8]),s&64&&(n.valueCol=l[6]),s&512&&(n.type=l[9]),s&33554432&&(n.color=l[25]),s&9216&&(n.valueFmt=l[13]??l[10]),s&2048&&(n.dateFmt=l[11]),s&4&&(n.yScale=l[2]),s&4096&&(n.connectGroup=l[12]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function An(i){let e,t,l,s;const n=[_a,ba],r=[];function a(f,o){return f[1]?0:1}return e=a(i),t=r[e]=n[e](i),{c(){t.c(),l=$e()},l(f){t.l(f),l=$e()},m(f,o){r[e].m(f,o),E(f,l,o),s=!0},p(f,o){let u=e;e=a(f),e===u?r[e].p(f,o):(Je(),U(r[u],1,1,()=>{r[u]=null}),xe(),t=r[e],t?t.p(f,o):(t=r[e]=n[e](f),t.c()),O(t,1),t.m(l.parentNode,l))},i(f){s||(O(t),s=!0)},o(f){U(t),s=!1},d(f){f&&d(l),r[e].d(f)}}}function ba(i){let e,t,l,s,n,r,a;const f=[Ta,Ca],o=[];function u(c,y){return c[22]?0:1}return t=u(i),l=o[t]=f[t](i),{c(){e=D("p"),l.c(),s=x(),n=D("span"),r=Le(i[4]),this.h()},l(c){e=M(c,"P",{class:!0});var y=Q(e);l.l(y),s=J(y),n=M(y,"SPAN",{});var T=Q(n);r=Te(T,i[4]),T.forEach(d),y.forEach(d),this.h()},h(){h(e,"class","text-xs font-sans /60 pt-[0.5px]")},m(c,y){E(c,e,y),o[t].m(e,null),I(e,s),I(e,n),I(n,r),a=!0},p(c,y){let T=t;t=u(c),t===T?o[t].p(c,y):(Je(),U(o[T],1,1,()=>{o[T]=null}),xe(),l=o[t],l?l.p(c,y):(l=o[t]=f[t](c),l.c()),O(l,1),l.m(e,s)),(!a||y&16)&&Ze(r,c[4])},i(c){a||(O(l),a=!0)},o(c){U(l),a=!1},d(c){c&&d(e),o[t].d()}}}function _a(i){let e,t,l,s;return t=new ms({props:{data:i[0],column:i[7],fmt:i[14],fontClass:"text-xs",symbolPosition:"left",neutralMin:i[15],neutralMax:i[16],text:i[4],downIsGood:i[5]}}),{c(){e=D("p"),ue(t.$$.fragment),this.h()},l(n){e=M(n,"P",{class:!0});var r=Q(e);oe(t.$$.fragment,r),r.forEach(d),this.h()},h(){h(e,"class",l=bl("text-xs font-sans mt-1",i[21]))},m(n,r){E(n,e,r),fe(t,e,null),s=!0},p(n,r){const a={};r&1&&(a.data=n[0]),r&128&&(a.column=n[7]),r&16384&&(a.fmt=n[14]),r&32768&&(a.neutralMin=n[15]),r&65536&&(a.neutralMax=n[16]),r&16&&(a.text=n[4]),r&32&&(a.downIsGood=n[5]),t.$set(a),(!s||r&2097152&&l!==(l=bl("text-xs font-sans mt-1",n[21])))&&h(e,"class",l)},i(n){s||(O(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&d(e),ae(t)}}}function Ca(i){let e,t;return e=new Ul({props:{data:i[0],column:i[7],fmt:i[14]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&1&&(n.data=l[0]),s&128&&(n.column=l[7]),s&16384&&(n.fmt=l[14]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Ta(i){let e,t,l,s;return t=new Ul({props:{data:i[0],column:i[7],fmt:i[14]}}),{c(){e=D("a"),ue(t.$$.fragment),this.h()},l(n){e=M(n,"A",{class:!0,href:!0});var r=Q(e);oe(t.$$.fragment,r),r.forEach(d),this.h()},h(){h(e,"class","hover:bg-base-200"),h(e,"href",l=Ll(i[22]))},m(n,r){E(n,e,r),fe(t,e,null),s=!0},p(n,r){const a={};r&1&&(a.data=n[0]),r&128&&(a.column=n[7]),r&16384&&(a.fmt=n[14]),t.$set(a),(!s||r&4194304&&l!==(l=Ll(n[22])))&&h(e,"href",l)},i(n){s||(O(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&d(e),ae(t)}}}function La(i){let e,t,l,s,n;const r=[ha,da],a=[];function f(o,u){return o[24].length>0?0:1}return t=f(i),l=a[t]=r[t](i),{c(){e=D("div"),l.c(),this.h()},l(o){e=M(o,"DIV",{class:!0,style:!0});var u=Q(e);l.l(u),u.forEach(d),this.h()},h(){h(e,"class","inline-block font-sans pt-2 pb-3 pl-0 mr-3 items-center align-top"),h(e,"style",s=`
        min-width: ${i[18]};
        max-width: ${i[17]};
		`)},m(o,u){E(o,e,u),a[t].m(e,null),n=!0},p(o,[u]){let c=t;t=f(o),t===c?a[t].p(o,u):(Je(),U(a[c],1,1,()=>{a[c]=null}),xe(),l=a[t],l?l.p(o,u):(l=a[t]=r[t](o),l.c()),O(l,1),l.m(e,null)),(!n||u&393216&&s!==(s=`
        min-width: ${o[18]};
        max-width: ${o[17]};
		`))&&h(e,"style",s)},i(o){n||(O(l),n=!0)},o(o){U(l),n=!1},d(o){o&&d(e),a[t].d()}}}function ka(i,e,t){let l;const{resolveColor:s}=Bt();let{data:n}=e,{value:r=null}=e,{comparison:a=null}=e,{comparisonDelta:f=!0}=e,{sparkline:o=null}=e,{sparklineType:u="line"}=e,{sparklineColor:c=void 0}=e,{sparklineValueFmt:y=void 0}=e,{sparklineDateFmt:T=void 0}=e,{sparklineYScale:g=!1}=e,{connectGroup:m=void 0}=e,{fmt:L=void 0}=e,{comparisonFmt:w=void 0}=e,{title:S=null}=e,{comparisonTitle:A=null}=e,{downIsGood:B=!1}=e,{neutralMin:H=0}=e,{neutralMax:V=0}=e,{maxWidth:K="none"}=e,{minWidth:z="18%"}=e,{titleClass:W=void 0}=e,{valueClass:b=void 0}=e,{comparisonClass:X=void 0}=e,{link:G=null}=e,{description:q=void 0}=e,Y=[];return i.$$set=N=>{"data"in N&&t(0,n=N.data),"value"in N&&t(6,r=N.value),"comparison"in N&&t(7,a=N.comparison),"comparisonDelta"in N&&t(1,f=N.comparisonDelta),"sparkline"in N&&t(8,o=N.sparkline),"sparklineType"in N&&t(9,u=N.sparklineType),"sparklineColor"in N&&t(26,c=N.sparklineColor),"sparklineValueFmt"in N&&t(10,y=N.sparklineValueFmt),"sparklineDateFmt"in N&&t(11,T=N.sparklineDateFmt),"sparklineYScale"in N&&t(2,g=N.sparklineYScale),"connectGroup"in N&&t(12,m=N.connectGroup),"fmt"in N&&t(13,L=N.fmt),"comparisonFmt"in N&&t(14,w=N.comparisonFmt),"title"in N&&t(3,S=N.title),"comparisonTitle"in N&&t(4,A=N.comparisonTitle),"downIsGood"in N&&t(5,B=N.downIsGood),"neutralMin"in N&&t(15,H=N.neutralMin),"neutralMax"in N&&t(16,V=N.neutralMax),"maxWidth"in N&&t(17,K=N.maxWidth),"minWidth"in N&&t(18,z=N.minWidth),"titleClass"in N&&t(19,W=N.titleClass),"valueClass"in N&&t(20,b=N.valueClass),"comparisonClass"in N&&t(21,X=N.comparisonClass),"link"in N&&t(22,G=N.link),"description"in N&&t(23,q=N.description)},i.$$.update=()=>{if(i.$$.dirty&2&&t(1,f=f==="true"||f===!0),i.$$.dirty&67108864&&t(25,l=s(c)),i.$$.dirty&4&&t(2,g=g==="true"||g===!0),i.$$.dirty&32&&t(5,B=B==="true"||B===!0),i.$$.dirty&16777689)try{Array.isArray(n)||t(0,n=[n]),Cl(n,[r]);let N=_l(n,"array"),p=N.find(ne=>ne.id===r);if(t(3,S=S??(p?p.title:null)),a!==null){Cl(n,[a]);let ne=N.find(j=>j.id===a);t(4,A=A??(ne?ne.title:null))}o!==null&&Cl(n,[o])}catch(N){if(t(24,Y=[...Y,N]),Mi)throw Y}},[n,f,g,S,A,B,r,a,o,u,y,T,m,L,w,H,V,K,z,W,b,X,G,q,Y,l,c]}let Sa=class extends st{constructor(e){super(),rt(this,e,ka,La,nt,{data:0,value:6,comparison:7,comparisonDelta:1,sparkline:8,sparklineType:9,sparklineColor:26,sparklineValueFmt:10,sparklineDateFmt:11,sparklineYScale:2,connectGroup:12,fmt:13,comparisonFmt:14,title:3,comparisonTitle:4,downIsGood:5,neutralMin:15,neutralMax:16,maxWidth:17,minWidth:18,titleClass:19,valueClass:20,comparisonClass:21,link:22,description:23})}};function Ea(i){let e;const t=i[6].default,l=el(t,i,i[7],null);return{c(){l&&l.c()},l(s){l&&l.l(s)},m(s,n){l&&l.m(s,n),e=!0},p(s,n){l&&l.p&&(!e||n&128)&&tl(l,t,s,s[7],e?il(t,s[7],n,null):ll(s[7]),null)},i(s){e||(O(l,s),e=!0)},o(s){U(l,s),e=!1},d(s){l&&l.d(s)}}}function Aa(i){let e,t;const l=[i[4],{data:Mt.isQuery(i[9])?Array.from(i[9]):i[9]}];let s={$$slots:{default:[Ea]},$$scope:{ctx:i}};for(let n=0;n<l.length;n+=1)s=Et(s,l[n]);return e=new Sa({props:s}),{c(){ue(e.$$.fragment)},l(n){oe(e.$$.fragment,n)},m(n,r){fe(e,n,r),t=!0},p(n,r){const a=r&528?Kl(l,[r&16&&Zl(n[4]),r&512&&{data:Mt.isQuery(n[9])?Array.from(n[9]):n[9]}]):{};r&128&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(O(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){ae(e,n)}}}function wa(i){let e,t,l,s;return t=new ds({props:{error:i[9].error.message}}),{c(){e=D("div"),ue(t.$$.fragment),this.h()},l(n){e=M(n,"DIV",{slot:!0,class:!0,style:!0});var r=Q(e);oe(t.$$.fragment,r),r.forEach(d),this.h()},h(){h(e,"slot","error"),h(e,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(e,"style",l=`
				min-width: ${i[5].minWidth};
				max-width: ${i[5].maxWidth};
		`)},m(n,r){E(n,e,r),fe(t,e,null),s=!0},p(n,r){const a={};r&512&&(a.error=n[9].error.message),t.$set(a),(!s||r&32&&l!==(l=`
				min-width: ${n[5].minWidth};
				max-width: ${n[5].maxWidth};
		`))&&h(e,"style",l)},i(n){s||(O(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&d(e),ae(t)}}}function Oa(i){let e,t,l,s;return t=new Jl({props:{emptyMessage:i[2],emptySet:i[1],chartType:Da,isInitial:i[3]}}),{c(){e=D("div"),ue(t.$$.fragment),this.h()},l(n){e=M(n,"DIV",{slot:!0,class:!0,style:!0});var r=Q(e);oe(t.$$.fragment,r),r.forEach(d),this.h()},h(){h(e,"slot","empty"),h(e,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(e,"style",l=`
				min-width: ${i[5].minWidth};
				max-width: ${i[5].maxWidth};
		`)},m(n,r){E(n,e,r),fe(t,e,null),s=!0},p(n,r){const a={};r&4&&(a.emptyMessage=n[2]),r&2&&(a.emptySet=n[1]),r&8&&(a.isInitial=n[3]),t.$set(a),(!s||r&32&&l!==(l=`
				min-width: ${n[5].minWidth};
				max-width: ${n[5].maxWidth};
		`))&&h(e,"style",l)},i(n){s||(O(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&d(e),ae(t)}}}function Ia(i){let e,t,l=(i[5].title??" ")+"",s,n,r,a,f;return r=new Ul({props:{column:i[5].value,fmt:i[5].fmt,data:i[9]}}),{c(){e=D("div"),t=D("p"),s=Le(l),n=x(),ue(r.$$.fragment),this.h()},l(o){e=M(o,"DIV",{class:!0,style:!0,slot:!0});var u=Q(e);t=M(u,"P",{class:!0});var c=Q(t);s=Te(c,l),c.forEach(d),n=J(u),oe(r.$$.fragment,u),u.forEach(d),this.h()},h(){h(t,"class","text-sm"),h(e,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(e,"style",a=`
			min-width: ${i[5].minWidth};
			max-width: ${i[5].maxWidth};
		`),h(e,"slot","skeleton")},m(o,u){E(o,e,u),I(e,t),I(t,s),I(e,n),fe(r,e,null),f=!0},p(o,u){(!f||u&32)&&l!==(l=(o[5].title??" ")+"")&&Ze(s,l);const c={};u&32&&(c.column=o[5].value),u&32&&(c.fmt=o[5].fmt),u&512&&(c.data=o[9]),r.$set(c),(!f||u&32&&a!==(a=`
			min-width: ${o[5].minWidth};
			max-width: ${o[5].maxWidth};
		`))&&h(e,"style",a)},i(o){f||(O(r.$$.fragment,o),f=!0)},o(o){U(r.$$.fragment,o),f=!1},d(o){o&&d(e),ae(r)}}}function Ma(i){let e,t;return e=new Ql({props:{data:i[0],$$slots:{skeleton:[Ia,({loaded:l})=>({9:l}),({loaded:l})=>l?512:0],empty:[Oa],error:[wa,({loaded:l})=>({9:l}),({loaded:l})=>l?512:0],default:[Aa,({loaded:l})=>({9:l}),({loaded:l})=>l?512:0]},$$scope:{ctx:i}}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,[s]){const n={};s&1&&(n.data=l[0]),s&702&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}let Da="Big Value";function Na(i,e,t){let l,{$$slots:s={},$$scope:n}=e,{data:r}=e;const a=Mt.isQuery(r)?r.hash:void 0;let f=(r==null?void 0:r.hash)===a,{emptySet:o=void 0}=e,{emptyMessage:u=void 0}=e;return i.$$set=c=>{t(5,e=Et(Et({},e),Gt(c))),"data"in c&&t(0,r=c.data),"emptySet"in c&&t(1,o=c.emptySet),"emptyMessage"in c&&t(2,u=c.emptyMessage),"$$scope"in c&&t(7,n=c.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&t(3,f=(r==null?void 0:r.hash)===a),t(4,l=Object.fromEntries(Object.entries(e).filter(([,c])=>c!==void 0)))},e=Gt(e),[r,o,u,f,l,e,s,n]}class wn extends st{constructor(e){super(),rt(this,e,Na,Ma,nt,{data:0,emptySet:1,emptyMessage:2})}}function Fa(i,e,t){let l,s,n,r,a,f,o,u,c,y,T,g,m,L,w,S,A=ce,B=()=>(A(),A=St(l,R=>t(44,S=R)),l),H,V=ce,K=()=>(V(),V=St(s,R=>t(45,H=R)),s),z;i.$$.on_destroy.push(()=>A()),i.$$.on_destroy.push(()=>V());let W=Pl(Di);Dt(i,W,R=>t(46,z=R));let b=Pl(Ni);const{resolveColor:X}=Bt();let{y:G=void 0}=e;const q=!!G;let{y2:Y=void 0}=e;const N=!!Y;let{series:p=void 0}=e;const ne=!!p;let{options:j=void 0}=e,{name:ee=void 0}=e,{lineColor:se=void 0}=e,{lineWidth:Z=2}=e,{lineType:te="solid"}=e,{lineOpacity:me=void 0}=e,{markers:he=!1}=e,{markerShape:ge="circle"}=e,{markerSize:$=8}=e,{labels:re=!1}=e,{labelSize:Be=11}=e,{labelPosition:Me="top"}=e,{labelColor:de=void 0}=e,{labelFmt:Ne=void 0}=e,ze;Ne&&(ze=It(Ne));let{yLabelFmt:Ce=void 0}=e,Fe;Ce&&(Fe=It(Ce));let{y2LabelFmt:we=void 0}=e,Ee;we&&(Ee=It(we));let{y2SeriesType:Xe=void 0}=e,{showAllLabels:We=!1}=e,{handleMissing:ke="gap"}=e,{step:De=!1}=e,{stepPosition:Qe="end"}=e,{seriesOrder:Ie=void 0}=e,{seriesLabelFmt:Pe=void 0}=e;const qe={above:"top",below:"bottom",middle:"inside"},Se={above:"right",below:"left",middle:"inside"};let pe=a?"right":"top";return In(()=>{b.update(R=>{if(a)R.yAxis={...R.yAxis,...w.xAxis},R.xAxis={...R.xAxis,...w.yAxis};else if(R.yAxis[0]={...R.yAxis[0],...w.yAxis},R.xAxis={...R.xAxis,...w.xAxis},Y&&(R.yAxis[1]={...R.yAxis[1],show:!0},["line","bar","scatter"].includes(Xe)))for(let ye=0;ye<c;ye++)R.series[u+ye].type=Xe;return re&&(R.axisPointer={triggerEmphasis:!1}),R})}),i.$$set=R=>{"y"in R&&t(3,G=R.y),"y2"in R&&t(4,Y=R.y2),"series"in R&&t(5,p=R.series),"options"in R&&t(12,j=R.options),"name"in R&&t(6,ee=R.name),"lineColor"in R&&t(13,se=R.lineColor),"lineWidth"in R&&t(14,Z=R.lineWidth),"lineType"in R&&t(15,te=R.lineType),"lineOpacity"in R&&t(16,me=R.lineOpacity),"markers"in R&&t(7,he=R.markers),"markerShape"in R&&t(17,ge=R.markerShape),"markerSize"in R&&t(18,$=R.markerSize),"labels"in R&&t(8,re=R.labels),"labelSize"in R&&t(19,Be=R.labelSize),"labelPosition"in R&&t(9,Me=R.labelPosition),"labelColor"in R&&t(20,de=R.labelColor),"labelFmt"in R&&t(21,Ne=R.labelFmt),"yLabelFmt"in R&&t(22,Ce=R.yLabelFmt),"y2LabelFmt"in R&&t(23,we=R.y2LabelFmt),"y2SeriesType"in R&&t(24,Xe=R.y2SeriesType),"showAllLabels"in R&&t(10,We=R.showAllLabels),"handleMissing"in R&&t(25,ke=R.handleMissing),"step"in R&&t(11,De=R.step),"stepPosition"in R&&t(26,Qe=R.stepPosition),"seriesOrder"in R&&t(27,Ie=R.seriesOrder),"seriesLabelFmt"in R&&t(28,Pe=R.seriesLabelFmt)},i.$$.update=()=>{if(i.$$.dirty[0]&8192&&B(t(1,l=X(se))),i.$$.dirty[0]&128&&t(7,he=Ve(he)),i.$$.dirty[0]&256&&t(8,re=Ve(re)),i.$$.dirty[0]&1048576&&K(t(0,s=X(de))),i.$$.dirty[0]&1024&&t(10,We=Ve(We)),i.$$.dirty[0]&2048&&t(11,De=Ve(De)),i.$$.dirty[1]&32768&&t(41,n=z.data),i.$$.dirty[1]&32768&&t(40,r=z.x),i.$$.dirty[0]&8|i.$$.dirty[1]&32768&&t(3,G=q?G:z.y),i.$$.dirty[0]&16|i.$$.dirty[1]&32768&&t(4,Y=N?Y:z.y2),i.$$.dirty[1]&32768&&t(34,a=z.swapXY),i.$$.dirty[1]&32768&&t(43,f=z.yFormat),i.$$.dirty[1]&32768&&t(42,o=z.y2Format),i.$$.dirty[1]&32768&&t(32,u=z.yCount),i.$$.dirty[1]&32768&&t(33,c=z.y2Count),i.$$.dirty[1]&32768&&t(35,y=z.xType),i.$$.dirty[1]&32768&&t(38,T=z.xMismatch),i.$$.dirty[1]&32768&&t(37,g=z.columnSummary),i.$$.dirty[0]&32|i.$$.dirty[1]&32768&&t(5,p=ne?p:z.series),i.$$.dirty[0]&104|i.$$.dirty[1]&1600)if(!p&&typeof G!="object")t(6,ee=ee??Wt(G,g[G].title));else try{t(41,n=Fl(n,r,G,p))}catch(R){console.warn("Failed to complete data",{e:R}),t(41,n=[])}if(i.$$.dirty[0]&33554472|i.$$.dirty[1]&1536&&ke==="zero")try{t(41,n=Fl(n,r,G,p,!0))}catch(R){console.warn("Failed to complete data",{e:R}),t(41,n=[])}i.$$.dirty[0]&512|i.$$.dirty[1]&8&&t(9,Me=(a?Se[Me]:qe[Me])??pe),i.$$.dirty[0]&1712312192|i.$$.dirty[1]&30735&&t(39,m={type:"line",label:{show:re,formatter(R){return R.value[a?0:1]===0?"":ct(R.value[a?0:1],[Fe??ze??f,Ee??ze??o][Pi(R.componentIndex,u,c)])},fontSize:Be,color:H,position:Me,padding:3},labelLayout:{hideOverlap:!We},connectNulls:ke==="connect",emphasis:{focus:"series",endLabel:{show:!1},lineStyle:{opacity:1,width:3}},lineStyle:{width:parseInt(Z),type:te,opacity:me},itemStyle:{color:S,opacity:me},showSymbol:re||he,symbol:ge,symbolSize:re&&!he?0:$,step:De?Qe:!1}),i.$$.dirty[0]&402653304|i.$$.dirty[1]&1992&&t(36,L=zn(n,r,G,p,a,m,ee,T,g,Ie,void 0,void 0,Y,Pe)),i.$$.dirty[1]&32&&b.update(R=>(R.series.push(...L),R.legend.data.push(...L.map(ye=>ye.name.toString())),R)),i.$$.dirty[0]&4096&&j&&b.update(R=>({...R,...j})),i.$$.dirty[1]&16&&(w={yAxis:{boundaryGap:["0%","1%"]},xAxis:{boundaryGap:[y==="time"?"2%":"0%","2%"]}})},[s,l,W,G,Y,p,ee,he,re,Me,We,De,j,se,Z,te,me,ge,$,Be,de,Ne,Ce,we,Xe,ke,Qe,Ie,Pe,ze,Fe,Ee,u,c,a,y,L,g,T,m,r,n,o,f,S,H,z]}class Pa extends st{constructor(e){super(),rt(this,e,Fa,null,nt,{y:3,y2:4,series:5,options:12,name:6,lineColor:13,lineWidth:14,lineType:15,lineOpacity:16,markers:7,markerShape:17,markerSize:18,labels:8,labelSize:19,labelPosition:9,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,showAllLabels:10,handleMissing:25,step:11,stepPosition:26,seriesOrder:27,seriesLabelFmt:28},null,[-1,-1])}}function Ra(i){let e,t,l;e=new Pa({props:{lineColor:i[73],lineWidth:i[38],lineOpacity:i[37],lineType:i[36],markers:i[40],markerShape:i[41],markerSize:i[42],handleMissing:i[43],step:i[44],stepPosition:i[45],labels:i[47],labelSize:i[48],labelPosition:i[49],labelColor:i[71],labelFmt:i[50],yLabelFmt:i[51],y2LabelFmt:i[52],showAllLabels:i[53],y2SeriesType:i[8],seriesOrder:i[62],seriesLabelFmt:i[64]}});const s=i[80].default,n=el(s,i,i[81],null);return{c(){ue(e.$$.fragment),t=x(),n&&n.c()},l(r){oe(e.$$.fragment,r),t=J(r),n&&n.l(r)},m(r,a){fe(e,r,a),E(r,t,a),n&&n.m(r,a),l=!0},p(r,a){const f={};a[2]&2048&&(f.lineColor=r[73]),a[1]&128&&(f.lineWidth=r[38]),a[1]&64&&(f.lineOpacity=r[37]),a[1]&32&&(f.lineType=r[36]),a[1]&512&&(f.markers=r[40]),a[1]&1024&&(f.markerShape=r[41]),a[1]&2048&&(f.markerSize=r[42]),a[1]&4096&&(f.handleMissing=r[43]),a[1]&8192&&(f.step=r[44]),a[1]&16384&&(f.stepPosition=r[45]),a[1]&65536&&(f.labels=r[47]),a[1]&131072&&(f.labelSize=r[48]),a[1]&262144&&(f.labelPosition=r[49]),a[2]&512&&(f.labelColor=r[71]),a[1]&524288&&(f.labelFmt=r[50]),a[1]&1048576&&(f.yLabelFmt=r[51]),a[1]&2097152&&(f.y2LabelFmt=r[52]),a[1]&4194304&&(f.showAllLabels=r[53]),a[0]&256&&(f.y2SeriesType=r[8]),a[2]&1&&(f.seriesOrder=r[62]),a[2]&4&&(f.seriesLabelFmt=r[64]),e.$set(f),n&&n.p&&(!l||a[2]&524288)&&tl(n,s,r,r[81],l?il(s,r[81],a,null):ll(r[81]),null)},i(r){l||(O(e.$$.fragment,r),O(n,r),l=!0)},o(r){U(e.$$.fragment,r),U(n,r),l=!1},d(r){r&&d(t),ae(e,r),n&&n.d(r)}}}function Ba(i){let e,t;return e=new Bn({props:{data:i[0],x:i[1],y:i[2],y2:i[3],xFmt:i[10],yFmt:i[9],y2Fmt:i[11],series:i[4],xType:i[5],yLog:i[6],yLogBase:i[7],legend:i[14],xAxisTitle:i[15],yAxisTitle:i[16],y2AxisTitle:i[17],xGridlines:i[18],yGridlines:i[19],y2Gridlines:i[20],xAxisLabels:i[21],yAxisLabels:i[22],y2AxisLabels:i[23],xBaseline:i[24],yBaseline:i[25],y2Baseline:i[26],xTickMarks:i[27],yTickMarks:i[28],y2TickMarks:i[29],yAxisColor:i[70],y2AxisColor:i[69],yMin:i[30],yMax:i[31],yScale:i[32],y2Min:i[33],y2Max:i[34],y2Scale:i[35],title:i[12],subtitle:i[13],chartType:"Line Chart",sort:i[46],chartAreaHeight:i[39],colorPalette:i[72],echartsOptions:i[54],seriesOptions:i[55],printEchartsConfig:i[56],emptySet:i[57],emptyMessage:i[58],renderer:i[59],downloadableData:i[60],downloadableImage:i[61],connectGroup:i[63],seriesColors:i[68],leftPadding:i[65],rightPadding:i[66],xLabelWrap:i[67],$$slots:{default:[Ra]},$$scope:{ctx:i}}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s[0]&1&&(n.data=l[0]),s[0]&2&&(n.x=l[1]),s[0]&4&&(n.y=l[2]),s[0]&8&&(n.y2=l[3]),s[0]&1024&&(n.xFmt=l[10]),s[0]&512&&(n.yFmt=l[9]),s[0]&2048&&(n.y2Fmt=l[11]),s[0]&16&&(n.series=l[4]),s[0]&32&&(n.xType=l[5]),s[0]&64&&(n.yLog=l[6]),s[0]&128&&(n.yLogBase=l[7]),s[0]&16384&&(n.legend=l[14]),s[0]&32768&&(n.xAxisTitle=l[15]),s[0]&65536&&(n.yAxisTitle=l[16]),s[0]&131072&&(n.y2AxisTitle=l[17]),s[0]&262144&&(n.xGridlines=l[18]),s[0]&524288&&(n.yGridlines=l[19]),s[0]&1048576&&(n.y2Gridlines=l[20]),s[0]&2097152&&(n.xAxisLabels=l[21]),s[0]&4194304&&(n.yAxisLabels=l[22]),s[0]&8388608&&(n.y2AxisLabels=l[23]),s[0]&16777216&&(n.xBaseline=l[24]),s[0]&33554432&&(n.yBaseline=l[25]),s[0]&67108864&&(n.y2Baseline=l[26]),s[0]&134217728&&(n.xTickMarks=l[27]),s[0]&268435456&&(n.yTickMarks=l[28]),s[0]&536870912&&(n.y2TickMarks=l[29]),s[2]&256&&(n.yAxisColor=l[70]),s[2]&128&&(n.y2AxisColor=l[69]),s[0]&1073741824&&(n.yMin=l[30]),s[1]&1&&(n.yMax=l[31]),s[1]&2&&(n.yScale=l[32]),s[1]&4&&(n.y2Min=l[33]),s[1]&8&&(n.y2Max=l[34]),s[1]&16&&(n.y2Scale=l[35]),s[0]&4096&&(n.title=l[12]),s[0]&8192&&(n.subtitle=l[13]),s[1]&32768&&(n.sort=l[46]),s[1]&256&&(n.chartAreaHeight=l[39]),s[2]&1024&&(n.colorPalette=l[72]),s[1]&8388608&&(n.echartsOptions=l[54]),s[1]&16777216&&(n.seriesOptions=l[55]),s[1]&33554432&&(n.printEchartsConfig=l[56]),s[1]&67108864&&(n.emptySet=l[57]),s[1]&134217728&&(n.emptyMessage=l[58]),s[1]&268435456&&(n.renderer=l[59]),s[1]&536870912&&(n.downloadableData=l[60]),s[1]&1073741824&&(n.downloadableImage=l[61]),s[2]&2&&(n.connectGroup=l[63]),s[2]&64&&(n.seriesColors=l[68]),s[2]&8&&(n.leftPadding=l[65]),s[2]&16&&(n.rightPadding=l[66]),s[2]&32&&(n.xLabelWrap=l[67]),s[0]&256|s[1]&8355552|s[2]&526853&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function za(i,e,t){let l,s,n,r,a,f,{$$slots:o={},$$scope:u}=e;const{resolveColor:c,resolveColorsObject:y,resolveColorPalette:T}=Bt();let{data:g=void 0}=e,{x:m=void 0}=e,{y:L=void 0}=e,{y2:w=void 0}=e,{series:S=void 0}=e,{xType:A=void 0}=e,{yLog:B=void 0}=e,{yLogBase:H=void 0}=e,{y2SeriesType:V=void 0}=e,{yFmt:K=void 0}=e,{xFmt:z=void 0}=e,{y2Fmt:W=void 0}=e,{title:b=void 0}=e,{subtitle:X=void 0}=e,{legend:G=void 0}=e,{xAxisTitle:q=void 0}=e,{yAxisTitle:Y=w?"true":void 0}=e,{y2AxisTitle:N=w?"true":void 0}=e,{xGridlines:p=void 0}=e,{yGridlines:ne=void 0}=e,{y2Gridlines:j=void 0}=e,{xAxisLabels:ee=void 0}=e,{yAxisLabels:se=void 0}=e,{y2AxisLabels:Z=void 0}=e,{xBaseline:te=void 0}=e,{yBaseline:me=void 0}=e,{y2Baseline:he=void 0}=e,{xTickMarks:ge=void 0}=e,{yTickMarks:$=void 0}=e,{y2TickMarks:re=void 0}=e,{yMin:Be=void 0}=e,{yMax:Me=void 0}=e,{yScale:de=void 0}=e,{y2Min:Ne=void 0}=e,{y2Max:ze=void 0}=e,{y2Scale:Ce=void 0}=e,{lineColor:Fe=void 0}=e,{lineType:we=void 0}=e,{lineOpacity:Ee=void 0}=e,{lineWidth:Xe=void 0}=e,{chartAreaHeight:We=void 0}=e,{markers:ke=void 0}=e,{markerShape:De=void 0}=e,{markerSize:Qe=void 0}=e,{handleMissing:Ie=void 0}=e,{step:Pe=void 0}=e,{stepPosition:qe=void 0}=e,{sort:Se=void 0}=e,{colorPalette:pe="default"}=e,{labels:R=void 0}=e,{labelSize:ye=void 0}=e,{labelPosition:at=void 0}=e,{labelColor:ve=void 0}=e,{labelFmt:je=void 0}=e,{yLabelFmt:bt=void 0}=e,{y2LabelFmt:et=void 0}=e,{showAllLabels:Ge=void 0}=e,{yAxisColor:P=void 0}=e,{y2AxisColor:be=void 0}=e,{echartsOptions:Ke=void 0}=e,{seriesOptions:tt=void 0}=e,{printEchartsConfig:_t=!1}=e,{emptySet:lt=void 0}=e,{emptyMessage:mt=void 0}=e,{renderer:ft=void 0}=e,{downloadableData:At=void 0}=e,{downloadableImage:Ct=void 0}=e,{seriesColors:Re=void 0}=e,{seriesOrder:Tt=void 0}=e,{connectGroup:ut=void 0}=e,{seriesLabelFmt:Lt=void 0}=e,{leftPadding:yt=void 0}=e,{rightPadding:dt=void 0}=e,{xLabelWrap:Ue=void 0}=e;return i.$$set=_=>{"data"in _&&t(0,g=_.data),"x"in _&&t(1,m=_.x),"y"in _&&t(2,L=_.y),"y2"in _&&t(3,w=_.y2),"series"in _&&t(4,S=_.series),"xType"in _&&t(5,A=_.xType),"yLog"in _&&t(6,B=_.yLog),"yLogBase"in _&&t(7,H=_.yLogBase),"y2SeriesType"in _&&t(8,V=_.y2SeriesType),"yFmt"in _&&t(9,K=_.yFmt),"xFmt"in _&&t(10,z=_.xFmt),"y2Fmt"in _&&t(11,W=_.y2Fmt),"title"in _&&t(12,b=_.title),"subtitle"in _&&t(13,X=_.subtitle),"legend"in _&&t(14,G=_.legend),"xAxisTitle"in _&&t(15,q=_.xAxisTitle),"yAxisTitle"in _&&t(16,Y=_.yAxisTitle),"y2AxisTitle"in _&&t(17,N=_.y2AxisTitle),"xGridlines"in _&&t(18,p=_.xGridlines),"yGridlines"in _&&t(19,ne=_.yGridlines),"y2Gridlines"in _&&t(20,j=_.y2Gridlines),"xAxisLabels"in _&&t(21,ee=_.xAxisLabels),"yAxisLabels"in _&&t(22,se=_.yAxisLabels),"y2AxisLabels"in _&&t(23,Z=_.y2AxisLabels),"xBaseline"in _&&t(24,te=_.xBaseline),"yBaseline"in _&&t(25,me=_.yBaseline),"y2Baseline"in _&&t(26,he=_.y2Baseline),"xTickMarks"in _&&t(27,ge=_.xTickMarks),"yTickMarks"in _&&t(28,$=_.yTickMarks),"y2TickMarks"in _&&t(29,re=_.y2TickMarks),"yMin"in _&&t(30,Be=_.yMin),"yMax"in _&&t(31,Me=_.yMax),"yScale"in _&&t(32,de=_.yScale),"y2Min"in _&&t(33,Ne=_.y2Min),"y2Max"in _&&t(34,ze=_.y2Max),"y2Scale"in _&&t(35,Ce=_.y2Scale),"lineColor"in _&&t(74,Fe=_.lineColor),"lineType"in _&&t(36,we=_.lineType),"lineOpacity"in _&&t(37,Ee=_.lineOpacity),"lineWidth"in _&&t(38,Xe=_.lineWidth),"chartAreaHeight"in _&&t(39,We=_.chartAreaHeight),"markers"in _&&t(40,ke=_.markers),"markerShape"in _&&t(41,De=_.markerShape),"markerSize"in _&&t(42,Qe=_.markerSize),"handleMissing"in _&&t(43,Ie=_.handleMissing),"step"in _&&t(44,Pe=_.step),"stepPosition"in _&&t(45,qe=_.stepPosition),"sort"in _&&t(46,Se=_.sort),"colorPalette"in _&&t(75,pe=_.colorPalette),"labels"in _&&t(47,R=_.labels),"labelSize"in _&&t(48,ye=_.labelSize),"labelPosition"in _&&t(49,at=_.labelPosition),"labelColor"in _&&t(76,ve=_.labelColor),"labelFmt"in _&&t(50,je=_.labelFmt),"yLabelFmt"in _&&t(51,bt=_.yLabelFmt),"y2LabelFmt"in _&&t(52,et=_.y2LabelFmt),"showAllLabels"in _&&t(53,Ge=_.showAllLabels),"yAxisColor"in _&&t(77,P=_.yAxisColor),"y2AxisColor"in _&&t(78,be=_.y2AxisColor),"echartsOptions"in _&&t(54,Ke=_.echartsOptions),"seriesOptions"in _&&t(55,tt=_.seriesOptions),"printEchartsConfig"in _&&t(56,_t=_.printEchartsConfig),"emptySet"in _&&t(57,lt=_.emptySet),"emptyMessage"in _&&t(58,mt=_.emptyMessage),"renderer"in _&&t(59,ft=_.renderer),"downloadableData"in _&&t(60,At=_.downloadableData),"downloadableImage"in _&&t(61,Ct=_.downloadableImage),"seriesColors"in _&&t(79,Re=_.seriesColors),"seriesOrder"in _&&t(62,Tt=_.seriesOrder),"connectGroup"in _&&t(63,ut=_.connectGroup),"seriesLabelFmt"in _&&t(64,Lt=_.seriesLabelFmt),"leftPadding"in _&&t(65,yt=_.leftPadding),"rightPadding"in _&&t(66,dt=_.rightPadding),"xLabelWrap"in _&&t(67,Ue=_.xLabelWrap),"$$scope"in _&&t(81,u=_.$$scope)},i.$$.update=()=>{i.$$.dirty[2]&4096&&t(73,l=c(Fe)),i.$$.dirty[2]&8192&&t(72,s=T(pe)),i.$$.dirty[2]&16384&&t(71,n=c(ve)),i.$$.dirty[2]&32768&&t(70,r=c(P)),i.$$.dirty[2]&65536&&t(69,a=c(be)),i.$$.dirty[2]&131072&&t(68,f=y(Re))},[g,m,L,w,S,A,B,H,V,K,z,W,b,X,G,q,Y,N,p,ne,j,ee,se,Z,te,me,he,ge,$,re,Be,Me,de,Ne,ze,Ce,we,Ee,Xe,We,ke,De,Qe,Ie,Pe,qe,Se,R,ye,at,je,bt,et,Ge,Ke,tt,_t,lt,mt,ft,At,Ct,Tt,ut,Lt,yt,dt,Ue,f,a,r,n,s,l,Fe,pe,ve,P,be,Re,o,u]}class Ga extends st{constructor(e){super(),rt(this,e,za,Ba,nt,{data:0,x:1,y:2,y2:3,series:4,xType:5,yLog:6,yLogBase:7,y2SeriesType:8,yFmt:9,xFmt:10,y2Fmt:11,title:12,subtitle:13,legend:14,xAxisTitle:15,yAxisTitle:16,y2AxisTitle:17,xGridlines:18,yGridlines:19,y2Gridlines:20,xAxisLabels:21,yAxisLabels:22,y2AxisLabels:23,xBaseline:24,yBaseline:25,y2Baseline:26,xTickMarks:27,yTickMarks:28,y2TickMarks:29,yMin:30,yMax:31,yScale:32,y2Min:33,y2Max:34,y2Scale:35,lineColor:74,lineType:36,lineOpacity:37,lineWidth:38,chartAreaHeight:39,markers:40,markerShape:41,markerSize:42,handleMissing:43,step:44,stepPosition:45,sort:46,colorPalette:75,labels:47,labelSize:48,labelPosition:49,labelColor:76,labelFmt:50,yLabelFmt:51,y2LabelFmt:52,showAllLabels:53,yAxisColor:77,y2AxisColor:78,echartsOptions:54,seriesOptions:55,printEchartsConfig:56,emptySet:57,emptyMessage:58,renderer:59,downloadableData:60,downloadableImage:61,seriesColors:79,seriesOrder:62,connectGroup:63,seriesLabelFmt:64,leftPadding:65,rightPadding:66,xLabelWrap:67},null,[-1,-1,-1])}}function Ua(i){let e,t=Ae.title+"",l;return{c(){e=D("h1"),l=Le(t),this.h()},l(s){e=M(s,"H1",{class:!0});var n=Q(e);l=Te(n,t),n.forEach(d),this.h()},h(){h(e,"class","title")},m(s,n){E(s,e,n),I(e,l)},p:ce,d(s){s&&d(e)}}}function Ha(i){return{c(){this.h()},l(e){this.h()},h(){document.title="Evidence"},m:ce,p:ce,d:ce}}function Va(i){let e,t,l,s,n;return document.title=e=Ae.title,{c(){t=x(),l=D("meta"),s=x(),n=D("meta"),this.h()},l(r){t=J(r),l=M(r,"META",{property:!0,content:!0}),s=J(r),n=M(r,"META",{name:!0,content:!0}),this.h()},h(){var r,a;h(l,"property","og:title"),h(l,"content",((r=Ae.og)==null?void 0:r.title)??Ae.title),h(n,"name","twitter:title"),h(n,"content",((a=Ae.og)==null?void 0:a.title)??Ae.title)},m(r,a){E(r,t,a),E(r,l,a),E(r,s,a),E(r,n,a)},p(r,a){a&0&&e!==(e=Ae.title)&&(document.title=e)},d(r){r&&(d(t),d(l),d(s),d(n))}}}function Wa(i){var n,r;let e,t,l=(Ae.description||((n=Ae.og)==null?void 0:n.description))&&qa(),s=((r=Ae.og)==null?void 0:r.image)&&va();return{c(){l&&l.c(),e=x(),s&&s.c(),t=$e()},l(a){l&&l.l(a),e=J(a),s&&s.l(a),t=$e()},m(a,f){l&&l.m(a,f),E(a,e,f),s&&s.m(a,f),E(a,t,f)},p(a,f){var o,u;(Ae.description||(o=Ae.og)!=null&&o.description)&&l.p(a,f),(u=Ae.og)!=null&&u.image&&s.p(a,f)},d(a){a&&(d(e),d(t)),l&&l.d(a),s&&s.d(a)}}}function qa(i){let e,t,l,s,n;return{c(){e=D("meta"),t=x(),l=D("meta"),s=x(),n=D("meta"),this.h()},l(r){e=M(r,"META",{name:!0,content:!0}),t=J(r),l=M(r,"META",{property:!0,content:!0}),s=J(r),n=M(r,"META",{name:!0,content:!0}),this.h()},h(){var r,a,f;h(e,"name","description"),h(e,"content",Ae.description??((r=Ae.og)==null?void 0:r.description)),h(l,"property","og:description"),h(l,"content",((a=Ae.og)==null?void 0:a.description)??Ae.description),h(n,"name","twitter:description"),h(n,"content",((f=Ae.og)==null?void 0:f.description)??Ae.description)},m(r,a){E(r,e,a),E(r,t,a),E(r,l,a),E(r,s,a),E(r,n,a)},p:ce,d(r){r&&(d(e),d(t),d(l),d(s),d(n))}}}function va(i){let e,t,l;return{c(){e=D("meta"),t=x(),l=D("meta"),this.h()},l(s){e=M(s,"META",{property:!0,content:!0}),t=J(s),l=M(s,"META",{name:!0,content:!0}),this.h()},h(){var s,n;h(e,"property","og:image"),h(e,"content",Ll((s=Ae.og)==null?void 0:s.image)),h(l,"name","twitter:image"),h(l,"content",Ll((n=Ae.og)==null?void 0:n.image))},m(s,n){E(s,e,n),E(s,t,n),E(s,l,n)},p:ce,d(s){s&&(d(e),d(t),d(l))}}}function On(i){let e,t;return e=new er({props:{queryID:"sample_sales",queryResult:i[0]}}),{c(){ue(e.$$.fragment)},l(l){oe(e.$$.fragment,l)},m(l,s){fe(e,l,s),t=!0},p(l,s){const n={};s&1&&(n.queryResult=l[0]),e.$set(n)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function ja(i){let e,t,l,s,n,r,a='<a href="#domo-data-loader">Domo Data Loader</a>',f,o,u="Load datasets from Domo into DuckDB for analysis with Evidence.",c,y,T='<h3 class="svelte-16omzez">🚀 Enhanced Domo DDX Integration</h3> <p>This app now supports real DuckDB integration, multiple dataset loading, iframe compatibility, and data visualizations!</p>',g,m,L,w,S,A="Select Dataset:",B,H,V,K="Choose a dataset...",z,W,b='<h4>📊 Dataset Information</h4> <div id="preview-content" class="preview-content"><div id="dataset-info" class="dataset-info-grid"></div> <div class="dataset-tabs"><button class="tab-button active" data-tab="schema">Schema</button> <button class="tab-button" data-tab="sample">Sample Data</button> <button class="tab-button" data-tab="metadata">Metadata</button></div> <div id="schema-tab" class="tab-content active"><div id="schema-table" class="schema-table"></div></div> <div id="sample-tab" class="tab-content"><div class="preview-actions svelte-16omzez"><button id="preview-btn" class="btn btn-secondary svelte-16omzez">🔍 Load Sample Data</button> <span class="preview-note">Shows first 10 rows</span></div> <div id="data-preview" class="data-preview svelte-16omzez" style="display: none;"></div></div> <div id="metadata-tab" class="tab-content"><div id="dataset-metadata" class="dataset-metadata"></div></div></div>',X,G,q,Y="⚙️ Loading Configuration",N,p,ne,j='<label for="table-name" class="svelte-16omzez">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-16omzez"/> <small class="field-help">Use lowercase letters, numbers, and underscores only</small>',ee,se,Z,te="Refresh Mode:",me,he,ge,$="Replace existing data",re,Be="Append to existing data",Me,de,Ne="Choose how to handle existing data",ze,Ce,Fe='<label for="row-limit" class="svelte-16omzez">Row Limit (optional):</label> <input id="row-limit" type="number" placeholder="Leave empty for all rows" min="1" max="1000000" class="svelte-16omzez"/> <small class="field-help">Limit rows for testing (max 1M)</small>',we,Ee,Xe=`<label class="svelte-16omzez"><input type="checkbox" id="create-index" checked="" class="svelte-16omzez"/>
            Create indexes for better performance</label>`,We,ke,De='<div class="action-buttons"><button id="validate-config-btn" class="btn btn-secondary svelte-16omzez">✅ Validate Configuration</button> <button id="load-dataset-btn" class="btn btn-primary svelte-16omzez">📊 Load Dataset into DuckDB</button></div> <div id="validation-results" class="validation-results" style="display: none;"></div>',Qe,Ie,Pe='<div class="loading-spinner svelte-16omzez"></div> <p id="loading-message" class="svelte-16omzez">Loading...</p> <div class="progress-bar"><div class="progress-fill" id="progress-fill"></div></div>',qe,Se,pe='<h2 class="markdown">📚 Loaded Datasets</h2> <div class="loaded-datasets-header"><p>Datasets currently available in DuckDB for analysis:</p> <button id="refresh-loaded-btn" class="btn btn-secondary btn-small svelte-16omzez">🔄 Refresh</button></div> <div id="loaded-datasets-list" class="loaded-datasets-grid"></div>',R,ye,at='<a href="#sample-evidence-visualizations">Sample Evidence Visualizations</a>',ve,je,bt="Once you load data, you can create Evidence-style visualizations. Here are some examples:",et,Ge,P='<a href="#sales-performance-dashboard">Sales Performance Dashboard</a>',be,Ke,tt,_t,lt,mt,ft,At='<a href="#key-metrics">Key Metrics</a>',Ct,Re,Tt,ut,Lt,yt,dt,Ue,_='<strong class="markdown">Next Steps:</strong>',C,wt,kl='<li class="markdown">Select a dataset from the dropdown above</li> <li class="markdown">Configure loading options</li> <li class="markdown">Load the data into DuckDB</li> <li class="markdown">Run SQL queries to explore your data</li> <li class="markdown">Create Evidence visualizations with your results</li>',Ut,Ot=typeof Ae<"u"&&Ae.title&&Ae.hide_title!==!0&&Ua();function jt(F,ie){return typeof Ae<"u"&&Ae.title?Va:Ha}let _e=jt()(i),kt=typeof Ae=="object"&&Wa(),He=i[0]&&On(i);return tt=new Yr({props:{data:i[0],x:"product",y:"revenue",title:"Revenue by Product"}}),lt=new Ga({props:{data:i[0],x:"month",y:"units_sold",series:"product",title:"Units Sold Trend"}}),Re=new wn({props:{data:i[0],value:"revenue",title:"Total Revenue",fmt:"$#,##0"}}),ut=new wn({props:{data:i[0],value:"units_sold",title:"Total Units",fmt:"#,##0"}}),{c(){Ot&&Ot.c(),e=x(),_e.c(),t=D("meta"),l=D("meta"),kt&&kt.c(),s=$e(),n=x(),r=D("h1"),r.innerHTML=a,f=x(),o=D("p"),o.textContent=u,c=x(),y=D("div"),y.innerHTML=T,g=x(),m=D("div"),L=D("div"),w=D("div"),S=D("label"),S.textContent=A,B=x(),H=D("select"),V=D("option"),V.textContent=K,z=x(),W=D("div"),W.innerHTML=b,X=x(),G=D("div"),q=D("h4"),q.textContent=Y,N=x(),p=D("div"),ne=D("div"),ne.innerHTML=j,ee=x(),se=D("div"),Z=D("label"),Z.textContent=te,me=x(),he=D("select"),ge=D("option"),ge.textContent=$,re=D("option"),re.textContent=Be,Me=x(),de=D("small"),de.textContent=Ne,ze=x(),Ce=D("div"),Ce.innerHTML=Fe,we=x(),Ee=D("div"),Ee.innerHTML=Xe,We=x(),ke=D("div"),ke.innerHTML=De,Qe=x(),Ie=D("div"),Ie.innerHTML=Pe,qe=x(),Se=D("div"),Se.innerHTML=pe,R=x(),ye=D("h2"),ye.innerHTML=at,ve=x(),je=D("p"),je.textContent=bt,et=x(),Ge=D("h3"),Ge.innerHTML=P,be=x(),He&&He.c(),Ke=x(),ue(tt.$$.fragment),_t=x(),ue(lt.$$.fragment),mt=x(),ft=D("h3"),ft.innerHTML=At,Ct=x(),ue(Re.$$.fragment),Tt=x(),ue(ut.$$.fragment),Lt=x(),yt=D("hr"),dt=x(),Ue=D("p"),Ue.innerHTML=_,C=x(),wt=D("ol"),wt.innerHTML=kl,this.h()},l(F){Ot&&Ot.l(F),e=J(F);const ie=Xn("svelte-2igo1p",document.head);_e.l(ie),t=M(ie,"META",{name:!0,content:!0}),l=M(ie,"META",{name:!0,content:!0}),kt&&kt.l(ie),s=$e(),ie.forEach(d),n=J(F),r=M(F,"H1",{class:!0,id:!0,"data-svelte-h":!0}),Oe(r)!=="svelte-94lco6"&&(r.innerHTML=a),f=J(F),o=M(F,"P",{class:!0,"data-svelte-h":!0}),Oe(o)!=="svelte-1s6ws79"&&(o.textContent=u),c=J(F),y=M(F,"DIV",{class:!0,"data-svelte-h":!0}),Oe(y)!=="svelte-1top7fi"&&(y.innerHTML=T),g=J(F),m=M(F,"DIV",{class:!0});var Nt=Q(m);L=M(Nt,"DIV",{id:!0,class:!0});var Ye=Q(L);w=M(Ye,"DIV",{class:!0});var Ft=Q(w);S=M(Ft,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),Oe(S)!=="svelte-1fci9ty"&&(S.textContent=A),B=J(Ft),H=M(Ft,"SELECT",{id:!0,class:!0});var ht=Q(H);V=M(ht,"OPTION",{"data-svelte-h":!0}),Oe(V)!=="svelte-59d9xk"&&(V.textContent=K),ht.forEach(d),Ft.forEach(d),z=J(Ye),W=M(Ye,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),Oe(W)!=="svelte-19tcyh0"&&(W.innerHTML=b),X=J(Ye),G=M(Ye,"DIV",{id:!0,class:!0,style:!0});var it=Q(G);q=M(it,"H4",{"data-svelte-h":!0}),Oe(q)!=="svelte-6xztzo"&&(q.textContent=Y),N=J(it),p=M(it,"DIV",{class:!0});var ot=Q(p);ne=M(ot,"DIV",{class:!0,"data-svelte-h":!0}),Oe(ne)!=="svelte-199xsoc"&&(ne.innerHTML=j),ee=J(ot),se=M(ot,"DIV",{class:!0});var Pt=Q(se);Z=M(Pt,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),Oe(Z)!=="svelte-p1qydn"&&(Z.textContent=te),me=J(Pt),he=M(Pt,"SELECT",{id:!0,class:!0});var Yt=Q(he);ge=M(Yt,"OPTION",{"data-svelte-h":!0}),Oe(ge)!=="svelte-qvzdub"&&(ge.textContent=$),re=M(Yt,"OPTION",{"data-svelte-h":!0}),Oe(re)!=="svelte-idsvi6"&&(re.textContent=Be),Yt.forEach(d),Me=J(Pt),de=M(Pt,"SMALL",{class:!0,"data-svelte-h":!0}),Oe(de)!=="svelte-fpi9b6"&&(de.textContent=Ne),Pt.forEach(d),ze=J(ot),Ce=M(ot,"DIV",{class:!0,"data-svelte-h":!0}),Oe(Ce)!=="svelte-15v44ck"&&(Ce.innerHTML=Fe),we=J(ot),Ee=M(ot,"DIV",{class:!0,"data-svelte-h":!0}),Oe(Ee)!=="svelte-5ivc8q"&&(Ee.innerHTML=Xe),ot.forEach(d),it.forEach(d),We=J(Ye),ke=M(Ye,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),Oe(ke)!=="svelte-68tw8y"&&(ke.innerHTML=De),Qe=J(Ye),Ie=M(Ye,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),Oe(Ie)!=="svelte-1oq3jl0"&&(Ie.innerHTML=Pe),Ye.forEach(d),Nt.forEach(d),qe=J(F),Se=M(F,"DIV",{id:!0,style:!0,"data-svelte-h":!0}),Oe(Se)!=="svelte-n8w8em"&&(Se.innerHTML=pe),R=J(F),ye=M(F,"H2",{class:!0,id:!0,"data-svelte-h":!0}),Oe(ye)!=="svelte-1t5nmyw"&&(ye.innerHTML=at),ve=J(F),je=M(F,"P",{class:!0,"data-svelte-h":!0}),Oe(je)!=="svelte-nbtkwk"&&(je.textContent=bt),et=J(F),Ge=M(F,"H3",{class:!0,id:!0,"data-svelte-h":!0}),Oe(Ge)!=="svelte-bguwp4"&&(Ge.innerHTML=P),be=J(F),He&&He.l(F),Ke=J(F),oe(tt.$$.fragment,F),_t=J(F),oe(lt.$$.fragment,F),mt=J(F),ft=M(F,"H3",{class:!0,id:!0,"data-svelte-h":!0}),Oe(ft)!=="svelte-aivela"&&(ft.innerHTML=At),Ct=J(F),oe(Re.$$.fragment,F),Tt=J(F),oe(ut.$$.fragment,F),Lt=J(F),yt=M(F,"HR",{class:!0}),dt=J(F),Ue=M(F,"P",{class:!0,"data-svelte-h":!0}),Oe(Ue)!=="svelte-1mg5bp7"&&(Ue.innerHTML=_),C=J(F),wt=M(F,"OL",{class:!0,"data-svelte-h":!0}),Oe(wt)!=="svelte-1ei1c0u"&&(wt.innerHTML=kl),this.h()},h(){h(t,"name","twitter:card"),h(t,"content","summary_large_image"),h(l,"name","twitter:site"),h(l,"content","@evidence_dev"),h(r,"class","markdown"),h(r,"id","domo-data-loader"),h(o,"class","markdown"),h(y,"class","dev-banner svelte-16omzez"),h(S,"for","dataset-selector"),h(S,"class","svelte-16omzez"),V.__value="",Dl(V,V.__value),h(H,"id","dataset-selector"),h(H,"class","dataset-dropdown svelte-16omzez"),h(w,"class","workflow-step svelte-16omzez"),h(W,"id","dataset-preview"),h(W,"class","dataset-preview svelte-16omzez"),v(W,"display","none"),h(ne,"class","config-item"),h(Z,"for","refresh-mode"),h(Z,"class","svelte-16omzez"),ge.__value="replace",Dl(ge,ge.__value),re.__value="append",Dl(re,re.__value),h(he,"id","refresh-mode"),h(he,"class","svelte-16omzez"),h(de,"class","field-help"),h(se,"class","config-item"),h(Ce,"class","config-item"),h(Ee,"class","config-item"),h(p,"class","config-grid svelte-16omzez"),h(G,"id","loading-config"),h(G,"class","workflow-step svelte-16omzez"),v(G,"display","none"),h(ke,"id","workflow-actions"),h(ke,"class","workflow-actions svelte-16omzez"),v(ke,"display","none"),h(Ie,"id","loading-status"),h(Ie,"class","loading-status svelte-16omzez"),v(Ie,"display","none"),h(L,"id","domo-workflow-picker"),h(L,"class","workflow-picker svelte-16omzez"),h(m,"class","workflow-picker-section svelte-16omzez"),h(Se,"id","loaded-datasets-section"),v(Se,"display","none"),h(ye,"class","markdown"),h(ye,"id","sample-evidence-visualizations"),h(je,"class","markdown"),h(Ge,"class","markdown"),h(Ge,"id","sales-performance-dashboard"),h(ft,"class","markdown"),h(ft,"id","key-metrics"),h(yt,"class","markdown"),h(Ue,"class","markdown"),h(wt,"class","markdown")},m(F,ie){Ot&&Ot.m(F,ie),E(F,e,ie),_e.m(document.head,null),I(document.head,t),I(document.head,l),kt&&kt.m(document.head,null),I(document.head,s),E(F,n,ie),E(F,r,ie),E(F,f,ie),E(F,o,ie),E(F,c,ie),E(F,y,ie),E(F,g,ie),E(F,m,ie),I(m,L),I(L,w),I(w,S),I(w,B),I(w,H),I(H,V),I(L,z),I(L,W),I(L,X),I(L,G),I(G,q),I(G,N),I(G,p),I(p,ne),I(p,ee),I(p,se),I(se,Z),I(se,me),I(se,he),I(he,ge),I(he,re),I(se,Me),I(se,de),I(p,ze),I(p,Ce),I(p,we),I(p,Ee),I(L,We),I(L,ke),I(L,Qe),I(L,Ie),E(F,qe,ie),E(F,Se,ie),E(F,R,ie),E(F,ye,ie),E(F,ve,ie),E(F,je,ie),E(F,et,ie),E(F,Ge,ie),E(F,be,ie),He&&He.m(F,ie),E(F,Ke,ie),fe(tt,F,ie),E(F,_t,ie),fe(lt,F,ie),E(F,mt,ie),E(F,ft,ie),E(F,Ct,ie),fe(Re,F,ie),E(F,Tt,ie),fe(ut,F,ie),E(F,Lt,ie),E(F,yt,ie),E(F,dt,ie),E(F,Ue,ie),E(F,C,ie),E(F,wt,ie),Ut=!0},p(F,[ie]){typeof Ae<"u"&&Ae.title&&Ae.hide_title!==!0&&Ot.p(F,ie),_e.p(F,ie),typeof Ae=="object"&&kt.p(F,ie),F[0]?He?(He.p(F,ie),ie&1&&O(He,1)):(He=On(F),He.c(),O(He,1),He.m(Ke.parentNode,Ke)):He&&(Je(),U(He,1,1,()=>{He=null}),xe());const Nt={};ie&1&&(Nt.data=F[0]),tt.$set(Nt);const Ye={};ie&1&&(Ye.data=F[0]),lt.$set(Ye);const Ft={};ie&1&&(Ft.data=F[0]),Re.$set(Ft);const ht={};ie&1&&(ht.data=F[0]),ut.$set(ht)},i(F){Ut||(O(He),O(tt.$$.fragment,F),O(lt.$$.fragment,F),O(Re.$$.fragment,F),O(ut.$$.fragment,F),Ut=!0)},o(F){U(He),U(tt.$$.fragment,F),U(lt.$$.fragment,F),U(Re.$$.fragment,F),U(ut.$$.fragment,F),Ut=!1},d(F){F&&(d(e),d(n),d(r),d(f),d(o),d(c),d(y),d(g),d(m),d(qe),d(Se),d(R),d(ye),d(ve),d(je),d(et),d(Ge),d(be),d(Ke),d(_t),d(mt),d(ft),d(Ct),d(Tt),d(Lt),d(yt),d(dt),d(Ue),d(C),d(wt)),Ot&&Ot.d(F),_e.d(F),d(t),d(l),kt&&kt.d(F),d(s),He&&He.d(F),ae(tt,F),ae(lt,F),ae(Re,F),ae(ut,F)}}}const Ae={title:"Domo Data Loader"};function Ya(i,e,t){let l,s;Dt(i,Fn,S=>t(7,l=S)),Dt(i,Xi,S=>t(13,s=S));let{data:n}=e,{data:r={},customFormattingSettings:a,__db:f,inputs:o}=n;Ti(Xi,s="6666cd76f96956469e7be39d750cc7d9",s);let u=hs(Ei(o));Qn(u.subscribe(S=>o=S)),Li(bs,{getCustomFormats:()=>a.customFormats||[]});const c=(S,A)=>_s(f.query,S,{query_name:A});ys(c),l.params,Kn(()=>!0);let y={initialData:void 0,initialError:void 0},T=Qi`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`,g=`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`;r.sample_sales_data&&(r.sample_sales_data instanceof Error?y.initialError=r.sample_sales_data:y.initialData=r.sample_sales_data,r.sample_sales_columns&&(y.knownColumns=r.sample_sales_columns));let m,L=!1;const w=Mt.createReactive({callback:S=>{t(0,m=S)},execFn:c},{id:"sample_sales",...y});return w(g,{noResolve:T,...y}),globalThis[Symbol.for("sample_sales")]={get value(){return m}},i.$$set=S=>{"data"in S&&t(1,n=S.data)},i.$$.update=()=>{i.$$.dirty&2&&t(2,{data:r={},customFormattingSettings:a,__db:f}=n,r),i.$$.dirty&4&&gs.set(Object.keys(r).length>0),i.$$.dirty&128&&l.params,i.$$.dirty&120&&(T||!L?T||(w(g,{noResolve:T,...y}),t(6,L=!0)):w(g,{noResolve:T}))},t(4,T=Qi`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`),t(5,g=`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`),[m,n,r,y,T,g,L,l]}class $a extends st{constructor(e){super(),rt(this,e,Ya,ja,nt,{data:1})}}export{$a as component};

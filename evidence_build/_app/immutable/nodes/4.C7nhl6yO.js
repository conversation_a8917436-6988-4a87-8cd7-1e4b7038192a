import{s as tt,v as de,d as m,b as h,i as S,e as D,h as N,j as K,a7 as Ll,m as F,a8 as kl,C as Tt,a9 as Zl,p as ll,l as yt,N as v,k as Z,n as J,w as Qe,x as Ce,y as Te,z as Vl,aa as Bl,ab as Un,r as pe,ac as Hn,ad as vn,q as Se,t as Nt,J as jt,ae as qn,af as Li,D as Vn,ag as Wn,a1 as Ul,A as il,E as nl,a2 as Wi,ah as jn,a3 as Lt,a4 as Ht,I as Yn,a0 as ki,c as sl,u as rl,g as al,a as ol,ai as In,aj as Xn,ak as Qn,B as Kn}from"../chunks/scheduler.DQwIXrE4.js";import{S as lt,i as it,d as se,t as U,f as Qt,j as wi,a as M,m as re,b as ae,e as oe,g as <PERSON>,c as Ze,k as Zn}from"../chunks/index.BEt_7cXZ.js";import{g as Pt,h as Yt,Y as Oi,e as Kt,Z as Mn,_ as El,$ as Jn,a0 as ut,P as ji,a1 as xn,a2 as Yi,a3 as Hl,a4 as pn,a5 as $n,a6 as Ii,a7 as Mi,a8 as Dn,a9 as es,f as ts,aa as ls,ab as el,ac as is,ad as ns,ae as Ti,af as Si,ag as Ei,V as qe,ah as Al,ai as ss,aj as Ot,ak as Xt,al as Kl,am as Di,an as Ni,ao as Fi,ap as Pi,aq as Jl,ar as It,O as xl,S as pl,as as $l,at as rs,au as as,av as os,aw as fs,X as Nn,ax as us,W as cs,R as Sl,ay as Ol,az as ds,aA as ms,aB as hs,aC as ys,aD as bs,aE as Xi,aF as gs}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.ebVGE6DV.js";import{w as Ai}from"../chunks/entry.DSMgDpC3.js";import{h as Qi,p as _s}from"../chunks/setTrackProxy.DjIbdjlZ.js";import{p as Fn}from"../chunks/stores.DZ5VrdAm.js";function Ki(i){return t=>t.map(l=>{var s;const n={},r=Object.keys(l);for(const a of r){const o=(s=i[a])!=null?s:a;n[o]=l[a]}return n})}function Cs(i,e){if(i.length===0||e.length===0)return{};const t=Object.keys(i[0]),l=Object.keys(e[0]),s={};for(const n of t)l.includes(n)&&(s[n]=n);return s}function Ts(i,e,t){for(const l in t){const s=t[l];if(i[s]!==e[l])return!1}return!0}function Ls(i,e){return l=>{if(!i.length)return l;const s=Cs(l,i),n=Object.keys(i[0]);return l.flatMap(a=>{const o=i.filter(u=>Ts(a,u,s));if(o.length)return o.map(u=>({...a,...u}));const f=Object.fromEntries(n.filter(u=>a[u]==null).map(u=>[u,void 0]));return{...a,...f}})}}function Zi(i){return t=>{const l=t.map(s=>({...s}));for(const s in i){const n=i[s],r=typeof n=="function"?n(l):n,a=r!=null&&r[Symbol.iterator]&&typeof r!="string"?r:t.map(()=>r);let o=-1;for(const f of l)f[s]=a[++o]}return l}}function ks(i){return t=>{const l=Es(i),s=[];for(const n in l){const r=l[n];let a;typeof r=="function"?a=r(t):Array.isArray(r)?a=r:a=Array.from(new Set(t.map(o=>o[n]))),s.push(a.map(o=>({[n]:o})))}return Ss(s)}}function Ss(i){function e(l,s,n){if(!n.length&&s!=null){l.push(s);return}const r=n[0],a=n.slice(1);for(const o of r)e(l,{...s,...o},a)}const t=[];return e(t,null,i),t}function Es(i){if(Array.isArray(i)){const e={};for(const t of i)e[t]=t;return e}else if(typeof i=="object")return i;return{[i]:i}}function As(i){return t=>{const l=[];for(const s of t){const n={...s};for(const r in i)n[r]==null&&(n[r]=i[r]);l.push(n)}return l}}function Ji(i,e){return l=>{const s=ks(i)(l),n=Ls(l)(s);return e?As(e)(n):n}}function xi(i,e,t){return i==null||e==null?void 0:e===0&&i===0?0:!t&&e===0?void 0:i/e}function pi(i,e,t){const l=typeof i=="function"?i:a=>a[i],s=a=>a[e],{predicate:n,allowDivideByZero:r}={};return n==null?(a,o,f)=>{const u=s(a),c=l(a,o,f);return xi(c,u,r)}:(a,o,f)=>{if(!n(a,o,f))return;const u=s(a),c=l(a,o,f);return xi(c,u,r)}}function ws(i){let e,t,l;return{c(){e=F("span"),t=kl("svg"),l=kl("path"),this.h()},l(s){e=N(s,"SPAN",{"aria-expanded":!0,class:!0});var n=K(e);t=Ll(n,"svg",{viewBox:!0,width:!0,height:!0,class:!0});var r=K(t);l=Ll(r,"path",{fill:!0,"fill-rule":!0,d:!0}),K(l).forEach(m),r.forEach(m),n.forEach(m),this.h()},h(){h(l,"fill",i[3]),h(l,"fill-rule","evenodd"),h(l,"d","M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"),h(t,"viewBox","0 0 16 16"),h(t,"width",i[1]),h(t,"height",i[1]),h(t,"class","svelte-lqleyo"),h(e,"aria-expanded",i[0]),h(e,"class","svelte-lqleyo")},m(s,n){S(s,e,n),D(e,t),D(t,l)},p(s,[n]){n&8&&h(l,"fill",s[3]),n&2&&h(t,"width",s[1]),n&2&&h(t,"height",s[1]),n&1&&h(e,"aria-expanded",s[0])},i:de,o:de,d(s){s&&m(e)}}}function Os(i,e,t){let l,s,n=de,r=()=>(n(),n=Tt(l,c=>t(3,s=c)),l);i.$$.on_destroy.push(()=>n());const{resolveColor:a}=Pt();let{toggled:o=!1}=e,{color:f="base-content"}=e,{size:u=10}=e;return i.$$set=c=>{"toggled"in c&&t(0,o=c.toggled),"color"in c&&t(4,f=c.color),"size"in c&&t(1,u=c.size)},i.$$.update=()=>{i.$$.dirty&16&&r(t(2,l=a(f)))},[o,u,l,s,f]}class Pn extends lt{constructor(e){super(),it(this,e,Os,ws,tt,{toggled:0,color:4,size:1})}}function $i(i,e,t){const l=i.slice();return l[12]=e[t],l[14]=t,l}function en(i,e,t){const l=i.slice();return l[15]=e[t],l[17]=t,l}function tn(i,e,t){const l=i.slice();return l[15]=e[t],l}function ln(i,e,t){const l=i.slice();return l[15]=e[t],l}function nn(i){let e,t=i[15].id+"",l,s,n,r;return{c(){e=F("th"),l=Te(t),this.h()},l(a){e=N(a,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var o=K(e);l=Ce(o,t),o.forEach(m),this.h()},h(){var a,o;h(e,"class",s="py-0 px-2 font-medium "+i[15].type+" svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"evidencetype",n=((a=i[15].evidenceColumnType)==null?void 0:a.evidenceType)||"unavailable"),h(e,"evidencetypefidelity",r=((o=i[15].evidenceColumnType)==null?void 0:o.typeFidelity)||"unavailable")},m(a,o){S(a,e,o),D(e,l)},p(a,o){var f,u;o&8&&t!==(t=a[15].id+"")&&Qe(l,t),o&8&&s!==(s="py-0 px-2 font-medium "+a[15].type+" svelte-ghf30y")&&h(e,"class",s),o&64&&v(e,"width",a[6]+"%"),o&8&&n!==(n=((f=a[15].evidenceColumnType)==null?void 0:f.evidenceType)||"unavailable")&&h(e,"evidencetype",n),o&8&&r!==(r=((u=a[15].evidenceColumnType)==null?void 0:u.typeFidelity)||"unavailable")&&h(e,"evidencetypefidelity",r)},d(a){a&&m(e)}}}function sn(i){let e,t=i[15].type+"",l,s,n,r;return{c(){e=F("th"),l=Te(t),this.h()},l(a){e=N(a,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var o=K(e);l=Ce(o,t),o.forEach(m),this.h()},h(){var a,o;h(e,"class",s=i[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"evidencetype",n=((a=i[15].evidenceColumnType)==null?void 0:a.evidenceType)||"unavailable"),h(e,"evidencetypefidelity",r=((o=i[15].evidenceColumnType)==null?void 0:o.typeFidelity)||"unavailable")},m(a,o){S(a,e,o),D(e,l)},p(a,o){var f,u;o&8&&t!==(t=a[15].type+"")&&Qe(l,t),o&8&&s!==(s=a[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y")&&h(e,"class",s),o&64&&v(e,"width",a[6]+"%"),o&8&&n!==(n=((f=a[15].evidenceColumnType)==null?void 0:f.evidenceType)||"unavailable")&&h(e,"evidencetype",n),o&8&&r!==(r=((u=a[15].evidenceColumnType)==null?void 0:u.typeFidelity)||"unavailable")&&h(e,"evidencetypefidelity",r)},d(a){a&&m(e)}}}function Is(i){let e=(i[2]+i[14]+1).toLocaleString()+"",t;return{c(){t=Te(e)},l(l){t=Ce(l,e)},m(l,s){S(l,t,s)},p(l,s){s&4&&e!==(e=(l[2]+l[14]+1).toLocaleString()+"")&&Qe(t,e)},d(l){l&&m(t)}}}function Ms(i){let e=(i[2]+i[14]+1).toLocaleString()+"",t;return{c(){t=Te(e)},l(l){t=Ce(l,e)},m(l,s){S(l,t,s)},p(l,s){s&4&&e!==(e=(l[2]+l[14]+1).toLocaleString()+"")&&Qe(t,e)},d(l){l&&m(t)}}}function Ds(i){let e,t=(i[12][i[15].id]||"Ø")+"",l;return{c(){e=F("td"),l=Te(t),this.h()},l(s){e=N(s,"TD",{class:!0,style:!0});var n=K(e);l=Ce(n,t),n.forEach(m),this.h()},h(){h(e,"class","other svelte-ghf30y"),v(e,"width",i[6]+"%")},m(s,n){S(s,e,n),D(e,l)},p(s,n){n&40&&t!==(t=(s[12][s[15].id]||"Ø")+"")&&Qe(l,t),n&64&&v(e,"width",s[6]+"%")},d(s){s&&m(e)}}}function Ns(i){let e,t,l=(i[12][i[15].id]??"Ø")+"",s,n;return{c(){e=F("td"),t=F("div"),s=Te(l),this.h()},l(r){e=N(r,"TD",{class:!0,style:!0,title:!0});var a=K(e);t=N(a,"DIV",{class:!0});var o=K(t);s=Ce(o,l),o.forEach(m),a.forEach(m),this.h()},h(){h(t,"class","svelte-ghf30y"),h(e,"class","boolean svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"title",n=i[12][i[15].id])},m(r,a){S(r,e,a),D(e,t),D(t,s)},p(r,a){a&40&&l!==(l=(r[12][r[15].id]??"Ø")+"")&&Qe(s,l),a&64&&v(e,"width",r[6]+"%"),a&40&&n!==(n=r[12][r[15].id])&&h(e,"title",n)},d(r){r&&m(e)}}}function Fs(i){let e,t,l=(i[12][i[15].id]||"Ø")+"",s,n;return{c(){e=F("td"),t=F("div"),s=Te(l),this.h()},l(r){e=N(r,"TD",{class:!0,style:!0,title:!0});var a=K(e);t=N(a,"DIV",{class:!0});var o=K(t);s=Ce(o,l),o.forEach(m),a.forEach(m),this.h()},h(){h(t,"class","svelte-ghf30y"),h(e,"class","string svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"title",n=i[12][i[15].id])},m(r,a){S(r,e,a),D(e,t),D(t,s)},p(r,a){a&40&&l!==(l=(r[12][r[15].id]||"Ø")+"")&&Qe(s,l),a&64&&v(e,"width",r[6]+"%"),a&40&&n!==(n=r[12][r[15].id])&&h(e,"title",n)},d(r){r&&m(e)}}}function Ps(i){let e,t,l=ut(i[12][i[15].id],i[3][i[17]].format,i[3][i[17]].columnUnitSummary)+"",s,n;return{c(){e=F("td"),t=F("div"),s=Te(l),this.h()},l(r){e=N(r,"TD",{class:!0,style:!0,title:!0});var a=K(e);t=N(a,"DIV",{class:!0});var o=K(t);s=Ce(o,l),o.forEach(m),a.forEach(m),this.h()},h(){h(t,"class","svelte-ghf30y"),h(e,"class","string svelte-ghf30y"),v(e,"width",i[6]+"%"),h(e,"title",n=ut(i[12][i[15].id],i[3][i[17]].format,i[3][i[17]].columnUnitSummary))},m(r,a){S(r,e,a),D(e,t),D(t,s)},p(r,a){a&40&&l!==(l=ut(r[12][r[15].id],r[3][r[17]].format,r[3][r[17]].columnUnitSummary)+"")&&Qe(s,l),a&64&&v(e,"width",r[6]+"%"),a&40&&n!==(n=ut(r[12][r[15].id],r[3][r[17]].format,r[3][r[17]].columnUnitSummary))&&h(e,"title",n)},d(r){r&&m(e)}}}function Rs(i){let e,t=ut(i[12][i[15].id],i[3][i[17]].format,i[3][i[17]].columnUnitSummary)+"",l;return{c(){e=F("td"),l=Te(t),this.h()},l(s){e=N(s,"TD",{class:!0,style:!0});var n=K(e);l=Ce(n,t),n.forEach(m),this.h()},h(){h(e,"class","number svelte-ghf30y"),v(e,"width",i[6]+"%")},m(s,n){S(s,e,n),D(e,l)},p(s,n){n&40&&t!==(t=ut(s[12][s[15].id],s[3][s[17]].format,s[3][s[17]].columnUnitSummary)+"")&&Qe(l,t),n&64&&v(e,"width",s[6]+"%")},d(s){s&&m(e)}}}function Bs(i){let e,t="Ø",l,s;return{c(){e=F("td"),l=Te(t),this.h()},l(n){e=N(n,"TD",{class:!0,style:!0});var r=K(e);l=Ce(r,t),r.forEach(m),this.h()},h(){h(e,"class",s="text-base-content-muted "+i[3][i[17]].type+" svelte-ghf30y"),v(e,"width",i[6]+"%")},m(n,r){S(n,e,r),D(e,l)},p(n,r){r&8&&s!==(s="text-base-content-muted "+n[3][n[17]].type+" svelte-ghf30y")&&h(e,"class",s),r&64&&v(e,"width",n[6]+"%")},d(n){n&&m(e)}}}function rn(i){let e;function t(n,r){return n[12][n[15].id]==null?Bs:n[3][n[17]].type==="number"?Rs:n[3][n[17]].type==="date"?Ps:n[3][n[17]].type==="string"?Fs:n[3][n[17]].type==="boolean"?Ns:Ds}let l=t(i),s=l(i);return{c(){s.c(),e=pe()},l(n){s.l(n),e=pe()},m(n,r){s.m(n,r),S(n,e,r)},p(n,r){l===(l=t(n))&&s?s.p(n,r):(s.d(1),s=l(n),s&&(s.c(),s.m(e.parentNode,e)))},d(n){n&&m(e),s.d(n)}}}function an(i){let e,t,l,s;function n(u,c){return u[14]===0?Ms:Is}let a=n(i)(i),o=Yt(i[3]),f=[];for(let u=0;u<o.length;u+=1)f[u]=rn(en(i,o,u));return{c(){e=F("tr"),t=F("td"),a.c(),l=J();for(let u=0;u<f.length;u+=1)f[u].c();s=J(),this.h()},l(u){e=N(u,"TR",{});var c=K(e);t=N(c,"TD",{class:!0,style:!0});var y=K(t);a.l(y),y.forEach(m),l=Z(c);for(let T=0;T<f.length;T+=1)f[T].l(c);s=Z(c),c.forEach(m),this.h()},h(){h(t,"class","index text-base-content-muted svelte-ghf30y"),v(t,"width","10%")},m(u,c){S(u,e,c),D(e,t),a.m(t,null),D(e,l);for(let y=0;y<f.length;y+=1)f[y]&&f[y].m(e,null);D(e,s)},p(u,c){if(a.p(u,c),c&104){o=Yt(u[3]);let y;for(y=0;y<o.length;y+=1){const T=en(u,o,y);f[y]?f[y].p(T,c):(f[y]=rn(T),f[y].c(),f[y].m(e,s))}for(;y<f.length;y+=1)f[y].d(1);f.length=o.length}},d(u){u&&m(e),a.d(),Zl(f,u)}}}function on(i){let e,t,l,s,n=(i[2]+tl).toLocaleString()+"",r,a,o=(i[4]+tl).toLocaleString()+"",f,u,c;return{c(){e=F("div"),t=F("input"),l=J(),s=F("span"),r=Te(n),a=Te(" of "),f=Te(o),this.h()},l(y){e=N(y,"DIV",{class:!0});var T=K(e);t=N(T,"INPUT",{type:!0,max:!0,step:!0,class:!0}),l=Z(T),s=N(T,"SPAN",{class:!0});var b=K(s);r=Ce(b,n),a=Ce(b," of "),f=Ce(b,o),b.forEach(m),T.forEach(m),this.h()},h(){h(t,"type","range"),h(t,"max",i[4]),h(t,"step","1"),h(t,"class","slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"),h(s,"class","text-xs svelte-ghf30y"),h(e,"class","pagination svelte-ghf30y")},m(y,T){S(y,e,T),D(e,t),Bl(t,i[2]),D(e,l),D(e,s),D(s,r),D(s,a),D(s,f),u||(c=[yt(t,"change",i[9]),yt(t,"input",i[9]),yt(t,"input",i[7])],u=!0)},p(y,T){T&16&&h(t,"max",y[4]),T&4&&Bl(t,y[2]),T&4&&n!==(n=(y[2]+tl).toLocaleString()+"")&&Qe(r,n),T&16&&o!==(o=(y[4]+tl).toLocaleString()+"")&&Qe(f,o)},d(y){y&&m(e),u=!1,Vl(c)}}}function zs(i){let e,t,l,s,n,r,a,o,f,u,c,y,T,b,d,L,I,E,w,P,H,V,Q,B,q,g,X=Yt(i[3]),G=[];for(let W=0;W<X.length;W+=1)G[W]=nn(ln(i,X,W));let j=Yt(i[3]),Y=[];for(let W=0;W<j.length;W+=1)Y[W]=sn(tn(i,j,W));let O=Yt(i[5]),$=[];for(let W=0;W<O.length;W+=1)$[W]=an($i(i,O,W));let ce=i[4]>0&&on(i);return V=new Oi({props:{class:"download-button",data:i[1],queryID:i[0],display:!0}}),{c(){e=F("div"),t=F("div"),l=F("table"),s=F("thead"),n=F("tr"),r=F("th"),a=J();for(let W=0;W<G.length;W+=1)G[W].c();o=J(),f=F("tr"),u=J(),c=F("tr"),y=F("th"),T=J();for(let W=0;W<Y.length;W+=1)Y[W].c();b=J(),d=F("tr"),L=J(),I=F("tbody");for(let W=0;W<$.length;W+=1)$[W].c();w=J(),ce&&ce.c(),P=J(),H=F("div"),oe(V.$$.fragment),this.h()},l(W){e=N(W,"DIV",{class:!0});var p=K(e);t=N(p,"DIV",{class:!0});var fe=K(t);l=N(fe,"TABLE",{class:!0});var x=K(l);s=N(x,"THEAD",{});var le=K(s);n=N(le,"TR",{});var ue=K(n);r=N(ue,"TH",{class:!0,style:!0}),K(r).forEach(m),a=Z(ue);for(let ne=0;ne<G.length;ne+=1)G[ne].l(ue);o=Z(ue),ue.forEach(m),f=N(le,"TR",{}),K(f).forEach(m),u=Z(le),c=N(le,"TR",{class:!0});var ye=K(c);y=N(ye,"TH",{class:!0,style:!0}),K(y).forEach(m),T=Z(ye);for(let ne=0;ne<Y.length;ne+=1)Y[ne].l(ye);b=Z(ye),ye.forEach(m),d=N(le,"TR",{}),K(d).forEach(m),le.forEach(m),L=Z(x),I=N(x,"TBODY",{});var Ne=K(I);for(let ne=0;ne<$.length;ne+=1)$[ne].l(Ne);Ne.forEach(m),x.forEach(m),fe.forEach(m),w=Z(p),ce&&ce.l(p),P=Z(p),H=N(p,"DIV",{class:!0});var te=K(H);ae(V.$$.fragment,te),te.forEach(m),p.forEach(m),this.h()},h(){h(r,"class","py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y"),v(r,"width","10%"),h(y,"class","py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y"),v(y,"width","10%"),h(c,"class","type-indicator svelte-ghf30y"),h(l,"class","text-xs svelte-ghf30y"),h(t,"class","scrollbox pretty-scrollbar svelte-ghf30y"),h(H,"class","footer svelte-ghf30y"),h(e,"class","results-pane py-1 svelte-ghf30y")},m(W,p){S(W,e,p),D(e,t),D(t,l),D(l,s),D(s,n),D(n,r),D(n,a);for(let fe=0;fe<G.length;fe+=1)G[fe]&&G[fe].m(n,null);D(n,o),D(s,f),D(s,u),D(s,c),D(c,y),D(c,T);for(let fe=0;fe<Y.length;fe+=1)Y[fe]&&Y[fe].m(c,null);D(c,b),D(s,d),D(l,L),D(l,I);for(let fe=0;fe<$.length;fe+=1)$[fe]&&$[fe].m(I,null);D(e,w),ce&&ce.m(e,null),D(e,P),D(e,H),re(V,H,null),B=!0,q||(g=yt(I,"wheel",i[8]),q=!0)},p(W,[p]){if(p&72){X=Yt(W[3]);let x;for(x=0;x<X.length;x+=1){const le=ln(W,X,x);G[x]?G[x].p(le,p):(G[x]=nn(le),G[x].c(),G[x].m(n,o))}for(;x<G.length;x+=1)G[x].d(1);G.length=X.length}if(p&72){j=Yt(W[3]);let x;for(x=0;x<j.length;x+=1){const le=tn(W,j,x);Y[x]?Y[x].p(le,p):(Y[x]=sn(le),Y[x].c(),Y[x].m(c,b))}for(;x<Y.length;x+=1)Y[x].d(1);Y.length=j.length}if(p&108){O=Yt(W[5]);let x;for(x=0;x<O.length;x+=1){const le=$i(W,O,x);$[x]?$[x].p(le,p):($[x]=an(le),$[x].c(),$[x].m(I,null))}for(;x<$.length;x+=1)$[x].d(1);$.length=O.length}W[4]>0?ce?ce.p(W,p):(ce=on(W),ce.c(),ce.m(e,P)):ce&&(ce.d(1),ce=null);const fe={};p&2&&(fe.data=W[1]),p&1&&(fe.queryID=W[0]),V.$set(fe)},i(W){B||(W&&(E||ll(()=>{E=wi(l,Mn,{}),E.start()})),M(V.$$.fragment,W),W&&ll(()=>{B&&(Q||(Q=Qt(e,Kt,{},!0)),Q.run(1))}),B=!0)},o(W){U(V.$$.fragment,W),W&&(Q||(Q=Qt(e,Kt,{},!1)),Q.run(0)),B=!1},d(W){W&&m(e),Zl(G,W),Zl(Y,W),Zl($,W),ce&&ce.d(),se(V),W&&Q&&Q.end(),q=!1,g()}}}let tl=5;function Gs(i,e,t){let l,s,n,r,{queryID:a}=e,{data:o}=e,f=0,u;function c(){u=o.slice(f,f+tl),t(5,r=u)}const y=Jn(d=>{t(2,f=Math.min(Math.max(0,f+Math.floor(d.deltaY/Math.abs(d.deltaY))),n)),c()},60);function T(d){if(Math.abs(d.deltaX)>=Math.abs(d.deltaY))return;const L=d.deltaY<0&&f===0,I=d.deltaY>0&&f===n;L||I||(d.preventDefault(),y(d))}function b(){f=Un(this.value),t(2,f)}return i.$$set=d=>{"queryID"in d&&t(0,a=d.queryID),"data"in d&&t(1,o=d.data)},i.$$.update=()=>{i.$$.dirty&2&&t(3,l=El(o,"array")),i.$$.dirty&8&&t(6,s=90/(l.length+1)),i.$$.dirty&2&&t(4,n=Math.max(o.length-tl,0)),i.$$.dirty&6&&t(5,r=o.slice(f,f+tl))},[a,o,f,l,n,r,s,c,T,b]}class Us extends lt{constructor(e){super(),it(this,e,Gs,zs,tt,{queryID:0,data:1})}}const fn={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/};function Hs(i){let e,t,l,s,n=ji.highlight(i[0],fn)+"",r;return{c(){e=F("pre"),t=Te("  "),l=F("code"),s=new vn(!1),r=Te(`
`),this.h()},l(a){e=N(a,"PRE",{class:!0});var o=K(e);t=Ce(o,"  "),l=N(o,"CODE",{class:!0});var f=K(l);s=Hn(f,!1),f.forEach(m),r=Ce(o,`
`),o.forEach(m),this.h()},h(){s.a=null,h(l,"class","language-sql svelte-re3fhx"),h(e,"class","text-xs max-h-56 overflow-auto pretty-scrollbar")},m(a,o){S(a,e,o),D(e,t),D(e,l),s.m(n,l),D(e,r)},p(a,[o]){o&1&&n!==(n=ji.highlight(a[0],fn)+"")&&s.p(n)},i:de,o:de,d(a){a&&m(e)}}}function vs(i,e,t){let{code:l=""}=e;return i.$$set=s=>{"code"in s&&t(0,l=s.code)},[l]}class Rn extends lt{constructor(e){super(),it(this,e,vs,Hs,tt,{code:0})}}function qs(i){let e,t="Compiled",l,s,n="Written",r,a;return{c(){e=F("button"),e.textContent=t,l=J(),s=F("button"),s.textContent=n,this.h()},l(o){e=N(o,"BUTTON",{class:!0,"data-svelte-h":!0}),Se(e)!=="svelte-1vzm9jy"&&(e.textContent=t),l=Z(o),s=N(o,"BUTTON",{class:!0,"data-svelte-h":!0}),Se(s)!=="svelte-qu81ez"&&(s.textContent=n),this.h()},h(){h(e,"class","off svelte-ska6l4"),h(s,"class","text-info bg-info/10 border border-info svelte-ska6l4")},m(o,f){S(o,e,f),S(o,l,f),S(o,s,f),r||(a=yt(e,"click",i[1]),r=!0)},p:de,d(o){o&&(m(e),m(l),m(s)),r=!1,a()}}}function Vs(i){let e,t="Compiled",l,s,n="Written",r,a;return{c(){e=F("button"),e.textContent=t,l=J(),s=F("button"),s.textContent=n,this.h()},l(o){e=N(o,"BUTTON",{class:!0,"data-svelte-h":!0}),Se(e)!=="svelte-wrfleh"&&(e.textContent=t),l=Z(o),s=N(o,"BUTTON",{class:!0,"data-svelte-h":!0}),Se(s)!=="svelte-v36xno"&&(s.textContent=n),this.h()},h(){h(e,"class","text-info bg-info/10 border border-info svelte-ska6l4"),h(s,"class","off svelte-ska6l4")},m(o,f){S(o,e,f),S(o,l,f),S(o,s,f),r||(a=yt(s,"click",i[1]),r=!0)},p:de,d(o){o&&(m(e),m(l),m(s)),r=!1,a()}}}function Ws(i){let e,t,l;function s(a,o){return a[0]?Vs:qs}let n=s(i),r=n(i);return{c(){e=F("div"),r.c(),this.h()},l(a){e=N(a,"DIV",{class:!0});var o=K(e);r.l(o),o.forEach(m),this.h()},h(){h(e,"class","toggle svelte-ska6l4")},m(a,o){S(a,e,o),r.m(e,null),l=!0},p(a,[o]){n===(n=s(a))&&r?r.p(a,o):(r.d(1),r=n(a),r&&(r.c(),r.m(e,null)))},i(a){l||(a&&ll(()=>{l&&(t||(t=Qt(e,Kt,{},!0)),t.run(1))}),l=!0)},o(a){a&&(t||(t=Qt(e,Kt,{},!1)),t.run(0)),l=!1},d(a){a&&m(e),r.d(),a&&t&&t.end()}}}function js(i,e,t){let{showCompiled:l}=e;const s=function(){t(0,l=!l)};return i.$$set=n=>{"showCompiled"in n&&t(0,l=n.showCompiled)},[l,s]}class Ys extends lt{constructor(e){super(),it(this,e,js,Ws,tt,{showCompiled:0})}}function un(i){let e,t,l,s,n,r,a,o,f,u,c,y,T,b,d,L,I;s=new Pn({props:{toggled:i[10]}});let E=i[10]&&i[4]&&cn(i),w=i[10]&&dn(i);const P=[xs,Js,Zs,Ks],H=[];function V(B,q){return B[6]?0:B[8]?1:B[2].loading?2:3}c=V(i),y=H[c]=P[c](i);let Q=i[8]>0&&!i[6]&&i[9]&&mn(i);return{c(){e=F("div"),t=F("div"),l=F("button"),oe(s.$$.fragment),n=J(),r=Te(i[0]),a=J(),E&&E.c(),o=J(),w&&w.c(),f=J(),u=F("button"),y.c(),T=J(),Q&&Q.c(),this.h()},l(B){e=N(B,"DIV",{class:!0});var q=K(e);t=N(q,"DIV",{class:!0});var g=K(t);l=N(g,"BUTTON",{type:!0,"aria-label":!0,class:!0});var X=K(l);ae(s.$$.fragment,X),n=Z(X),r=Ce(X,i[0]),X.forEach(m),a=Z(g),E&&E.l(g),o=Z(g),w&&w.l(g),g.forEach(m),f=Z(q),u=N(q,"BUTTON",{type:!0,"aria-label":!0,class:!0});var G=K(u);y.l(G),G.forEach(m),T=Z(q),Q&&Q.l(q),q.forEach(m),this.h()},h(){h(l,"type","button"),h(l,"aria-label","show-sql"),h(l,"class","title svelte-1ursthx"),h(t,"class","container-a svelte-1ursthx"),h(u,"type","button"),h(u,"aria-label","view-query"),h(u,"class",qn("status-bar")+" svelte-1ursthx"),jt(u,"error",i[6]),jt(u,"success",!i[6]),jt(u,"open",i[9]),jt(u,"closed",!i[9]),h(e,"class","scrollbox my-3 svelte-1ursthx")},m(B,q){S(B,e,q),D(e,t),D(t,l),re(s,l,null),D(l,n),D(l,r),D(t,a),E&&E.m(t,null),D(t,o),w&&w.m(t,null),D(e,f),D(e,u),H[c].m(u,null),D(e,T),Q&&Q.m(e,null),d=!0,L||(I=[yt(l,"click",i[15]),yt(u,"click",i[16])],L=!0)},p(B,q){const g={};q&1024&&(g.toggled=B[10]),s.$set(g),(!d||q&1)&&Qe(r,B[0]),B[10]&&B[4]?E?(E.p(B,q),q&1040&&M(E,1)):(E=cn(B),E.c(),M(E,1),E.m(t,o)):E&&(Ke(),U(E,1,1,()=>{E=null}),Ze()),B[10]?w?(w.p(B,q),q&1024&&M(w,1)):(w=dn(B),w.c(),M(w,1),w.m(t,null)):w&&(Ke(),U(w,1,1,()=>{w=null}),Ze());let X=c;c=V(B),c===X?H[c].p(B,q):(Ke(),U(H[X],1,1,()=>{H[X]=null}),Ze(),y=H[c],y?y.p(B,q):(y=H[c]=P[c](B),y.c()),M(y,1),y.m(u,null)),(!d||q&64)&&jt(u,"error",B[6]),(!d||q&64)&&jt(u,"success",!B[6]),(!d||q&512)&&jt(u,"open",B[9]),(!d||q&512)&&jt(u,"closed",!B[9]),B[8]>0&&!B[6]&&B[9]?Q?(Q.p(B,q),q&832&&M(Q,1)):(Q=mn(B),Q.c(),M(Q,1),Q.m(e,null)):Q&&(Ke(),U(Q,1,1,()=>{Q=null}),Ze())},i(B){d||(M(s.$$.fragment,B),M(E),M(w),M(y),M(Q),B&&ll(()=>{d&&(b||(b=Qt(e,Kt,{},!0)),b.run(1))}),d=!0)},o(B){U(s.$$.fragment,B),U(E),U(w),U(y),U(Q),B&&(b||(b=Qt(e,Kt,{},!1)),b.run(0)),d=!1},d(B){B&&m(e),se(s),E&&E.d(),w&&w.d(),H[c].d(),Q&&Q.d(),B&&b&&b.end(),L=!1,Vl(I)}}}function cn(i){let e,t,l;function s(r){i[20](r)}let n={};return i[5]!==void 0&&(n.showCompiled=i[5]),e=new Ys({props:n}),Vn.push(()=>Zn(e,"showCompiled",s)),{c(){oe(e.$$.fragment)},l(r){ae(e.$$.fragment,r)},m(r,a){re(e,r,a),l=!0},p(r,a){const o={};!t&&a&32&&(t=!0,o.showCompiled=r[5],Wn(()=>t=!1)),e.$set(o)},i(r){l||(M(e.$$.fragment,r),l=!0)},o(r){U(e.$$.fragment,r),l=!1},d(r){se(e,r)}}}function dn(i){let e,t,l,s,n;const r=[Qs,Xs],a=[];function o(f,u){return f[5]?0:1}return t=o(i),l=a[t]=r[t](i),{c(){e=F("div"),l.c(),this.h()},l(f){e=N(f,"DIV",{class:!0});var u=K(e);l.l(u),u.forEach(m),this.h()},h(){h(e,"class","code-container svelte-1ursthx")},m(f,u){S(f,e,u),a[t].m(e,null),n=!0},p(f,u){let c=t;t=o(f),t===c?a[t].p(f,u):(Ke(),U(a[c],1,1,()=>{a[c]=null}),Ze(),l=a[t],l?l.p(f,u):(l=a[t]=r[t](f),l.c()),M(l,1),l.m(e,null))},i(f){n||(M(l),f&&ll(()=>{n&&(s||(s=Qt(e,Kt,{},!0)),s.run(1))}),n=!0)},o(f){U(l),f&&(s||(s=Qt(e,Kt,{},!1)),s.run(0)),n=!1},d(f){f&&m(e),a[t].d(),f&&s&&s.end()}}}function Xs(i){let e,t;return e=new Rn({props:{code:i[3]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&8&&(n.code=l[3]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Qs(i){let e,t;return e=new Rn({props:{code:i[1].originalText}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&2&&(n.code=l[1].originalText),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Ks(i){let e;return{c(){e=Te("ran successfully but no data was returned")},l(t){e=Ce(t,"ran successfully but no data was returned")},m(t,l){S(t,e,l)},p:de,i:de,o:de,d(t){t&&m(e)}}}function Zs(i){let e;return{c(){e=Te("loading...")},l(t){e=Ce(t,"loading...")},m(t,l){S(t,e,l)},p:de,i:de,o:de,d(t){t&&m(e)}}}function Js(i){let e,t,l=i[8].toLocaleString()+"",s,n,r=i[8]>1?"records":"record",a,o,f=i[7].toLocaleString()+"",u,c,y=i[7]>1?"properties":"property",T,b;return e=new Pn({props:{toggled:i[9],color:i[12].colors.info}}),{c(){oe(e.$$.fragment),t=J(),s=Te(l),n=J(),a=Te(r),o=Te(" with "),u=Te(f),c=J(),T=Te(y)},l(d){ae(e.$$.fragment,d),t=Z(d),s=Ce(d,l),n=Z(d),a=Ce(d,r),o=Ce(d," with "),u=Ce(d,f),c=Z(d),T=Ce(d,y)},m(d,L){re(e,d,L),S(d,t,L),S(d,s,L),S(d,n,L),S(d,a,L),S(d,o,L),S(d,u,L),S(d,c,L),S(d,T,L),b=!0},p(d,L){const I={};L&512&&(I.toggled=d[9]),L&4096&&(I.color=d[12].colors.info),e.$set(I),(!b||L&256)&&l!==(l=d[8].toLocaleString()+"")&&Qe(s,l),(!b||L&256)&&r!==(r=d[8]>1?"records":"record")&&Qe(a,r),(!b||L&128)&&f!==(f=d[7].toLocaleString()+"")&&Qe(u,f),(!b||L&128)&&y!==(y=d[7]>1?"properties":"property")&&Qe(T,y)},i(d){b||(M(e.$$.fragment,d),b=!0)},o(d){U(e.$$.fragment,d),b=!1},d(d){d&&(m(t),m(s),m(n),m(a),m(o),m(u),m(c),m(T)),se(e,d)}}}function xs(i){let e=i[6].message+"",t;return{c(){t=Te(e)},l(l){t=Ce(l,e)},m(l,s){S(l,t,s)},p(l,s){s&64&&e!==(e=l[6].message+"")&&Qe(t,e)},i:de,o:de,d(l){l&&m(t)}}}function mn(i){let e,t;return e=new Us({props:{data:i[1],queryID:i[0]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&2&&(n.data=l[1]),s&1&&(n.queryID=l[0]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function ps(i){let e,t,l,s=i[11]&&un(i);return{c(){e=F("div"),s&&s.c(),this.h()},l(n){e=N(n,"DIV",{class:!0});var r=K(e);s&&s.l(r),r.forEach(m),this.h()},h(){h(e,"class","over-container svelte-1ursthx")},m(n,r){S(n,e,r),s&&s.m(e,null),l=!0},p(n,[r]){n[11]?s?(s.p(n,r),r&2048&&M(s,1)):(s=un(n),s.c(),M(s,1),s.m(e,null)):s&&(Ke(),U(s,1,1,()=>{s=null}),Ze())},i(n){l||(M(s),n&&(t||ll(()=>{t=wi(e,Mn,{}),t.start()})),l=!0)},o(n){U(s),l=!1},d(n){n&&m(e),s&&s.d()}}}function $s(i,e,t){let l,s,n,r,a=de,o=()=>(a(),a=Tt(d,g=>t(2,r=g)),d),f,u,c,y,T;Nt(i,Fn,g=>t(19,c=g)),Nt(i,xn,g=>t(11,y=g)),i.$$.on_destroy.push(()=>a());let{queryID:b}=e,{queryResult:d}=e;o();let L=Yi("showSQL_".concat(b),!1);Nt(i,L,g=>t(10,u=g));let I=Yi(`showResults_${b}`);Nt(i,I,g=>t(9,f=g));const E=function(){Li(L,u=!u,u)},w=function(){!Q&&r.length>0&&Li(I,f=!f,f)};let P,H,V=!0,Q;const{theme:B}=Pt();Nt(i,B,g=>t(12,T=g));function q(g){V=g,t(5,V)}return i.$$set=g=>{"queryID"in g&&t(0,b=g.queryID),"queryResult"in g&&o(t(1,d=g.queryResult))},i.$$.update=()=>{if(i.$$.dirty&524288&&t(18,l=c.data.evidencemeta.queries),i.$$.dirty&4&&(r?t(6,Q=r.error):t(6,Q=new Error("queryResult is undefined"))),i.$$.dirty&4&&t(8,s=(r==null?void 0:r.length)??0),i.$$.dirty&4&&t(7,n=r.columns.length??(r==null?void 0:r._evidenceColumnTypes.length)??0),i.$$.dirty&262145){let g=l==null?void 0:l.find(X=>X.id===b);g&&(t(3,P=g.inputQueryString),t(4,H=g.compiled&&g.compileError===void 0))}},[b,d,r,P,H,V,Q,n,s,f,u,y,T,L,I,E,w,B,l,c,q]}class er extends lt{constructor(e){super(),it(this,e,$s,ps,tt,{queryID:0,queryResult:1})}}const wl=Symbol.for("__evidence-chart-window-debug__"),tr=(i,e)=>{window[wl]||(window[wl]={}),window[wl][i]=e},lr=i=>{window[wl]||(window[wl]={}),delete window[wl][i]},Tl=500,ir=(i,e)=>{var b;const t=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)&&i.clientWidth*3*i.clientHeight*3>16777215;Hl("light",Mi),Hl("dark",Dn);let l;const s=()=>{l=Ii(i,e.theme,{renderer:t?"svg":e.renderer??"canvas"})};s(),tr(l.id,l),e.connectGroup&&(l.group=e.connectGroup,pn(e.connectGroup));const n=()=>{if(e.seriesColors){const d=l.getOption();if(!d)return;const L={...d};for(const I of Object.keys(e.seriesColors)){const E=d.series.findIndex(w=>w.name===I);E!==-1&&(L.series[E]={...L.series[E],itemStyle:{...L.series[E].itemStyle,color:e.seriesColors[I]}})}l.setOption(L)}},r=()=>{e.echartsOptions&&l.setOption({...e.echartsOptions})},a=()=>{let d=[];if(e.seriesOptions){const L=e.config.series.reduce((I,{evidenceSeriesType:E},w)=>((E==="reference_line"||E==="reference_area"||E==="reference_point")&&I.push(w),I),[]);for(let I=0;I<e.config.series.length;I++)L.includes(I)?d.push({}):d.push({...e.seriesOptions});l.setOption({series:d})}};l.setOption({...e.config,animationDuration:Tl,animationDurationUpdate:Tl}),n(),r(),a();const o=e.dispatch;l.on("click",function(d){o("click",d)});const f=i.parentElement,u=$n(()=>{l.resize({animation:{duration:Tl}}),y()},100);let c;window.ResizeObserver&&f?(c=new ResizeObserver(u),c.observe(f)):window.addEventListener("resize",u);const y=()=>{if(e.showAllXAxisLabels){const d=l.getOption();if(!d)return;const L=new Set(d.series.flatMap(w=>{var P;return(P=w.data)==null?void 0:P.map(H=>H[0])})),I=4/5,E=(i==null?void 0:i.clientWidth)??0;if(!e.swapXY){const w={xAxis:{axisLabel:{interval:0,overflow:e.xAxisLabelOverflow,width:E*I/L.size}}};l.setOption(w)}}},T=d=>{d.theme!==e.theme&&(l.dispose(),e=d,s()),e=d,l.setOption({...e.config,animationDuration:Tl,animationDurationUpdate:Tl},!0),n(),r(),a(),l.resize({animation:{duration:Tl}}),y()};return u(),window[b=Symbol.for("chart renders")]??(window[b]=0),window[Symbol.for("chart renders")]++,{update(d){window[Symbol.for("chart renders")]++,T(d)},destroy(){c?c.unobserve(f):window.removeEventListener("resize",u),l.dispose(),lr(l.id)}}},nr=(i,e)=>{Hl("light",Mi),Hl("dark",Dn),console.log("echartsCanvasDownloadAction",e.theme);const t=Ii(i,e.theme,{renderer:"canvas"});e.config.animation=!1,t.setOption(e.config);const l=()=>{if(e.seriesColors){const f=t.getOption();if(!f)return;const u={...f};for(const c of Object.keys(e.seriesColors)){const y=f.series.findIndex(T=>T.name===c);y!==-1&&(u.series[y]={...u.series[y],itemStyle:{...u.series[y].itemStyle,color:e.seriesColors[c]}})}t.setOption(u)}},s=()=>{e.echartsOptions&&t.setOption({...e.echartsOptions})},n=()=>{let f=[];if(e.seriesOptions){const u=e.config.series.reduce((c,{evidenceSeriesType:y},T)=>((y==="reference_line"||y==="reference_area"||y==="reference_point")&&c.push(T),c),[]);for(let c=0;c<e.config.series.length;c++)u.includes(c)?f.push({}):f.push({...e.seriesOptions});t.setOption({series:f})}};s(),l(),n();let r=t.getConnectedDataURL({type:"png",pixelRatio:3,backgroundColor:e.backgroundColor,excludeComponents:["toolbox"]});const a=new Date,o=new Date(a.getTime()-a.getTimezoneOffset()*6e4).toISOString().slice(0,19).replaceAll(":","-");return es(r,(e.evidenceChartTitle??e.queryID??"evidence-chart")+`_${o}.png`),t.dispose(),{destroy(){t.dispose()}}},vl=(i,e)=>{Hl("evidence-light",Mi);const{config:t,ratio:l,echartsOptions:s,seriesOptions:n,seriesColors:r,isMap:a,extraHeight:o,width:f}=e;let u={renderer:"canvas"};a&&(u.height=f*.5+o,i&&i.parentNode&&(i.style.height=u.height+"px",i.parentNode.style.height=u.height+"px"));const c=Ii(i,"evidence-light",u);t.animation=!1,c.setOption(t),s&&c.setOption(s);const y=()=>{if(r){const L=c.getOption();if(!L)return;const I={...L};for(const E of Object.keys(r)){const w=L.series.findIndex(P=>P.name===E);w!==-1&&(I.series[w]={...I.series[w],itemStyle:{...I.series[w].itemStyle,color:r[E]}})}c.setOption(I)}},T=()=>{s&&c.setOption({...s})},b=()=>{let L=[];if(n){const I=t.series.reduce((E,{evidenceSeriesType:w},P)=>((w==="reference_line"||w==="reference_area"||w==="reference_point")&&E.push(P),E),[]);for(let E=0;E<t.series.length;E++)I.includes(E)?L.push({}):L.push({...n});c.setOption({series:L})}};T(),y(),b();let d=c.getConnectedDataURL({type:"jpeg",pixelRatio:l,backgroundColor:"#fff",excludeComponents:["toolbox"]});i.innerHTML=`<img src=${d} width="100%" style="
        position: absolute; 
        top: 0;
        user-select: all;
        -webkit-user-select: all;
        -moz-user-select: all;
        -ms-user-select: all;
    " />`,e.config.animation=!0};function sr(i){let e;function t(n,r){return n[9]?or:ar}let l=t(i),s=l(i);return{c(){s.c(),e=pe()},l(n){s.l(n),e=pe()},m(n,r){s.m(n,r),S(n,e,r)},p(n,r){l===(l=t(n))&&s?s.p(n,r):(s.d(1),s=l(n),s&&(s.c(),s.m(e.parentNode,e)))},d(n){n&&m(e),s.d(n)}}}function rr(i){let e,t,l,s;return{c(){e=F("div"),this.h()},l(n){e=N(n,"DIV",{class:!0,style:!0}),K(e).forEach(m),this.h()},h(){h(e,"class","chart"),v(e,"height",i[1]),v(e,"width",i[2]),v(e,"margin-left","0"),v(e,"margin-top","15px"),v(e,"margin-bottom","10px"),v(e,"overflow","visible"),v(e,"break-inside","avoid")},m(n,r){S(n,e,r),l||(s=il(t=vl.call(null,e,{config:i[0],ratio:2,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13]})),l=!0)},p(n,r){r&2&&v(e,"height",n[1]),r&4&&v(e,"width",n[2]),t&&nl(t.update)&&r&8289&&t.update.call(null,{config:n[0],ratio:2,echartsOptions:n[5],seriesOptions:n[6],seriesColors:n[13]})},d(n){n&&m(e),l=!1,s()}}}function ar(i){let e,t,l,s,n,r,a;return{c(){e=F("div"),l=J(),s=F("div"),this.h()},l(o){e=N(o,"DIV",{class:!0,style:!0}),K(e).forEach(m),l=Z(o),s=N(o,"DIV",{class:!0,style:!0}),K(s).forEach(m),this.h()},h(){h(e,"class","chart md:hidden"),v(e,"height",i[1]),v(e,"width","650px"),v(e,"margin-left","0"),v(e,"margin-top","15px"),v(e,"margin-bottom","10px"),v(e,"overflow","visible"),v(e,"break-inside","avoid"),h(s,"class","chart hidden md:block"),v(s,"height",i[1]),v(s,"width","841px"),v(s,"margin-left","0"),v(s,"margin-top","15px"),v(s,"margin-bottom","10px"),v(s,"overflow","visible"),v(s,"break-inside","avoid")},m(o,f){S(o,e,f),S(o,l,f),S(o,s,f),r||(a=[il(t=vl.call(null,e,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:650})),il(n=vl.call(null,s,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:841}))],r=!0)},p(o,f){f&2&&v(e,"height",o[1]),t&&nl(t.update)&&f&8673&&t.update.call(null,{config:o[0],ratio:4,echartsOptions:o[5],seriesOptions:o[6],seriesColors:o[13],isMap:o[7],extraHeight:o[8],width:650}),f&2&&v(s,"height",o[1]),n&&nl(n.update)&&f&8673&&n.update.call(null,{config:o[0],ratio:4,echartsOptions:o[5],seriesOptions:o[6],seriesColors:o[13],isMap:o[7],extraHeight:o[8],width:841})},d(o){o&&(m(e),m(l),m(s)),r=!1,Vl(a)}}}function or(i){let e,t,l,s,n,r,a;return{c(){e=F("div"),l=J(),s=F("div"),this.h()},l(o){e=N(o,"DIV",{class:!0,style:!0}),K(e).forEach(m),l=Z(o),s=N(o,"DIV",{class:!0,style:!0}),K(s).forEach(m),this.h()},h(){h(e,"class","chart md:hidden"),v(e,"height",i[1]),v(e,"width",i[11]+"px"),v(e,"margin-left","0"),v(e,"margin-top","15px"),v(e,"margin-bottom","10px"),v(e,"overflow","visible"),v(e,"break-inside","avoid"),h(s,"class","chart hidden md:block"),v(s,"height",i[1]),v(s,"width",i[10]+"px"),v(s,"margin-left","0"),v(s,"margin-top","15px"),v(s,"margin-bottom","10px"),v(s,"overflow","visible"),v(s,"break-inside","avoid")},m(o,f){S(o,e,f),S(o,l,f),S(o,s,f),r||(a=[il(t=vl.call(null,e,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:i[11]})),il(n=vl.call(null,s,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:i[10]}))],r=!0)},p(o,f){f&2&&v(e,"height",o[1]),f&2048&&v(e,"width",o[11]+"px"),t&&nl(t.update)&&f&10721&&t.update.call(null,{config:o[0],ratio:4,echartsOptions:o[5],seriesOptions:o[6],seriesColors:o[13],isMap:o[7],extraHeight:o[8],width:o[11]}),f&2&&v(s,"height",o[1]),f&1024&&v(s,"width",o[10]+"px"),n&&nl(n.update)&&f&9697&&n.update.call(null,{config:o[0],ratio:4,echartsOptions:o[5],seriesOptions:o[6],seriesColors:o[13],isMap:o[7],extraHeight:o[8],width:o[10]})},d(o){o&&(m(e),m(l),m(s)),r=!1,Vl(a)}}}function fr(i){let e;function t(n,r){if(n[3])return rr;if(n[4])return sr}let l=t(i),s=l&&l(i);return{c(){s&&s.c(),e=pe()},l(n){s&&s.l(n),e=pe()},m(n,r){s&&s.m(n,r),S(n,e,r)},p(n,[r]){l===(l=t(n))&&s?s.p(n,r):(s&&s.d(1),s=l&&l(n),s&&(s.c(),s.m(e.parentNode,e)))},i:de,o:de,d(n){n&&m(e),s&&s.d(n)}}}function ur(i,e,t){let l,s,n,r,a,o,f=de,u=()=>(f(),f=Tt(l,g=>t(13,o=g)),l);i.$$.on_destroy.push(()=>f());const{resolveColorsObject:c}=Pt();let{config:y=void 0}=e,{height:T="291px"}=e,{width:b="100%"}=e,{copying:d=!1}=e,{printing:L=!1}=e,{echartsOptions:I=void 0}=e,{seriesOptions:E=void 0}=e,{seriesColors:w=void 0}=e,{isMap:P=!1}=e,{extraHeight:H=void 0}=e,V=!1,Q,B;const q=Ul("gridConfig");return q&&(V=!0,{cols:Q,gapWidth:B}=q),i.$$set=g=>{"config"in g&&t(0,y=g.config),"height"in g&&t(1,T=g.height),"width"in g&&t(2,b=g.width),"copying"in g&&t(3,d=g.copying),"printing"in g&&t(4,L=g.printing),"echartsOptions"in g&&t(5,I=g.echartsOptions),"seriesOptions"in g&&t(6,E=g.seriesOptions),"seriesColors"in g&&t(14,w=g.seriesColors),"isMap"in g&&t(7,P=g.isMap),"extraHeight"in g&&t(8,H=g.extraHeight)},i.$$.update=()=>{i.$$.dirty&16384&&u(t(12,l=c(w))),i.$$.dirty&32768&&t(18,s=Math.min(Number(Q),2)),i.$$.dirty&327680&&t(11,n=(650-Number(B)*(s-1))/s),i.$$.dirty&32768&&t(17,r=Math.min(Number(Q),3)),i.$$.dirty&196608&&t(10,a=(841-Number(B)*(r-1))/r)},[y,T,b,d,L,I,E,P,H,V,a,n,l,o,w,Q,B,r,s]}class cr extends lt{constructor(e){super(),it(this,e,ur,fr,tt,{config:0,height:1,width:2,copying:3,printing:4,echartsOptions:5,seriesOptions:6,seriesColors:14,isMap:7,extraHeight:8})}}function dr(i){let e,t,l="Loading...",s,n,r;return{c(){e=F("div"),t=F("span"),t.textContent=l,s=J(),n=F("div"),this.h()},l(a){e=N(a,"DIV",{role:!0,class:!0});var o=K(e);t=N(o,"SPAN",{class:!0,"data-svelte-h":!0}),Se(t)!=="svelte-1wtojot"&&(t.textContent=l),s=Z(o),n=N(o,"DIV",{class:!0,style:!0}),K(n).forEach(m),o.forEach(m),this.h()},h(){h(t,"class","sr-only"),h(n,"class","bg-base-100 rounded-md max-w-[100%]"),v(n,"height",i[0]),v(n,"margin-top","15px"),v(n,"margin-bottom","31px"),h(e,"role","status"),h(e,"class","animate-pulse")},m(a,o){S(a,e,o),D(e,t),D(e,s),D(e,n)},p(a,[o]){o&1&&v(n,"height",a[0])},i(a){a&&(r||ll(()=>{r=wi(e,ts,{}),r.start()}))},o:de,d(a){a&&m(e)}}}function mr(i,e,t){let{height:l="231px"}=e;return i.$$set=s=>{"height"in s&&t(0,l=s.height)},[l]}class hr extends lt{constructor(e){super(),it(this,e,mr,dr,tt,{height:0})}}function hn(i){let e,t,l,s;const n=[br,yr],r=[];function a(o,f){return 1}return e=a(),t=r[e]=n[e](i),{c(){t.c(),l=pe()},l(o){t.l(o),l=pe()},m(o,f){r[e].m(o,f),S(o,l,f),s=!0},p(o,f){t.p(o,f)},i(o){s||(M(t),s=!0)},o(o){U(t),s=!1},d(o){o&&m(l),r[e].d(o)}}}function yr(i){let e,t,l,s;return{c(){e=F("div"),this.h()},l(n){e=N(n,"DIV",{class:!0,style:!0}),K(e).forEach(m),this.h()},h(){h(e,"class","chart svelte-db4qxn"),v(e,"height",i[3]),v(e,"width",i[4]),v(e,"overflow","visible"),v(e,"display",i[15]?"none":"inherit")},m(n,r){S(n,e,r),l||(s=il(t=ir.call(null,e,{config:i[0],...i[25],echartsOptions:i[9],seriesOptions:i[10],dispatch:i[24],renderer:i[6],connectGroup:i[12],xAxisLabelOverflow:i[13],seriesColors:i[19],theme:i[20]})),l=!0)},p(n,r){r[0]&8&&v(e,"height",n[3]),r[0]&16&&v(e,"width",n[4]),r[0]&32768&&v(e,"display",n[15]?"none":"inherit"),t&&nl(t.update)&&r[0]&35141185&&t.update.call(null,{config:n[0],...n[25],echartsOptions:n[9],seriesOptions:n[10],dispatch:n[24],renderer:n[6],connectGroup:n[12],xAxisLabelOverflow:n[13],seriesColors:n[19],theme:n[20]})},i:de,o:de,d(n){n&&m(e),l=!1,s()}}}function br(i){let e,t;return e=new hr({props:{height:i[3]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s[0]&8&&(n.height=l[3]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function yn(i){let e,t,l,s=i[8]&&bn(i),n=i[5]&&i[7]&&gn(i);return{c(){e=F("div"),s&&s.c(),t=J(),n&&n.c(),this.h()},l(r){e=N(r,"DIV",{class:!0});var a=K(e);s&&s.l(a),t=Z(a),n&&n.l(a),a.forEach(m),this.h()},h(){h(e,"class","chart-footer svelte-db4qxn")},m(r,a){S(r,e,a),s&&s.m(e,null),D(e,t),n&&n.m(e,null),l=!0},p(r,a){r[8]?s?(s.p(r,a),a[0]&256&&M(s,1)):(s=bn(r),s.c(),M(s,1),s.m(e,t)):s&&(Ke(),U(s,1,1,()=>{s=null}),Ze()),r[5]&&r[7]?n?(n.p(r,a),a[0]&160&&M(n,1)):(n=gn(r),n.c(),M(n,1),n.m(e,null)):n&&(Ke(),U(n,1,1,()=>{n=null}),Ze())},i(r){l||(M(s),M(n),l=!0)},o(r){U(s),U(n),l=!1},d(r){r&&m(e),s&&s.d(),n&&n.d()}}}function bn(i){let e,t;return e=new Oi({props:{text:"Save Image",class:"download-button",downloadData:i[32],display:i[17],queryID:i[1],$$slots:{default:[gr]},$$scope:{ctx:i}}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s[0]&16384&&(n.downloadData=l[32]),s[0]&131072&&(n.display=l[17]),s[0]&2&&(n.queryID=l[1]),s[1]&32&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function gr(i){let e,t,l,s;return{c(){e=kl("svg"),t=kl("rect"),l=kl("circle"),s=kl("path"),this.h()},l(n){e=Ll(n,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var r=K(e);t=Ll(r,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0}),K(t).forEach(m),l=Ll(r,"circle",{cx:!0,cy:!0,r:!0}),K(l).forEach(m),s=Ll(r,"path",{d:!0}),K(s).forEach(m),r.forEach(m),this.h()},h(){h(t,"x","3"),h(t,"y","3"),h(t,"width","18"),h(t,"height","18"),h(t,"rx","2"),h(l,"cx","8.5"),h(l,"cy","8.5"),h(l,"r","1.5"),h(s,"d","M20.4 14.5L16 10 4 20"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"width","12"),h(e,"height","12"),h(e,"viewBox","0 0 24 24"),h(e,"fill","none"),h(e,"stroke","#000"),h(e,"stroke-width","2"),h(e,"stroke-linecap","round"),h(e,"stroke-linejoin","round")},m(n,r){S(n,e,r),D(e,t),D(e,l),D(e,s)},p:de,d(n){n&&m(e)}}}function gn(i){let e,t;return e=new Oi({props:{text:"Download Data",data:i[5],queryID:i[1],class:"download-button",display:i[17]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s[0]&32&&(n.data=l[5]),s[0]&2&&(n.queryID=l[1]),s[0]&131072&&(n.display=l[17]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function _n(i){let e,t;return e=new ls({props:{source:JSON.stringify(i[0],void 0,3),copyToClipboard:!0,$$slots:{default:[_r]},$$scope:{ctx:i}}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s[0]&1&&(n.source=JSON.stringify(l[0],void 0,3)),s[0]&1|s[1]&32&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function _r(i){let e=JSON.stringify(i[0],void 0,3)+"",t;return{c(){t=Te(e)},l(l){t=Ce(l,e)},m(l,s){S(l,t,s)},p(l,s){s[0]&1&&e!==(e=JSON.stringify(l[0],void 0,3)+"")&&Qe(t,e)},d(l){l&&m(t)}}}function Cn(i){let e,t,l,s;return{c(){e=F("div"),this.h()},l(n){e=N(n,"DIV",{class:!0,style:!0}),K(e).forEach(m),this.h()},h(){h(e,"class","chart svelte-db4qxn"),v(e,"display","none"),v(e,"visibility","visible"),v(e,"height",i[3]),v(e,"width","666px"),v(e,"margin-left","0"),v(e,"margin-top","15px"),v(e,"margin-bottom","15px"),v(e,"overflow","visible")},m(n,r){S(n,e,r),l||(s=il(t=nr.call(null,e,{config:i[0],...i[25],echartsOptions:i[9],seriesOptions:i[10],seriesColors:i[19],queryID:i[1],evidenceChartTitle:i[2],theme:i[20],backgroundColor:i[21].colors["base-100"]})),l=!0)},p(n,r){r[0]&8&&v(e,"height",n[3]),t&&nl(t.update)&&r[0]&37225991&&t.update.call(null,{config:n[0],...n[25],echartsOptions:n[9],seriesOptions:n[10],seriesColors:n[19],queryID:n[1],evidenceChartTitle:n[2],theme:n[20],backgroundColor:n[21].colors["base-100"]})},d(n){n&&m(e),l=!1,s()}}}function Cr(i){let e,t,l,s,n,r,a,o,f,u,c=!i[16]&&hn(i);l=new cr({props:{config:i[0],height:i[3],width:i[4],copying:i[15],printing:i[16],echartsOptions:i[9],seriesOptions:i[10],seriesColors:i[18]}});let y=(i[7]||i[8])&&yn(i),T=i[11]&&!i[16]&&_n(i),b=i[14]&&Cn(i);return{c(){e=F("div"),c&&c.c(),t=J(),oe(l.$$.fragment),s=J(),y&&y.c(),n=J(),T&&T.c(),r=J(),b&&b.c(),a=pe(),this.h()},l(d){e=N(d,"DIV",{role:!0,class:!0});var L=K(e);c&&c.l(L),t=Z(L),ae(l.$$.fragment,L),s=Z(L),y&&y.l(L),n=Z(L),T&&T.l(L),L.forEach(m),r=Z(d),b&&b.l(d),a=pe(),this.h()},h(){h(e,"role","none"),h(e,"class","chart-container mt-2 mb-3 svelte-db4qxn")},m(d,L){S(d,e,L),c&&c.m(e,null),D(e,t),re(l,e,null),D(e,s),y&&y.m(e,null),D(e,n),T&&T.m(e,null),S(d,r,L),b&&b.m(d,L),S(d,a,L),o=!0,f||(u=[yt(window,"copy",i[27]),yt(window,"beforeprint",i[28]),yt(window,"afterprint",i[29]),yt(window,"export-beforeprint",i[30]),yt(window,"export-afterprint",i[31]),yt(e,"mouseenter",i[33]),yt(e,"mouseleave",i[34])],f=!0)},p(d,L){d[16]?c&&(Ke(),U(c,1,1,()=>{c=null}),Ze()):c?(c.p(d,L),L[0]&65536&&M(c,1)):(c=hn(d),c.c(),M(c,1),c.m(e,t));const I={};L[0]&1&&(I.config=d[0]),L[0]&8&&(I.height=d[3]),L[0]&16&&(I.width=d[4]),L[0]&32768&&(I.copying=d[15]),L[0]&65536&&(I.printing=d[16]),L[0]&512&&(I.echartsOptions=d[9]),L[0]&1024&&(I.seriesOptions=d[10]),L[0]&262144&&(I.seriesColors=d[18]),l.$set(I),d[7]||d[8]?y?(y.p(d,L),L[0]&384&&M(y,1)):(y=yn(d),y.c(),M(y,1),y.m(e,n)):y&&(Ke(),U(y,1,1,()=>{y=null}),Ze()),d[11]&&!d[16]?T?(T.p(d,L),L[0]&67584&&M(T,1)):(T=_n(d),T.c(),M(T,1),T.m(e,null)):T&&(Ke(),U(T,1,1,()=>{T=null}),Ze()),d[14]?b?b.p(d,L):(b=Cn(d),b.c(),b.m(a.parentNode,a)):b&&(b.d(1),b=null)},i(d){o||(M(c),M(l.$$.fragment,d),M(y),M(T),o=!0)},o(d){U(c),U(l.$$.fragment,d),U(y),U(T),o=!1},d(d){d&&(m(e),m(r),m(a)),c&&c.d(),se(l),y&&y.d(),T&&T.d(),b&&b.d(d),f=!1,Vl(u)}}}function Tr(i,e,t){let l;const s=["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"];let n=Wi(e,s),r,a=de,o=()=>(a(),a=Tt(l,te=>t(19,r=te)),l),f,u;i.$$.on_destroy.push(()=>a());const{activeAppearance:c,theme:y,resolveColorsObject:T}=Pt();Nt(i,c,te=>t(20,f=te)),Nt(i,y,te=>t(21,u=te));let{config:b=void 0}=e,{queryID:d=void 0}=e,{evidenceChartTitle:L=void 0}=e,{height:I="291px"}=e,{width:E="100%"}=e,{data:w}=e,{renderer:P=void 0}=e,{downloadableData:H=void 0}=e,{downloadableImage:V=void 0}=e,{echartsOptions:Q=void 0}=e,{seriesOptions:B=void 0}=e,{printEchartsConfig:q}=e,{seriesColors:g=void 0}=e,{connectGroup:X=void 0}=e,{xAxisLabelOverflow:G=void 0}=e;const j=jn();let Y=!1,O=!1,$=!1,ce=!1;const W=()=>{t(15,O=!0),Yn(),setTimeout(()=>{t(15,O=!1)},0)},p=()=>t(16,$=!0),fe=()=>t(16,$=!1),x=()=>t(16,$=!0),le=()=>t(16,$=!1),ue=()=>{t(14,Y=!0),setTimeout(()=>{t(14,Y=!1)},0)},ye=()=>t(17,ce=!0),Ne=()=>t(17,ce=!1);return i.$$set=te=>{e=Lt(Lt({},e),Ht(te)),t(25,n=Wi(e,s)),"config"in te&&t(0,b=te.config),"queryID"in te&&t(1,d=te.queryID),"evidenceChartTitle"in te&&t(2,L=te.evidenceChartTitle),"height"in te&&t(3,I=te.height),"width"in te&&t(4,E=te.width),"data"in te&&t(5,w=te.data),"renderer"in te&&t(6,P=te.renderer),"downloadableData"in te&&t(7,H=te.downloadableData),"downloadableImage"in te&&t(8,V=te.downloadableImage),"echartsOptions"in te&&t(9,Q=te.echartsOptions),"seriesOptions"in te&&t(10,B=te.seriesOptions),"printEchartsConfig"in te&&t(11,q=te.printEchartsConfig),"seriesColors"in te&&t(26,g=te.seriesColors),"connectGroup"in te&&t(12,X=te.connectGroup),"xAxisLabelOverflow"in te&&t(13,G=te.xAxisLabelOverflow)},i.$$.update=()=>{i.$$.dirty[0]&67108864&&o(t(18,l=T(g)))},[b,d,L,I,E,w,P,H,V,Q,B,q,X,G,Y,O,$,ce,l,r,f,u,c,y,j,n,g,W,p,fe,x,le,ue,ye,Ne]}class Lr extends lt{constructor(e){super(),it(this,e,Tr,Cr,tt,{config:0,queryID:1,evidenceChartTitle:2,height:3,width:4,data:5,renderer:6,downloadableData:7,downloadableImage:8,echartsOptions:9,seriesOptions:10,printEchartsConfig:11,seriesColors:26,connectGroup:12,xAxisLabelOverflow:13},null,[-1,-1])}}function ql(i,e){const t=new Set(i.map(l=>l[e]));return Array.from(t)}function kr(i,e){return el(i,is({count:ns(e)}))[0].count}function Sr(i,e,t){let l;if(typeof t!="object")l=el(i,Si(e,Zi({xTotal:Ei(t)})),Ti({percentOfX:pi(t,"xTotal")}),Ki({percentOfX:t+"_pct"}));else{l=el(i,Ti({valueSum:0}));for(let s=0;s<l.length;s++){l[s].valueSum=0;for(let n=0;n<t.length;n++)l[s].valueSum=l[s].valueSum+l[s][t[n]]}l=el(l,Si(e,Zi({xTotal:Ei("valueSum")})));for(let s=0;s<t.length;s++)l=el(l,Ti({percentOfX:pi(t[s],"xTotal")}),Ki({percentOfX:t[s]+"_pct"}))}return l}function zl(i,e,t){return[...i].sort((l,s)=>(l[e]<s[e]?-1:1)*(t?1:-1))}function Ri(i,e,t){const l=e+t;return i%l<e?0:1}function Er(i){let e,t;return e=new Pi({props:{error:i[14],title:i[8]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s[0]&16384&&(n.error=l[14]),s[0]&256&&(n.title=l[8]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Ar(i){let e,t,l;const s=i[136].default,n=sl(s,i,i[135],null);return t=new Lr({props:{config:i[20],height:i[15],width:i[13],data:i[0],queryID:i[6],evidenceChartTitle:i[7],showAllXAxisLabels:i[1],swapXY:i[3],echartsOptions:i[9],seriesOptions:i[10],printEchartsConfig:i[2],renderer:i[11],downloadableData:i[4],downloadableImage:i[5],connectGroup:i[12],xAxisLabelOverflow:i[23],seriesColors:i[16]}}),{c(){n&&n.c(),e=J(),oe(t.$$.fragment)},l(r){n&&n.l(r),e=Z(r),ae(t.$$.fragment,r)},m(r,a){n&&n.m(r,a),S(r,e,a),re(t,r,a),l=!0},p(r,a){n&&n.p&&(!l||a[4]&2048)&&rl(n,s,r,r[135],l?ol(s,r[135],a,null):al(r[135]),null);const o={};a[0]&1048576&&(o.config=r[20]),a[0]&32768&&(o.height=r[15]),a[0]&8192&&(o.width=r[13]),a[0]&1&&(o.data=r[0]),a[0]&64&&(o.queryID=r[6]),a[0]&128&&(o.evidenceChartTitle=r[7]),a[0]&2&&(o.showAllXAxisLabels=r[1]),a[0]&8&&(o.swapXY=r[3]),a[0]&512&&(o.echartsOptions=r[9]),a[0]&1024&&(o.seriesOptions=r[10]),a[0]&4&&(o.printEchartsConfig=r[2]),a[0]&2048&&(o.renderer=r[11]),a[0]&16&&(o.downloadableData=r[4]),a[0]&32&&(o.downloadableImage=r[5]),a[0]&4096&&(o.connectGroup=r[12]),a[0]&65536&&(o.seriesColors=r[16]),t.$set(o)},i(r){l||(M(n,r),M(t.$$.fragment,r),l=!0)},o(r){U(n,r),U(t.$$.fragment,r),l=!1},d(r){r&&m(e),n&&n.d(r),se(t,r)}}}function wr(i){let e,t,l,s;const n=[Ar,Er],r=[];function a(o,f){return o[14]?1:0}return e=a(i),t=r[e]=n[e](i),{c(){t.c(),l=pe()},l(o){t.l(o),l=pe()},m(o,f){r[e].m(o,f),S(o,l,f),s=!0},p(o,f){let u=e;e=a(o),e===u?r[e].p(o,f):(Ke(),U(r[u],1,1,()=>{r[u]=null}),Ze(),t=r[e],t?t.p(o,f):(t=r[e]=n[e](o),t.c()),M(t,1),t.m(l.parentNode,l))},i(o){s||(M(t),s=!0)},o(o){U(t),s=!1},d(o){o&&m(l),r[e].d(o)}}}function Or(i,e,t){let l,s,n,r,a,o=de,f=()=>(o(),o=Tt(n,k=>t(131,a=k)),n),u,c,y=de,T=()=>(y(),y=Tt(s,k=>t(133,c=k)),s),b,d=de,L=()=>(d(),d=Tt(l,k=>t(134,b=k)),l),I;i.$$.on_destroy.push(()=>o()),i.$$.on_destroy.push(()=>y()),i.$$.on_destroy.push(()=>d());let{$$slots:E={},$$scope:w}=e,P=Ai({}),H=Ai({});Nt(i,H,k=>t(20,I=k));const{theme:V,resolveColor:Q,resolveColorsObject:B,resolveColorPalette:q}=Pt();Nt(i,V,k=>t(132,u=k));let{data:g=void 0}=e,{queryID:X=void 0}=e,{x:G=void 0}=e,{y:j=void 0}=e,{y2:Y=void 0}=e,{series:O=void 0}=e,{size:$=void 0}=e,{tooltipTitle:ce=void 0}=e,{showAllXAxisLabels:W=void 0}=e,{printEchartsConfig:p=!1}=e,fe=!!j,x=!!G,{swapXY:le=!1}=e,{title:ue=void 0}=e,{subtitle:ye=void 0}=e,{chartType:Ne="Chart"}=e,{bubble:te=!1}=e,{hist:ne=!1}=e,{boxplot:Le=!1}=e,Fe,{xType:me=void 0}=e,{xAxisTitle:Pe="false"}=e,{xBaseline:ze=!0}=e,{xTickMarks:_e=!1}=e,{xGridlines:Re=!1}=e,{xAxisLabels:Oe=!0}=e,{sort:Ee=!0}=e,{xFmt:je=void 0}=e,{xMin:Ve=void 0}=e,{xMax:Ie=void 0}=e,{yLog:De=!1}=e,{yType:Ye=De===!0?"log":"value"}=e,{yLogBase:Me=10}=e,{yAxisTitle:Be="false"}=e,{yBaseline:Xe=!1}=e,{yTickMarks:ke=!1}=e,{yGridlines:Je=!0}=e,{yAxisLabels:z=!0}=e,{yMin:be=void 0}=e,{yMax:nt=void 0}=e,{yScale:We=!1}=e,{yFmt:Ge=void 0}=e,{yAxisColor:bt="true"}=e,{y2AxisTitle:$e="false"}=e,{y2Baseline:Ue=!1}=e,{y2TickMarks:R=!1}=e,{y2Gridlines:ge=!0}=e,{y2AxisLabels:He=!0}=e,{y2Min:ht=void 0}=e,{y2Max:gt=void 0}=e,{y2Scale:et=!1}=e,{y2Fmt:_t=void 0}=e,{y2AxisColor:Ct="true"}=e,{sizeFmt:ft=void 0}=e,{colorPalette:at="default"}=e,{legend:xe=void 0}=e,{echartsOptions:ot=void 0}=e,{seriesOptions:kt=void 0}=e,{seriesColors:st=void 0}=e,{stackType:Mt=void 0}=e,{stacked100:ct=!1}=e,{chartAreaHeight:ve}=e,{renderer:C=void 0}=e,{downloadableData:_=!0}=e,{downloadableImage:zt=!0}=e,{connectGroup:vt=void 0}=e,{leftPadding:qt=void 0}=e,{rightPadding:Dt=void 0}=e,{xLabelWrap:Vt=!1}=e;const fl=Vt?"break":"truncate";let he,Zt,Gt=[],dt=[],Il,ul,mt,St,Ae,A,ee,Rt,rt,Et,Bt,Wt,At,wt,Jt,ei,Ml,cl,ti,li,ii,ni,si,ri,ai,oi,fi,ui,ci,dl,Dl,Nl,jl,Yl,di,mi,ml,hi,yi,Xl,Bi,bi,xt=[],hl=!0,Ut=[],Fl=[],Ft,Pl,gi,pt;return i.$$set=k=>{"data"in k&&t(0,g=k.data),"queryID"in k&&t(6,X=k.queryID),"x"in k&&t(24,G=k.x),"y"in k&&t(25,j=k.y),"y2"in k&&t(49,Y=k.y2),"series"in k&&t(50,O=k.series),"size"in k&&t(51,$=k.size),"tooltipTitle"in k&&t(52,ce=k.tooltipTitle),"showAllXAxisLabels"in k&&t(1,W=k.showAllXAxisLabels),"printEchartsConfig"in k&&t(2,p=k.printEchartsConfig),"swapXY"in k&&t(3,le=k.swapXY),"title"in k&&t(7,ue=k.title),"subtitle"in k&&t(53,ye=k.subtitle),"chartType"in k&&t(8,Ne=k.chartType),"bubble"in k&&t(54,te=k.bubble),"hist"in k&&t(55,ne=k.hist),"boxplot"in k&&t(56,Le=k.boxplot),"xType"in k&&t(26,me=k.xType),"xAxisTitle"in k&&t(27,Pe=k.xAxisTitle),"xBaseline"in k&&t(28,ze=k.xBaseline),"xTickMarks"in k&&t(29,_e=k.xTickMarks),"xGridlines"in k&&t(30,Re=k.xGridlines),"xAxisLabels"in k&&t(31,Oe=k.xAxisLabels),"sort"in k&&t(32,Ee=k.sort),"xFmt"in k&&t(57,je=k.xFmt),"xMin"in k&&t(58,Ve=k.xMin),"xMax"in k&&t(59,Ie=k.xMax),"yLog"in k&&t(33,De=k.yLog),"yType"in k&&t(60,Ye=k.yType),"yLogBase"in k&&t(61,Me=k.yLogBase),"yAxisTitle"in k&&t(34,Be=k.yAxisTitle),"yBaseline"in k&&t(35,Xe=k.yBaseline),"yTickMarks"in k&&t(36,ke=k.yTickMarks),"yGridlines"in k&&t(37,Je=k.yGridlines),"yAxisLabels"in k&&t(38,z=k.yAxisLabels),"yMin"in k&&t(62,be=k.yMin),"yMax"in k&&t(63,nt=k.yMax),"yScale"in k&&t(39,We=k.yScale),"yFmt"in k&&t(64,Ge=k.yFmt),"yAxisColor"in k&&t(65,bt=k.yAxisColor),"y2AxisTitle"in k&&t(40,$e=k.y2AxisTitle),"y2Baseline"in k&&t(41,Ue=k.y2Baseline),"y2TickMarks"in k&&t(42,R=k.y2TickMarks),"y2Gridlines"in k&&t(43,ge=k.y2Gridlines),"y2AxisLabels"in k&&t(44,He=k.y2AxisLabels),"y2Min"in k&&t(66,ht=k.y2Min),"y2Max"in k&&t(67,gt=k.y2Max),"y2Scale"in k&&t(45,et=k.y2Scale),"y2Fmt"in k&&t(68,_t=k.y2Fmt),"y2AxisColor"in k&&t(69,Ct=k.y2AxisColor),"sizeFmt"in k&&t(70,ft=k.sizeFmt),"colorPalette"in k&&t(71,at=k.colorPalette),"legend"in k&&t(46,xe=k.legend),"echartsOptions"in k&&t(9,ot=k.echartsOptions),"seriesOptions"in k&&t(10,kt=k.seriesOptions),"seriesColors"in k&&t(72,st=k.seriesColors),"stackType"in k&&t(73,Mt=k.stackType),"stacked100"in k&&t(74,ct=k.stacked100),"chartAreaHeight"in k&&t(47,ve=k.chartAreaHeight),"renderer"in k&&t(11,C=k.renderer),"downloadableData"in k&&t(4,_=k.downloadableData),"downloadableImage"in k&&t(5,zt=k.downloadableImage),"connectGroup"in k&&t(12,vt=k.connectGroup),"leftPadding"in k&&t(75,qt=k.leftPadding),"rightPadding"in k&&t(76,Dt=k.rightPadding),"xLabelWrap"in k&&t(48,Vt=k.xLabelWrap),"$$scope"in k&&t(135,w=k.$$scope)},i.$$.update=()=>{var k,zi,Gi,Ui,Hi,vi;if(i.$$.dirty[0]&4&&t(2,p=qe(p)),i.$$.dirty[0]&8&&t(3,le=qe(le)),i.$$.dirty[0]&268435456&&t(28,ze=qe(ze)),i.$$.dirty[0]&536870912&&t(29,_e=qe(_e)),i.$$.dirty[0]&1073741824&&t(30,Re=qe(Re)),i.$$.dirty[1]&1&&t(31,Oe=qe(Oe)),i.$$.dirty[1]&2&&t(32,Ee=qe(Ee)),i.$$.dirty[1]&4&&t(33,De=qe(De)),i.$$.dirty[1]&16&&t(35,Xe=qe(Xe)),i.$$.dirty[1]&32&&t(36,ke=qe(ke)),i.$$.dirty[1]&64&&t(37,Je=qe(Je)),i.$$.dirty[1]&128&&t(38,z=qe(z)),i.$$.dirty[1]&256&&t(39,We=qe(We)),i.$$.dirty[2]&8&&L(t(19,l=Q(bt))),i.$$.dirty[1]&1024&&t(41,Ue=qe(Ue)),i.$$.dirty[1]&2048&&t(42,R=qe(R)),i.$$.dirty[1]&4096&&t(43,ge=qe(ge)),i.$$.dirty[1]&8192&&t(44,He=qe(He)),i.$$.dirty[1]&16384&&t(45,et=qe(et)),i.$$.dirty[2]&128&&T(t(18,s=Q(Ct))),i.$$.dirty[2]&512&&f(t(17,n=q(at))),i.$$.dirty[2]&1024&&t(16,r=B(st)),i.$$.dirty[0]&16&&t(4,_=qe(_)),i.$$.dirty[0]&32&&t(5,zt=qe(zt)),i.$$.dirty[1]&131072&&t(48,Vt=qe(Vt)),i.$$.dirty[0]&2130731403|i.$$.dirty[1]&2147352575|i.$$.dirty[2]&2147481975|i.$$.dirty[3]&2147483647|i.$$.dirty[4]&2047)try{if(t(14,Pl=void 0),t(124,xt=[]),t(83,dt=[]),t(126,Ut=[]),t(127,Fl=[]),t(85,ul=[]),t(77,fe=!!j),t(78,x=!!G),Al(g),t(80,he=El(g)),t(81,Zt=Object.keys(he)),x||t(24,G=Zt[0]),!fe){t(82,Gt=Zt.filter(function(ie){return![G,O,$].includes(ie)}));for(let ie=0;ie<Gt.length;ie++)t(85,ul=Gt[ie]),t(84,Il=he[ul].type),Il==="number"&&dt.push(ul);t(25,j=dt.length>1?dt:dt[0])}te?t(79,Fe={x:G,y:j,size:$}):ne?t(79,Fe={x:G}):Le?t(79,Fe={}):t(79,Fe={x:G,y:j});for(let ie in Fe)Fe[ie]==null&&xt.push(ie);if(xt.length===1)throw Error(new Intl.ListFormat().format(xt)+" is required");if(xt.length>1)throw Error(new Intl.ListFormat().format(xt)+" are required");if(ct===!0&&j.includes("_pct")&&hl===!1)if(typeof j=="object"){for(let ie=0;ie<j.length;ie++)t(25,j[ie]=j[ie].replace("_pct",""),j);t(125,hl=!1)}else t(25,j=j.replace("_pct","")),t(125,hl=!1);if(G&&Ut.push(G),j)if(typeof j=="object")for(t(128,Ft=0);Ft<j.length;t(128,Ft++,Ft))Ut.push(j[Ft]);else Ut.push(j);if(Y)if(typeof Y=="object")for(t(128,Ft=0);Ft<Y.length;t(128,Ft++,Ft))Ut.push(Y[Ft]);else Ut.push(Y);if($&&Ut.push($),O&&Fl.push(O),ce&&Fl.push(ce),Al(g,Ut,Fl),ct===!0){if(t(0,g=Sr(g,G,j)),typeof j=="object"){for(let ie=0;ie<j.length;ie++)t(25,j[ie]=j[ie]+"_pct",j);t(125,hl=!1)}else t(25,j=j+"_pct"),t(125,hl=!1);t(80,he=El(g))}switch(t(86,mt=he[G].type),mt){case"number":t(86,mt="value");break;case"string":t(86,mt="category");break;case"date":t(86,mt="time");break;default:break}if(t(26,me=me==="category"?"category":mt),W?t(1,W=W==="true"||W===!0):t(1,W=me==="category"),le&&me!=="category")throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(le&&Y)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(le&&t(26,me="category"),t(87,St=mt==="value"&&me==="category"),t(0,g=Ee?mt==="category"?zl(g,j,!1):zl(g,G,!0):g),mt==="time"&&t(0,g=zl(g,G,!0)),t(129,gi=El(g,"array")),t(130,pt=gi.filter(ie=>ie.type==="date")),t(130,pt=pt.map(ie=>ie.id)),pt.length>0)for(let ie=0;ie<pt.length;ie++)t(0,g=ss(g,pt[ie]));je?t(88,Ae=Ot(je,(k=he[G].format)==null?void 0:k.valueType)):t(88,Ae=he[G].format),j?Ge?typeof j=="object"?t(89,A=Ot(Ge,(zi=he[j[0]].format)==null?void 0:zi.valueType)):t(89,A=Ot(Ge,(Gi=he[j].format)==null?void 0:Gi.valueType)):typeof j=="object"?t(89,A=he[j[0]].format):t(89,A=he[j].format):t(89,A="str"),Y&&(_t?typeof Y=="object"?t(90,ee=Ot(_t,(Ui=he[Y[0]].format)==null?void 0:Ui.valueType)):t(90,ee=Ot(_t,(Hi=he[Y].format)==null?void 0:Hi.valueType)):typeof Y=="object"?t(90,ee=he[Y[0]].format):t(90,ee=he[Y].format)),$&&(ft?t(91,Rt=Ot(ft,(vi=he[$].format)==null?void 0:vi.valueType)):t(91,Rt=he[$].format)),t(92,rt=he[G].columnUnitSummary),j&&(typeof j=="object"?t(93,Et=he[j[0]].columnUnitSummary):t(93,Et=he[j].columnUnitSummary)),Y&&(typeof Y=="object"?t(94,Bt=he[Y[0]].columnUnitSummary):t(94,Bt=he[Y].columnUnitSummary)),t(27,Pe=Pe==="true"?Xt(G,Ae):Pe==="false"?"":Pe),t(34,Be=Be==="true"?typeof j=="object"?"":Xt(j,A):Be==="false"?"":Be),t(40,$e=$e==="true"?typeof Y=="object"?"":Xt(Y,ee):$e==="false"?"":$e);let yl=typeof j=="object"?j.length:1,qi=O?kr(g,O):1,Rl=yl*qi,_i=typeof Y=="object"?Y.length:Y?1:0,Ci=Rl+_i;if(xe!==void 0&&t(46,xe=xe==="true"||xe===!0),t(46,xe=xe??Ci>1),ct===!0&&De===!0)throw Error("Log axis cannot be used in a 100% stacked chart");if(Mt==="stacked"&&Ci>1&&De===!0)throw Error("Log axis cannot be used in a stacked chart");let bl;if(typeof j=="object"){bl=he[j[0]].columnUnitSummary.min;for(let ie=0;ie<j.length;ie++)he[j[ie]].columnUnitSummary.min<bl&&(bl=he[j[ie]].columnUnitSummary.min)}else j&&(bl=he[j].columnUnitSummary.min);if(De===!0&&bl<=0&&bl!==null)throw Error("Log axis cannot display values less than or equal to zero");P.update(ie=>({...ie,data:g,x:G,y:j,y2:Y,series:O,swapXY:le,sort:Ee,xType:me,xFormat:Ae,yFormat:A,y2Format:ee,sizeFormat:Rt,xMismatch:St,size:$,yMin:be,y2Min:ht,columnSummary:he,xAxisTitle:Pe,yAxisTitle:Be,y2AxisTitle:$e,tooltipTitle:ce,chartAreaHeight:ve,chartType:Ne,yCount:yl,y2Count:_i})),t(95,Wt=ql(g,G));let Vi;if(le?t(96,At={type:Ye,logBase:Me,position:"top",axisLabel:{show:z,hideOverlap:!0,showMaxLabel:!0,formatter(ie){return Kl(ie,A,Et)},margin:4},min:be,max:nt,scale:We,splitLine:{show:Je},axisLine:{show:Xe,onZero:!1},axisTick:{show:ke},boundaryGap:!1,z:2}):t(96,At={type:me,min:Ve,max:Ie,tooltip:{show:!0,position:"inside",formatter(ie){if(ie.isTruncated())return ie.name}},splitLine:{show:Re},axisLine:{show:ze},axisTick:{show:_e},axisLabel:{show:Oe,hideOverlap:!0,showMaxLabel:me==="category"||me==="value",formatter:me==="time"||me==="category"?!1:function(ie){return Kl(ie,Ae,rt)},margin:6},scale:!0,z:2}),le?t(97,wt={type:me,inverse:"true",splitLine:{show:Re},axisLine:{show:ze},axisTick:{show:_e},axisLabel:{show:Oe,hideOverlap:!0},scale:!0,min:Ve,max:Ie,z:2}):(t(97,wt={type:Ye,logBase:Me,splitLine:{show:Je},axisLine:{show:Xe,onZero:!1},axisTick:{show:ke},axisLabel:{show:z,hideOverlap:!0,margin:4,formatter(ie){return Kl(ie,A,Et)},color:Y?b==="true"?a[0]:b!=="false"?b:void 0:void 0},name:Be,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:Y?b==="true"?a[0]:b!=="false"?b:void 0:void 0},nameGap:6,min:be,max:nt,scale:We,boundaryGap:["0%","1%"],z:2}),Vi={type:"value",show:!1,alignTicks:!0,splitLine:{show:ge},axisLine:{show:Ue,onZero:!1},axisTick:{show:R},axisLabel:{show:He,hideOverlap:!0,margin:4,formatter(ie){return Kl(ie,ee,Bt)},color:c==="true"?a[Rl]:c!=="false"?c:void 0},name:$e,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:c==="true"?a[Rl]:c!=="false"?c:void 0},nameGap:6,min:ht,max:gt,scale:et,boundaryGap:["0%","1%"],z:2},t(97,wt=[wt,Vi])),ve){if(t(47,ve=Number(ve)),isNaN(ve))throw Error("chartAreaHeight must be a number");if(ve<=0)throw Error("chartAreaHeight must be a positive number")}else t(47,ve=180);t(100,Ml=!!ue),t(101,cl=!!ye),t(102,ti=xe*(O!==null||typeof j=="object"&&j.length>1)),t(103,li=Be!==""&&le),t(104,ii=Pe!==""&&!le),t(105,ni=15),t(106,si=13),t(107,ri=6*cl),t(108,ai=Ml*ni+cl*si+ri*Math.max(Ml,cl)),t(109,oi=10),t(110,fi=10),t(111,ui=14),t(112,ci=14),t(113,dl=15),t(113,dl=dl*ti),t(114,Dl=7),t(114,Dl=Dl*Math.max(Ml,cl)),t(115,Nl=ai+Dl),t(116,jl=Nl+dl+ci*li+oi),t(117,Yl=ii*ui+fi),t(121,hi=8),t(123,Xl=1),le&&(t(122,yi=Wt.length),t(123,Xl=Math.max(1,yi/hi))),t(118,di=ve*Xl+jl+Yl),t(119,mi=Nl+dl+7),t(15,Bi=di+"px"),t(13,bi="100%"),t(120,ml=le?Be:Pe),ml!==""&&t(120,ml=ml+" →"),t(98,Jt={id:"horiz-axis-title",type:"text",style:{text:ml,textAlign:"right",fill:u.colors["base-content-muted"]},cursor:"auto",right:le?"2%":"3%",top:le?mi:null,bottom:le?null:"2%"}),t(99,ei={title:{text:ue,subtext:ye,subtextStyle:{width:bi}},tooltip:{trigger:"axis",show:!0,formatter(ie){let gl,_l,Cl,Ql;if(Ci>1){_l=ie[0].value[le?1:0],gl=`<span id="tooltip" style='font-weight: 600;'>${ut(_l,Ae)}</span>`;for(let $t=ie.length-1;$t>=0;$t--)ie[$t].seriesName!=="stackTotal"&&(Cl=ie[$t].value[le?0:1],gl=gl+`<br> <span style='font-size: 11px;'>${ie[$t].marker} ${ie[$t].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${ut(Cl,Ri(ie[$t].componentIndex,yl,_i)===0?A:ee)}</span>`)}else me==="value"?(_l=ie[0].value[le?1:0],Cl=ie[0].value[le?0:1],Ql=ie[0].seriesName,gl=`<span id="tooltip" style='font-weight: 600;'>${Xt(G,Ae)}: </span><span style='float:right; margin-left: 10px;'>${ut(_l,Ae)}</span><br/><span style='font-weight: 600;'>${Xt(Ql,A)}: </span><span style='float:right; margin-left: 10px;'>${ut(Cl,A)}</span>`):(_l=ie[0].value[le?1:0],Cl=ie[0].value[le?0:1],Ql=ie[0].seriesName,gl=`<span id="tooltip" style='font-weight: 600;'>${ut(_l,Ae)}</span><br/><span>${Xt(Ql,A)}: </span><span style='float:right; margin-left: 10px;'>${ut(Cl,A)}</span>`);return gl},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:xe,type:"scroll",top:Nl,padding:[0,0,0,0],data:[]},grid:{left:qt??(le?"1%":"0.8%"),right:Dt??(le?"4%":"3%"),bottom:Yl,top:jl,containLabel:!0},xAxis:At,yAxis:wt,series:[],animation:!0,graphic:Jt,color:a}),H.update(()=>ei)}catch(yl){if(t(14,Pl=yl.message),console.error("\x1B[31m%s\x1B[0m",`Error in ${Ne}: ${yl.message}`),Di)throw Pl;P.update(Rl=>({...Rl,error:Pl}))}i.$$.dirty[0]&1},ki(Ni,P),ki(Fi,H),[g,W,p,le,_,zt,X,ue,Ne,ot,kt,C,vt,bi,Pl,Bi,r,n,s,l,I,H,V,fl,G,j,me,Pe,ze,_e,Re,Oe,Ee,De,Be,Xe,ke,Je,z,We,$e,Ue,R,ge,He,et,xe,ve,Vt,Y,O,$,ce,ye,te,ne,Le,je,Ve,Ie,Ye,Me,be,nt,Ge,bt,ht,gt,_t,Ct,ft,at,st,Mt,ct,qt,Dt,fe,x,Fe,he,Zt,Gt,dt,Il,ul,mt,St,Ae,A,ee,Rt,rt,Et,Bt,Wt,At,wt,Jt,ei,Ml,cl,ti,li,ii,ni,si,ri,ai,oi,fi,ui,ci,dl,Dl,Nl,jl,Yl,di,mi,ml,hi,yi,Xl,xt,hl,Ut,Fl,Ft,gi,pt,a,u,c,b,w,E]}class Ir extends lt{constructor(e){super(),it(this,e,Or,wr,tt,{data:0,queryID:6,x:24,y:25,y2:49,series:50,size:51,tooltipTitle:52,showAllXAxisLabels:1,printEchartsConfig:2,swapXY:3,title:7,subtitle:53,chartType:8,bubble:54,hist:55,boxplot:56,xType:26,xAxisTitle:27,xBaseline:28,xTickMarks:29,xGridlines:30,xAxisLabels:31,sort:32,xFmt:57,xMin:58,xMax:59,yLog:33,yType:60,yLogBase:61,yAxisTitle:34,yBaseline:35,yTickMarks:36,yGridlines:37,yAxisLabels:38,yMin:62,yMax:63,yScale:39,yFmt:64,yAxisColor:65,y2AxisTitle:40,y2Baseline:41,y2TickMarks:42,y2Gridlines:43,y2AxisLabels:44,y2Min:66,y2Max:67,y2Scale:45,y2Fmt:68,y2AxisColor:69,sizeFmt:70,colorPalette:71,legend:46,echartsOptions:9,seriesOptions:10,seriesColors:72,stackType:73,stacked100:74,chartAreaHeight:47,renderer:11,downloadableData:4,downloadableImage:5,connectGroup:12,leftPadding:75,rightPadding:76,xLabelWrap:48},null,[-1,-1,-1,-1,-1])}}function Mr(i){let e;const t=i[7].default,l=sl(t,i,i[8],null);return{c(){l&&l.c()},l(s){l&&l.l(s)},m(s,n){l&&l.m(s,n),e=!0},p(s,n){l&&l.p&&(!e||n&256)&&rl(l,t,s,s[8],e?ol(t,s[8],n,null):al(s[8]),null)},i(s){e||(M(l,s),e=!0)},o(s){U(l,s),e=!1},d(s){l&&l.d(s)}}}function Dr(i){let e,t;const l=[i[5],{data:It.isQuery(i[11])?Array.from(i[11]):i[11]},{queryID:i[6]}];let s={$$slots:{default:[Mr]},$$scope:{ctx:i}};for(let n=0;n<l.length;n+=1)s=Lt(s,l[n]);return e=new Ir({props:s}),{c(){oe(e.$$.fragment)},l(n){ae(e.$$.fragment,n)},m(n,r){re(e,n,r),t=!0},p(n,r){const a=r&2144?xl(l,[r&32&&pl(n[5]),r&2048&&{data:It.isQuery(n[11])?Array.from(n[11]):n[11]},r&64&&{queryID:n[6]}]):{};r&256&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(M(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){se(e,n)}}}function Nr(i){let e,t;return e=new $l({props:{slot:"empty",emptyMessage:i[2],emptySet:i[1],chartType:i[5].chartType,isInitial:i[4]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&4&&(n.emptyMessage=l[2]),s&2&&(n.emptySet=l[1]),s&32&&(n.chartType=l[5].chartType),s&16&&(n.isInitial=l[4]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Fr(i){let e,t;return e=new Pi({props:{slot:"error",title:i[5].chartType,error:i[11].error.message}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&32&&(n.title=l[5].chartType),s&2048&&(n.error=l[11].error.message),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Pr(i){let e,t;return e=new Jl({props:{data:i[0],height:i[3],$$slots:{error:[Fr,({loaded:l})=>({11:l}),({loaded:l})=>l?2048:0],empty:[Nr],default:[Dr,({loaded:l})=>({11:l}),({loaded:l})=>l?2048:0]},$$scope:{ctx:i}}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,[s]){const n={};s&1&&(n.data=l[0]),s&8&&(n.height=l[3]),s&2358&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Rr(i,e,t){let l,{$$slots:s={},$$scope:n}=e,{data:r}=e;const a=It.isQuery(r)?r.hash:void 0;let o=(r==null?void 0:r.hash)===a,{emptySet:f=void 0}=e,{emptyMessage:u=void 0}=e,{height:c=200}=e,y=r==null?void 0:r.id;return i.$$set=T=>{t(10,e=Lt(Lt({},e),Ht(T))),"data"in T&&t(0,r=T.data),"emptySet"in T&&t(1,f=T.emptySet),"emptyMessage"in T&&t(2,u=T.emptyMessage),"height"in T&&t(3,c=T.height),"$$scope"in T&&t(8,n=T.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&t(4,o=(r==null?void 0:r.hash)===a),t(5,l={...Object.fromEntries(Object.entries(e).filter(([,T])=>T!==void 0))})},e=Ht(e),[r,f,u,c,o,l,y,s,n]}class Bn extends lt{constructor(e){super(),it(this,e,Rr,Pr,tt,{data:0,emptySet:1,emptyMessage:2,height:3})}}function zn(i,e,t,l,s,n,r,a,o,f,u=void 0,c=void 0,y=void 0,T=void 0){function b(g,X,G,j){let Y={name:X,data:g,yAxisIndex:G};return Y={...j,...Y},Y}let d,L,I,E=[],w,P,H,V,Q;function B(g,X){const G=[];function j(O){return typeof O>"u"}function Y(O,$){j(O)||(Array.isArray(O)?O.forEach(ce=>G.push([ce,$])):G.push([O,$]))}return Y(g,0),Y(X,1),G}let q=B(t,y);if(l!=null&&q.length===1)for(V=ql(i,l),d=0;d<V.length;d++){if(P=i.filter(g=>g[l]===V[d]),s?w=P.map(g=>[g[q[0][0]],a?g[e].toString():g[e]]):w=P.map(g=>[a?g[e].toString():g[e],g[q[0][0]]]),u){let g=P.map(X=>X[u]);w.forEach((X,G)=>X.push(g[G]))}if(c){let g=P.map(X=>X[c]);w.forEach((X,G)=>X.push(g[G]))}H=V[d]??"null",Q=q[0][1],I=b(w,H,Q,n),E.push(I)}if(l!=null&&q.length>1)for(V=ql(i,l),d=0;d<V.length;d++)for(P=i.filter(g=>g[l]===V[d]),L=0;L<q.length;L++){if(s?w=P.map(g=>[g[q[L][0]],a?g[e].toString():g[e]]):w=P.map(g=>[a?g[e].toString():g[e],g[q[L][0]]]),u){let g=P.map(X=>X[u]);w.forEach((X,G)=>X.push(g[G]))}if(c){let g=P.map(X=>X[c]);w.forEach((X,G)=>X.push(g[G]))}H=(V[d]??"null")+" - "+o[q[L][0]].title,Q=q[L][1],I=b(w,H,Q,n),E.push(I)}if(l==null&&q.length>1)for(d=0;d<q.length;d++){if(s?w=i.map(g=>[g[q[d][0]],a?g[e].toString():g[e]]):w=i.map(g=>[a?g[e].toString():g[e],g[q[d][0]]]),u){let g=i.map(X=>X[u]);w.forEach((X,G)=>X.push(g[G]))}if(c){let g=i.map(X=>X[c]);w.forEach((X,G)=>X.push(g[G]))}H=o[q[d][0]].title,Q=q[d][1],I=b(w,H,Q,n),E.push(I)}if(l==null&&q.length===1){if(s?w=i.map(g=>[g[q[0][0]],a?g[e].toString():g[e]]):w=i.map(g=>[a?g[e].toString():g[e],g[q[0][0]]]),u){let g=i.map(X=>X[u]);w.forEach((X,G)=>X.push(g[G]))}if(c){let g=i.map(X=>X[c]);w.forEach((X,G)=>X.push(g[G]))}H=o[q[0][0]].title,Q=q[0][1],I=b(w,H,Q,n),E.push(I)}return f&&E.sort((g,X)=>f.indexOf(g.name)-f.indexOf(X.name)),T&&E.forEach(g=>{g.name=rs(g.name,T)}),E}function Br(i){let e=[];for(let t=1;t<i.length;t++)e.push(i[t]-i[t-1]);return e}function Gn(i,e){return(typeof i!="number"||isNaN(i))&&(i=0),(typeof e!="number"||isNaN(e))&&(e=0),i=Math.abs(i),e=Math.abs(e),e<=.01?i:Gn(e,i%e)}function zr(i,e){if(!Array.isArray(i))throw new TypeError("Cannot calculate extent of non-array value.");let t,l;for(const s of i)typeof s=="number"&&(t===void 0?s>=s&&(t=l=s):(t>s&&(t=s),l<s&&(l=s)));return[t,l]}function Gr(i,e){let[t,l]=zr(i);const s=[];let n=t;for(;n<=l;)s.push(Math.round((n+Number.EPSILON)*1e8)/1e8),n+=e;return s}function Ur(i){if(i.length<=1)return;i.sort(function(t,l){return t-l}),i=i.map(function(t){return t*1e8}),i=Br(i);let e=i.reduce((t,l)=>Gn(t,l))/1e8;return e=Math.round((e+Number.EPSILON)*1e8)/1e8,e}function Gl(i,e,t,l,s=!1,n=!1){var T;let r=!1;const a=i.map(b=>Object.assign({},b,{[e]:b[e]instanceof Date?(r=!0,b[e].toISOString()):b[e]})).filter(b=>b[e]!==void 0&&b[e]!==null),o=Array.from(a).reduce((b,d)=>(d[e]instanceof Date&&(d[e]=d[e].toISOString(),r=!0),l?(b[d[l]??"null"]||(b[d[l]??"null"]=[]),b[d[l]??"null"].push(d)):(b.default||(b.default=[]),b.default.push(d)),b),{}),f={};let u;const c=((T=a.find(b=>b&&b[e]!==null&&b[e]!==void 0))==null?void 0:T[e])??null;switch(typeof c){case"object":throw c===null?new Error(`Column '${e}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(u=ql(a,e),n){const b=Ur(u);f[e]=Gr(u,b)}break;case"string":u=ql(a,e),f[e]=u;break}const y=[];for(const b of Object.values(o)){const d=l?{[l]:null}:{};if(s)if(t instanceof Array)for(let I=0;I<t.length;I++)d[t[I]]=0;else d[t]=0;else if(t instanceof Array)for(let I=0;I<t.length;I++)d[t[I]]=null;else d[t]=null;l&&(f[l]=l);const L=[];Object.keys(f).length===0?L.push(Ji([e],d)):L.push(Ji(f,d)),y.push(el(b,...L))}return r?y.flat().map(b=>({...b,[e]:new Date(b[e])})):y.flat()}function Tn(i,e,t){let l=el(i,Si(e,[as(t,Ei)]));if(typeof t=="object")for(let s=0;s<l.length;s++){l[s].stackTotal=0;for(let n=0;n<t.length;n++)l[s].stackTotal=l[s].stackTotal+l[s][t[n]]}return l}let Hr=60;function vr(i,e,t){let l,s,n,r,a,o,f,u,c,y,T,b,d,L,I,E,w,P,H,V,Q=de,B=()=>(Q(),Q=Tt(r,R=>t(49,V=R)),r),q,g=de,X=()=>(g(),g=Tt(n,R=>t(50,q=R)),n),G,j=de,Y=()=>(j(),j=Tt(a,R=>t(51,G=R)),a),O,$=de,ce=()=>($(),$=Tt(l,R=>t(52,O=R)),l);i.$$.on_destroy.push(()=>Q()),i.$$.on_destroy.push(()=>g()),i.$$.on_destroy.push(()=>j()),i.$$.on_destroy.push(()=>$());const{resolveColor:W}=Pt();let{y:p=void 0}=e;const fe=!!p;let{y2:x=void 0}=e;const le=!!x;let{series:ue=void 0}=e;const ye=!!ue;let{options:Ne=void 0}=e,{name:te=void 0}=e,{type:ne="stacked"}=e,{stackName:Le=void 0}=e,{fillColor:Fe=void 0}=e,{fillOpacity:me=void 0}=e,{outlineColor:Pe=void 0}=e,{outlineWidth:ze=void 0}=e,{labels:_e=!1}=e,{seriesLabels:Re=!0}=e,{labelSize:Oe=11}=e,{labelPosition:Ee=void 0}=e,{labelColor:je=void 0}=e,{labelFmt:Ve=void 0}=e,Ie;Ve&&(Ie=Ot(Ve));let{yLabelFmt:De=void 0}=e,Ye;De&&(Ye=Ot(De));let{y2LabelFmt:Me=void 0}=e,Be;Me&&(Be=Ot(Me));let{y2SeriesType:Xe="bar"}=e,{stackTotalLabel:ke=!0}=e,{showAllLabels:Je=!1}=e,{seriesOrder:z=void 0}=e,be,nt,We,Ge;const bt={outside:"top",inside:"inside"},$e={outside:"right",inside:"inside"};let{seriesLabelFmt:Ue=void 0}=e;return In(()=>{Ne&&s.update(R=>({...R,...Ne})),H&&s.update(R=>{if(ne.includes("stacked")?R.tooltip={...R.tooltip,order:"seriesDesc"}:R.tooltip={...R.tooltip,order:"seriesAsc"},ne==="stacked100"&&(b?R.xAxis={...R.xAxis,max:1}:R.yAxis[0]={...R.yAxis[0],max:1}),b)R.yAxis={...R.yAxis,...H.xAxis},R.xAxis={...R.xAxis,...H.yAxis};else if(R.yAxis[0]={...R.yAxis[0],...H.yAxis},R.xAxis={...R.xAxis,...H.xAxis},x&&(R.yAxis[1]={...R.yAxis[1],show:!0},["line","bar","scatter"].includes(Xe)))for(let ge=0;ge<T;ge++)R.series[y+ge].type=Xe,R.series[y+ge].stack=void 0;return R})}),i.$$set=R=>{"y"in R&&t(4,p=R.y),"y2"in R&&t(5,x=R.y2),"series"in R&&t(6,ue=R.series),"options"in R&&t(13,Ne=R.options),"name"in R&&t(7,te=R.name),"type"in R&&t(14,ne=R.type),"stackName"in R&&t(8,Le=R.stackName),"fillColor"in R&&t(15,Fe=R.fillColor),"fillOpacity"in R&&t(16,me=R.fillOpacity),"outlineColor"in R&&t(17,Pe=R.outlineColor),"outlineWidth"in R&&t(18,ze=R.outlineWidth),"labels"in R&&t(9,_e=R.labels),"seriesLabels"in R&&t(10,Re=R.seriesLabels),"labelSize"in R&&t(19,Oe=R.labelSize),"labelPosition"in R&&t(11,Ee=R.labelPosition),"labelColor"in R&&t(20,je=R.labelColor),"labelFmt"in R&&t(21,Ve=R.labelFmt),"yLabelFmt"in R&&t(22,De=R.yLabelFmt),"y2LabelFmt"in R&&t(23,Me=R.y2LabelFmt),"y2SeriesType"in R&&t(24,Xe=R.y2SeriesType),"stackTotalLabel"in R&&t(12,ke=R.stackTotalLabel),"showAllLabels"in R&&t(25,Je=R.showAllLabels),"seriesOrder"in R&&t(26,z=R.seriesOrder),"seriesLabelFmt"in R&&t(27,Ue=R.seriesLabelFmt)},i.$$.update=()=>{i.$$.dirty[0]&32768&&X(t(2,n=W(Fe))),i.$$.dirty[0]&131072&&B(t(1,r=W(Pe))),i.$$.dirty[0]&512&&t(9,_e=_e==="true"||_e===!0),i.$$.dirty[0]&1024&&t(10,Re=Re==="true"||Re===!0),i.$$.dirty[0]&1048576&&Y(t(0,a=W(je))),i.$$.dirty[0]&4096&&t(12,ke=ke==="true"||ke===!0),i.$$.dirty[1]&2097152&&t(46,o=O.data),i.$$.dirty[1]&2097152&&t(42,f=O.x),i.$$.dirty[0]&16|i.$$.dirty[1]&2097152&&t(4,p=fe?p:O.y),i.$$.dirty[0]&32|i.$$.dirty[1]&2097152&&t(5,x=le?x:O.y2),i.$$.dirty[1]&2097152&&t(40,u=O.yFormat),i.$$.dirty[1]&2097152&&t(47,c=O.y2Format),i.$$.dirty[1]&2097152&&t(35,y=O.yCount),i.$$.dirty[1]&2097152&&t(36,T=O.y2Count),i.$$.dirty[1]&2097152&&t(37,b=O.swapXY),i.$$.dirty[1]&2097152&&t(39,d=O.xType),i.$$.dirty[1]&2097152&&t(43,L=O.xMismatch),i.$$.dirty[1]&2097152&&t(44,I=O.columnSummary),i.$$.dirty[1]&2097152&&t(48,E=O.sort),i.$$.dirty[0]&64|i.$$.dirty[1]&2097152&&t(6,ue=ye?ue:O.series),i.$$.dirty[0]&16848|i.$$.dirty[1]&174403&&(!ue&&typeof p!="object"?(t(7,te=te??Xt(p,I[p].title)),b&&d!=="category"&&(t(46,o=Gl(o,f,p,ue,!0,d!=="time")),t(39,d="category")),t(8,Le="stack1"),t(33,We=b?"right":"top")):(E===!0&&d==="category"&&(t(31,be=Tn(o,f,p)),typeof p=="object"?t(31,be=zl(be,"stackTotal",!1)):t(31,be=zl(be,p,!1)),t(32,nt=be.map(R=>R[f])),t(46,o=[...o].sort(function(R,ge){return nt.indexOf(R[f])-nt.indexOf(ge[f])}))),b||(d==="value"||d==="category")&&ne.includes("stacked")?(t(46,o=Gl(o,f,p,ue,!0,d==="value")),t(39,d="category")):d==="time"&&ne.includes("stacked")&&t(46,o=Gl(o,f,p,ue,!0,!0)),ne.includes("stacked")?(t(8,Le=Le??"stack1"),t(33,We="inside")):(t(8,Le=void 0),t(33,We=b?"right":"top")))),i.$$.dirty[0]&16400|i.$$.dirty[1]&34816&&ne==="stacked"&&t(34,Ge=Tn(o,f,p)),i.$$.dirty[0]&2048|i.$$.dirty[1]&68&&t(11,Ee=(b?$e[Ee]:bt[Ee])??We),i.$$.dirty[0]&1913458432|i.$$.dirty[1]&1901168&&t(45,w={type:"bar",stack:Le,label:{show:_e&&Re,formatter(R){return R.value[b?0:1]===0?"":ut(R.value[b?0:1],[Ye??Ie??u,Be??Ie??c][Ri(R.componentIndex,y,T)])},position:Ee,fontSize:Oe,color:G},labelLayout:{hideOverlap:!Je},emphasis:{focus:"series"},barMaxWidth:Hr,itemStyle:{color:q,opacity:me,borderColor:V,borderWidth:ze}}),i.$$.dirty[0]&201326832|i.$$.dirty[1]&63552&&t(41,P=zn(o,f,p,ue,b,w,te,L,I,z,void 0,void 0,x,Ue)),i.$$.dirty[0]&268981072|i.$$.dirty[1]&7880&&s.update(R=>(R.series.push(...P),R.legend.data.push(...P.map(ge=>ge.name.toString())),_e===!0&&ne==="stacked"&&typeof p=="object"|ue!==void 0&&ke===!0&&ue!==f&&(R.series.push({type:"bar",stack:Le,name:"stackTotal",color:"none",data:Ge.map(ge=>[b?0:L?ge[f].toString():ge[f],b?L?ge[f].toString():ge[f]:0]),label:{show:!0,position:b?"right":"top",formatter(ge){let He=0;return P.forEach(ht=>{He+=ht.data[ge.dataIndex][b?0:1]}),He===0?"":ut(He,Ie??u)},fontWeight:"bold",fontSize:Oe,padding:b?[0,0,0,5]:void 0}}),R.legend.selectedMode=!1),R)),i.$$.dirty[1]&256&&(H={xAxis:{boundaryGap:["1%","2%"],type:d}})},ce(t(3,l=Ul(Ni))),t(38,s=Ul(Fi)),[a,r,n,l,p,x,ue,te,Le,_e,Re,Ee,ke,Ne,ne,Fe,me,Pe,ze,Oe,je,Ve,De,Me,Xe,Je,z,Ue,Ie,Ye,Be,be,nt,We,Ge,y,T,b,s,d,u,P,f,L,I,w,o,c,E,V,q,G,O]}class qr extends lt{constructor(e){super(),it(this,e,vr,null,tt,{y:4,y2:5,series:6,options:13,name:7,type:14,stackName:8,fillColor:15,fillOpacity:16,outlineColor:17,outlineWidth:18,labels:9,seriesLabels:10,labelSize:19,labelPosition:11,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,stackTotalLabel:12,showAllLabels:25,seriesOrder:26,seriesLabelFmt:27},null,[-1,-1])}}function Vr(i){let e,t,l;e=new qr({props:{type:i[38],fillColor:i[72],fillOpacity:i[39],outlineColor:i[71],outlineWidth:i[40],labels:i[43],labelSize:i[44],labelPosition:i[45],labelColor:i[69],labelFmt:i[46],yLabelFmt:i[47],y2LabelFmt:i[48],stackTotalLabel:i[49],seriesLabels:i[50],showAllLabels:i[51],y2SeriesType:i[9],seriesOrder:i[60],seriesLabelFmt:i[62]}});const s=i[81].default,n=sl(s,i,i[82],null);return{c(){oe(e.$$.fragment),t=J(),n&&n.c()},l(r){ae(e.$$.fragment,r),t=Z(r),n&&n.l(r)},m(r,a){re(e,r,a),S(r,t,a),n&&n.m(r,a),l=!0},p(r,a){const o={};a[1]&128&&(o.type=r[38]),a[2]&1024&&(o.fillColor=r[72]),a[1]&256&&(o.fillOpacity=r[39]),a[2]&512&&(o.outlineColor=r[71]),a[1]&512&&(o.outlineWidth=r[40]),a[1]&4096&&(o.labels=r[43]),a[1]&8192&&(o.labelSize=r[44]),a[1]&16384&&(o.labelPosition=r[45]),a[2]&128&&(o.labelColor=r[69]),a[1]&32768&&(o.labelFmt=r[46]),a[1]&65536&&(o.yLabelFmt=r[47]),a[1]&131072&&(o.y2LabelFmt=r[48]),a[1]&262144&&(o.stackTotalLabel=r[49]),a[1]&524288&&(o.seriesLabels=r[50]),a[1]&1048576&&(o.showAllLabels=r[51]),a[0]&512&&(o.y2SeriesType=r[9]),a[1]&536870912&&(o.seriesOrder=r[60]),a[2]&1&&(o.seriesLabelFmt=r[62]),e.$set(o),n&&n.p&&(!l||a[2]&1048576)&&rl(n,s,r,r[82],l?ol(s,r[82],a,null):al(r[82]),null)},i(r){l||(M(e.$$.fragment,r),M(n,r),l=!0)},o(r){U(e.$$.fragment,r),U(n,r),l=!1},d(r){r&&m(t),se(e,r),n&&n.d(r)}}}function Wr(i){let e,t;return e=new Bn({props:{data:i[1],x:i[2],y:i[3],y2:i[4],xFmt:i[12],yFmt:i[10],y2Fmt:i[11],series:i[5],xType:i[6],yLog:i[7],yLogBase:i[8],legend:i[15],xAxisTitle:i[16],yAxisTitle:i[17],y2AxisTitle:i[18],xGridlines:i[19],yGridlines:i[20],y2Gridlines:i[21],xAxisLabels:i[22],yAxisLabels:i[23],y2AxisLabels:i[24],xBaseline:i[25],yBaseline:i[26],y2Baseline:i[27],xTickMarks:i[28],yTickMarks:i[29],y2TickMarks:i[30],yAxisColor:i[68],y2AxisColor:i[67],yMin:i[31],yMax:i[32],yScale:i[33],y2Min:i[34],y2Max:i[35],y2Scale:i[36],swapXY:i[0],title:i[13],subtitle:i[14],chartType:"Bar Chart",stackType:i[38],sort:i[42],stacked100:i[73],chartAreaHeight:i[41],showAllXAxisLabels:i[37],colorPalette:i[70],echartsOptions:i[52],seriesOptions:i[53],printEchartsConfig:i[54],emptySet:i[55],emptyMessage:i[56],renderer:i[57],downloadableData:i[58],downloadableImage:i[59],connectGroup:i[61],xLabelWrap:i[65],seriesColors:i[66],leftPadding:i[63],rightPadding:i[64],$$slots:{default:[Vr]},$$scope:{ctx:i}}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s[0]&2&&(n.data=l[1]),s[0]&4&&(n.x=l[2]),s[0]&8&&(n.y=l[3]),s[0]&16&&(n.y2=l[4]),s[0]&4096&&(n.xFmt=l[12]),s[0]&1024&&(n.yFmt=l[10]),s[0]&2048&&(n.y2Fmt=l[11]),s[0]&32&&(n.series=l[5]),s[0]&64&&(n.xType=l[6]),s[0]&128&&(n.yLog=l[7]),s[0]&256&&(n.yLogBase=l[8]),s[0]&32768&&(n.legend=l[15]),s[0]&65536&&(n.xAxisTitle=l[16]),s[0]&131072&&(n.yAxisTitle=l[17]),s[0]&262144&&(n.y2AxisTitle=l[18]),s[0]&524288&&(n.xGridlines=l[19]),s[0]&1048576&&(n.yGridlines=l[20]),s[0]&2097152&&(n.y2Gridlines=l[21]),s[0]&4194304&&(n.xAxisLabels=l[22]),s[0]&8388608&&(n.yAxisLabels=l[23]),s[0]&16777216&&(n.y2AxisLabels=l[24]),s[0]&33554432&&(n.xBaseline=l[25]),s[0]&67108864&&(n.yBaseline=l[26]),s[0]&134217728&&(n.y2Baseline=l[27]),s[0]&268435456&&(n.xTickMarks=l[28]),s[0]&536870912&&(n.yTickMarks=l[29]),s[0]&1073741824&&(n.y2TickMarks=l[30]),s[2]&64&&(n.yAxisColor=l[68]),s[2]&32&&(n.y2AxisColor=l[67]),s[1]&1&&(n.yMin=l[31]),s[1]&2&&(n.yMax=l[32]),s[1]&4&&(n.yScale=l[33]),s[1]&8&&(n.y2Min=l[34]),s[1]&16&&(n.y2Max=l[35]),s[1]&32&&(n.y2Scale=l[36]),s[0]&1&&(n.swapXY=l[0]),s[0]&8192&&(n.title=l[13]),s[0]&16384&&(n.subtitle=l[14]),s[1]&128&&(n.stackType=l[38]),s[1]&2048&&(n.sort=l[42]),s[1]&1024&&(n.chartAreaHeight=l[41]),s[1]&64&&(n.showAllXAxisLabels=l[37]),s[2]&256&&(n.colorPalette=l[70]),s[1]&2097152&&(n.echartsOptions=l[52]),s[1]&4194304&&(n.seriesOptions=l[53]),s[1]&8388608&&(n.printEchartsConfig=l[54]),s[1]&16777216&&(n.emptySet=l[55]),s[1]&33554432&&(n.emptyMessage=l[56]),s[1]&67108864&&(n.renderer=l[57]),s[1]&134217728&&(n.downloadableData=l[58]),s[1]&268435456&&(n.downloadableImage=l[59]),s[1]&1073741824&&(n.connectGroup=l[61]),s[2]&8&&(n.xLabelWrap=l[65]),s[2]&16&&(n.seriesColors=l[66]),s[2]&2&&(n.leftPadding=l[63]),s[2]&4&&(n.rightPadding=l[64]),s[0]&512|s[1]&538964864|s[2]&1050241&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function jr(i,e,t){let l,s,n,r,a,o,f,{$$slots:u={},$$scope:c}=e;const{resolveColor:y,resolveColorsObject:T,resolveColorPalette:b}=Pt();let{data:d=void 0}=e,{x:L=void 0}=e,{y:I=void 0}=e,{y2:E=void 0}=e,{series:w=void 0}=e,{xType:P=void 0}=e,{yLog:H=void 0}=e,{yLogBase:V=void 0}=e,{y2SeriesType:Q=void 0}=e,{yFmt:B=void 0}=e,{y2Fmt:q=void 0}=e,{xFmt:g=void 0}=e,{title:X=void 0}=e,{subtitle:G=void 0}=e,{legend:j=void 0}=e,{xAxisTitle:Y=void 0}=e,{yAxisTitle:O=E?"true":void 0}=e,{y2AxisTitle:$=E?"true":void 0}=e,{xGridlines:ce=void 0}=e,{yGridlines:W=void 0}=e,{y2Gridlines:p=void 0}=e,{xAxisLabels:fe=void 0}=e,{yAxisLabels:x=void 0}=e,{y2AxisLabels:le=void 0}=e,{xBaseline:ue=void 0}=e,{yBaseline:ye=void 0}=e,{y2Baseline:Ne=void 0}=e,{xTickMarks:te=void 0}=e,{yTickMarks:ne=void 0}=e,{y2TickMarks:Le=void 0}=e,{yMin:Fe=void 0}=e,{yMax:me=void 0}=e,{yScale:Pe=void 0}=e,{y2Min:ze=void 0}=e,{y2Max:_e=void 0}=e,{y2Scale:Re=void 0}=e,{swapXY:Oe=!1}=e,{showAllXAxisLabels:Ee}=e,{type:je="stacked"}=e,Ve=je==="stacked100",{fillColor:Ie=void 0}=e,{fillOpacity:De=void 0}=e,{outlineColor:Ye=void 0}=e,{outlineWidth:Me=void 0}=e,{chartAreaHeight:Be=void 0}=e,{sort:Xe=void 0}=e,{colorPalette:ke="default"}=e,{labels:Je=void 0}=e,{labelSize:z=void 0}=e,{labelPosition:be=void 0}=e,{labelColor:nt=void 0}=e,{labelFmt:We=void 0}=e,{yLabelFmt:Ge=void 0}=e,{y2LabelFmt:bt=void 0}=e,{stackTotalLabel:$e=void 0}=e,{seriesLabels:Ue=void 0}=e,{showAllLabels:R=void 0}=e,{yAxisColor:ge=void 0}=e,{y2AxisColor:He=void 0}=e,{echartsOptions:ht=void 0}=e,{seriesOptions:gt=void 0}=e,{printEchartsConfig:et=!1}=e,{emptySet:_t=void 0}=e,{emptyMessage:Ct=void 0}=e,{renderer:ft=void 0}=e,{downloadableData:at=void 0}=e,{downloadableImage:xe=void 0}=e,{seriesColors:ot=void 0}=e,{seriesOrder:kt=void 0}=e,{connectGroup:st=void 0}=e,{seriesLabelFmt:Mt=void 0}=e,{leftPadding:ct=void 0}=e,{rightPadding:ve=void 0}=e,{xLabelWrap:C=void 0}=e;return i.$$set=_=>{"data"in _&&t(1,d=_.data),"x"in _&&t(2,L=_.x),"y"in _&&t(3,I=_.y),"y2"in _&&t(4,E=_.y2),"series"in _&&t(5,w=_.series),"xType"in _&&t(6,P=_.xType),"yLog"in _&&t(7,H=_.yLog),"yLogBase"in _&&t(8,V=_.yLogBase),"y2SeriesType"in _&&t(9,Q=_.y2SeriesType),"yFmt"in _&&t(10,B=_.yFmt),"y2Fmt"in _&&t(11,q=_.y2Fmt),"xFmt"in _&&t(12,g=_.xFmt),"title"in _&&t(13,X=_.title),"subtitle"in _&&t(14,G=_.subtitle),"legend"in _&&t(15,j=_.legend),"xAxisTitle"in _&&t(16,Y=_.xAxisTitle),"yAxisTitle"in _&&t(17,O=_.yAxisTitle),"y2AxisTitle"in _&&t(18,$=_.y2AxisTitle),"xGridlines"in _&&t(19,ce=_.xGridlines),"yGridlines"in _&&t(20,W=_.yGridlines),"y2Gridlines"in _&&t(21,p=_.y2Gridlines),"xAxisLabels"in _&&t(22,fe=_.xAxisLabels),"yAxisLabels"in _&&t(23,x=_.yAxisLabels),"y2AxisLabels"in _&&t(24,le=_.y2AxisLabels),"xBaseline"in _&&t(25,ue=_.xBaseline),"yBaseline"in _&&t(26,ye=_.yBaseline),"y2Baseline"in _&&t(27,Ne=_.y2Baseline),"xTickMarks"in _&&t(28,te=_.xTickMarks),"yTickMarks"in _&&t(29,ne=_.yTickMarks),"y2TickMarks"in _&&t(30,Le=_.y2TickMarks),"yMin"in _&&t(31,Fe=_.yMin),"yMax"in _&&t(32,me=_.yMax),"yScale"in _&&t(33,Pe=_.yScale),"y2Min"in _&&t(34,ze=_.y2Min),"y2Max"in _&&t(35,_e=_.y2Max),"y2Scale"in _&&t(36,Re=_.y2Scale),"swapXY"in _&&t(0,Oe=_.swapXY),"showAllXAxisLabels"in _&&t(37,Ee=_.showAllXAxisLabels),"type"in _&&t(38,je=_.type),"fillColor"in _&&t(74,Ie=_.fillColor),"fillOpacity"in _&&t(39,De=_.fillOpacity),"outlineColor"in _&&t(75,Ye=_.outlineColor),"outlineWidth"in _&&t(40,Me=_.outlineWidth),"chartAreaHeight"in _&&t(41,Be=_.chartAreaHeight),"sort"in _&&t(42,Xe=_.sort),"colorPalette"in _&&t(76,ke=_.colorPalette),"labels"in _&&t(43,Je=_.labels),"labelSize"in _&&t(44,z=_.labelSize),"labelPosition"in _&&t(45,be=_.labelPosition),"labelColor"in _&&t(77,nt=_.labelColor),"labelFmt"in _&&t(46,We=_.labelFmt),"yLabelFmt"in _&&t(47,Ge=_.yLabelFmt),"y2LabelFmt"in _&&t(48,bt=_.y2LabelFmt),"stackTotalLabel"in _&&t(49,$e=_.stackTotalLabel),"seriesLabels"in _&&t(50,Ue=_.seriesLabels),"showAllLabels"in _&&t(51,R=_.showAllLabels),"yAxisColor"in _&&t(78,ge=_.yAxisColor),"y2AxisColor"in _&&t(79,He=_.y2AxisColor),"echartsOptions"in _&&t(52,ht=_.echartsOptions),"seriesOptions"in _&&t(53,gt=_.seriesOptions),"printEchartsConfig"in _&&t(54,et=_.printEchartsConfig),"emptySet"in _&&t(55,_t=_.emptySet),"emptyMessage"in _&&t(56,Ct=_.emptyMessage),"renderer"in _&&t(57,ft=_.renderer),"downloadableData"in _&&t(58,at=_.downloadableData),"downloadableImage"in _&&t(59,xe=_.downloadableImage),"seriesColors"in _&&t(80,ot=_.seriesColors),"seriesOrder"in _&&t(60,kt=_.seriesOrder),"connectGroup"in _&&t(61,st=_.connectGroup),"seriesLabelFmt"in _&&t(62,Mt=_.seriesLabelFmt),"leftPadding"in _&&t(63,ct=_.leftPadding),"rightPadding"in _&&t(64,ve=_.rightPadding),"xLabelWrap"in _&&t(65,C=_.xLabelWrap),"$$scope"in _&&t(82,c=_.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&1&&(Oe==="true"||Oe===!0?t(0,Oe=!0):t(0,Oe=!1)),i.$$.dirty[2]&4096&&t(72,l=y(Ie)),i.$$.dirty[2]&8192&&t(71,s=y(Ye)),i.$$.dirty[2]&16384&&t(70,n=b(ke)),i.$$.dirty[2]&32768&&t(69,r=y(nt)),i.$$.dirty[2]&65536&&t(68,a=y(ge)),i.$$.dirty[2]&131072&&t(67,o=y(He)),i.$$.dirty[2]&262144&&t(66,f=T(ot))},[Oe,d,L,I,E,w,P,H,V,Q,B,q,g,X,G,j,Y,O,$,ce,W,p,fe,x,le,ue,ye,Ne,te,ne,Le,Fe,me,Pe,ze,_e,Re,Ee,je,De,Me,Be,Xe,Je,z,be,We,Ge,bt,$e,Ue,R,ht,gt,et,_t,Ct,ft,at,xe,kt,st,Mt,ct,ve,C,f,o,a,r,n,s,l,Ve,Ie,Ye,ke,nt,ge,He,ot,u,c]}class Yr extends lt{constructor(e){super(),it(this,e,jr,Wr,tt,{data:1,x:2,y:3,y2:4,series:5,xType:6,yLog:7,yLogBase:8,y2SeriesType:9,yFmt:10,y2Fmt:11,xFmt:12,title:13,subtitle:14,legend:15,xAxisTitle:16,yAxisTitle:17,y2AxisTitle:18,xGridlines:19,yGridlines:20,y2Gridlines:21,xAxisLabels:22,yAxisLabels:23,y2AxisLabels:24,xBaseline:25,yBaseline:26,y2Baseline:27,xTickMarks:28,yTickMarks:29,y2TickMarks:30,yMin:31,yMax:32,yScale:33,y2Min:34,y2Max:35,y2Scale:36,swapXY:0,showAllXAxisLabels:37,type:38,fillColor:74,fillOpacity:39,outlineColor:75,outlineWidth:40,chartAreaHeight:41,sort:42,colorPalette:76,labels:43,labelSize:44,labelPosition:45,labelColor:77,labelFmt:46,yLabelFmt:47,y2LabelFmt:48,stackTotalLabel:49,seriesLabels:50,showAllLabels:51,yAxisColor:78,y2AxisColor:79,echartsOptions:52,seriesOptions:53,printEchartsConfig:54,emptySet:55,emptyMessage:56,renderer:57,downloadableData:58,downloadableImage:59,seriesColors:80,seriesOrder:60,connectGroup:61,seriesLabelFmt:62,leftPadding:63,rightPadding:64,xLabelWrap:65},null,[-1,-1,-1])}}function Xr(i){let e,t;return e=new fs({props:{error:i[3]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&8&&(n.error=l[3]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Qr(i){let e,t=ut(i[2],i[4])+"",l,s,n,r=i[1]&&Ln(i);return{c(){e=F("span"),l=Te(t),s=J(),r&&r.c(),this.h()},l(a){e=N(a,"SPAN",{style:!0});var o=K(e);l=Ce(o,t),s=Z(o),r&&r.l(o),o.forEach(m),this.h()},h(){v(e,"color",i[5])},m(a,o){S(a,e,o),D(e,l),D(e,s),r&&r.m(e,null),n=!0},p(a,o){(!n||o&20)&&t!==(t=ut(a[2],a[4])+"")&&Qe(l,t),a[1]?r?(r.p(a,o),o&2&&M(r,1)):(r=Ln(a),r.c(),M(r,1),r.m(e,null)):r&&(Ke(),U(r,1,1,()=>{r=null}),Ze()),(!n||o&32)&&v(e,"color",a[5])},i(a){n||(M(r),n=!0)},o(a){U(r),n=!1},d(a){a&&m(e),r&&r.d()}}}function Kr(i){let e,t,l,s,n,r="Placeholder: no data currently referenced.";return{c(){e=F("span"),t=Te("["),l=Te(i[0]),s=Te("]"),n=F("span"),n.textContent=r,this.h()},l(a){e=N(a,"SPAN",{class:!0});var o=K(e);t=Ce(o,"["),l=Ce(o,i[0]),s=Ce(o,"]"),n=N(o,"SPAN",{class:!0,"data-svelte-h":!0}),Se(n)!=="svelte-ddarzq"&&(n.textContent=r),o.forEach(m),this.h()},h(){h(n,"class","error-msg svelte-1mb9o01"),h(e,"class","placeholder svelte-1mb9o01")},m(a,o){S(a,e,o),D(e,t),D(e,l),D(e,s),D(e,n)},p(a,o){o&1&&Qe(l,a[0])},i:de,o:de,d(a){a&&m(e)}}}function Ln(i){let e,t;return e=new Nn({props:{description:i[1]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&2&&(n.description=l[1]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Zr(i){let e,t,l,s;const n=[Kr,Qr,Xr],r=[];function a(o,f){return o[0]?0:o[3]?2:1}return e=a(i),t=r[e]=n[e](i),{c(){t.c(),l=pe()},l(o){t.l(o),l=pe()},m(o,f){r[e].m(o,f),S(o,l,f),s=!0},p(o,[f]){let u=e;e=a(o),e===u?r[e].p(o,f):(Ke(),U(r[u],1,1,()=>{r[u]=null}),Ze(),t=r[e],t?t.p(o,f):(t=r[e]=n[e](o),t.c()),M(t,1),t.m(l.parentNode,l))},i(o){s||(M(t),s=!0)},o(o){U(t),s=!1},d(o){o&&m(l),r[e].d(o)}}}function Jr(i,e,t){let l,s,n=de,r=()=>(n(),n=Tt(l,V=>t(15,s=V)),l);i.$$.on_destroy.push(()=>n());const{resolveColor:a}=Pt();let{data:o=null}=e,{row:f=0}=e,{column:u=null}=e,{value:c=null}=e,{placeholder:y=null}=e,{description:T=void 0}=e,{fmt:b=void 0}=e,d,L,I,{color:E=void 0}=e,w="",{redNegatives:P=!1}=e,H;return i.$$set=V=>{"data"in V&&t(7,o=V.data),"row"in V&&t(10,f=V.row),"column"in V&&t(8,u=V.column),"value"in V&&t(11,c=V.value),"placeholder"in V&&t(0,y=V.placeholder),"description"in V&&t(1,T=V.description),"fmt"in V&&t(12,b=V.fmt),"color"in V&&t(13,E=V.color),"redNegatives"in V&&t(9,P=V.redNegatives)},i.$$.update=()=>{var V;if(i.$$.dirty&2304&&t(8,u=u??c),i.$$.dirty&21897)try{if(t(3,I=void 0),!y)if(o){if(typeof o=="string")throw Error(`Received: data=${o}, expected: data={${o}}`);if(Array.isArray(o)||t(7,o=[o]),isNaN(f))throw Error("row must be a number (row="+f+")");try{Object.keys(o[f])[0]}catch{throw Error("Row "+f+" does not exist in the dataset")}t(8,u=u??Object.keys(o[f])[0]),Al(o,[u]),t(14,H=El(o,"array"));const Q=H.filter(B=>{var q;return B.type==="date"&&!(((q=o[0])==null?void 0:q[B.id])instanceof Date)}).map(B=>B.id);for(let B=0;B<Q.length;B++)t(7,o=os(o,Q[B]));t(2,L=o[f][u]),t(14,H=H.filter(B=>B.id===u)),b?t(4,d=Ot(b,(V=H[0].format)==null?void 0:V.valueType)):t(4,d=H[0].format)}else throw Error("No data provided. If you referenced a query result, check that the name is correct.")}catch(Q){if(t(3,I=Q.message),console.error("\x1B[31m%s\x1B[0m",`Error in Value: ${I}`),Di)throw I}i.$$.dirty&2304&&c&&u&&console.warn('Both "value" and "column" were supplied as props to Value. "value" will be ignored.'),i.$$.dirty&8192&&r(t(6,l=a(E))),i.$$.dirty&512&&t(9,P=P==="true"||P===!0),i.$$.dirty&33284&&(P||s)&&(P&&L<0?t(5,w="rgb(220 38 38)"):s&&t(5,w=s))},[y,T,L,I,d,w,l,o,u,P,f,c,b,E,H,s]}class xr extends lt{constructor(e){super(),it(this,e,Jr,Zr,tt,{data:7,row:10,column:8,value:11,placeholder:0,description:1,fmt:12,color:13,redNegatives:9})}}function pr(i){let e;const t=i[7].default,l=sl(t,i,i[8],null);return{c(){l&&l.c()},l(s){l&&l.l(s)},m(s,n){l&&l.m(s,n),e=!0},p(s,n){l&&l.p&&(!e||n&256)&&rl(l,t,s,s[8],e?ol(t,s[8],n,null):al(s[8]),null)},i(s){e||(M(l,s),e=!0)},o(s){U(l,s),e=!1},d(s){l&&l.d(s)}}}function $r(i){let e,t;const l=[i[4],{data:It.isQuery(i[11])?Array.from(i[11]):i[11]}];let s={$$slots:{default:[pr]},$$scope:{ctx:i}};for(let n=0;n<l.length;n+=1)s=Lt(s,l[n]);return e=new xr({props:s}),{c(){oe(e.$$.fragment)},l(n){ae(e.$$.fragment,n)},m(n,r){re(e,n,r),t=!0},p(n,r){const a=r&2064?xl(l,[r&16&&pl(n[4]),r&2048&&{data:It.isQuery(n[11])?Array.from(n[11]):n[11]}]):{};r&256&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(M(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){se(e,n)}}}function kn(i){let e,t;return e=new $l({props:{emptyMessage:i[2],emptySet:i[1],chartType:ia,isInitial:i[3]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&4&&(n.emptyMessage=l[2]),s&2&&(n.emptySet=l[1]),s&8&&(n.isInitial=l[3]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function ea(i){let e,t,l=!i[4].placeholder&&kn(i);return{c(){e=F("span"),l&&l.c(),this.h()},l(s){e=N(s,"SPAN",{slot:!0});var n=K(e);l&&l.l(n),n.forEach(m),this.h()},h(){h(e,"slot","empty")},m(s,n){S(s,e,n),l&&l.m(e,null),t=!0},p(s,n){s[4].placeholder?l&&(Ke(),U(l,1,1,()=>{l=null}),Ze()):l?(l.p(s,n),n&16&&M(l,1)):(l=kn(s),l.c(),M(l,1),l.m(e,null))},i(s){t||(M(l),t=!0)},o(s){U(l),t=!1},d(s){s&&m(e),l&&l.d()}}}function ta(i){let e,t="Loading...";return{c(){e=F("span"),e.textContent=t,this.h()},l(l){e=N(l,"SPAN",{slot:!0,class:!0,"data-svelte-h":!0}),Se(e)!=="svelte-89gxhc"&&(e.textContent=t),this.h()},h(){h(e,"slot","skeleton"),h(e,"class","text-base-content-muted")},m(l,s){S(l,e,s)},p:de,d(l){l&&m(e)}}}function la(i){let e,t;return e=new Jl({props:{data:i[0],$$slots:{skeleton:[ta],empty:[ea],default:[$r,({loaded:l})=>({11:l}),({loaded:l})=>l?2048:0]},$$scope:{ctx:i}}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,[s]){const n={};s&1&&(n.data=l[0]),s&2334&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}let ia="Value";function na(i,e,t){let l,{$$slots:s={},$$scope:n}=e,{data:r}=e,{column:a}=e,{agg:o}=e;const f=It.isQuery(r)?r.hash:void 0;let u=(r==null?void 0:r.hash)===f,{emptySet:c=void 0}=e,{emptyMessage:y=void 0}=e;return i.$$set=T=>{t(10,e=Lt(Lt({},e),Ht(T))),"data"in T&&t(0,r=T.data),"column"in T&&t(5,a=T.column),"agg"in T&&t(6,o=T.agg),"emptySet"in T&&t(1,c=T.emptySet),"emptyMessage"in T&&t(2,y=T.emptyMessage),"$$scope"in T&&t(8,n=T.$$scope)},i.$$.update=()=>{i.$$.dirty&97&&o&&t(0,r=r.groupBy(void 0).agg({[o]:{col:a,as:a}})),i.$$.dirty&1&&t(3,u=(r==null?void 0:r.hash)===f),t(4,l=Object.fromEntries(Object.entries(e).filter(([,T])=>T!==void 0)))},e=Ht(e),[r,c,y,u,l,a,o,s,n]}class Wl extends lt{constructor(e){super(),it(this,e,na,la,tt,{data:0,column:5,agg:6,emptySet:1,emptyMessage:2})}}function sa(i){let e;const t=i[6].default,l=sl(t,i,i[7],null);return{c(){l&&l.c()},l(s){l&&l.l(s)},m(s,n){l&&l.m(s,n),e=!0},p(s,n){l&&l.p&&(!e||n&128)&&rl(l,t,s,s[7],e?ol(t,s[7],n,null):al(s[7]),null)},i(s){e||(M(l,s),e=!0)},o(s){U(l,s),e=!1},d(s){l&&l.d(s)}}}function ra(i){let e,t;const l=[i[4],{data:It.isQuery(i[10])?Array.from(i[10]):i[10]},{queryID:i[5]}];let s={$$slots:{default:[sa]},$$scope:{ctx:i}};for(let n=0;n<l.length;n+=1)s=Lt(s,l[n]);return e=new us({props:s}),{c(){oe(e.$$.fragment)},l(n){ae(e.$$.fragment,n)},m(n,r){re(e,n,r),t=!0},p(n,r){const a=r&1072?xl(l,[r&16&&pl(n[4]),r&1024&&{data:It.isQuery(n[10])?Array.from(n[10]):n[10]},r&32&&{queryID:n[5]}]):{};r&128&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(M(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){se(e,n)}}}function aa(i){let e,t;return e=new $l({props:{slot:"empty",emptyMessage:i[2],emptySet:i[1],chartType:i[4].chartType,isInitial:i[3]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&4&&(n.emptyMessage=l[2]),s&2&&(n.emptySet=l[1]),s&16&&(n.chartType=l[4].chartType),s&8&&(n.isInitial=l[3]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function oa(i){let e,t;return e=new Pi({props:{slot:"error",title:ua,error:i[10].error.message}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&1024&&(n.error=l[10].error.message),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function fa(i){let e,t;return e=new Jl({props:{data:i[0],$$slots:{error:[oa,({loaded:l})=>({10:l}),({loaded:l})=>l?1024:0],empty:[aa],default:[ra,({loaded:l})=>({10:l}),({loaded:l})=>l?1024:0]},$$scope:{ctx:i}}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,[s]){const n={};s&1&&(n.data=l[0]),s&1182&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}let ua="Sparkline";function ca(i,e,t){let l,{$$slots:s={},$$scope:n}=e,{data:r}=e;const a=It.isQuery(r)?r.hash:void 0;let o=(r==null?void 0:r.hash)===a,{emptySet:f=void 0}=e,{emptyMessage:u=void 0}=e,c=r==null?void 0:r.id;return i.$$set=y=>{t(9,e=Lt(Lt({},e),Ht(y))),"data"in y&&t(0,r=y.data),"emptySet"in y&&t(1,f=y.emptySet),"emptyMessage"in y&&t(2,u=y.emptyMessage),"$$scope"in y&&t(7,n=y.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&t(3,o=(r==null?void 0:r.hash)===a),t(4,l={...Object.fromEntries(Object.entries(e).filter(([,y])=>y!==void 0))})},e=Ht(e),[r,f,u,o,l,c,s,n]}class da extends lt{constructor(e){super(),it(this,e,ca,fa,tt,{data:0,emptySet:1,emptyMessage:2})}}function ma(i){let e,t,l,s,n,r,a,o,f,u,c,y,T,b=i[23]&&Sn(i);const d=[ba,ya],L=[];function I(P,H){return P[22]?0:1}a=I(i),o=L[a]=d[a](i);let E=i[8]&&En(i),w=i[7]&&An(i);return{c(){e=F("p"),t=Te(i[3]),l=J(),b&&b.c(),n=J(),r=F("div"),o.c(),f=J(),E&&E.c(),c=J(),w&&w.c(),y=pe(),this.h()},l(P){e=N(P,"P",{class:!0});var H=K(e);t=Ce(H,i[3]),l=Z(H),b&&b.l(H),H.forEach(m),n=Z(P),r=N(P,"DIV",{class:!0});var V=K(r);o.l(V),f=Z(V),E&&E.l(V),V.forEach(m),c=Z(P),w&&w.l(P),y=pe(),this.h()},h(){h(e,"class",s=Sl("text-sm align-top leading-none",i[19])),h(r,"class",u=Sl("relative text-xl font-medium mt-1.5",i[20]))},m(P,H){S(P,e,H),D(e,t),D(e,l),b&&b.m(e,null),S(P,n,H),S(P,r,H),L[a].m(r,null),D(r,f),E&&E.m(r,null),S(P,c,H),w&&w.m(P,H),S(P,y,H),T=!0},p(P,H){(!T||H&8)&&Qe(t,P[3]),P[23]?b?(b.p(P,H),H&8388608&&M(b,1)):(b=Sn(P),b.c(),M(b,1),b.m(e,null)):b&&(Ke(),U(b,1,1,()=>{b=null}),Ze()),(!T||H&524288&&s!==(s=Sl("text-sm align-top leading-none",P[19])))&&h(e,"class",s);let V=a;a=I(P),a===V?L[a].p(P,H):(Ke(),U(L[V],1,1,()=>{L[V]=null}),Ze(),o=L[a],o?o.p(P,H):(o=L[a]=d[a](P),o.c()),M(o,1),o.m(r,f)),P[8]?E?(E.p(P,H),H&256&&M(E,1)):(E=En(P),E.c(),M(E,1),E.m(r,null)):E&&(Ke(),U(E,1,1,()=>{E=null}),Ze()),(!T||H&1048576&&u!==(u=Sl("relative text-xl font-medium mt-1.5",P[20])))&&h(r,"class",u),P[7]?w?(w.p(P,H),H&128&&M(w,1)):(w=An(P),w.c(),M(w,1),w.m(y.parentNode,y)):w&&(Ke(),U(w,1,1,()=>{w=null}),Ze())},i(P){T||(M(b),M(o),M(E),M(w),T=!0)},o(P){U(b),U(o),U(E),U(w),T=!1},d(P){P&&(m(e),m(n),m(r),m(c),m(y)),b&&b.d(),L[a].d(),E&&E.d(),w&&w.d(P)}}}function ha(i){let e,t;return e=new cs({props:{inputType:"BigValue",error:i[24],width:"148",height:"28"}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&16777216&&(n.error=l[24]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Sn(i){let e,t;return e=new Nn({props:{description:i[23],size:"3"}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&8388608&&(n.description=l[23]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function ya(i){let e,t;return e=new Wl({props:{data:i[0],column:i[6],fmt:i[13]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&1&&(n.data=l[0]),s&64&&(n.column=l[6]),s&8192&&(n.fmt=l[13]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function ba(i){let e,t,l,s;return t=new Wl({props:{data:i[0],column:i[6],fmt:i[13]}}),{c(){e=F("a"),oe(t.$$.fragment),this.h()},l(n){e=N(n,"A",{class:!0,href:!0});var r=K(e);ae(t.$$.fragment,r),r.forEach(m),this.h()},h(){h(e,"class","hover:bg-base-200"),h(e,"href",l=Ol(i[22]))},m(n,r){S(n,e,r),re(t,e,null),s=!0},p(n,r){const a={};r&1&&(a.data=n[0]),r&64&&(a.column=n[6]),r&8192&&(a.fmt=n[13]),t.$set(a),(!s||r&4194304&&l!==(l=Ol(n[22])))&&h(e,"href",l)},i(n){s||(M(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&m(e),se(t)}}}function En(i){let e,t;return e=new da({props:{height:"15",data:i[0],dateCol:i[8],valueCol:i[6],type:i[9],interactive:"true",color:i[25],valueFmt:i[13]??i[10],dateFmt:i[11],yScale:i[2],connectGroup:i[12]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&1&&(n.data=l[0]),s&256&&(n.dateCol=l[8]),s&64&&(n.valueCol=l[6]),s&512&&(n.type=l[9]),s&33554432&&(n.color=l[25]),s&9216&&(n.valueFmt=l[13]??l[10]),s&2048&&(n.dateFmt=l[11]),s&4&&(n.yScale=l[2]),s&4096&&(n.connectGroup=l[12]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function An(i){let e,t,l,s;const n=[_a,ga],r=[];function a(o,f){return o[1]?0:1}return e=a(i),t=r[e]=n[e](i),{c(){t.c(),l=pe()},l(o){t.l(o),l=pe()},m(o,f){r[e].m(o,f),S(o,l,f),s=!0},p(o,f){let u=e;e=a(o),e===u?r[e].p(o,f):(Ke(),U(r[u],1,1,()=>{r[u]=null}),Ze(),t=r[e],t?t.p(o,f):(t=r[e]=n[e](o),t.c()),M(t,1),t.m(l.parentNode,l))},i(o){s||(M(t),s=!0)},o(o){U(t),s=!1},d(o){o&&m(l),r[e].d(o)}}}function ga(i){let e,t,l,s,n,r,a;const o=[Ta,Ca],f=[];function u(c,y){return c[22]?0:1}return t=u(i),l=f[t]=o[t](i),{c(){e=F("p"),l.c(),s=J(),n=F("span"),r=Te(i[4]),this.h()},l(c){e=N(c,"P",{class:!0});var y=K(e);l.l(y),s=Z(y),n=N(y,"SPAN",{});var T=K(n);r=Ce(T,i[4]),T.forEach(m),y.forEach(m),this.h()},h(){h(e,"class","text-xs font-sans /60 pt-[0.5px]")},m(c,y){S(c,e,y),f[t].m(e,null),D(e,s),D(e,n),D(n,r),a=!0},p(c,y){let T=t;t=u(c),t===T?f[t].p(c,y):(Ke(),U(f[T],1,1,()=>{f[T]=null}),Ze(),l=f[t],l?l.p(c,y):(l=f[t]=o[t](c),l.c()),M(l,1),l.m(e,s)),(!a||y&16)&&Qe(r,c[4])},i(c){a||(M(l),a=!0)},o(c){U(l),a=!1},d(c){c&&m(e),f[t].d()}}}function _a(i){let e,t,l,s;return t=new ds({props:{data:i[0],column:i[7],fmt:i[14],fontClass:"text-xs",symbolPosition:"left",neutralMin:i[15],neutralMax:i[16],text:i[4],downIsGood:i[5]}}),{c(){e=F("p"),oe(t.$$.fragment),this.h()},l(n){e=N(n,"P",{class:!0});var r=K(e);ae(t.$$.fragment,r),r.forEach(m),this.h()},h(){h(e,"class",l=Sl("text-xs font-sans mt-1",i[21]))},m(n,r){S(n,e,r),re(t,e,null),s=!0},p(n,r){const a={};r&1&&(a.data=n[0]),r&128&&(a.column=n[7]),r&16384&&(a.fmt=n[14]),r&32768&&(a.neutralMin=n[15]),r&65536&&(a.neutralMax=n[16]),r&16&&(a.text=n[4]),r&32&&(a.downIsGood=n[5]),t.$set(a),(!s||r&2097152&&l!==(l=Sl("text-xs font-sans mt-1",n[21])))&&h(e,"class",l)},i(n){s||(M(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&m(e),se(t)}}}function Ca(i){let e,t;return e=new Wl({props:{data:i[0],column:i[7],fmt:i[14]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&1&&(n.data=l[0]),s&128&&(n.column=l[7]),s&16384&&(n.fmt=l[14]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Ta(i){let e,t,l,s;return t=new Wl({props:{data:i[0],column:i[7],fmt:i[14]}}),{c(){e=F("a"),oe(t.$$.fragment),this.h()},l(n){e=N(n,"A",{class:!0,href:!0});var r=K(e);ae(t.$$.fragment,r),r.forEach(m),this.h()},h(){h(e,"class","hover:bg-base-200"),h(e,"href",l=Ol(i[22]))},m(n,r){S(n,e,r),re(t,e,null),s=!0},p(n,r){const a={};r&1&&(a.data=n[0]),r&128&&(a.column=n[7]),r&16384&&(a.fmt=n[14]),t.$set(a),(!s||r&4194304&&l!==(l=Ol(n[22])))&&h(e,"href",l)},i(n){s||(M(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&m(e),se(t)}}}function La(i){let e,t,l,s,n;const r=[ha,ma],a=[];function o(f,u){return f[24].length>0?0:1}return t=o(i),l=a[t]=r[t](i),{c(){e=F("div"),l.c(),this.h()},l(f){e=N(f,"DIV",{class:!0,style:!0});var u=K(e);l.l(u),u.forEach(m),this.h()},h(){h(e,"class","inline-block font-sans pt-2 pb-3 pl-0 mr-3 items-center align-top"),h(e,"style",s=`
        min-width: ${i[18]};
        max-width: ${i[17]};
		`)},m(f,u){S(f,e,u),a[t].m(e,null),n=!0},p(f,[u]){let c=t;t=o(f),t===c?a[t].p(f,u):(Ke(),U(a[c],1,1,()=>{a[c]=null}),Ze(),l=a[t],l?l.p(f,u):(l=a[t]=r[t](f),l.c()),M(l,1),l.m(e,null)),(!n||u&393216&&s!==(s=`
        min-width: ${f[18]};
        max-width: ${f[17]};
		`))&&h(e,"style",s)},i(f){n||(M(l),n=!0)},o(f){U(l),n=!1},d(f){f&&m(e),a[t].d()}}}function ka(i,e,t){let l;const{resolveColor:s}=Pt();let{data:n}=e,{value:r=null}=e,{comparison:a=null}=e,{comparisonDelta:o=!0}=e,{sparkline:f=null}=e,{sparklineType:u="line"}=e,{sparklineColor:c=void 0}=e,{sparklineValueFmt:y=void 0}=e,{sparklineDateFmt:T=void 0}=e,{sparklineYScale:b=!1}=e,{connectGroup:d=void 0}=e,{fmt:L=void 0}=e,{comparisonFmt:I=void 0}=e,{title:E=null}=e,{comparisonTitle:w=null}=e,{downIsGood:P=!1}=e,{neutralMin:H=0}=e,{neutralMax:V=0}=e,{maxWidth:Q="none"}=e,{minWidth:B="18%"}=e,{titleClass:q=void 0}=e,{valueClass:g=void 0}=e,{comparisonClass:X=void 0}=e,{link:G=null}=e,{description:j=void 0}=e,Y=[];return i.$$set=O=>{"data"in O&&t(0,n=O.data),"value"in O&&t(6,r=O.value),"comparison"in O&&t(7,a=O.comparison),"comparisonDelta"in O&&t(1,o=O.comparisonDelta),"sparkline"in O&&t(8,f=O.sparkline),"sparklineType"in O&&t(9,u=O.sparklineType),"sparklineColor"in O&&t(26,c=O.sparklineColor),"sparklineValueFmt"in O&&t(10,y=O.sparklineValueFmt),"sparklineDateFmt"in O&&t(11,T=O.sparklineDateFmt),"sparklineYScale"in O&&t(2,b=O.sparklineYScale),"connectGroup"in O&&t(12,d=O.connectGroup),"fmt"in O&&t(13,L=O.fmt),"comparisonFmt"in O&&t(14,I=O.comparisonFmt),"title"in O&&t(3,E=O.title),"comparisonTitle"in O&&t(4,w=O.comparisonTitle),"downIsGood"in O&&t(5,P=O.downIsGood),"neutralMin"in O&&t(15,H=O.neutralMin),"neutralMax"in O&&t(16,V=O.neutralMax),"maxWidth"in O&&t(17,Q=O.maxWidth),"minWidth"in O&&t(18,B=O.minWidth),"titleClass"in O&&t(19,q=O.titleClass),"valueClass"in O&&t(20,g=O.valueClass),"comparisonClass"in O&&t(21,X=O.comparisonClass),"link"in O&&t(22,G=O.link),"description"in O&&t(23,j=O.description)},i.$$.update=()=>{if(i.$$.dirty&2&&t(1,o=o==="true"||o===!0),i.$$.dirty&67108864&&t(25,l=s(c)),i.$$.dirty&4&&t(2,b=b==="true"||b===!0),i.$$.dirty&32&&t(5,P=P==="true"||P===!0),i.$$.dirty&16777689)try{Array.isArray(n)||t(0,n=[n]),Al(n,[r]);let O=El(n,"array"),$=O.find(ce=>ce.id===r);if(t(3,E=E??($?$.title:null)),a!==null){Al(n,[a]);let ce=O.find(W=>W.id===a);t(4,w=w??(ce?ce.title:null))}f!==null&&Al(n,[f])}catch(O){if(t(24,Y=[...Y,O]),Di)throw Y}},[n,o,b,E,w,P,r,a,f,u,y,T,d,L,I,H,V,Q,B,q,g,X,G,j,Y,l,c]}let Sa=class extends lt{constructor(e){super(),it(this,e,ka,La,tt,{data:0,value:6,comparison:7,comparisonDelta:1,sparkline:8,sparklineType:9,sparklineColor:26,sparklineValueFmt:10,sparklineDateFmt:11,sparklineYScale:2,connectGroup:12,fmt:13,comparisonFmt:14,title:3,comparisonTitle:4,downIsGood:5,neutralMin:15,neutralMax:16,maxWidth:17,minWidth:18,titleClass:19,valueClass:20,comparisonClass:21,link:22,description:23})}};function Ea(i){let e;const t=i[6].default,l=sl(t,i,i[7],null);return{c(){l&&l.c()},l(s){l&&l.l(s)},m(s,n){l&&l.m(s,n),e=!0},p(s,n){l&&l.p&&(!e||n&128)&&rl(l,t,s,s[7],e?ol(t,s[7],n,null):al(s[7]),null)},i(s){e||(M(l,s),e=!0)},o(s){U(l,s),e=!1},d(s){l&&l.d(s)}}}function Aa(i){let e,t;const l=[i[4],{data:It.isQuery(i[9])?Array.from(i[9]):i[9]}];let s={$$slots:{default:[Ea]},$$scope:{ctx:i}};for(let n=0;n<l.length;n+=1)s=Lt(s,l[n]);return e=new Sa({props:s}),{c(){oe(e.$$.fragment)},l(n){ae(e.$$.fragment,n)},m(n,r){re(e,n,r),t=!0},p(n,r){const a=r&528?xl(l,[r&16&&pl(n[4]),r&512&&{data:It.isQuery(n[9])?Array.from(n[9]):n[9]}]):{};r&128&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(M(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){se(e,n)}}}function wa(i){let e,t,l,s;return t=new ms({props:{error:i[9].error.message}}),{c(){e=F("div"),oe(t.$$.fragment),this.h()},l(n){e=N(n,"DIV",{slot:!0,class:!0,style:!0});var r=K(e);ae(t.$$.fragment,r),r.forEach(m),this.h()},h(){h(e,"slot","error"),h(e,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(e,"style",l=`
				min-width: ${i[5].minWidth};
				max-width: ${i[5].maxWidth};
		`)},m(n,r){S(n,e,r),re(t,e,null),s=!0},p(n,r){const a={};r&512&&(a.error=n[9].error.message),t.$set(a),(!s||r&32&&l!==(l=`
				min-width: ${n[5].minWidth};
				max-width: ${n[5].maxWidth};
		`))&&h(e,"style",l)},i(n){s||(M(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&m(e),se(t)}}}function Oa(i){let e,t,l,s;return t=new $l({props:{emptyMessage:i[2],emptySet:i[1],chartType:Da,isInitial:i[3]}}),{c(){e=F("div"),oe(t.$$.fragment),this.h()},l(n){e=N(n,"DIV",{slot:!0,class:!0,style:!0});var r=K(e);ae(t.$$.fragment,r),r.forEach(m),this.h()},h(){h(e,"slot","empty"),h(e,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(e,"style",l=`
				min-width: ${i[5].minWidth};
				max-width: ${i[5].maxWidth};
		`)},m(n,r){S(n,e,r),re(t,e,null),s=!0},p(n,r){const a={};r&4&&(a.emptyMessage=n[2]),r&2&&(a.emptySet=n[1]),r&8&&(a.isInitial=n[3]),t.$set(a),(!s||r&32&&l!==(l=`
				min-width: ${n[5].minWidth};
				max-width: ${n[5].maxWidth};
		`))&&h(e,"style",l)},i(n){s||(M(t.$$.fragment,n),s=!0)},o(n){U(t.$$.fragment,n),s=!1},d(n){n&&m(e),se(t)}}}function Ia(i){let e,t,l=(i[5].title??" ")+"",s,n,r,a,o;return r=new Wl({props:{column:i[5].value,fmt:i[5].fmt,data:i[9]}}),{c(){e=F("div"),t=F("p"),s=Te(l),n=J(),oe(r.$$.fragment),this.h()},l(f){e=N(f,"DIV",{class:!0,style:!0,slot:!0});var u=K(e);t=N(u,"P",{class:!0});var c=K(t);s=Ce(c,l),c.forEach(m),n=Z(u),ae(r.$$.fragment,u),u.forEach(m),this.h()},h(){h(t,"class","text-sm"),h(e,"class","inline-block font-sans pt-2 pb-3 pr-3 pl-0 mr-3 items-center align-top"),h(e,"style",a=`
			min-width: ${i[5].minWidth};
			max-width: ${i[5].maxWidth};
		`),h(e,"slot","skeleton")},m(f,u){S(f,e,u),D(e,t),D(t,s),D(e,n),re(r,e,null),o=!0},p(f,u){(!o||u&32)&&l!==(l=(f[5].title??" ")+"")&&Qe(s,l);const c={};u&32&&(c.column=f[5].value),u&32&&(c.fmt=f[5].fmt),u&512&&(c.data=f[9]),r.$set(c),(!o||u&32&&a!==(a=`
			min-width: ${f[5].minWidth};
			max-width: ${f[5].maxWidth};
		`))&&h(e,"style",a)},i(f){o||(M(r.$$.fragment,f),o=!0)},o(f){U(r.$$.fragment,f),o=!1},d(f){f&&m(e),se(r)}}}function Ma(i){let e,t;return e=new Jl({props:{data:i[0],$$slots:{skeleton:[Ia,({loaded:l})=>({9:l}),({loaded:l})=>l?512:0],empty:[Oa],error:[wa,({loaded:l})=>({9:l}),({loaded:l})=>l?512:0],default:[Aa,({loaded:l})=>({9:l}),({loaded:l})=>l?512:0]},$$scope:{ctx:i}}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,[s]){const n={};s&1&&(n.data=l[0]),s&702&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}let Da="Big Value";function Na(i,e,t){let l,{$$slots:s={},$$scope:n}=e,{data:r}=e;const a=It.isQuery(r)?r.hash:void 0;let o=(r==null?void 0:r.hash)===a,{emptySet:f=void 0}=e,{emptyMessage:u=void 0}=e;return i.$$set=c=>{t(5,e=Lt(Lt({},e),Ht(c))),"data"in c&&t(0,r=c.data),"emptySet"in c&&t(1,f=c.emptySet),"emptyMessage"in c&&t(2,u=c.emptyMessage),"$$scope"in c&&t(7,n=c.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&t(3,o=(r==null?void 0:r.hash)===a),t(4,l=Object.fromEntries(Object.entries(e).filter(([,c])=>c!==void 0)))},e=Ht(e),[r,f,u,o,l,e,s,n]}class wn extends lt{constructor(e){super(),it(this,e,Na,Ma,tt,{data:0,emptySet:1,emptyMessage:2})}}function Fa(i,e,t){let l,s,n,r,a,o,f,u,c,y,T,b,d,L,I,E,w=de,P=()=>(w(),w=Tt(l,z=>t(44,E=z)),l),H,V=de,Q=()=>(V(),V=Tt(s,z=>t(45,H=z)),s),B;i.$$.on_destroy.push(()=>w()),i.$$.on_destroy.push(()=>V());let q=Ul(Ni);Nt(i,q,z=>t(46,B=z));let g=Ul(Fi);const{resolveColor:X}=Pt();let{y:G=void 0}=e;const j=!!G;let{y2:Y=void 0}=e;const O=!!Y;let{series:$=void 0}=e;const ce=!!$;let{options:W=void 0}=e,{name:p=void 0}=e,{lineColor:fe=void 0}=e,{lineWidth:x=2}=e,{lineType:le="solid"}=e,{lineOpacity:ue=void 0}=e,{markers:ye=!1}=e,{markerShape:Ne="circle"}=e,{markerSize:te=8}=e,{labels:ne=!1}=e,{labelSize:Le=11}=e,{labelPosition:Fe="top"}=e,{labelColor:me=void 0}=e,{labelFmt:Pe=void 0}=e,ze;Pe&&(ze=Ot(Pe));let{yLabelFmt:_e=void 0}=e,Re;_e&&(Re=Ot(_e));let{y2LabelFmt:Oe=void 0}=e,Ee;Oe&&(Ee=Ot(Oe));let{y2SeriesType:je=void 0}=e,{showAllLabels:Ve=!1}=e,{handleMissing:Ie="gap"}=e,{step:De=!1}=e,{stepPosition:Ye="end"}=e,{seriesOrder:Me=void 0}=e,{seriesLabelFmt:Be=void 0}=e;const Xe={above:"top",below:"bottom",middle:"inside"},ke={above:"right",below:"left",middle:"inside"};let Je=a?"right":"top";return In(()=>{g.update(z=>{if(a)z.yAxis={...z.yAxis,...I.xAxis},z.xAxis={...z.xAxis,...I.yAxis};else if(z.yAxis[0]={...z.yAxis[0],...I.yAxis},z.xAxis={...z.xAxis,...I.xAxis},Y&&(z.yAxis[1]={...z.yAxis[1],show:!0},["line","bar","scatter"].includes(je)))for(let be=0;be<c;be++)z.series[u+be].type=je;return ne&&(z.axisPointer={triggerEmphasis:!1}),z})}),i.$$set=z=>{"y"in z&&t(3,G=z.y),"y2"in z&&t(4,Y=z.y2),"series"in z&&t(5,$=z.series),"options"in z&&t(12,W=z.options),"name"in z&&t(6,p=z.name),"lineColor"in z&&t(13,fe=z.lineColor),"lineWidth"in z&&t(14,x=z.lineWidth),"lineType"in z&&t(15,le=z.lineType),"lineOpacity"in z&&t(16,ue=z.lineOpacity),"markers"in z&&t(7,ye=z.markers),"markerShape"in z&&t(17,Ne=z.markerShape),"markerSize"in z&&t(18,te=z.markerSize),"labels"in z&&t(8,ne=z.labels),"labelSize"in z&&t(19,Le=z.labelSize),"labelPosition"in z&&t(9,Fe=z.labelPosition),"labelColor"in z&&t(20,me=z.labelColor),"labelFmt"in z&&t(21,Pe=z.labelFmt),"yLabelFmt"in z&&t(22,_e=z.yLabelFmt),"y2LabelFmt"in z&&t(23,Oe=z.y2LabelFmt),"y2SeriesType"in z&&t(24,je=z.y2SeriesType),"showAllLabels"in z&&t(10,Ve=z.showAllLabels),"handleMissing"in z&&t(25,Ie=z.handleMissing),"step"in z&&t(11,De=z.step),"stepPosition"in z&&t(26,Ye=z.stepPosition),"seriesOrder"in z&&t(27,Me=z.seriesOrder),"seriesLabelFmt"in z&&t(28,Be=z.seriesLabelFmt)},i.$$.update=()=>{if(i.$$.dirty[0]&8192&&P(t(1,l=X(fe))),i.$$.dirty[0]&128&&t(7,ye=qe(ye)),i.$$.dirty[0]&256&&t(8,ne=qe(ne)),i.$$.dirty[0]&1048576&&Q(t(0,s=X(me))),i.$$.dirty[0]&1024&&t(10,Ve=qe(Ve)),i.$$.dirty[0]&2048&&t(11,De=qe(De)),i.$$.dirty[1]&32768&&t(41,n=B.data),i.$$.dirty[1]&32768&&t(40,r=B.x),i.$$.dirty[0]&8|i.$$.dirty[1]&32768&&t(3,G=j?G:B.y),i.$$.dirty[0]&16|i.$$.dirty[1]&32768&&t(4,Y=O?Y:B.y2),i.$$.dirty[1]&32768&&t(34,a=B.swapXY),i.$$.dirty[1]&32768&&t(43,o=B.yFormat),i.$$.dirty[1]&32768&&t(42,f=B.y2Format),i.$$.dirty[1]&32768&&t(32,u=B.yCount),i.$$.dirty[1]&32768&&t(33,c=B.y2Count),i.$$.dirty[1]&32768&&t(35,y=B.xType),i.$$.dirty[1]&32768&&t(38,T=B.xMismatch),i.$$.dirty[1]&32768&&t(37,b=B.columnSummary),i.$$.dirty[0]&32|i.$$.dirty[1]&32768&&t(5,$=ce?$:B.series),i.$$.dirty[0]&104|i.$$.dirty[1]&1600)if(!$&&typeof G!="object")t(6,p=p??Xt(G,b[G].title));else try{t(41,n=Gl(n,r,G,$))}catch(z){console.warn("Failed to complete data",{e:z}),t(41,n=[])}if(i.$$.dirty[0]&33554472|i.$$.dirty[1]&1536&&Ie==="zero")try{t(41,n=Gl(n,r,G,$,!0))}catch(z){console.warn("Failed to complete data",{e:z}),t(41,n=[])}i.$$.dirty[0]&512|i.$$.dirty[1]&8&&t(9,Fe=(a?ke[Fe]:Xe[Fe])??Je),i.$$.dirty[0]&1712312192|i.$$.dirty[1]&30735&&t(39,d={type:"line",label:{show:ne,formatter(z){return z.value[a?0:1]===0?"":ut(z.value[a?0:1],[Re??ze??o,Ee??ze??f][Ri(z.componentIndex,u,c)])},fontSize:Le,color:H,position:Fe,padding:3},labelLayout:{hideOverlap:!Ve},connectNulls:Ie==="connect",emphasis:{focus:"series",endLabel:{show:!1},lineStyle:{opacity:1,width:3}},lineStyle:{width:parseInt(x),type:le,opacity:ue},itemStyle:{color:E,opacity:ue},showSymbol:ne||ye,symbol:Ne,symbolSize:ne&&!ye?0:te,step:De?Ye:!1}),i.$$.dirty[0]&402653304|i.$$.dirty[1]&1992&&t(36,L=zn(n,r,G,$,a,d,p,T,b,Me,void 0,void 0,Y,Be)),i.$$.dirty[1]&32&&g.update(z=>(z.series.push(...L),z.legend.data.push(...L.map(be=>be.name.toString())),z)),i.$$.dirty[0]&4096&&W&&g.update(z=>({...z,...W})),i.$$.dirty[1]&16&&(I={yAxis:{boundaryGap:["0%","1%"]},xAxis:{boundaryGap:[y==="time"?"2%":"0%","2%"]}})},[s,l,q,G,Y,$,p,ye,ne,Fe,Ve,De,W,fe,x,le,ue,Ne,te,Le,me,Pe,_e,Oe,je,Ie,Ye,Me,Be,ze,Re,Ee,u,c,a,y,L,b,T,d,r,n,f,o,E,H,B]}class Pa extends lt{constructor(e){super(),it(this,e,Fa,null,tt,{y:3,y2:4,series:5,options:12,name:6,lineColor:13,lineWidth:14,lineType:15,lineOpacity:16,markers:7,markerShape:17,markerSize:18,labels:8,labelSize:19,labelPosition:9,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,showAllLabels:10,handleMissing:25,step:11,stepPosition:26,seriesOrder:27,seriesLabelFmt:28},null,[-1,-1])}}function Ra(i){let e,t,l;e=new Pa({props:{lineColor:i[73],lineWidth:i[38],lineOpacity:i[37],lineType:i[36],markers:i[40],markerShape:i[41],markerSize:i[42],handleMissing:i[43],step:i[44],stepPosition:i[45],labels:i[47],labelSize:i[48],labelPosition:i[49],labelColor:i[71],labelFmt:i[50],yLabelFmt:i[51],y2LabelFmt:i[52],showAllLabels:i[53],y2SeriesType:i[8],seriesOrder:i[62],seriesLabelFmt:i[64]}});const s=i[80].default,n=sl(s,i,i[81],null);return{c(){oe(e.$$.fragment),t=J(),n&&n.c()},l(r){ae(e.$$.fragment,r),t=Z(r),n&&n.l(r)},m(r,a){re(e,r,a),S(r,t,a),n&&n.m(r,a),l=!0},p(r,a){const o={};a[2]&2048&&(o.lineColor=r[73]),a[1]&128&&(o.lineWidth=r[38]),a[1]&64&&(o.lineOpacity=r[37]),a[1]&32&&(o.lineType=r[36]),a[1]&512&&(o.markers=r[40]),a[1]&1024&&(o.markerShape=r[41]),a[1]&2048&&(o.markerSize=r[42]),a[1]&4096&&(o.handleMissing=r[43]),a[1]&8192&&(o.step=r[44]),a[1]&16384&&(o.stepPosition=r[45]),a[1]&65536&&(o.labels=r[47]),a[1]&131072&&(o.labelSize=r[48]),a[1]&262144&&(o.labelPosition=r[49]),a[2]&512&&(o.labelColor=r[71]),a[1]&524288&&(o.labelFmt=r[50]),a[1]&1048576&&(o.yLabelFmt=r[51]),a[1]&2097152&&(o.y2LabelFmt=r[52]),a[1]&4194304&&(o.showAllLabels=r[53]),a[0]&256&&(o.y2SeriesType=r[8]),a[2]&1&&(o.seriesOrder=r[62]),a[2]&4&&(o.seriesLabelFmt=r[64]),e.$set(o),n&&n.p&&(!l||a[2]&524288)&&rl(n,s,r,r[81],l?ol(s,r[81],a,null):al(r[81]),null)},i(r){l||(M(e.$$.fragment,r),M(n,r),l=!0)},o(r){U(e.$$.fragment,r),U(n,r),l=!1},d(r){r&&m(t),se(e,r),n&&n.d(r)}}}function Ba(i){let e,t;return e=new Bn({props:{data:i[0],x:i[1],y:i[2],y2:i[3],xFmt:i[10],yFmt:i[9],y2Fmt:i[11],series:i[4],xType:i[5],yLog:i[6],yLogBase:i[7],legend:i[14],xAxisTitle:i[15],yAxisTitle:i[16],y2AxisTitle:i[17],xGridlines:i[18],yGridlines:i[19],y2Gridlines:i[20],xAxisLabels:i[21],yAxisLabels:i[22],y2AxisLabels:i[23],xBaseline:i[24],yBaseline:i[25],y2Baseline:i[26],xTickMarks:i[27],yTickMarks:i[28],y2TickMarks:i[29],yAxisColor:i[70],y2AxisColor:i[69],yMin:i[30],yMax:i[31],yScale:i[32],y2Min:i[33],y2Max:i[34],y2Scale:i[35],title:i[12],subtitle:i[13],chartType:"Line Chart",sort:i[46],chartAreaHeight:i[39],colorPalette:i[72],echartsOptions:i[54],seriesOptions:i[55],printEchartsConfig:i[56],emptySet:i[57],emptyMessage:i[58],renderer:i[59],downloadableData:i[60],downloadableImage:i[61],connectGroup:i[63],seriesColors:i[68],leftPadding:i[65],rightPadding:i[66],xLabelWrap:i[67],$$slots:{default:[Ra]},$$scope:{ctx:i}}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s[0]&1&&(n.data=l[0]),s[0]&2&&(n.x=l[1]),s[0]&4&&(n.y=l[2]),s[0]&8&&(n.y2=l[3]),s[0]&1024&&(n.xFmt=l[10]),s[0]&512&&(n.yFmt=l[9]),s[0]&2048&&(n.y2Fmt=l[11]),s[0]&16&&(n.series=l[4]),s[0]&32&&(n.xType=l[5]),s[0]&64&&(n.yLog=l[6]),s[0]&128&&(n.yLogBase=l[7]),s[0]&16384&&(n.legend=l[14]),s[0]&32768&&(n.xAxisTitle=l[15]),s[0]&65536&&(n.yAxisTitle=l[16]),s[0]&131072&&(n.y2AxisTitle=l[17]),s[0]&262144&&(n.xGridlines=l[18]),s[0]&524288&&(n.yGridlines=l[19]),s[0]&1048576&&(n.y2Gridlines=l[20]),s[0]&2097152&&(n.xAxisLabels=l[21]),s[0]&4194304&&(n.yAxisLabels=l[22]),s[0]&8388608&&(n.y2AxisLabels=l[23]),s[0]&16777216&&(n.xBaseline=l[24]),s[0]&33554432&&(n.yBaseline=l[25]),s[0]&67108864&&(n.y2Baseline=l[26]),s[0]&134217728&&(n.xTickMarks=l[27]),s[0]&268435456&&(n.yTickMarks=l[28]),s[0]&536870912&&(n.y2TickMarks=l[29]),s[2]&256&&(n.yAxisColor=l[70]),s[2]&128&&(n.y2AxisColor=l[69]),s[0]&1073741824&&(n.yMin=l[30]),s[1]&1&&(n.yMax=l[31]),s[1]&2&&(n.yScale=l[32]),s[1]&4&&(n.y2Min=l[33]),s[1]&8&&(n.y2Max=l[34]),s[1]&16&&(n.y2Scale=l[35]),s[0]&4096&&(n.title=l[12]),s[0]&8192&&(n.subtitle=l[13]),s[1]&32768&&(n.sort=l[46]),s[1]&256&&(n.chartAreaHeight=l[39]),s[2]&1024&&(n.colorPalette=l[72]),s[1]&8388608&&(n.echartsOptions=l[54]),s[1]&16777216&&(n.seriesOptions=l[55]),s[1]&33554432&&(n.printEchartsConfig=l[56]),s[1]&67108864&&(n.emptySet=l[57]),s[1]&134217728&&(n.emptyMessage=l[58]),s[1]&268435456&&(n.renderer=l[59]),s[1]&536870912&&(n.downloadableData=l[60]),s[1]&1073741824&&(n.downloadableImage=l[61]),s[2]&2&&(n.connectGroup=l[63]),s[2]&64&&(n.seriesColors=l[68]),s[2]&8&&(n.leftPadding=l[65]),s[2]&16&&(n.rightPadding=l[66]),s[2]&32&&(n.xLabelWrap=l[67]),s[0]&256|s[1]&8355552|s[2]&526853&&(n.$$scope={dirty:s,ctx:l}),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function za(i,e,t){let l,s,n,r,a,o,{$$slots:f={},$$scope:u}=e;const{resolveColor:c,resolveColorsObject:y,resolveColorPalette:T}=Pt();let{data:b=void 0}=e,{x:d=void 0}=e,{y:L=void 0}=e,{y2:I=void 0}=e,{series:E=void 0}=e,{xType:w=void 0}=e,{yLog:P=void 0}=e,{yLogBase:H=void 0}=e,{y2SeriesType:V=void 0}=e,{yFmt:Q=void 0}=e,{xFmt:B=void 0}=e,{y2Fmt:q=void 0}=e,{title:g=void 0}=e,{subtitle:X=void 0}=e,{legend:G=void 0}=e,{xAxisTitle:j=void 0}=e,{yAxisTitle:Y=I?"true":void 0}=e,{y2AxisTitle:O=I?"true":void 0}=e,{xGridlines:$=void 0}=e,{yGridlines:ce=void 0}=e,{y2Gridlines:W=void 0}=e,{xAxisLabels:p=void 0}=e,{yAxisLabels:fe=void 0}=e,{y2AxisLabels:x=void 0}=e,{xBaseline:le=void 0}=e,{yBaseline:ue=void 0}=e,{y2Baseline:ye=void 0}=e,{xTickMarks:Ne=void 0}=e,{yTickMarks:te=void 0}=e,{y2TickMarks:ne=void 0}=e,{yMin:Le=void 0}=e,{yMax:Fe=void 0}=e,{yScale:me=void 0}=e,{y2Min:Pe=void 0}=e,{y2Max:ze=void 0}=e,{y2Scale:_e=void 0}=e,{lineColor:Re=void 0}=e,{lineType:Oe=void 0}=e,{lineOpacity:Ee=void 0}=e,{lineWidth:je=void 0}=e,{chartAreaHeight:Ve=void 0}=e,{markers:Ie=void 0}=e,{markerShape:De=void 0}=e,{markerSize:Ye=void 0}=e,{handleMissing:Me=void 0}=e,{step:Be=void 0}=e,{stepPosition:Xe=void 0}=e,{sort:ke=void 0}=e,{colorPalette:Je="default"}=e,{labels:z=void 0}=e,{labelSize:be=void 0}=e,{labelPosition:nt=void 0}=e,{labelColor:We=void 0}=e,{labelFmt:Ge=void 0}=e,{yLabelFmt:bt=void 0}=e,{y2LabelFmt:$e=void 0}=e,{showAllLabels:Ue=void 0}=e,{yAxisColor:R=void 0}=e,{y2AxisColor:ge=void 0}=e,{echartsOptions:He=void 0}=e,{seriesOptions:ht=void 0}=e,{printEchartsConfig:gt=!1}=e,{emptySet:et=void 0}=e,{emptyMessage:_t=void 0}=e,{renderer:Ct=void 0}=e,{downloadableData:ft=void 0}=e,{downloadableImage:at=void 0}=e,{seriesColors:xe=void 0}=e,{seriesOrder:ot=void 0}=e,{connectGroup:kt=void 0}=e,{seriesLabelFmt:st=void 0}=e,{leftPadding:Mt=void 0}=e,{rightPadding:ct=void 0}=e,{xLabelWrap:ve=void 0}=e;return i.$$set=C=>{"data"in C&&t(0,b=C.data),"x"in C&&t(1,d=C.x),"y"in C&&t(2,L=C.y),"y2"in C&&t(3,I=C.y2),"series"in C&&t(4,E=C.series),"xType"in C&&t(5,w=C.xType),"yLog"in C&&t(6,P=C.yLog),"yLogBase"in C&&t(7,H=C.yLogBase),"y2SeriesType"in C&&t(8,V=C.y2SeriesType),"yFmt"in C&&t(9,Q=C.yFmt),"xFmt"in C&&t(10,B=C.xFmt),"y2Fmt"in C&&t(11,q=C.y2Fmt),"title"in C&&t(12,g=C.title),"subtitle"in C&&t(13,X=C.subtitle),"legend"in C&&t(14,G=C.legend),"xAxisTitle"in C&&t(15,j=C.xAxisTitle),"yAxisTitle"in C&&t(16,Y=C.yAxisTitle),"y2AxisTitle"in C&&t(17,O=C.y2AxisTitle),"xGridlines"in C&&t(18,$=C.xGridlines),"yGridlines"in C&&t(19,ce=C.yGridlines),"y2Gridlines"in C&&t(20,W=C.y2Gridlines),"xAxisLabels"in C&&t(21,p=C.xAxisLabels),"yAxisLabels"in C&&t(22,fe=C.yAxisLabels),"y2AxisLabels"in C&&t(23,x=C.y2AxisLabels),"xBaseline"in C&&t(24,le=C.xBaseline),"yBaseline"in C&&t(25,ue=C.yBaseline),"y2Baseline"in C&&t(26,ye=C.y2Baseline),"xTickMarks"in C&&t(27,Ne=C.xTickMarks),"yTickMarks"in C&&t(28,te=C.yTickMarks),"y2TickMarks"in C&&t(29,ne=C.y2TickMarks),"yMin"in C&&t(30,Le=C.yMin),"yMax"in C&&t(31,Fe=C.yMax),"yScale"in C&&t(32,me=C.yScale),"y2Min"in C&&t(33,Pe=C.y2Min),"y2Max"in C&&t(34,ze=C.y2Max),"y2Scale"in C&&t(35,_e=C.y2Scale),"lineColor"in C&&t(74,Re=C.lineColor),"lineType"in C&&t(36,Oe=C.lineType),"lineOpacity"in C&&t(37,Ee=C.lineOpacity),"lineWidth"in C&&t(38,je=C.lineWidth),"chartAreaHeight"in C&&t(39,Ve=C.chartAreaHeight),"markers"in C&&t(40,Ie=C.markers),"markerShape"in C&&t(41,De=C.markerShape),"markerSize"in C&&t(42,Ye=C.markerSize),"handleMissing"in C&&t(43,Me=C.handleMissing),"step"in C&&t(44,Be=C.step),"stepPosition"in C&&t(45,Xe=C.stepPosition),"sort"in C&&t(46,ke=C.sort),"colorPalette"in C&&t(75,Je=C.colorPalette),"labels"in C&&t(47,z=C.labels),"labelSize"in C&&t(48,be=C.labelSize),"labelPosition"in C&&t(49,nt=C.labelPosition),"labelColor"in C&&t(76,We=C.labelColor),"labelFmt"in C&&t(50,Ge=C.labelFmt),"yLabelFmt"in C&&t(51,bt=C.yLabelFmt),"y2LabelFmt"in C&&t(52,$e=C.y2LabelFmt),"showAllLabels"in C&&t(53,Ue=C.showAllLabels),"yAxisColor"in C&&t(77,R=C.yAxisColor),"y2AxisColor"in C&&t(78,ge=C.y2AxisColor),"echartsOptions"in C&&t(54,He=C.echartsOptions),"seriesOptions"in C&&t(55,ht=C.seriesOptions),"printEchartsConfig"in C&&t(56,gt=C.printEchartsConfig),"emptySet"in C&&t(57,et=C.emptySet),"emptyMessage"in C&&t(58,_t=C.emptyMessage),"renderer"in C&&t(59,Ct=C.renderer),"downloadableData"in C&&t(60,ft=C.downloadableData),"downloadableImage"in C&&t(61,at=C.downloadableImage),"seriesColors"in C&&t(79,xe=C.seriesColors),"seriesOrder"in C&&t(62,ot=C.seriesOrder),"connectGroup"in C&&t(63,kt=C.connectGroup),"seriesLabelFmt"in C&&t(64,st=C.seriesLabelFmt),"leftPadding"in C&&t(65,Mt=C.leftPadding),"rightPadding"in C&&t(66,ct=C.rightPadding),"xLabelWrap"in C&&t(67,ve=C.xLabelWrap),"$$scope"in C&&t(81,u=C.$$scope)},i.$$.update=()=>{i.$$.dirty[2]&4096&&t(73,l=c(Re)),i.$$.dirty[2]&8192&&t(72,s=T(Je)),i.$$.dirty[2]&16384&&t(71,n=c(We)),i.$$.dirty[2]&32768&&t(70,r=c(R)),i.$$.dirty[2]&65536&&t(69,a=c(ge)),i.$$.dirty[2]&131072&&t(68,o=y(xe))},[b,d,L,I,E,w,P,H,V,Q,B,q,g,X,G,j,Y,O,$,ce,W,p,fe,x,le,ue,ye,Ne,te,ne,Le,Fe,me,Pe,ze,_e,Oe,Ee,je,Ve,Ie,De,Ye,Me,Be,Xe,ke,z,be,nt,Ge,bt,$e,Ue,He,ht,gt,et,_t,Ct,ft,at,ot,kt,st,Mt,ct,ve,o,a,r,n,s,l,Re,Je,We,R,ge,xe,f,u]}class Ga extends lt{constructor(e){super(),it(this,e,za,Ba,tt,{data:0,x:1,y:2,y2:3,series:4,xType:5,yLog:6,yLogBase:7,y2SeriesType:8,yFmt:9,xFmt:10,y2Fmt:11,title:12,subtitle:13,legend:14,xAxisTitle:15,yAxisTitle:16,y2AxisTitle:17,xGridlines:18,yGridlines:19,y2Gridlines:20,xAxisLabels:21,yAxisLabels:22,y2AxisLabels:23,xBaseline:24,yBaseline:25,y2Baseline:26,xTickMarks:27,yTickMarks:28,y2TickMarks:29,yMin:30,yMax:31,yScale:32,y2Min:33,y2Max:34,y2Scale:35,lineColor:74,lineType:36,lineOpacity:37,lineWidth:38,chartAreaHeight:39,markers:40,markerShape:41,markerSize:42,handleMissing:43,step:44,stepPosition:45,sort:46,colorPalette:75,labels:47,labelSize:48,labelPosition:49,labelColor:76,labelFmt:50,yLabelFmt:51,y2LabelFmt:52,showAllLabels:53,yAxisColor:77,y2AxisColor:78,echartsOptions:54,seriesOptions:55,printEchartsConfig:56,emptySet:57,emptyMessage:58,renderer:59,downloadableData:60,downloadableImage:61,seriesColors:79,seriesOrder:62,connectGroup:63,seriesLabelFmt:64,leftPadding:65,rightPadding:66,xLabelWrap:67},null,[-1,-1,-1])}}function Ua(i){let e,t=we.title+"",l;return{c(){e=F("h1"),l=Te(t),this.h()},l(s){e=N(s,"H1",{class:!0});var n=K(e);l=Ce(n,t),n.forEach(m),this.h()},h(){h(e,"class","title")},m(s,n){S(s,e,n),D(e,l)},p:de,d(s){s&&m(e)}}}function Ha(i){return{c(){this.h()},l(e){this.h()},h(){document.title="Evidence"},m:de,p:de,d:de}}function va(i){let e,t,l,s,n;return document.title=e=we.title,{c(){t=J(),l=F("meta"),s=J(),n=F("meta"),this.h()},l(r){t=Z(r),l=N(r,"META",{property:!0,content:!0}),s=Z(r),n=N(r,"META",{name:!0,content:!0}),this.h()},h(){var r,a;h(l,"property","og:title"),h(l,"content",((r=we.og)==null?void 0:r.title)??we.title),h(n,"name","twitter:title"),h(n,"content",((a=we.og)==null?void 0:a.title)??we.title)},m(r,a){S(r,t,a),S(r,l,a),S(r,s,a),S(r,n,a)},p(r,a){a&0&&e!==(e=we.title)&&(document.title=e)},d(r){r&&(m(t),m(l),m(s),m(n))}}}function qa(i){var n,r;let e,t,l=(we.description||((n=we.og)==null?void 0:n.description))&&Va(),s=((r=we.og)==null?void 0:r.image)&&Wa();return{c(){l&&l.c(),e=J(),s&&s.c(),t=pe()},l(a){l&&l.l(a),e=Z(a),s&&s.l(a),t=pe()},m(a,o){l&&l.m(a,o),S(a,e,o),s&&s.m(a,o),S(a,t,o)},p(a,o){var f,u;(we.description||(f=we.og)!=null&&f.description)&&l.p(a,o),(u=we.og)!=null&&u.image&&s.p(a,o)},d(a){a&&(m(e),m(t)),l&&l.d(a),s&&s.d(a)}}}function Va(i){let e,t,l,s,n;return{c(){e=F("meta"),t=J(),l=F("meta"),s=J(),n=F("meta"),this.h()},l(r){e=N(r,"META",{name:!0,content:!0}),t=Z(r),l=N(r,"META",{property:!0,content:!0}),s=Z(r),n=N(r,"META",{name:!0,content:!0}),this.h()},h(){var r,a,o;h(e,"name","description"),h(e,"content",we.description??((r=we.og)==null?void 0:r.description)),h(l,"property","og:description"),h(l,"content",((a=we.og)==null?void 0:a.description)??we.description),h(n,"name","twitter:description"),h(n,"content",((o=we.og)==null?void 0:o.description)??we.description)},m(r,a){S(r,e,a),S(r,t,a),S(r,l,a),S(r,s,a),S(r,n,a)},p:de,d(r){r&&(m(e),m(t),m(l),m(s),m(n))}}}function Wa(i){let e,t,l;return{c(){e=F("meta"),t=J(),l=F("meta"),this.h()},l(s){e=N(s,"META",{property:!0,content:!0}),t=Z(s),l=N(s,"META",{name:!0,content:!0}),this.h()},h(){var s,n;h(e,"property","og:image"),h(e,"content",Ol((s=we.og)==null?void 0:s.image)),h(l,"name","twitter:image"),h(l,"content",Ol((n=we.og)==null?void 0:n.image))},m(s,n){S(s,e,n),S(s,t,n),S(s,l,n)},p:de,d(s){s&&(m(e),m(t),m(l))}}}function On(i){let e,t;return e=new er({props:{queryID:"sample_sales",queryResult:i[0]}}),{c(){oe(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,s){re(e,l,s),t=!0},p(l,s){const n={};s&1&&(n.queryResult=l[0]),e.$set(n)},i(l){t||(M(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function ja(i){let e,t,l,s,n,r,a='<a href="#domo-data-loader">Domo Data Loader</a>',o,f,u="Load datasets from Domo into DuckDB for analysis with Evidence.",c,y,T='<h3 class="svelte-16omzez">🚀 Enhanced Domo DDX Integration</h3> <p>This app now supports real DuckDB integration, multiple dataset loading, iframe compatibility, and data visualizations!</p>',b,d,L,I,E='<label for="dataset-search" class="svelte-16omzez">Search Datasets:</label> <input id="dataset-search" type="text" placeholder="Type to search datasets..." class="dataset-search svelte-16omzez"/> <div class="dataset-stats" id="dataset-stats"><span id="dataset-count">Loading datasets...</span></div>',w,P,H,V="Select Dataset:",Q,B,q,g="Choose a dataset...",X,G,j='<h4>📊 Dataset Information</h4> <div id="preview-content" class="preview-content"><div id="dataset-info" class="dataset-info-grid"></div> <div class="dataset-tabs"><button class="tab-button active" data-tab="schema">Schema</button> <button class="tab-button" data-tab="sample">Sample Data</button> <button class="tab-button" data-tab="metadata">Metadata</button></div> <div id="schema-tab" class="tab-content active"><div id="schema-table" class="schema-table"></div></div> <div id="sample-tab" class="tab-content"><div class="preview-actions svelte-16omzez"><button id="preview-btn" class="btn btn-secondary svelte-16omzez">🔍 Load Sample Data</button> <span class="preview-note">Shows first 10 rows</span></div> <div id="data-preview" class="data-preview svelte-16omzez" style="display: none;"></div></div> <div id="metadata-tab" class="tab-content"><div id="dataset-metadata" class="dataset-metadata"></div></div></div>',Y,O,$,ce="⚙️ Loading Configuration",W,p,fe,x='<label for="table-name" class="svelte-16omzez">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-16omzez"/> <small class="field-help">Use lowercase letters, numbers, and underscores only</small>',le,ue,ye,Ne="Refresh Mode:",te,ne,Le,Fe="Replace existing data",me,Pe="Append to existing data",ze,_e,Re="Choose how to handle existing data",Oe,Ee,je='<label for="row-limit" class="svelte-16omzez">Row Limit (optional):</label> <input id="row-limit" type="number" placeholder="Leave empty for all rows" min="1" max="1000000" class="svelte-16omzez"/> <small class="field-help">Limit rows for testing (max 1M)</small>',Ve,Ie,De=`<label class="svelte-16omzez"><input type="checkbox" id="create-index" checked="" class="svelte-16omzez"/>
            Create indexes for better performance</label>`,Ye,Me,Be='<div class="action-buttons"><button id="validate-config-btn" class="btn btn-secondary svelte-16omzez">✅ Validate Configuration</button> <button id="load-dataset-btn" class="btn btn-primary svelte-16omzez">📊 Load Dataset into DuckDB</button></div> <div id="validation-results" class="validation-results" style="display: none;"></div>',Xe,ke,Je='<div class="loading-spinner svelte-16omzez"></div> <p id="loading-message" class="svelte-16omzez">Loading...</p> <div class="progress-bar"><div class="progress-fill" id="progress-fill"></div></div>',z,be,nt='<h2 class="markdown">📚 Loaded Datasets</h2> <div class="loaded-datasets-header"><p>Datasets currently available in DuckDB for analysis:</p> <button id="refresh-loaded-btn" class="btn btn-secondary btn-small svelte-16omzez">🔄 Refresh</button></div> <div id="loaded-datasets-list" class="loaded-datasets-grid"></div>',We,Ge,bt='<h2 class="markdown">🔍 Query Your Data</h2> <div class="query-interface"><div class="query-input"><label for="sql-query">SQL Query:</label> <textarea id="sql-query" placeholder="SELECT * FROM your_table_name LIMIT 10;" rows="4"></textarea> <div class="query-actions"><button id="run-query-btn" class="btn btn-primary svelte-16omzez">▶️ Run Query</button> <button id="clear-query-btn" class="btn btn-secondary svelte-16omzez">🗑️ Clear</button></div></div> <div id="query-results" class="query-results" style="display: none;"></div></div>',$e,Ue,R='<a href="#sample-evidence-visualizations">Sample Evidence Visualizations</a>',ge,He,ht="Once you load data, you can create Evidence-style visualizations. Here are some examples:",gt,et,_t='<a href="#sales-performance-dashboard">Sales Performance Dashboard</a>',Ct,ft,at,xe,ot,kt,st,Mt='<a href="#key-metrics">Key Metrics</a>',ct,ve,C,_,zt,vt,qt,Dt,Vt='<strong class="markdown">Next Steps:</strong>',fl,he,Zt='<li class="markdown">Select a dataset from the dropdown above</li> <li class="markdown">Configure loading options</li> <li class="markdown">Load the data into DuckDB</li> <li class="markdown">Run SQL queries to explore your data</li> <li class="markdown">Create Evidence visualizations with your results</li>',Gt,dt=typeof we<"u"&&we.title&&we.hide_title!==!0&&Ua();function Il(A,ee){return typeof we<"u"&&we.title?va:Ha}let mt=Il()(i),St=typeof we=="object"&&qa(),Ae=i[0]&&On(i);return at=new Yr({props:{data:i[0],x:"product",y:"revenue",title:"Revenue by Product"}}),ot=new Ga({props:{data:i[0],x:"month",y:"units_sold",series:"product",title:"Units Sold Trend"}}),ve=new wn({props:{data:i[0],value:"revenue",title:"Total Revenue",fmt:"$#,##0"}}),_=new wn({props:{data:i[0],value:"units_sold",title:"Total Units",fmt:"#,##0"}}),{c(){dt&&dt.c(),e=J(),mt.c(),t=F("meta"),l=F("meta"),St&&St.c(),s=pe(),n=J(),r=F("h1"),r.innerHTML=a,o=J(),f=F("p"),f.textContent=u,c=J(),y=F("div"),y.innerHTML=T,b=J(),d=F("div"),L=F("div"),I=F("div"),I.innerHTML=E,w=J(),P=F("div"),H=F("label"),H.textContent=V,Q=J(),B=F("select"),q=F("option"),q.textContent=g,X=J(),G=F("div"),G.innerHTML=j,Y=J(),O=F("div"),$=F("h4"),$.textContent=ce,W=J(),p=F("div"),fe=F("div"),fe.innerHTML=x,le=J(),ue=F("div"),ye=F("label"),ye.textContent=Ne,te=J(),ne=F("select"),Le=F("option"),Le.textContent=Fe,me=F("option"),me.textContent=Pe,ze=J(),_e=F("small"),_e.textContent=Re,Oe=J(),Ee=F("div"),Ee.innerHTML=je,Ve=J(),Ie=F("div"),Ie.innerHTML=De,Ye=J(),Me=F("div"),Me.innerHTML=Be,Xe=J(),ke=F("div"),ke.innerHTML=Je,z=J(),be=F("div"),be.innerHTML=nt,We=J(),Ge=F("div"),Ge.innerHTML=bt,$e=J(),Ue=F("h2"),Ue.innerHTML=R,ge=J(),He=F("p"),He.textContent=ht,gt=J(),et=F("h3"),et.innerHTML=_t,Ct=J(),Ae&&Ae.c(),ft=J(),oe(at.$$.fragment),xe=J(),oe(ot.$$.fragment),kt=J(),st=F("h3"),st.innerHTML=Mt,ct=J(),oe(ve.$$.fragment),C=J(),oe(_.$$.fragment),zt=J(),vt=F("hr"),qt=J(),Dt=F("p"),Dt.innerHTML=Vt,fl=J(),he=F("ol"),he.innerHTML=Zt,this.h()},l(A){dt&&dt.l(A),e=Z(A);const ee=Xn("svelte-2igo1p",document.head);mt.l(ee),t=N(ee,"META",{name:!0,content:!0}),l=N(ee,"META",{name:!0,content:!0}),St&&St.l(ee),s=pe(),ee.forEach(m),n=Z(A),r=N(A,"H1",{class:!0,id:!0,"data-svelte-h":!0}),Se(r)!=="svelte-94lco6"&&(r.innerHTML=a),o=Z(A),f=N(A,"P",{class:!0,"data-svelte-h":!0}),Se(f)!=="svelte-1s6ws79"&&(f.textContent=u),c=Z(A),y=N(A,"DIV",{class:!0,"data-svelte-h":!0}),Se(y)!=="svelte-1top7fi"&&(y.innerHTML=T),b=Z(A),d=N(A,"DIV",{class:!0});var Rt=K(d);L=N(Rt,"DIV",{id:!0,class:!0});var rt=K(L);I=N(rt,"DIV",{class:!0,"data-svelte-h":!0}),Se(I)!=="svelte-qgc35d"&&(I.innerHTML=E),w=Z(rt),P=N(rt,"DIV",{class:!0});var Et=K(P);H=N(Et,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),Se(H)!=="svelte-1fci9ty"&&(H.textContent=V),Q=Z(Et),B=N(Et,"SELECT",{id:!0,class:!0});var Bt=K(B);q=N(Bt,"OPTION",{"data-svelte-h":!0}),Se(q)!=="svelte-59d9xk"&&(q.textContent=g),Bt.forEach(m),Et.forEach(m),X=Z(rt),G=N(rt,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),Se(G)!=="svelte-19tcyh0"&&(G.innerHTML=j),Y=Z(rt),O=N(rt,"DIV",{id:!0,class:!0,style:!0});var Wt=K(O);$=N(Wt,"H4",{"data-svelte-h":!0}),Se($)!=="svelte-6xztzo"&&($.textContent=ce),W=Z(Wt),p=N(Wt,"DIV",{class:!0});var At=K(p);fe=N(At,"DIV",{class:!0,"data-svelte-h":!0}),Se(fe)!=="svelte-199xsoc"&&(fe.innerHTML=x),le=Z(At),ue=N(At,"DIV",{class:!0});var wt=K(ue);ye=N(wt,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),Se(ye)!=="svelte-p1qydn"&&(ye.textContent=Ne),te=Z(wt),ne=N(wt,"SELECT",{id:!0,class:!0});var Jt=K(ne);Le=N(Jt,"OPTION",{"data-svelte-h":!0}),Se(Le)!=="svelte-qvzdub"&&(Le.textContent=Fe),me=N(Jt,"OPTION",{"data-svelte-h":!0}),Se(me)!=="svelte-idsvi6"&&(me.textContent=Pe),Jt.forEach(m),ze=Z(wt),_e=N(wt,"SMALL",{class:!0,"data-svelte-h":!0}),Se(_e)!=="svelte-fpi9b6"&&(_e.textContent=Re),wt.forEach(m),Oe=Z(At),Ee=N(At,"DIV",{class:!0,"data-svelte-h":!0}),Se(Ee)!=="svelte-15v44ck"&&(Ee.innerHTML=je),Ve=Z(At),Ie=N(At,"DIV",{class:!0,"data-svelte-h":!0}),Se(Ie)!=="svelte-5ivc8q"&&(Ie.innerHTML=De),At.forEach(m),Wt.forEach(m),Ye=Z(rt),Me=N(rt,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),Se(Me)!=="svelte-68tw8y"&&(Me.innerHTML=Be),Xe=Z(rt),ke=N(rt,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),Se(ke)!=="svelte-1oq3jl0"&&(ke.innerHTML=Je),rt.forEach(m),Rt.forEach(m),z=Z(A),be=N(A,"DIV",{id:!0,style:!0,"data-svelte-h":!0}),Se(be)!=="svelte-n8w8em"&&(be.innerHTML=nt),We=Z(A),Ge=N(A,"DIV",{id:!0,style:!0,"data-svelte-h":!0}),Se(Ge)!=="svelte-1i72rmk"&&(Ge.innerHTML=bt),$e=Z(A),Ue=N(A,"H2",{class:!0,id:!0,"data-svelte-h":!0}),Se(Ue)!=="svelte-1t5nmyw"&&(Ue.innerHTML=R),ge=Z(A),He=N(A,"P",{class:!0,"data-svelte-h":!0}),Se(He)!=="svelte-nbtkwk"&&(He.textContent=ht),gt=Z(A),et=N(A,"H3",{class:!0,id:!0,"data-svelte-h":!0}),Se(et)!=="svelte-bguwp4"&&(et.innerHTML=_t),Ct=Z(A),Ae&&Ae.l(A),ft=Z(A),ae(at.$$.fragment,A),xe=Z(A),ae(ot.$$.fragment,A),kt=Z(A),st=N(A,"H3",{class:!0,id:!0,"data-svelte-h":!0}),Se(st)!=="svelte-aivela"&&(st.innerHTML=Mt),ct=Z(A),ae(ve.$$.fragment,A),C=Z(A),ae(_.$$.fragment,A),zt=Z(A),vt=N(A,"HR",{class:!0}),qt=Z(A),Dt=N(A,"P",{class:!0,"data-svelte-h":!0}),Se(Dt)!=="svelte-1mg5bp7"&&(Dt.innerHTML=Vt),fl=Z(A),he=N(A,"OL",{class:!0,"data-svelte-h":!0}),Se(he)!=="svelte-1ei1c0u"&&(he.innerHTML=Zt),this.h()},h(){h(t,"name","twitter:card"),h(t,"content","summary_large_image"),h(l,"name","twitter:site"),h(l,"content","@evidence_dev"),h(r,"class","markdown"),h(r,"id","domo-data-loader"),h(f,"class","markdown"),h(y,"class","dev-banner svelte-16omzez"),h(I,"class","workflow-step svelte-16omzez"),h(H,"for","dataset-selector"),h(H,"class","svelte-16omzez"),q.__value="",Bl(q,q.__value),h(B,"id","dataset-selector"),h(B,"class","dataset-dropdown svelte-16omzez"),h(P,"class","workflow-step svelte-16omzez"),h(G,"id","dataset-preview"),h(G,"class","dataset-preview svelte-16omzez"),v(G,"display","none"),h(fe,"class","config-item"),h(ye,"for","refresh-mode"),h(ye,"class","svelte-16omzez"),Le.__value="replace",Bl(Le,Le.__value),me.__value="append",Bl(me,me.__value),h(ne,"id","refresh-mode"),h(ne,"class","svelte-16omzez"),h(_e,"class","field-help"),h(ue,"class","config-item"),h(Ee,"class","config-item"),h(Ie,"class","config-item"),h(p,"class","config-grid svelte-16omzez"),h(O,"id","loading-config"),h(O,"class","workflow-step svelte-16omzez"),v(O,"display","none"),h(Me,"id","workflow-actions"),h(Me,"class","workflow-actions svelte-16omzez"),v(Me,"display","none"),h(ke,"id","loading-status"),h(ke,"class","loading-status svelte-16omzez"),v(ke,"display","none"),h(L,"id","domo-workflow-picker"),h(L,"class","workflow-picker svelte-16omzez"),h(d,"class","workflow-picker-section svelte-16omzez"),h(be,"id","loaded-datasets-section"),v(be,"display","none"),h(Ge,"id","query-section"),v(Ge,"display","none"),h(Ue,"class","markdown"),h(Ue,"id","sample-evidence-visualizations"),h(He,"class","markdown"),h(et,"class","markdown"),h(et,"id","sales-performance-dashboard"),h(st,"class","markdown"),h(st,"id","key-metrics"),h(vt,"class","markdown"),h(Dt,"class","markdown"),h(he,"class","markdown")},m(A,ee){dt&&dt.m(A,ee),S(A,e,ee),mt.m(document.head,null),D(document.head,t),D(document.head,l),St&&St.m(document.head,null),D(document.head,s),S(A,n,ee),S(A,r,ee),S(A,o,ee),S(A,f,ee),S(A,c,ee),S(A,y,ee),S(A,b,ee),S(A,d,ee),D(d,L),D(L,I),D(L,w),D(L,P),D(P,H),D(P,Q),D(P,B),D(B,q),D(L,X),D(L,G),D(L,Y),D(L,O),D(O,$),D(O,W),D(O,p),D(p,fe),D(p,le),D(p,ue),D(ue,ye),D(ue,te),D(ue,ne),D(ne,Le),D(ne,me),D(ue,ze),D(ue,_e),D(p,Oe),D(p,Ee),D(p,Ve),D(p,Ie),D(L,Ye),D(L,Me),D(L,Xe),D(L,ke),S(A,z,ee),S(A,be,ee),S(A,We,ee),S(A,Ge,ee),S(A,$e,ee),S(A,Ue,ee),S(A,ge,ee),S(A,He,ee),S(A,gt,ee),S(A,et,ee),S(A,Ct,ee),Ae&&Ae.m(A,ee),S(A,ft,ee),re(at,A,ee),S(A,xe,ee),re(ot,A,ee),S(A,kt,ee),S(A,st,ee),S(A,ct,ee),re(ve,A,ee),S(A,C,ee),re(_,A,ee),S(A,zt,ee),S(A,vt,ee),S(A,qt,ee),S(A,Dt,ee),S(A,fl,ee),S(A,he,ee),Gt=!0},p(A,[ee]){typeof we<"u"&&we.title&&we.hide_title!==!0&&dt.p(A,ee),mt.p(A,ee),typeof we=="object"&&St.p(A,ee),A[0]?Ae?(Ae.p(A,ee),ee&1&&M(Ae,1)):(Ae=On(A),Ae.c(),M(Ae,1),Ae.m(ft.parentNode,ft)):Ae&&(Ke(),U(Ae,1,1,()=>{Ae=null}),Ze());const Rt={};ee&1&&(Rt.data=A[0]),at.$set(Rt);const rt={};ee&1&&(rt.data=A[0]),ot.$set(rt);const Et={};ee&1&&(Et.data=A[0]),ve.$set(Et);const Bt={};ee&1&&(Bt.data=A[0]),_.$set(Bt)},i(A){Gt||(M(Ae),M(at.$$.fragment,A),M(ot.$$.fragment,A),M(ve.$$.fragment,A),M(_.$$.fragment,A),Gt=!0)},o(A){U(Ae),U(at.$$.fragment,A),U(ot.$$.fragment,A),U(ve.$$.fragment,A),U(_.$$.fragment,A),Gt=!1},d(A){A&&(m(e),m(n),m(r),m(o),m(f),m(c),m(y),m(b),m(d),m(z),m(be),m(We),m(Ge),m($e),m(Ue),m(ge),m(He),m(gt),m(et),m(Ct),m(ft),m(xe),m(kt),m(st),m(ct),m(C),m(zt),m(vt),m(qt),m(Dt),m(fl),m(he)),dt&&dt.d(A),mt.d(A),m(t),m(l),St&&St.d(A),m(s),Ae&&Ae.d(A),se(at,A),se(ot,A),se(ve,A),se(_,A)}}}const we={title:"Domo Data Loader"};function Ya(i,e,t){let l,s;Nt(i,Fn,E=>t(7,l=E)),Nt(i,Xi,E=>t(13,s=E));let{data:n}=e,{data:r={},customFormattingSettings:a,__db:o,inputs:f}=n;Li(Xi,s="6666cd76f96956469e7be39d750cc7d9",s);let u=hs(Ai(f));Qn(u.subscribe(E=>f=E)),ki(gs,{getCustomFormats:()=>a.customFormats||[]});const c=(E,w)=>_s(o.query,E,{query_name:w});ys(c),l.params,Kn(()=>!0);let y={initialData:void 0,initialError:void 0},T=Qi`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`,b=`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`;r.sample_sales_data&&(r.sample_sales_data instanceof Error?y.initialError=r.sample_sales_data:y.initialData=r.sample_sales_data,r.sample_sales_columns&&(y.knownColumns=r.sample_sales_columns));let d,L=!1;const I=It.createReactive({callback:E=>{t(0,d=E)},execFn:c},{id:"sample_sales",...y});return I(b,{noResolve:T,...y}),globalThis[Symbol.for("sample_sales")]={get value(){return d}},i.$$set=E=>{"data"in E&&t(1,n=E.data)},i.$$.update=()=>{i.$$.dirty&2&&t(2,{data:r={},customFormattingSettings:a,__db:o}=n,r),i.$$.dirty&4&&bs.set(Object.keys(r).length>0),i.$$.dirty&128&&l.params,i.$$.dirty&120&&(T||!L?T||(I(b,{noResolve:T,...y}),t(6,L=!0)):I(b,{noResolve:T}))},t(4,T=Qi`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`),t(5,b=`SELECT
  'Product A' as product,
  150000 as revenue,
  1250 as units_sold,
  '2024-01' as month
UNION ALL
SELECT 'Product B', 125000, 980, '2024-01'
UNION ALL
SELECT 'Product C', 180000, 1450, '2024-01'
UNION ALL
SELECT 'Product A', 165000, 1380, '2024-02'
UNION ALL
SELECT 'Product B', 140000, 1120, '2024-02'
UNION ALL
SELECT 'Product C', 195000, 1580, '2024-02'`),[d,n,r,y,T,b,L,l]}class $a extends lt{constructor(e){super(),it(this,e,Ya,ja,tt,{data:1})}}export{$a as component};

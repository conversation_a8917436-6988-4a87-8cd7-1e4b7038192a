<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="/favicon.ico" sizes="32x32" />
		<link rel="icon" href="/icon.svg" type="image/svg+xml" />
		<link rel="apple-touch-icon" href="/apple-touch-icon.png" />
		<link rel="manifest" href="/manifest.webmanifest" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<script>
			try {
				/** @type {'light' | 'dark' | 'system'} */
				const savedTheme = localStorage.getItem('evidence-theme') ?? 'system';
				const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
				const theme = savedTheme === 'system' ? (prefersDark ? 'dark' : 'light') : savedTheme;
				document.documentElement.classList.add(`theme-${theme}`);
			} catch (e) {}
		</script>
		
		<link href="/_app/immutable/assets/0.CcUSevux.css" rel="stylesheet">
		<link href="/_app/immutable/assets/VennDiagram.D7OGjfZg.css" rel="stylesheet">
		<link href="/_app/immutable/assets/4.CBSvq_j-.css" rel="stylesheet">
		<link rel="modulepreload" href="/_app/immutable/entry/start.C3d0B0DB.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/entry.BOA_ufO1.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/scheduler.DQwIXrE4.js">
		<link rel="modulepreload" href="/_app/immutable/entry/app.BniU_0Yn.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/preload-helper.D7HrI6pR.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/index.BEt_7cXZ.js">
		<link rel="modulepreload" href="/_app/immutable/nodes/0.DpCwa6IX.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/VennDiagram.svelte_svelte_type_style_lang.BpO3cxln.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/index.rV6zwFgL.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/setTrackProxy.DjIbdjlZ.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/stores.BFnZiBDk.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/index.CsWz5Mxk.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/AccordionItem.1aIbS2OP.js">
		<link rel="modulepreload" href="/_app/immutable/nodes/4.D5oKDmYv.js"><title>Domo Data Loader</title><!-- HEAD_svelte-2igo1p_START --> <meta property="og:title" content="Domo Data Loader"> <meta name="twitter:title" content="Domo Data Loader"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@evidence_dev"> <!-- HEAD_svelte-2igo1p_END -->
	</head>
	<body>
		<script>
			
		</script>
		<div>
			<!-- SvelteKit Hydrated Content -->
			   <div class="z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80"></div> <div data-sveltekit-preload-data="hover" class="antialiased"> <header class="fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden"><div class=" max-w-7xl mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between" style="max-width:undefinedpx;"><div class="flex gap-x-4 items-center"><button type="button" class="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 md:hidden"><span class="sr-only" data-svelte-h="svelte-73kebv">Open sidebar</span> <svg class="w-5 h-5" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M4 6l16 0"></path><path d="M4 12l16 0"></path><path d="M4 18l16 0"></path></svg></button> <a href="/" class="text-sm font-bold text-base-content hidden md:block"><img src="/_app/immutable/assets/wordmark-black.rfl-FBgf.png" alt="Home" class="h-5 aspect-auto block dark:hidden" href="/"> <img src="/_app/immutable/assets/wordmark-white.C8ZS96Ri.png" alt="Home" class="h-5 aspect-auto hidden dark:block" href="/"></a></div> <div class="flex gap-2 text-sm items-center"> <div class="flex gap-2 items-center">   </div> <div class="relative"> <button type="button" tabindex="0" aria-controls="ZcpWWg7HxZ" aria-expanded="false" data-state="closed" id="V4rl9n_YYJ" data-melt-dropdown-menu-trigger="" data-menu-trigger="" class="inline-flex items-center justify-center font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-base-content-muted disabled:pointer-events-none disabled:opacity-50 hover:text-base-content h-8 rounded-md text-xs px-1 hover:bg-base-200 shadow-base-200" aria-label="Menu" data-button-root=""><svg class="h-6 w-6" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M5 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path><path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path><path d="M19 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path></svg></button> </div></div></div></header> <div class=" max-w-7xl print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start" style="max-width:undefinedpx;"><div class="print:hidden">  <aside class="w-48 flex-none hidden md:flex"><div class="hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar"><div class="flex flex-col pb-6"><a class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading" href="/">Home</a> </div> </div> <div class="fixed bottom-0 text-xs py-2" data-svelte-h="svelte-fworv4"><a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a></div></aside></div> <main class="md:pl-8 md:pr-8  mt-16 sm:mt-20 flex-grow overflow-x-hidden print:px-0 print:mt-8"><div class="print:hidden"><div class="flex items-start mt-0 whitespace-nowrap overflow-auto"><div class="inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4"><a href="/" class="hover:underline">Home </a></div></div></div> <article id="evidence-main-article" class="select-text markdown pb-10">  <h1 class="title">Domo Data Loader</h1>     <h1 class="markdown" id="domo-data-loader" data-svelte-h="svelte-94lco6"><a href="#domo-data-loader">Domo Data Loader</a></h1> <p class="markdown" data-svelte-h="svelte-1s6ws79">Load datasets from Domo into DuckDB for analysis with Evidence.</p> <div class="dev-banner svelte-16omzez" data-svelte-h="svelte-1top7fi"><h3 class="svelte-16omzez">🚀 Enhanced Domo DDX Integration</h3> <p>This app now supports real DuckDB integration, multiple dataset loading, iframe compatibility, and data visualizations!</p></div>  <div class="workflow-picker-section svelte-16omzez"><div id="domo-workflow-picker" class="workflow-picker svelte-16omzez"><div class="workflow-step svelte-16omzez"><label for="dataset-selector" class="svelte-16omzez" data-svelte-h="svelte-1fci9ty">Select Dataset:</label> <select id="dataset-selector" class="dataset-dropdown svelte-16omzez"><option value="" data-svelte-h="svelte-59d9xk">Choose a dataset...</option></select></div> <div id="dataset-preview" class="dataset-preview svelte-16omzez" style="display: none;" data-svelte-h="svelte-19tcyh0"><h4>📊 Dataset Information</h4> <div id="preview-content" class="preview-content"><div id="dataset-info" class="dataset-info-grid"></div> <div class="dataset-tabs"><button class="tab-button active" data-tab="schema">Schema</button> <button class="tab-button" data-tab="sample">Sample Data</button> <button class="tab-button" data-tab="metadata">Metadata</button></div> <div id="schema-tab" class="tab-content active"><div id="schema-table" class="schema-table"></div></div> <div id="sample-tab" class="tab-content"><div class="preview-actions svelte-16omzez"><button id="preview-btn" class="btn btn-secondary svelte-16omzez">🔍 Load Sample Data</button> <span class="preview-note">Shows first 10 rows</span></div> <div id="data-preview" class="data-preview svelte-16omzez" style="display: none;"></div></div> <div id="metadata-tab" class="tab-content"><div id="dataset-metadata" class="dataset-metadata"></div></div></div></div> <div id="loading-config" class="workflow-step svelte-16omzez" style="display: none;"><h4 data-svelte-h="svelte-6xztzo">⚙️ Loading Configuration</h4> <div class="config-grid svelte-16omzez"><div class="config-item" data-svelte-h="svelte-199xsoc"><label for="table-name" class="svelte-16omzez">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-16omzez"> <small class="field-help">Use lowercase letters, numbers, and underscores only</small></div> <div class="config-item"><label for="refresh-mode" class="svelte-16omzez" data-svelte-h="svelte-p1qydn">Refresh Mode:</label> <select id="refresh-mode" class="svelte-16omzez"><option value="replace" data-svelte-h="svelte-qvzdub">Replace existing data</option><option value="append" data-svelte-h="svelte-idsvi6">Append to existing data</option></select> <small class="field-help" data-svelte-h="svelte-fpi9b6">Choose how to handle existing data</small></div> <div class="config-item" data-svelte-h="svelte-15v44ck"><label for="row-limit" class="svelte-16omzez">Row Limit (optional):</label> <input id="row-limit" type="number" placeholder="Leave empty for all rows" min="1" max="1000000" class="svelte-16omzez"> <small class="field-help">Limit rows for testing (max 1M)</small></div> <div class="config-item" data-svelte-h="svelte-5ivc8q"><label class="svelte-16omzez"><input type="checkbox" id="create-index" checked class="svelte-16omzez">
            Create indexes for better performance</label></div></div></div> <div id="workflow-actions" class="workflow-actions svelte-16omzez" style="display: none;" data-svelte-h="svelte-68tw8y"><div class="action-buttons"><button id="validate-config-btn" class="btn btn-secondary svelte-16omzez">✅ Validate Configuration</button> <button id="load-dataset-btn" class="btn btn-primary svelte-16omzez">📊 Load Dataset into DuckDB</button></div> <div id="validation-results" class="validation-results" style="display: none;"></div></div> <div id="loading-status" class="loading-status svelte-16omzez" style="display: none;" data-svelte-h="svelte-1oq3jl0"><div class="loading-spinner svelte-16omzez"></div> <p id="loading-message" class="svelte-16omzez">Loading...</p> <div class="progress-bar"><div class="progress-fill" id="progress-fill"></div></div></div></div></div> <div id="loaded-datasets-section" style="display: none;" data-svelte-h="svelte-n8w8em"><h2 class="markdown">📚 Loaded Datasets</h2> <div class="loaded-datasets-header"><p>Datasets currently available in DuckDB for analysis:</p> <button id="refresh-loaded-btn" class="btn btn-secondary btn-small svelte-16omzez">🔄 Refresh</button></div> <div id="loaded-datasets-list" class="loaded-datasets-grid"></div></div> <h2 class="markdown" id="sample-evidence-visualizations" data-svelte-h="svelte-1t5nmyw"><a href="#sample-evidence-visualizations">Sample Evidence Visualizations</a></h2> <p class="markdown" data-svelte-h="svelte-nbtkwk">Once you load data, you can create Evidence-style visualizations. Here are some examples:</p> <h3 class="markdown" id="sales-performance-dashboard" data-svelte-h="svelte-bguwp4"><a href="#sales-performance-dashboard">Sales Performance Dashboard</a></h3> <div class="over-container svelte-1ursthx"> </div>     <div role="none" class="chart-container mt-2 mb-3 svelte-db4qxn"><div role="status" class="animate-pulse"><span class="sr-only" data-svelte-h="svelte-1wtojot">Loading...</span> <div class="bg-base-100 rounded-md max-w-[100%]" style="height:222px; margin-top: 15px; margin-bottom: 31px;"></div></div>  <div class="chart-footer svelte-db4qxn"> </div> </div>      <div role="none" class="chart-container mt-2 mb-3 svelte-db4qxn"><div role="status" class="animate-pulse"><span class="sr-only" data-svelte-h="svelte-1wtojot">Loading...</span> <div class="bg-base-100 rounded-md max-w-[100%]" style="height:237px; margin-top: 15px; margin-bottom: 31px;"></div></div>  <div class="chart-footer svelte-db4qxn"> </div> </div>  <h3 class="markdown" id="key-metrics" data-svelte-h="svelte-aivela"><a href="#key-metrics">Key Metrics</a></h3>   <div class="inline-block font-sans pt-2 pb-3 pl-0 mr-3 items-center align-top" style="
        min-width: 18%;
        max-width: none;
		"><p class="text-sm align-top leading-none">Total Revenue </p> <div class="relative text-xl font-medium mt-1.5">   <span style="color: ">$150,000 </span> </div> </div>   <div class="inline-block font-sans pt-2 pb-3 pl-0 mr-3 items-center align-top" style="
        min-width: 18%;
        max-width: none;
		"><p class="text-sm align-top leading-none">Total Units </p> <div class="relative text-xl font-medium mt-1.5">   <span style="color: ">1,250 </span> </div> </div> <hr class="markdown"> <p class="markdown" data-svelte-h="svelte-1mg5bp7"><strong class="markdown">Next Steps:</strong></p> <ol class="markdown" data-svelte-h="svelte-1ei1c0u"><li class="markdown">Select a dataset from the dropdown above</li> <li class="markdown">Configure loading options</li> <li class="markdown">Load the data into DuckDB</li> <li class="markdown">Run SQL queries to explore your data</li> <li class="markdown">Create Evidence visualizations with your results</li></ol></article></main> <div class="print:hidden"><aside class="hidden lg:block w-48"><div class="fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar"></div></aside></div></div></div>  
			<script type="application/json" data-sveltekit-fetched data-url="/api/customFormattingSettings.json/GET.json">{"status":200,"statusText":"","headers":{},"body":"{\"customFormattingSettings\":{\"version\":\"1.0\",\"customFormats\":[]}}"}</script>
			<script type="application/json" data-sveltekit-fetched data-url="/api///evidencemeta.json">{"status":200,"statusText":"","headers":{},"body":"{\"queries\":[{\"id\":\"sample_sales\",\"compiledQueryString\":\"SELECT\\n  'Product A' as product,\\n  150000 as revenue,\\n  1250 as units_sold,\\n  '2024-01' as month\\nUNION ALL\\nSELECT 'Product B', 125000, 980, '2024-01'\\nUNION ALL\\nSELECT 'Product C', 180000, 1450, '2024-01'\\nUNION ALL\\nSELECT 'Product A', 165000, 1380, '2024-02'\\nUNION ALL\\nSELECT 'Product B', 140000, 1120, '2024-02'\\nUNION ALL\\nSELECT 'Product C', 195000, 1580, '2024-02'\",\"inputQueryString\":\"SELECT\\n  'Product A' as product,\\n  150000 as revenue,\\n  1250 as units_sold,\\n  '2024-01' as month\\nUNION ALL\\nSELECT 'Product B', 125000, 980, '2024-01'\\nUNION ALL\\nSELECT 'Product C', 180000, 1450, '2024-01'\\nUNION ALL\\nSELECT 'Product A', 165000, 1380, '2024-02'\\nUNION ALL\\nSELECT 'Product B', 140000, 1120, '2024-02'\\nUNION ALL\\nSELECT 'Product C', 195000, 1580, '2024-02'\",\"compiled\":false,\"inline\":true}]}"}</script>
			<script type="application/json" data-sveltekit-fetched data-url="/api/pagesManifest.json">{"status":200,"statusText":"","headers":{},"body":"{\"label\":\"Home\",\"href\":\"/\",\"children\":{},\"frontMatter\":{\"title\":\"Domo Data Loader\"},\"isTemplated\":false,\"isPage\":true}"}</script>
			<script>
				{
					__sveltekit_htt63e = {
						base: ""
					};

					const element = document.currentScript.parentElement;

					const data = [null,null];

					Promise.all([
						import("/_app/immutable/entry/start.C3d0B0DB.js"),
						import("/_app/immutable/entry/app.BniU_0Yn.js")
					]).then(([kit, app]) => {
						kit.start(app, element, {
							node_ids: [0, 4],
							data,
							form: null,
							error: null
						});
					});
				}
			</script>
		

			<!-- SplashScreen -->
			<div
				aria-disabled
				id="__evidence_project_splash"
				data-test-id="__evidence_project_splash"
				style="visibility: hidden"
			>
				<svg width="100" height="100" viewBox="-8 -8 588 588" xmlns="http://www.w3.org/2000/svg">
					<path
						d="M7.19462e-05 74.3583C109.309 74.3583 195.795 86.2578 286.834 37.825C377.872 -10.6077 466.416 1.29174 573.667 1.29175L573.667 126.549C466.416 126.549 377.373 114.91 286.834 163.082C196.294 211.254 109.309 199.615 6.11417e-05 199.615L7.19462e-05 74.3583Z"
						class="draw-path"
					/>
					<path
						d="M573.669 499.31C464.36 499.31 377.874 487.411 286.835 535.843C195.797 584.276 107.252 572.377 0.0014801 572.377V447.12C107.252 447.12 196.295 458.758 286.835 410.586C377.375 362.415 464.36 374.053 573.669 374.053V499.31Z"
						class="draw-path"
					/>
					<path
						d="M452.896 186.499C395.028 187.686 341.581 194.947 286.835 224.074C211.396 264.212 136.995 262.826 52.2355 261.247C35.2696 260.931 17.8887 260.608 0.0014801 260.608V385.865C18.1032 385.865 35.6721 386.204 52.81 386.534C137.212 388.162 211.162 389.589 286.835 349.331C341.838 320.07 395.18 312.831 452.896 311.685V186.499Z"
						class="draw-path"
					/>
				</svg>
			</div>
		</div>
	</body>
</html>

<style>
	#__evidence_project_splash {
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		background-color: #ffffff;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}

	.theme-dark #__evidence_project_splash {
		background-color: #000000;
	}

	.draw-path {
		fill: #000000;
		animation: blinking-logo 2s;
		animation-fill-mode: both;
		animation-iteration-count: infinite;
		animation-timing-function: ease-in-out;
	}

	.theme-dark .draw-path {
		fill: #ffffff;
	}

	@keyframes blinking-logo {
		0% {
			fill-opacity: 1;
		}
		50% {
			fill-opacity: 0.2;
		}
		100% {
			fill-opacity: 1;
		}
	}
</style>

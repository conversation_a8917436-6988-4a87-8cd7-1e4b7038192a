{"queries": [{"id": "sample_sales", "compiledQueryString": "SELECT\n  'Product A' as product,\n  150000 as revenue,\n  1250 as units_sold,\n  '2024-01' as month\nUNION ALL\nSELECT 'Product B', 125000, 980, '2024-01'\nUNION ALL\nSELECT 'Product C', 180000, 1450, '2024-01'\nUNION ALL\nSELECT 'Product A', 165000, 1380, '2024-02'\nUNION ALL\nSELECT 'Product B', 140000, 1120, '2024-02'\nUNION ALL\nSELECT 'Product C', 195000, 1580, '2024-02'", "inputQueryString": "SELECT\n  'Product A' as product,\n  150000 as revenue,\n  1250 as units_sold,\n  '2024-01' as month\nUNION ALL\nSELECT 'Product B', 125000, 980, '2024-01'\nUNION ALL\nSELECT 'Product C', 180000, 1450, '2024-01'\nUNION ALL\nSELECT 'Product A', 165000, 1380, '2024-02'\nUNION ALL\nSELECT 'Product B', 140000, 1120, '2024-02'\nUNION ALL\nSELECT 'Product C', 195000, 1580, '2024-02'", "compiled": false, "inline": true}]}